package middleware

import (
	"aimsg-server/global"
	"aimsg-server/model/common/response"
	"aimsg-server/utils"
	"bytes"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"io"
	"strings"
)

type AppResponseWriterWrapper struct {
	gin.ResponseWriter
	Body *bytes.Buffer // 缓存
}

func (w AppResponseWriterWrapper) Write(b []byte) (int, error) {
	return w.Body.Write(b)
}

func (w AppResponseWriterWrapper) WriteString(s string) (int, error) {
	return w.Body.WriteString(s)
}

func AppReqMiddleware(appInfo model.App) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			err            error
			raw            []byte
			newReqMapBytes []byte
			rawReqMap      = map[string]interface{}{}
			newReqMap      = map[string]interface{}{}
			resBodyMap     = map[string]interface{}{}
			appPrefix      = appInfo.Prefix
			prefix         = fmt.Sprintf("%s_", appPrefix)
		)
		if c.Request.Method == "GET" {
			raw = []byte(c.Request.URL.RawQuery)
		} else {
			raw, _ = c.GetRawData()
			if len(raw) == 0 {
				c.Request.Body = io.NopCloser(bytes.NewBuffer(raw))
			} else {
				if err = json.Unmarshal(raw, &rawReqMap); err != nil {
					response.FailWithMessage(fmt.Sprintf("AppReqMiddleware json.Unmarshal error %s", err.Error()), c)
					c.Abort()
					return
				}
				// 替换请求参数key
				newReqMap = utils.MapReduceKey(rawReqMap, prefix)
				if newReqMapBytes, err = json.Marshal(newReqMap); err != nil {
					response.FailWithMessage(fmt.Sprintf("AppReqMiddleware json.Marshal error %s", err.Error()), c)
					c.Abort()
					return
				}
				// 重新设置request的Body
				c.Request.Body = io.NopCloser(bytes.NewBuffer(newReqMapBytes))
			}
		}
		// 设置上下文变量
		c.Set(global.GinContextRawData, raw)
		appHeaderPrefix := utils.CapitalizeAndAddHyphen(appPrefix)
		// 替换请求头
		for headerKey, _ := range c.Request.Header {
			headerContent := c.Request.Header.Get(headerKey)
			if strings.Contains(headerKey, appHeaderPrefix) {
				newHeaderKey := strings.Replace(headerKey, appHeaderPrefix, "", 1)
				c.Request.Header.Add(newHeaderKey, headerContent)
			}
		}
		blw := &ResponseWriterWrapper{Body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = blw
		defer func() {
			blw.Body.Reset() // 重置缓冲区以备后续请求使用
		}()
		c.Next()
		resBodyBytes := blw.Body.Bytes()
		if len(resBodyBytes) > 0 {
			if err = json.Unmarshal(resBodyBytes, &resBodyMap); err != nil {
				response.FailWithMessage(fmt.Sprintf("AppReqMiddleware json.Unmarshal resBodyBytes error %s", err.Error()), c)
				c.Abort()
				return
			}
			dataInterface, dataInterfaceExist := resBodyMap["data"]
			if dataInterfaceExist {
				// 定义一个新的dataInterface
				var newDataInterface interface{}
				// 断言判断dataInterface的类型
				switch v := dataInterface.(type) {
				case map[string]interface{}:
					newDataInterface = utils.MapReplaceKey(v, appPrefix)
				case []interface{}:
					newDataInterface = utils.SliceReplaceKey(v, appPrefix)
				default:
					newDataInterface = dataInterface
				}
				// 替换data字段为新的数据
				resBodyMap["data"] = newDataInterface
				resBodyBytes, err = json.Marshal(resBodyMap)
				if err != nil {
					response.FailWithMessage(fmt.Sprintf("AppReqMiddleware json.Marshal resBodyMap error %s", err.Error()), c)
					c.Abort()
					return
				}
			}
		}
		_, _ = blw.ResponseWriter.Write(resBodyBytes)
	}
}
