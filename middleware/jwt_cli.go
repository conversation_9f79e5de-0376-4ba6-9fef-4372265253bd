package middleware

import (
	"aimsg-server/global"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"aimsg-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strings"
)

func JWTCliAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		authorizationStr := c.<PERSON>("Authorization")
		if authorizationStr == "" {
			response.JWTFail(c, "jwt is error, no Authorization, please check")
			c.Abort()
			return
		}
		authorizationStrArr := strings.Split(authorizationStr, " ")
		if len(authorizationStrArr) != 2 || authorizationStrArr[0] != "Bearer" {
			response.JWTFail(c, "jwt is error, no Bearer, please check")
			c.Abort()
			return
		}
		token := authorizationStrArr[1]
		if token == "" {
			response.JWTFail(c, "jwt is error, no token, please check")
			c.Abort()
			return
		}
		claims, err := utils.JwtCliObj.ParseToken(token)
		if err != nil {
			response.JWTFail(c, err.Error())
			c.Abort()
			return
		}
		userIdInterface, userIdExist := claims["id"]
		if !userIdExist {
			response.JWTFail(c, "jwt is error, no id, please check")
			c.Abort()
			return
		}
		appIdInterface, appIdExist := claims["app_id"]
		if !appIdExist {
			response.JWTFail(c, "jwt is error, no app_id, please check")
			c.Abort()
			return
		}
		userIdUint := utils.Uint(userIdInterface)
		go func() {
			defer func() {
				if rErr := recover(); rErr != nil {
					global.LOG.Error("JWTCliAuth Update active_at", zap.Any("err", rErr))
				}
			}()
			// 每天只更新一次用户活跃时间
			userService := service.ServiceGroup{}.BackendServiceGroup.DigitalUserService
			userService.SetDayUserActive(c, userIdUint)
		}()

		appIdUint := utils.Uint(appIdInterface)
		c.Set("claims", claims)
		c.Set(global.GinContextUserId, userIdUint)
		c.Set(global.GinContextAppId, appIdUint)
		c.Next()
	}
}
