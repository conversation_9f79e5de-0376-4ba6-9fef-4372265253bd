package middleware

import (
	"aimsg-server/global"
	"bytes"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"io"
	"time"
)

type ResponseWriterWrapper struct {
	gin.ResponseWriter
	Body *bytes.Buffer // 缓存
}

func (w ResponseWriterWrapper) Write(b []byte) (int, error) {
	return w.Body.Write(b)
}

func (w ResponseWriterWrapper) WriteString(s string) (int, error) {
	return w.Body.WriteString(s)
}

func ReqMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			raw            []byte
			reqHeaderBytes []byte
			reqHeaderStr   string
			resHeaderBytes []byte
			resHeaderStr   string
			blw            = &ResponseWriterWrapper{Body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		)
		if c.Request.Method == "GET" {
			raw = []byte(c.Request.URL.RawQuery)
		} else {
			raw, _ = c.GetRawData()
			// 重新设置request的Body
			c.Request.Body = io.NopCloser(bytes.NewBuffer(raw))
		}
		// 设置上下文变量
		c.Set(global.GinContextRawData, raw)

		now := time.Now()
		c.Writer = blw
		c.Next()
		latency := time.Since(now)
		reqHeaderBytes, _ = json.Marshal(c.Request.Header)
		reqHeaderStr = string(reqHeaderBytes)
		resHeaderBytes, _ = json.Marshal(blw.Header())
		resHeaderStr = string(resHeaderBytes)
		global.LOG.Info("ReqMiddleware",
			zap.String("URL", c.Request.URL.String()),
			zap.String("Method", c.Request.Method),
			zap.Float64("Latency", latency.Seconds()),
			zap.String("ReqHeader", reqHeaderStr),
			zap.String("ReqBody", string(raw)),
			zap.Int("ResStatus", c.Writer.Status()),
			zap.String("ResHeader", resHeaderStr),
			zap.String("ResBody", blw.Body.String()),
			zap.String("business", "req_middleware"),
		)
		blw.ResponseWriter.Write(blw.Body.Bytes())
	}
}
