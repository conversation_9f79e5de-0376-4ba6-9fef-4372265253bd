package middleware

import (
	"aimsg-server/global"
	"aimsg-server/model/common/response"
	"aimsg-server/utils"
	"fmt"
	"runtime"
	"strings"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
)

func CliHeader() gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			err       error
			cliHeader model.CliHeader
		)
		if err = c.ShouldBindHeader(&cliHeader); err != nil {
			response.FailWithMessage(err.Error(), c)
			c.Abort()
			return
		}
		if err = global.Validate.Struct(cliHeader); err != nil {
			response.FailWithMessage(err.Error(), c)
			c.Abort()
			return
		}
		// 获取App信息
		appInfo, exist := global.AppMap[cliHeader.AppId]
		if !exist {
			response.FailWithMessage("appId not exist", c)
			c.Abort()
			return
		}
		//
		if cliHeader.AppId == 3 && cliHeader.AppVersion == "1.0.0" {
			cliHeader.AppVersion = "1.16.0"
			c.Request.Header.Set("App-Version", "1.16.0")
		}
		// 检测sign
		md5PreStr := fmt.Sprintf("%s%s", cliHeader.ClientTime, appInfo.Key)
		genSign := utils.MD5(md5PreStr)
		if genSign != cliHeader.Sign {
			//global.LOG.Error("sign not equal", zap.String("md5PreStr", md5PreStr), zap.String("genSign", genSign), zap.String("cliHeaderSign", cliHeader.Sign), zap.String("AppVersion", cliHeader.AppVersion), zap.String("URL", c.Request.URL.String()), zap.String("Method", c.Request.Method), zap.Any("Header", c.Request.Header))
		}
		notSoldCountryCode := map[string]bool{
			"CN": true,
			"HK": true,
			"MO": true,
			"TW": true,
		}
		loginIp := c.ClientIP()
		if runtime.GOOS == "linux" {
			if !global.REDIS.SIsMember(c, global.IP_WHITE_LIST, loginIp).Val() {
				//global.LOG.Info(fmt.Sprintf("ip not %s", loginIp))
				countryCode := utils.GetCountryCode(c)
				if notSoldCountryCode[countryCode] {
					response.JWTFail(c, "警告！本产品不向中国大陆及港澳台以及中国人提供任何服务，请退出！Warning! This product does not provide any services to Chinese Mainland, Hong Kong, Macao, Taiwan, or countrymen. Please exit!")
					c.Abort()
					return
				}
			}
		}
		var clientLanguageLower string
		if cliHeader.ClientLanguage != "" {
			clientLanguageLower = strings.ToLower(cliHeader.ClientLanguage)
		}
		if clientLanguageLower != "" && strings.Contains(clientLanguageLower, "zh") {
			response.JWTFail(c, "警告！本产品不向中国大陆及港澳台以及中国人提供任何服务，请退出！Warning! This product does not provide any services to Chinese Mainland, Hong Kong, Macao, Taiwan, or countrymen. Please exit!")
			c.Abort()
			return
		}

		c.Set(global.GinContextCliHeader, cliHeader)
		c.Set(global.GinContextAppId, cliHeader.AppId)
		c.Set(global.GinContextAppVersion, cliHeader.AppVersion)
		c.Set(global.GinContextDeviceId, cliHeader.DeviceId)
		c.Next()
	}
}
