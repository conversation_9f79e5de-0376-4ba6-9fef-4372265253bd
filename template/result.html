<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
          name="viewport">
    <meta content="ie=edge" http-equiv="X-UA-Compatible">
    <title>Payment Result</title>
    <style>
        * {
            margin: 0;
            padding: 0
        }

        .title {
            font-size: 30px;
            color: red;
            text-align: center;
            padding-top: 20px
        }

        .content-box {
            padding: 10px;
            min-height: 100vh;
            max-height: 100vh;
            box-sizing: border-box
        }

        .line {
            border: 1px dashed #9f9f9f;
            margin: 20px 0
        }

        .content-txt {
            font-size: 16px;
            text-align: center;
        }

        .bottom-button {
            display: flex;
            justify-content: center;
            padding-top: 40px;

            button {
                cursor: pointer;
                border: none;
                width: 280px;
                height: 48px;
                background: linear-gradient(225deg, #ff6f58, #fd267d);
                border-radius: 24px;
                font-size: 18px;
                font-weight: 500;
                color: #fff;
                line-height: 20px
            }
        }
    </style>
</head>
<body>
<div class="content-box">
    <p class="title">Payment processing</p>
    <div class="line"></div>
    <p class="content-txt">Your payment order is being processed.</p>
    <p class="content-txt">Please return to the app to view the purchase results. </p>
    <p class="content-txt">If there are any abnormal purchasing situations, please contact us through Me > Setting >
        Feedback </p>
    <div class="bottom-button">
        <button class="btn-back" onclick="goToApp()">Go back App</button>
    </div>
</div>

<script>
    function goToApp() {
        try {
            if (window.Bak != null) {
                window.Bak.closeWebView()
            }
        } catch (e) {
            console.log(e)
        }
    }
</script>

</body>
</html>
