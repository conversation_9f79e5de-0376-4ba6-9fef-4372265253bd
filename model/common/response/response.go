package response

import (
	"aimsg-server/global"
	"net/http"

	"github.com/gin-gonic/gin"
)

type Response struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

const (
	ERROR       = 400
	JWT_ERROR   = 403
	REQUIRESUB  = 410
	REQUIREFLO  = 420
	REQUIRECHAT = 430
	TURNFLOSHOP = 440
	VOICENOFLO  = 450
	REQUIRESVIP = 460
	ContentVip  = 601
	ChatVip     = 602
	SUCCESS     = 0
	CREATELIMIT = 610
	EMAILISNULL = 611
)

func Result(code int, data interface{}, msg string, c *gin.Context) {
	// 开始时间
	c.JSON(http.StatusOK, Response{
		code,
		msg,
		data,
	})
}

func Ok(c *gin.Context) {
	Result(SUCCESS, nil, "success", c)
}

func RequireSub(c *gin.Context, data interface{}) {
	Result(REQUIRESUB, data, global.ChatLimitMsg, c)
}

func RequireSVip(c *gin.Context) {
	Result(REQUIRESVIP, nil, global.SVipMsg, c)
}

func RequireBuyFlo(c *gin.Context) {
	Result(REQUIREFLO, nil, "Gold coin amount is not enough", c)
}

func RequireContentVip(c *gin.Context) {
	Result(ContentVip, nil, "", c)
}

func RequireChatVip(c *gin.Context) {
	Result(ChatVip, nil, "", c)
}

func VoiceNoFlo(c *gin.Context) {
	Result(VOICENOFLO, nil, global.VoiceNoFloErrMsg, c)
}

func CreateLimit(c *gin.Context) {
	Result(CREATELIMIT, nil, "exceeded the upper limit of concurrent generation. Please try again later", c)
}

func EmailIsNull(c *gin.Context) {
	Result(EMAILISNULL, nil, "Please fill in your email address so that we can contact you in a timely manner", c)
}

func TurnToFloShop(c *gin.Context) {
	Result(TURNFLOSHOP, nil, global.TurnToFloShop, c)
}

func RequireBuyChat(c *gin.Context) {
	Result(REQUIRECHAT, nil, global.BuyChatMsg, c)
}

func OkWithMessage(message string, c *gin.Context) {
	Result(SUCCESS, map[string]interface{}{}, message, c)
}

func OkWithData(data interface{}, c *gin.Context) {
	Result(SUCCESS, data, "success", c)
}

func OkWithDetailed(data interface{}, message string, c *gin.Context) {
	Result(SUCCESS, data, message, c)
}

func Fail(c *gin.Context) {
	Result(ERROR, nil, "fail", c)
}

func JWTFail(c *gin.Context, message string) {
	Result(JWT_ERROR, nil, message, c)
}

func FailWithMessage(message string, c *gin.Context) {
	Result(ERROR, nil, message, c)
}

func FailWithDetailed(data interface{}, message string, c *gin.Context) {
	Result(ERROR, data, message, c)
}
