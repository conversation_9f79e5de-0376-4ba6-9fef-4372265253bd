package request

// PageInfo Paging common input parameter structure
type PageInfo struct {
	Page     int    `json:"page"      form:"page"`     // 页码
	PageSize int    `json:"pageSize"  form:"pageSize"` // 每页大小
	SortProp string `json:"sort_prop" form:"sort_prop"`
	SortDesc bool   `json:"sort_desc" form:"sort_desc"`
}

type PageInfoNew struct {
	Page     int    `json:"page"      form:"page"`       // 页码
	PageSize int    `json:"page_size"  form:"page_size"` // 每页大小
	SortProp string `json:"sort_prop" form:"sort_prop"`
	SortDesc bool   `json:"sort_desc" form:"sort_desc"`
}

type PageInfoM struct {
	Page     int64  `json:"page"      form:"page"`      // 页码
	PageSize int64  `json:"page_size" form:"page_size"` // 每页大小
	SortProp string `json:"sort_prop" form:"sort_prop"`
	SortDesc bool   `json:"sort_desc" form:"sort_desc"`
}

// GetById Find by id structure
type GetById struct {
	ID int `json:"id" form:"id"` // 主键ID
}

type IdReq struct {
	ID uint `json:"id" form:"id" validate:"required"`
}

func (r *GetById) Uint() uint {
	return uint(r.ID)
}

type IdsReq struct {
	Ids []int `json:"ids" form:"ids[]"`
}

// GetAuthorityId Get role by id structure
type GetAuthorityId struct {
	AuthorityId uint `json:"authorityId" form:"authorityId"` // 角色ID
}

type Empty struct{}

type SyncImgConfigItem struct {
	NormalList []string
	AuditList  []string
}
