package request

// Speed API Webhook 回调结构体
type SpeedWebhookRequest struct {
	ApiVersion string `json:"api_version"`
	Data       struct {
		Object struct {
			Amount          float64 `json:"amount"`
			Created         int64   `json:"created"`
			Currency        string  `json:"currency"`
			EstFees         float64 `json:"est_fees"`
			ExchangeRate    float64 `json:"exchange_rate"`
			FailureReason   string  `json:"failure_reason"`
			ID              string  `json:"id"`
			Modified        int64   `json:"modified"`
			Object          string  `json:"object"`
			PaymentPreimage string  `json:"payment_preimage"`
			ReferenceID     string  `json:"reference_id"`
			ReferenceType   string  `json:"reference_type"`
			SpeedFee        struct {
				Amount     float64 `json:"amount"`
				Percentage float64 `json:"percentage"`
			} `json:"speed_fee"`
			StatementDescriptor string  `json:"statement_descriptor"`
			Status              string  `json:"status"`
			TargetAmount        float64 `json:"target_amount"`
			TargetAmountPaid    float64 `json:"target_amount_paid"`
			TargetAmountPaidAt  int64   `json:"target_amount_paid_at"`
			TargetCurrency      string  `json:"target_currency"`
			WithdrawMethod      string  `json:"withdraw_method"`
			WithdrawRequest     string  `json:"withdraw_request"`
		} `json:"object"`
	} `json:"data"`
	EventType string `json:"event_type"`
	ID        string `json:"id"`
	Livemode  bool   `json:"livemode"`
	Object    string `json:"object"`
	Request   struct {
		ID string `json:"id"`
	} `json:"request"`
}
