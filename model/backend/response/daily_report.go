package response

import (
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/shopspring/decimal"
	"gorm.io/datatypes"
)

type DailyReportListRes struct {
	model.MODEL
	Day        datatypes.Date  `json:"day"         form:"day"         gorm:"column:day;comment:日期"`
	AiRoleId   uint            `json:"ai_role_id"  form:"ai_role_id"  gorm:"column:ai_role_id;comment:AI角色ID;index;"`
	RoleId     string          `json:"role_id"     form:"role_id"`
	RevenueUSD decimal.Decimal `json:"revenue_usd" form:"revenue_usd" gorm:"column:revenue_usd;default:0;comment:美元收入;"`
}
