// 自动生成模板AiBodyImgCache
package backend

import (
	"aimsg-server/global"
)

// aiBodyImgCache表 结构体  AiBodyImgCache
type AiBodyImgCache struct {
	global.GVA_MODEL
	AiId              int    `json:"ai_id" form:"ai_id" gorm:"column:ai_id;comment:角色ID;size:19;"binding:"required"`                                    // 角色ID
	AiRoleId          string `json:"ai_role_id" form:"ai_role_id" gorm:"column:ai_role_id;comment:AI角色ID;size:32;"binding:"required"`                   // AI角色ID
	BodyImgFullUrl    string `json:"body_img_full_url" form:"body_img_full_url" gorm:"column:body_img_full_url;comment:lv1图片换脸后的地址;size:512;"`          // lv1图片换脸后的地址
	BodyImgKey        string `json:"body_img_key" form:"body_img_key" gorm:"column:body_img_key;comment:lv1图片的key;size:512;"binding:"required"`         // lv1图片的key
	UndressImgFullUrl string `json:"undress_img_full_url" form:"undress_img_full_url" gorm:"column:undress_img_full_url;comment:lv2图片换脸后的地址;size:512;"` // lv2图片换脸后的地址
	UndressImgKey     string `json:"undress_img_key" form:"undress_img_key" gorm:"column:undress_img_key;comment:lv2图片的key;size:512;"`                  // lv2图片的key
}

// TableName aiBodyImgCache表 AiBodyImgCache自定义表名 ai_body_img_cache
func (AiBodyImgCache) TableName() string {
	return "ai_body_img_cache"
}
