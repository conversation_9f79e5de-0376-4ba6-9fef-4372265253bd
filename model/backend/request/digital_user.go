package request

import (
	"aimsg-server/model/common/request"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
)

type DigitalUserSearch struct {
	request.PageInfo
	model.DigitalUser
	CreatedAtRange []string `json:"created_at_range" form:"created_at_range[]"`
	UpdatedAtRange []string `json:"updated_at_range" form:"updated_at_range[]"`
}

type SetContinuousActiveDayReq struct {
	UserId uint `json:"user_id"`
	Day    int  `json:"day"`
}
