package request

type MsgAdsPlanUpdateReq struct {
	Plan  string      `json:"plan" form:"plan" validate:"required"`
	AppId uint        `json:"app_id" form:"app_id"`
	Data  interface{} `json:"data" form:"data" validate:"required"`
}

type SubNoBuyFloCfg struct {
	Minute int `json:"minute"`
	Flo    int `json:"flo"`
}

type AiFuncSwitchCfg struct {
	TouchMsgCount    int `json:"touch_msg_count"`
	OrdersMsgCount   int `json:"orders_msg_count"`
	RolePlayMsgCount int `json:"role_play_msg_count"`
	NSFWShowMsgCount int `json:"nsfw_show_msg_count"`
	GiftMsgCount     int `json:"gift_msg_count"`
	AsmrMsgCount     int `json:"asmr_msg_count"`
	DiyMsgCount      int `json:"diy_msg_count"`
	PrivateCount     int `json:"private_count"`
}

type DiyPhotoCfg struct {
	CountRange []int `json:"count_range"`
}
