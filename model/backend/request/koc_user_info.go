package request

import (
	"aimsg-server/model/common/request"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/shopspring/decimal"
)

type KocUserInfoSearch struct {
	request.PageInfo
	model.KocUserInfo
	CreatedAtRange []string `json:"created_at_range" form:"created_at_range[]"`
	UpdatedAtRange []string `json:"updated_at_range" form:"updated_at_range[]"`
}

type KocUserInfoRegisterReq struct {
	AiRoleId              string                 `json:"ai_role_id"       form:"ai_role_id"       gorm:"column:ai_role_id;comment:AI角色ID;uniqueIndex;"`
	Nickname              string                 `json:"nickname"         form:"nickname"         gorm:"column:nickname;comment:昵称;"`
	Platform              model.WithdrawPlatform `json:"platform"         form:"platform"         gorm:"column:platform;comment:提现平台;index;"`
	Account               string                 `json:"account"          form:"account"          gorm:"column:account;comment:提现账号;"`
	AllIncome             decimal.Decimal        `json:"all_income"       form:"all_income"       gorm:"column:all_income;default:0;comment:总收入;"`
	PendingIncome         decimal.Decimal        `json:"pending_income"   form:"pending_income"   gorm:"column:pending_income;default:0;comment:待提现收入;"`
	AiRoleIdUint          uint                   `json:"ai_role_id_uint" form:"ai_role_id_uint"`
	PendingIncomeDayRange []string               `json:"pending_income_day_range" form:"pending_income_day_range[]"`
}
