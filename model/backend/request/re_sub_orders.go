package request

import (
	"aimsg-server/model/common/request"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
)

type ReSubOrdersSearch struct {
	request.PageInfo
	model.ReSubOrder
	TestStatus     model.SwitchStatus `json:"test_status" form:"test_status"`
	CreatedAtRange []string           `json:"created_at_range" form:"created_at_range[]"`
	UpdatedAtRange []string           `json:"updated_at_range" form:"updated_at_range[]"`
}
