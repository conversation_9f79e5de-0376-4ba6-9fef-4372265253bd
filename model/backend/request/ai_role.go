package request

import (
	"aimsg-server/model/common/request"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
)

type AiRoleSearch struct {
	request.PageInfo
	model.AiRole
	NotRoleType    model.AiRoleType `json:"not_role_type" form:"not_role_type"`
	CreatedAtRange []string         `json:"created_at_range" form:"created_at_range[]"`
	UpdatedAtRange []string         `json:"updated_at_range" form:"updated_at_range[]"`
	IsSortUser     bool             `json:"-"`
}
