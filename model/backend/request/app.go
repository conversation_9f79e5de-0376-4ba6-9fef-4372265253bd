package request

import (
	"aimsg-server/model/common/request"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
)

type AppSearch struct {
	request.PageInfo
	model.App
	CreatedAtRange []string `json:"created_at_range" form:"created_at_range[]"`
	UpdatedAtRange []string `json:"updated_at_range" form:"updated_at_range[]"`
}

type SetAuditPlanReq struct {
	AppId uint `json:"app_id" form:"app_id" validate:"required"`
	Plan  int  `json:"plan" form:"plan" validate:"required"`
}

type SetIpWhiteReq struct {
	IP string `json:"ip" form:"ip" validate:"required"`
}

type CopyAppDataReq struct {
	SourceAppId uint `json:"source_app_id" form:"source_app_id" validate:"required"`
	TargetAppId uint `json:"target_app_id" form:"target_app_id" validate:"required"`
}
