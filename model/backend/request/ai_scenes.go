package request

import (
	"aimsg-server/model/common/request"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
)

type AiScenesSearch struct {
	request.PageInfo
	model.AiScenes
	CreatedAtRange []string `json:"created_at_range" form:"created_at_range[]"`
	UpdatedAtRange []string `json:"updated_at_range" form:"updated_at_range[]"`
}

type CliAiScenesSearch struct {
	request.PageInfo
	model.AiScenes
	RoleID string `json:"role_id" form:"role_id"`
}
