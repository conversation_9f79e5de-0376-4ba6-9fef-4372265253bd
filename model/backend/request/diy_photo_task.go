package request

import (
	"aimsg-server/model/common/request"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"time"
)

type DiyPhotoTaskSearch struct {
	request.PageInfo
	model.DiyPhotoTask
	CreatedAtMin   time.Time `json:"created_at_min" form:"created_at_min"`
	CreatedAtRange []string  `json:"created_at_range" form:"created_at_range[]"`
	UpdatedAtRange []string  `json:"updated_at_range" form:"updated_at_range[]"`
}

type RestartTaskReq struct {
	Id uint `json:"id" form:"id"`
}

type ReSendEmailReq struct {
	Id uint `json:"id" form:"id"`
}

type ReSendPushMsgReq struct {
	Id uint `json:"id" form:"id"`
}

type SwapFaceReq struct {
	AvatarS3Url string `json:"avatar_s3_url" form:"avatar_s3_url" validate:"required"`
	MaskS3Url   string `json:"mask_s3_url" form:"mask_s3_url" validate:"required"`
}
