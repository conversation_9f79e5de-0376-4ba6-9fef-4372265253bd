package request

import (
	"aimsg-server/model/common/request"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
)

type DailyReportSearch struct {
	request.PageInfo
	model.DailyReport
	RoleId         string   `json:"role_id"     form:"role_id"`
	DayRange       []string `json:"day_range" form:"day_range[]"`
	CreatedAtRange []string `json:"created_at_range" form:"created_at_range[]"`
	UpdatedAtRange []string `json:"updated_at_range" form:"updated_at_range[]"`
}
