package request

import (
	"aimsg-server/model/common/request"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/shopspring/decimal"
)

type OrdersSearch struct {
	request.PageInfo
	model.Order
	CreatedAtRange []string `json:"created_at_range" form:"created_at_range[]"`
	UpdatedAtRange []string `json:"updated_at_range" form:"updated_at_range[]"`
}

type TestTdPointerReq struct {
	UserId     uint                   `json:"user_id" form:"user_id"`
	Properties map[string]interface{} `json:"properties" form:"properties"`
}

type AdjustTestReq struct {
	OrderNo string          `json:"order_no" form:"order_no"`
	UserId  uint            `json:"user_id" form:"user_id"`
	Price   decimal.Decimal `json:"price" form:"price"`
}

type SubNoBuyFloQueueItem struct {
	UserId uint `json:"user_id"`
}
