package btc

import (
	"aimsg-server/pkg/channel"
	"encoding/json"
	"time"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
)

type Home struct {
	TotalAssets        *TotalAssets        `json:"total_assets"`
	FreeComputingPower *FreeComputingPower `json:"free_computing_power"`
	MoreComputingPower *MoreComputingPower `json:"more_computing_power"`
	FAQs               *FAQs               `json:"faqs"`
}

type TotalAssets struct {
	Content string        `json:"content"`
	List    []AssetDetail `json:"list"`
}

type AssetDetail struct {
	Title   string `json:"title"`
	Icon    string `json:"icon"`
	Content string `json:"content"`
}

type FreeComputingPower struct {
	Title    string                 `json:"title"`
	Header   []ComputingPowerHeader `json:"header"`
	SignList []SignDetail           `json:"sign_list"`
}

type ComputingPowerHeader struct {
	ID             uint   `json:"id"`
	Name           string `json:"name"`
	Img            string `json:"img"`
	Desc           string `json:"desc"`
	Coefficient    string `json:"coefficient"`
	CoefficientImg string `json:"coefficient_img"`
	Used           int    `json:"used"`
	Total          int    `json:"total"`
	Type           int    `json:"type"`
}

type SignDetail struct {
	ID      int    `json:"id"`
	Title   string `json:"title"`
	Content string `json:"content"`
	IsToday int    `json:"is_today"`
	Status  int    `json:"status"`
}

type MoreComputingPower struct {
	Title       string          `json:"title"`
	ProductList []ProductDetail `json:"product_list"`
}

type ProductDetailResp struct {
	Name            string          `json:"name"`
	ProductList     []ProductDetail `json:"product_list"`
	MiningSpeed     string          `json:"mining_speed"`
	MiningSpeedUnit string          `json:"mining_speed_unit"`
}

type ProductDetail struct {
	GooID               string `json:"goo_id"`
	IOSID               string `json:"ios_id"`
	Name                string `json:"name"`
	MiningSpeed         string `json:"mining_speed"`
	MiningSpeedUnit     string `json:"mining_speed_unit"`
	ID                  int    `json:"id"`
	Img                 string `json:"img"`
	Description         string `json:"description"`
	Bonus               string `json:"bonus"`
	APR                 string `json:"apr"`
	OriginPrice         string `json:"origin_price"`
	USDPrice            string `json:"usd_price"`
	IsBuy               bool   `json:"is_buy"`
	FreeCoefficient     string `json:"free_coefficient"`
	FreeCoefficientDesc string `json:"free_coefficient_desc"`
	BuyDiscount         string `json:"buy_discount"`
}

type FAQs struct {
	Title string      `json:"title"`
	Img   string      `json:"img"`
	Name  string      `json:"name"`
	List  []FAQDetail `json:"list"`
}

type FAQDetail struct {
	Img     string `json:"img"`
	Title   string `json:"title"`
	Content string `json:"content"`
}

type NFTReq struct {
	Type string `json:"type" form:"type"`
}
type NFTResp struct {
	List []NFTDetail `json:"list"`
}
type NFTDetail struct {
	Id          int    `json:"id"`
	Img         string `json:"img"`
	Desc        string `json:"desc"`
	MiningSpeed string `json:"mining_speed"`
	TimeLeft    int64  `json:"time_left"`
}

type IncomeDetailsResp struct {
	TotalEarnings string      `json:"total_earnings"`
	MoreBenefit   []*Benefits `json:"more_benefit"`
}
type Benefits struct {
	Id      int    `json:"id"`
	Content string `json:"content"`
	Date    string `json:"date"`
}

type WalletResp struct {
	TotalAssets string        `json:"total_assets"`
	List        []*WalletList `json:"list"`
}

type WalletList struct {
	Id         int    `json:"id"`
	Income     string `json:"income"`
	Date       string `json:"date"`
	Status     int    `json:"status"`
	FailReason string `json:"fail_reason"`
}

type WithdrawResp struct {
	TotalAssets string       `json:"total_assets"`
	WalletList  []WalletType `json:"wallet_list"`
	Amount      AmountLimit  `json:"amount"`
}

type AmountLimit struct {
	LimitMin string `json:"limit_min"`
	LimitMax string `json:"limit_max"`
}

type WalletType struct {
	Id     int      `json:"id"`
	Img    string   `json:"img"`
	Name   string   `json:"name"`
	Method []Method `json:"method"`
}

type CashOutResp struct{}
type CashOutReq struct {
	Id          int                 `json:"id" binding:"required"`
	Amount      string              `json:"amount" binding:"required"`
	Method      Method              `json:"method" binding:"required"`
	ChannelCode channel.ChannelCode `json:"channel_code"`
}

type Method struct {
	Id      int    `json:"id" binding:"required"`
	Network string `json:"network"`
	Address string `json:"address" binding:"required"`
}

type ClaimDailyRewardsReq struct {
	Type int `json:"type"` // 1广告 2签到
}

type GetRateByUserResp struct {
	MiningSpeed     string `json:"mining_speed"`
	MiningSpeedUnit string `json:"mining_speed_unit"`
}
type GetTotalUserResp struct {
	Total string `json:"total"`
}

type SelectNetwork struct {
	List []SelectNetworkItem `json:"list"`
}

type SelectNetworkItem struct {
	Id          int    `json:"id"`
	Network     string `json:"network"`
	IsRecommend bool   `json:"is_recommend"`
}

type ADDetailResp struct {
	ComputingPowerHeader
}

type SignDetailResp struct {
	SignList []SignDetail `json:"sign_list"`
}

type UserInfo struct {
	Total           string `json:"total"`
	MiningSpeed     string `json:"mining_speed"`
	MiningSpeedUnit string `json:"mining_speed_unit"`
}

type GetBtcWithdrawalRecordsReq struct {
	WithdrawalStatus []int    `json:"withdrawal_status" binding:"required"`
	LastID           int      `json:"last_id"`
	Page             int      `json:"page"`
	PageSize         int      `json:"page_size"`
	CreatedAtRange   []string `json:"created_at_range" form:"created_at_range[]"`
}

type GetBtcWithdrawalRecordsResp struct {
	List  []*BtcWithdrawalRecordsItem `json:"list"`
	Total int64                       `json:"total"`
}

type BtcWithdrawalRecordsItem struct {
	ID               int             `json:"id" form:"id" gorm:"primaryKey;autoIncrement;comment:记录ID"`
	UserID           uint            `json:"user_id" form:"user_id" gorm:"not null;comment:用户ID"`
	Amount           string          `json:"amount" form:"amount" gorm:"type:decimal(30,16);not null;default:0;comment:提现金额"`
	WithdrawalMethod int             `json:"withdrawal_method" form:"withdrawal_method" gorm:"not null;comment:提现方式"`
	WithdrawalStatus int             `json:"withdrawal_status" form:"withdrawal_status" gorm:"not null;comment:提现进度"` //1未审核 2成功 3失败
	Req              json.RawMessage `json:"req" form:"req" gorm:"not null;comment:提现的参数"`
	WithdrawalTime   string          `json:"withdrawal_time"`
	CreatedAt        string          `json:"created_at"`
	UpdatedAt        string          `json:"updated_at"`
	Method           string          `json:"method"`
	AppName          string          `json:"app_name"`
}

type GetBtcWithdrawalRecordsItem struct {
	ID               int             `json:"id" form:"id" gorm:"primaryKey;autoIncrement;comment:记录ID"`
	UserID           uint            `json:"user_id" form:"user_id" gorm:"not null;comment:用户ID"`
	Amount           string          `json:"amount" form:"amount" gorm:"type:decimal(30,16);not null;default:0;comment:提现金额"`
	WithdrawalMethod int             `json:"withdrawal_method" form:"withdrawal_method" gorm:"not null;comment:提现方式"`
	WithdrawalTime   time.Time       `json:"withdrawal_time" form:"withdrawal_time" gorm:"not null;comment:提现时间"`
	WithdrawalStatus int             `json:"withdrawal_status" form:"withdrawal_status" gorm:"not null;comment:提现进度"` //1未审核 2成功 3失败
	Req              json.RawMessage `json:"req" form:"req" gorm:"not null;comment:提现的参数"`
	CreatedAt        time.Time       `json:"created_at" gorm:"autoCreateTime;not null;comment:创建时间"` // 创建时间
	UpdatedAt        time.Time       `json:"updated_at" gorm:"autoUpdateTime;not null;comment:更新时间"` // 更新时间
}

type UpdateBtcWithdrawalStatus struct {
	WithdrawalStatus int   `json:"withdrawal_status" form:"withdrawal_status" gorm:"not null;comment:提现进度"` //1未审核 2成功 3失败
	IDS              []int `json:"ids" form:"id" gorm:"primaryKey;autoIncrement;comment:记录ID"`
}

type GetBtcTradesRequest struct {
	Page             int        `json:"page"`
	PageSize         int        `json:"page_size"`
	AppId            []uint     `json:"app_id"`
	Id               *int       `json:"id"`
	WithdrawalId     *int       `json:"withdrawal_id"`
	UserID           *int       `json:"user_id"`
	ChannelOrderNo   *string    `json:"channel_order_no"`
	AccountNo        *string    `json:"account_no"`
	WithdrawalStatus *int       `json:"withdrawal_status"`
	TradeStatus      *int       `json:"trade_status"`
	StartAt          *time.Time `json:"start_at"`
	EndAt            *time.Time `json:"end_at"`
}

type BtcTradesItem struct {
	ID               int                  `json:"id"`
	AppId            uint                 `json:"app_id"`
	UserID           uint                 `json:"user_id"`
	Amount           string               `json:"amount"`
	WithdrawalMethod int                  `json:"withdrawal_method"`
	WithdrawalStatus int                  `json:"withdrawal_status"` //1未审核 2成功 3失败
	TradeStatus      model.BtcTradeStatus `json:"trade_status"`
	ChannelOrderNo   string               `json:"channel_order_no"`
	Currency         string               `json:"currency"`
	Address          string               `json:"address"`
	Network          string               `json:"network"`
	Req              json.RawMessage      `json:"req"`
	MethodId         int                  `json:"method_id"`
	Method           string               `json:"method"`
	AppName          string               `json:"app_name"`
	WithdrawalTime   string               `json:"withdrawal_time"`
	Message          string               `json:"message"`
	CreatedAt        string               `json:"created_at"`
	UpdatedAt        string               `json:"updated_at"`
	TradePerformedAt *string              `json:"trade_performed_at"`
}

type GetBtcTradesResponse struct {
	List  []*BtcTradesItem `json:"list"`
	Total int64            `json:"total"`
}
