package request

type ConnectedReq struct {
	ChatNo    string `json:"chatNo"`
	Key       string `json:"key"`
	LevelType int    `json:"levelType"`
	RemoteUID string `json:"remoteUid"`
	RequestID string `json:"requestId"`
	RoomID    string `json:"roomId"`
	Type      string `json:"type"`
	Value     string `json:"value"`
	Version   int    `json:"version"`
}

type HangUpReq struct {
	ChatNo    string `json:"chatNo"`
	RemoteUID string `json:"remoteUid"`
	RequestID string `json:"requestId"`
	RoomID    string `json:"roomId"`
}

type MsgReq struct {
	RequestID   string `json:"requestId"`
	Payload     string `json:"payload"`
	OriginalUid int    `json:"original_uid"`
}

type CallStartReq struct {
	RoleId string `json:"role_id" form:"role_id" binding:"required" validate:"required"`
}

type CallCancelReq struct {
	VideoCallId uint   `json:"video_call_id" form:"video_call_id" binding:"required" validate:"required"`
	RoleId      string `json:"role_id"       form:"role_id"       binding:"required" validate:"required"`
	RoomId      string `json:"room_id"       form:"room_id"       binding:"required" validate:"required"`
}

type CallCloseReq struct {
	VideoCallId uint   `json:"video_call_id" form:"video_call_id" binding:"required" validate:"required"`
	RoleId      string `json:"role_id"       form:"role_id"       binding:"required" validate:"required"`
	RoomId      string `json:"room_id"       form:"room_id"       binding:"required" validate:"required"`
}

type CallDeductionReq struct {
	VideoCallId uint   `json:"video_call_id" form:"video_call_id" binding:"required" validate:"required"`
	RoleId      string `json:"role_id"       form:"role_id"       binding:"required" validate:"required"`
	RoomId      string `json:"room_id"       form:"room_id"       binding:"required" validate:"required"`
}

type GetRealAnchorInfoReq struct {
	RoleId string `json:"role_id" form:"role_id" binding:"required" validate:"required"`
}
