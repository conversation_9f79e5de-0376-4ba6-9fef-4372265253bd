package request

type PublicHeader struct {
	AppId      uint   `header:"App-Id"`
	ClientTime uint   `header:"Client-Time"`
	Sign       string `header:"Sign"`
	AppVersion string `header:"App-Version"`
	DeviceId   string `header:"Device-Id"`
	OsVersion  string `header:"Os-Version"`
	Idfa       string `header:"Idfa"`
	GpsAdid    string `header:"Gps-Adid"`
	Adid       string `header:"Adid"`
}

type AppParamsHeader struct {
	AppId       string `json:"app_id"`
	Version     string `json:"version"`
	Bundle      string `json:"bundle"`
	Uid         string `json:"uid"`
	DeviceId    string `json:"device_id"`
	Os          string `json:"os"`
	Osv         string `json:"osv"`
	Model       string `json:"model"`
	Contype     string `json:"contype"`
	GpsId       string `json:"gps_id"`
	Idfa        string `json:"idfa"`
	Idfv        string `json:"idfv"`
	DeviceTime  string `json:"device_time"`
	TimeZone    string `json:"time_zone"`
	Locale      string `json:"locale"`
	Mcc         string `json:"mcc"`
	AdjustId    string `json:"adjust_id"`
	AppflyerId  string `json:"appflyer_id"`
	NetworkName string `json:"network_name"`
}
