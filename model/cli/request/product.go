package request

import (
	"aimsg-server/global"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
)

type SVipProductListReq struct {
	Position model.Position `json:"position" form:"position"`
}

type ChatVipProductListReq struct {
	Position model.Position `json:"position" form:"position"`
}

type ContentVipProductListReq struct {
	Position model.Position `json:"position" form:"position"`
}

type GetSubListReq struct {
	Position model.Position  `json:"position" form:"position"`
	PageName global.PageName `json:"source" form:"source"`
}

type ClearSubReq struct {
	ProductInfoType model.ProductInfoType `json:"product_info_type" form:"product_info_type" validate:"required"`
}
