package request

type GenImgTaskReq struct {
	ImgId   uint   `json:"img_id" validate:"required"`
	UserImg string `json:"user_img" validate:"required"`
}

type GenHistoryReq struct {
	Page     int `json:"page" form:"page" validate:"required"`
	PageSize int `json:"page_size" form:"page_size" validate:"required"`
}

type ImgTemplateRecordFindOneReq struct {
	ImgId uint `json:"img_id" form:"img_id" validate:"required"`
}

type RemainingGenCountReq struct {
	Source string `json:"source" form:"source"`
}
