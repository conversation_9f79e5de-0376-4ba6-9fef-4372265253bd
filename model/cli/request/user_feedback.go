package request

type UserFeedbackSendMsg struct {
	Category int    `json:"category" form:"category" validate:"required"`
	ImgUrl   string `json:"img_url" form:"img_url" `
	Content  string `json:"content" form:"content"`
}

type UserFeedbackReply struct {
	SessionId int64  `json:"session_id" form:"session_id" validate:"required"`
	Content   string `json:"content" form:"content" validate:"required"`
	UserId    int    `json:"user_id" form:"user_id" validate:"required"`
	ImgUrl    string `json:"img_url" form:"img_url"`
}

type UserFeedbackSetEmail struct {
	Email string `json:"email" form:"email" validate:"required"`
}

type UserFeedbackListReq struct {
	Page     int `json:"page" form:"page" validate:"required"`
	PageSize int `json:"page_size" form:"page_size" validate:"required"`
	Status   int `json:"status" form:"status" `
	UserId   int `json:"user_id" form:"user_id"`
}

type UserFeedbackDeleteSessionReq struct {
	SessionId int64 `json:"session_id" form:"session_id" validate:"required"`
}
