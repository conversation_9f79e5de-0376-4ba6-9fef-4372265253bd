package request

import (
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm"
	"time"
)

type VideoTemplateListReq struct {
	Page     int `json:"page" form:"page" validate:"required"`
	PageSize int `json:"page_size" form:"page_size" validate:"required"`
}

type UserVideoListReq struct {
	Page     int `json:"page" form:"page" validate:"required"`
	PageSize int `json:"page_size" form:"page_size" validate:"required"`
}

type VideoBackendListReq struct {
	Page        int  `json:"page" form:"page" validate:"required"`
	PageSize    int  `json:"page_size" form:"page_size" validate:"required"`
	Id          uint `json:"id" form:"id"`
	KlingaiType int  `gorm:"column:klingai_type;type:tinyint;not null;default:0;comment:0-图生视频, 1-单图特效, 2-双图特效" form:"klingai_type"`
}

type AddTemplateReq struct {
	Id            uint   `json:"id" form:"id"`
	VideoUrL      string `json:"video_url" form:"video_url" validate:"required"`
	VideoCoverUrL string `json:"video_cover_url" form:"video_cover_url" validate:"required"`
	Title         string `json:"title" form:"title" validate:"required"`
	Description   string `json:"description" form:"description"`
	TemplateType  uint   `json:"template_type" form:"template_type" validate:"required"`
	Cost          int    `json:"cost" form:"cost" validate:"required"`
	Sort          int    `json:"sort" form:"sort" validate:"required"`
	ModelConfig   string `json:"model_config" form:"model_config" validate:"required"`
	KlingaiType   int    `gorm:"column:klingai_type;type:tinyint;not null;default:0;comment:0-图生视频, 1-单图特效, 2-双图特效" json:"klingai_type"`
}

type BackendTemplateListRes struct {
	Id             uint           `json:"id"         form:"id"         gorm:"primaryKey"`
	CreatedAt      time.Time      `json:"created_at" form:"created_at" gorm:"autoCreateTime;not null;comment:创建时间;"`
	UpdatedAt      time.Time      `json:"updated_at" form:"updated_at" gorm:"autoUpdateTime;not null;comment:更新时间;"`
	DeletedAt      gorm.DeletedAt `gorm:"index" json:"-"`
	VideoUrL       string         `gorm:"column:video_url;type:varchar(255);not null;comment:模版视频地址" json:"video_url"`
	VideoCoverUrL  string         `gorm:"column:video_cover_url;type:varchar(255);not null;comment:模版视频封面地址" json:"video_cover_url"`
	Title          string         `gorm:"column:title;type:varchar(100);not null;comment:模版标题" json:"title"`
	Description    string         `gorm:"column:description;type:varchar(255);comment:模版描述" json:"description"`
	TemplateType   uint           `gorm:"column:template_type;type:int;not null;comment:模版类型" json:"template_type"`
	Cost           int            `gorm:"column:cost;type:int;not null;comment:花费" json:"cost"`
	Sort           int            `gorm:"column:sort;type:int;not null;comment:排序" json:"sort"`
	ModelConfig    string         `gorm:"column:model_config;type:varchar(255);not null;comment:kLing模型配置相关" json:"model_config"`
	TagList        []string       `json:"tag_list"  form:"-"  gorm:"-" default:"0"`
	ModelName      string         `json:"model_name"`
	Mode           string         `json:"mode"`
	Duration       string         `json:"duration"`
	Prompt         string         `json:"prompt"`
	NegativePrompt string         `json:"negative_prompt"`
	ImageTail      string         `json:"image_tail"`
}

type ConsumeRecordReq struct {
	Page     int `json:"page" form:"page" validate:"required"`
	PageSize int `json:"page_size" form:"page_size" validate:"required"`
}

type UserCreateVideoReq struct {
	ImgUrl        string `json:"img_url" form:"img_url"`
	VideoTemplate uint   `json:"video_template" form:"video_template"`
	Text          string `json:"text" form:"text"`
	Title         string `json:"title"`
}

type TemplateInfo struct {
	VideoTemplate uint `json:"video_template" form:"video_template" validate:"required"`
}

type UserVideoInfoReq struct {
	UserVideoId uint `json:"user_video_id" form:"user_video_id" validate:"required"`
}

type VideoSimplifySubReq struct {
	ProductInfoType model.ProductInfoType `json:"product_info_type" form:"product_info_type" validate:"required"`
}

type AgainCreateVideoReq struct {
	VideoTemplateId uint   `json:"video_template_id" form:"video_template_id" validate:"required"`
	Duration        string `json:"duration" form:"duration" `
	ModelType       string `json:"model_type" form:"model_type" `
	Text            string `json:"text" form:"text"`
}

type DeleteUserVideoReq struct {
	UserVideoId uint `json:"user_video_id" form:"user_video_id" validate:"required"`
}

// VideoSimplifyCallbackResponse 可灵回调的响应结构
type VideoSimplifyCallbackResponse struct {
	TaskID        string     `json:"task_id"`         // 任务ID，系统生成
	TaskStatus    string     `json:"task_status"`     // 任务状态，枚举值：submitted、processing、succeed、failed
	TaskStatusMsg string     `json:"task_status_msg"` // 任务状态信息，当任务失败时展示失败原因
	TaskInfo      TaskInfo   `json:"task_info"`       // 任务创建时的参数信息
	CreatedAt     int64      `json:"created_at"`      // 任务创建时间，Unix时间戳，单位ms
	UpdatedAt     int64      `json:"updated_at"`      // 任务更新时间，Unix时间戳，单位ms
	TaskResult    TaskResult `json:"task_result"`     // 任务结果
}

type UserViewRecordReq struct {
	UserVideoId uint `json:"user_video_id" form:"user_video_id" validate:"required"`
}

// TaskInfo 任务创建时的参数信息
type TaskInfo struct {
	ExternalTaskID string `json:"external_task_id"` // 客户自定义任务ID
}

// TaskResult 任务结果
type TaskResult struct {
	Images []ImageResult `json:"images"` // 图片结果列表
	Videos []VideoResult `json:"videos"` // 视频结果列表
}

// ImageResult 图片类任务的结果
type ImageResult struct {
	Index int    `json:"index"` // 图片编号
	URL   string `json:"url"`   // 图片的URL
}

// VideoResult 视频类任务的结果
type VideoResult struct {
	ID       string `json:"id"`       // 视频ID，全局唯一
	URL      string `json:"url"`      // 视频的URL
	Duration string `json:"duration"` // 视频总时长，单位s
}

type CommonVideoSimplifyReq struct {
	Id uint `json:"id" form:"id" validate:"required"`
}

type DeleteVideoSimplifyReq struct {
	Id    uint   `json:"id" form:"id" validate:"required"`
	Title string `json:"title" form:"title" validate:"required"`
}

type UserCreateVideoReq2 struct {
	ImgUrl          string `json:"img_url" validate:"required"`
	VideoTemplateId uint   `json:"video_template_id"`
	Duration        string `json:"duration"`
	ModelType       string `json:"model_type"`
	Text            string `json:"text"`
	Title           string `json:"create_video_id"`
}
