package request

import "github.com/shopspring/decimal"

type FirebaseReport struct {
	UsdPrice float64 `json:"usd_price"`
	Currency string  `json:"currency"`
}

type FirebaseReportNew struct {
	UsdPrice decimal.Decimal `json:"usd_price"`
	Value    float64         `json:"value"`
	Currency string          `json:"currency"`
}

type FinishFirebaseReportEventReq struct {
	EventID string `json:"event_id" form:"event_id" binding:"required"`
}
