package request

import (
	"github.com/shopspring/decimal"
	"time"
)

type OrderPayReq struct {
	PayMethodId uint   `json:"pay_method_id" form:"pay_method_id" validate:"required"`
	ProductId   uint   `json:"product_id"    form:"product_id"    validate:"required"`
	AiRoleId    string `json:"ai_role_id"    form:"ai_role_id"`
	CountryCode string `json:"country_code"  form:"country_code"`
}

type GoogleQueryReq struct {
	PurchaseToken string `json:"purchase_token" form:"purchase_token" validate:"required"`
	OrderNo       string `json:"order_no" form:"order_no" validate:"required"`
}

type AppleQueryReq struct {
	PurchaseToken string `json:"purchase_token" form:"purchase_token" validate:"required"`
	OrderNo       string `json:"order_no" form:"order_no" validate:"required"`
}

type AppleNotifyReq struct {
	SignedPayload string `json:"signedPayload" form:"signedPayload" validate:"required"`
}

type GoogleNotifyReq struct {
	Message struct {
		Data         string    `json:"data"`
		MessageId    string    `json:"messageId"`
		MessageId1   string    `json:"message_id"`
		PublishTime  time.Time `json:"publishTime"`
		PublishTime1 time.Time `json:"publish_time"`
	} `json:"message"`
	Subscription string `json:"subscription"`
}

type PayMethodListReq struct {
	ProductId uint `json:"product_id"    form:"product_id"    validate:"required"`
}

type SubPayMethodListReq struct {
	ProductId uint `json:"product_id"    form:"product_id"    validate:"required"`
}

type PayCenterMethodListReq struct {
	AppId       uint
	BundleId    string
	ProjectId   uint   `json:"project_id"   form:"project_id"   validate:"required"` // 1 社交 2 游戏 3 AI
	CountryCode string `json:"country_code" form:"country_code" validate:"required,max=8"`
}

type PayCenterSubMethodListReq struct {
	AppId       uint   `json:"-"`
	BundleId    string `json:"-"`
	ProjectId   uint   `json:"project_id"   form:"project_id"   validate:"required"` // 1 社交 2 游戏 3 AI
	CountryCode string `json:"country_code" form:"country_code" validate:"required,max=8"`
}

type PayCenterReq struct {
	BundleId          string
	AppId             uint
	ProjectId         uint            `json:"project_id"    form:"project_id"    validate:"required"`
	UserId            string          `json:"user_id"       form:"user_id"       validate:"required,max=64"`
	UsdAmount         decimal.Decimal `json:"amount"        form:"amount"        validate:"required,min=0.01"`
	ExchangeRate      decimal.Decimal `json:"exchange_rate" form:"exchange_rate" validate:"required,min=0.01"`
	OrderPrice        decimal.Decimal `json:"order_price"   form:"order_price"   validate:"required,min=0.01"`
	OrderNo           string          `json:"order_no"      form:"order_no"      validate:"required,max=32"`
	Currency          string          `json:"currency"      form:"currency"      validate:"required,max=3"`
	MethodId          uint            `json:"method_id"     form:"method_id"     validate:"required,min=1"`
	CountryCode       string          `json:"country_code"  form:"country_code"  validate:"required,max=8"`
	ProductName       string          `json:"product_name"  form:"product_name"  validate:"required,max=64"`
	ProductId         uint            `json:"product_id"    form:"product_id"`
	ClientIp          string          `json:"client_ip"     form:"client_ip"     validate:"max=15"`
	RedirectUrl       string          `json:"redirect_url"  form:"redirect_url"  validate:"required,max=256"`
	NotifyUrl         string          `json:"notify_url"    form:"notify_url"    validate:"required,max=256"`
	TargetOrg         string          `json:"target_org"    form:"target_org"`
	PaymentMethodType string          `json:"payment_method_type"    form:"payment_method_type"`
	AppName           string          `json:"app_name"      form:"app_name"`
	Body              string          `json:"body" form:"body"`
}

type PayCenterSubscriptReq struct {
	BundleId     string
	AppId        uint            `json:"app_id"        form:"app_id"`
	ProjectId    uint            `json:"project_id"    form:"project_id"    validate:"required"`
	UserId       string          `json:"user_id"       form:"user_id"       validate:"required,max=64"`
	UsdAmount    decimal.Decimal `json:"amount"        form:"amount"        validate:"required,min=0.01"`
	ExchangeRate decimal.Decimal `json:"exchange_rate" form:"exchange_rate" validate:"required,min=0.01"`
	OrderPrice   decimal.Decimal `json:"order_price"   form:"order_price"   validate:"required,min=0.01"`
	OrderNo      string          `json:"order_no"      form:"order_no"      validate:"required,max=32"`
	Currency     string          `json:"currency"      form:"currency"      validate:"required,max=3"`
	MethodId     uint            `json:"method_id"     form:"method_id"     validate:"required,min=1"`
	CountryCode  string          `json:"country_code"  form:"country_code"  validate:"required,max=8"`
	SubscriptID  string          `json:"subscript_id"  form:"subscript_id"   validate:"required"`
	ClientIp     string          `json:"client_ip"     form:"client_ip"     validate:"max=15"`
	RedirectUrl  string          `json:"redirect_url"  form:"redirect_url"  validate:"required,max=256"`
	NotifyUrl    string          `json:"notify_url"    form:"notify_url"    validate:"required,max=256"`
	Email        string          `json:"email"         form:"email"`
	FirstName    string          `json:"first_name"    form:"first_name"`
	LastName     string          `json:"last_name"     form:"last_name"`
	PostalCode   string          `json:"postal_code"   form:"postal_code"`
	Province     string          `json:"province"      form:"province"`
	City         string          `json:"city"          form:"city"`
	Street       string          `json:"street"        form:"street"`
	Address      string          `json:"address"       form:"address"`
}
