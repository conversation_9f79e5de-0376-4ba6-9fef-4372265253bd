package request

type CreateVoiceCallReq struct {
	AiRoleId string `json:"ai_role_id"`
}

type VoiceCallDeductionReq struct {
	Id uint `json:"id" form:"id"`
}

type VoiceCallMsgReq struct {
	Id       uint   `json:"id"         form:"id"         validate:"required"`
	AiRoleId string `json:"ai_role_id" form:"ai_role_id" validate:"required"`
	Msg      string `json:"msg"        form:"msg"        validate:"required"`
}
