package request

import "time"

type SendType uint

const (
	SendTypeMsg SendType = iota + 1
	SendTypeNotifyCharge
	SendTypeGift
	SendTypeVoiceCall
	SendTypeUserQuickReply
)

type RedisMsgReq struct {
	UrlType     int      `json:"url_type"`
	SendType    SendType `json:"msg_type"`
	UserId      uint     `json:"user_id"`
	AiRoleId    string   `json:"ai_role_id"`
	RequireCost int      `json:"require_cost"`
	GiftId      uint     `json:"gift_id"`
	VoiceCallId uint     `json:"voice_call_id"`
	UserMsg     string   `json:"user_msg"`
	UUid        string   `json:"uuid"`
	Api3Time    time.Duration
}

type MsgJobReq struct {
	UrlType int    `json:"url_type"`
	ApiUrl  string `json:"api_url"`
}

type NotifyChargeTrainReq struct {
	UserId uint   `json:"user_id"`
	RoleId string `json:"role_id"`
}

type MsgItemV2 struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type LivePushMsgReq struct {
	LiveInfoId uint   `json:"live_info_id"`
	UserId     uint   `json:"user_id"`
	AiRoleId   string `json:"ai_role_id"`
}
