package request

type PayCenterCallbackReq struct {
	OrderNo string `json:"order_no" form:"order_no" validate:"required"`
	State   int    `json:"state" form:"state"`
	TradeNo string `json:"trade_no" form:"trade_no"`
}

type PayCenterSubCallbackReq struct {
	Event           string `json:"event"`
	NextBillTime    string `json:"nextBillTime"`
	OrderNo         string `json:"orderNo"`
	TradeNo         string `json:"trade_no"`
	PlanId          string `json:"plan_id"`
	Status          string `json:"status"`
	NextBillingTime string `json:"next_billing_time"`
	SubType         string `json:"sub_type"` // 订阅类型,1:首次订阅,2:续订
	EventID         string `json:"event_id"` // 订阅类型,1:首次订阅,2:续订
}
