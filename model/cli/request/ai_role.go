package request

import "git.costnovel.com/cashbox-ai/ai-par-model/model"

type AiRoleIdReq struct {
	AiRoleId string `json:"ai_role_id" form:"ai_role_id" validate:"required"`
}

type UndressImgReq struct {
	AiRoleId string `json:"ai_role_id" validate:"required"`
	ImgUrl   string `json:"img_url"`
}

type RoleIdReq struct {
	AiRoleId uint `json:"ai_role_id" form:"ai_role_id" validate:"required"`
}

type UnlockProfileContentReq struct {
	AiRoleId uint `json:"ai_role_id" form:"ai_role_id" validate:"required"`
	MediaID  uint `json:"media_id"   form:"media_id"   validate:"required"`
	Type     int  `json:"type"`
}

type AiRoleListV2Req struct {
	RoleType model.AiRoleType `json:"role_type" form:"role_type" validate:"required"`
	Page     int              `json:"page" form:"page" validate:"required"`
	PageSize int              `json:"page_size" form:"page_size" validate:"required"`
	AiType   int              `json:"ai_type" form:"ai_type" default:"0"`
}

type AiRoleListPubReq struct {
	RoleType model.AiRoleType `json:"role_type" form:"role_type" validate:"required"`
	Page     int              `json:"page" form:"page" validate:"required"`
	PageSize int              `json:"page_size" form:"page_size" validate:"required"`
}

type AiFansListReq struct {
	RoleType model.AiRoleType `json:"role_type" form:"role_type" validate:"required"`
	Page     int              `json:"page" form:"page" validate:"required"`
	PageSize int              `json:"page_size" form:"page_size" validate:"required"`
}

type RequireImgSubMsgReq struct {
	MessageId string `json:"message_id" form:"message_id" validate:"required"`
	AiRoleId  string `json:"ai_role_id" validate:"required"`
}

type IndexReq struct {
	RoleType model.AiRoleType `json:"role_type" form:"role_type" validate:"required"`
	Page     int              `json:"page" form:"page" validate:"required"`
	PageSize int              `json:"page_size" form:"page_size" validate:"required"`
	AiType   int              `json:"ai_type" form:"ai_type" default:"0"`
}
