package request

type EventType int

const (
	EventTypeChannelCreate                 EventType = 101 // 创建频道
	EventTypeChannelDestroy                EventType = 102 // 销毁频道
	EventTypeBroadcasterJoinChannel        EventType = 103 // 直播场景下，主播加入频道
	EventTypeBroadcasterLeaveChannel       EventType = 104 // 直播场景下，主播离开频道
	EventTypeAudienceJoinChannel           EventType = 105 // 直播场景下，观众加入频道
	EventTypeAudienceLeaveChannel          EventType = 106 // 直播场景下，观众离开频道
	EventTypeCommunicationModeJoinChannel  EventType = 107 // 通信场景下用户加入频道
	EventTypeCommunicationModeLeaveChannel EventType = 108 // 通信场景下用户离开频道
	EventTypeClientRoleChangeToBroadcaster EventType = 111 // 观众将角色切换为主播
	EventTypeClientRoleChangeToAudience    EventType = 112 // 主播将角色切换为观众
)

var EventTypeMap = map[EventType]string{
	EventTypeChannelCreate:                 "创建频道",
	EventTypeChannelDestroy:                "销毁频道",
	EventTypeBroadcasterJoinChannel:        "直播场景下，主播加入频道",
	EventTypeBroadcasterLeaveChannel:       "直播场景下，主播离开频道",
	EventTypeAudienceJoinChannel:           "直播场景下，观众加入频道",
	EventTypeAudienceLeaveChannel:          "直播场景下，观众离开频道",
	EventTypeCommunicationModeJoinChannel:  "通信场景下用户加入频道",
	EventTypeCommunicationModeLeaveChannel: "通信场景下用户离开频道",
	EventTypeClientRoleChangeToBroadcaster: "观众将角色切换为主播",
	EventTypeClientRoleChangeToAudience:    "主播将角色切换为观众",
}

type AgoraPlatform int

const (
	AgoraPlatformAndroid AgoraPlatform = 1 // Android
	AgoraPlatformIOS     AgoraPlatform = 2 // iOS
	AgoraPlatformWindows AgoraPlatform = 5 // Windows
	AgoraPlatformLinux   AgoraPlatform = 6 // Linux
	AgoraPlatformWeb     AgoraPlatform = 7 // Web
	AgoraPlatformMacOS   AgoraPlatform = 8 // macOS
	AgoraPlatformOther   AgoraPlatform = 0 // 其他平台
)

var AgoraPlatformMap = map[AgoraPlatform]string{
	AgoraPlatformAndroid: "Android",
	AgoraPlatformIOS:     "iOS",
	AgoraPlatformWindows: "Windows",
	AgoraPlatformLinux:   "Linux",
	AgoraPlatformWeb:     "Web",
	AgoraPlatformMacOS:   "macOS",
	AgoraPlatformOther:   "其他平台",
}

var LeaveChannelReasonMap = map[int]string{
	1:   "正常离开频道",
	2:   "客户端与声网业务服务器连接超时(判断标准为声网 SD-RTN 超过 10 秒未收到该主播的任何数据包，或连接单台服务器 4 秒超时并在 1 秒内没有完成重连)",
	3:   "权限问题(如被运营人员通过踢人 RESTful API 踢出频道)",
	4:   "声网业务服务器内部原因(如声网业务服务器在调整负载，和客户端短暂断开连接，之后会重新连接)",
	5:   "切换新设备，迫使旧设备下线",
	9:   "由于客户端有多个 IP 地址，SDK 主动与声网业务服务器断开连接并重连。(此过程用户无感知。请检查用户是否存在多个公网 IP 或使用了 VPN)",
	10:  "由于网络连接问题(例如 SDK 超过 4 秒未收到来自声网业务服务器的任何数据包或 socket 连接错误，SDK 主动与声网业务服务器断开连接并重连。此过程用户无感知。请检查网络连接状态)",
	999: "异常用户(例如，用户短时间内频繁登录登出频道会被判定为异常用户)",
}

type RTCWebhookReq struct {
	Sid       string      `json:"sid"`       // 会话 ID
	NoticeId  string      `json:"noticeId"`  // 通知 ID，标识来自声网业务服务器的一次事件通知
	ProductId int         `json:"productId"` // 业务 ID。值为 1 表示实时通信业务
	EventType EventType   `json:"eventType"` // 通知的事件类型
	NotifyMs  int64       `json:"notifyMs"`  // 声网消息服务器向你的服务器发送事件通知的 Unix 时间戳 (ms)。通知重试时该值会更新
	Payload   interface{} `json:"payload"`   // 通知事件的具体内容。payload 因 eventType 而异
}

// RTCPayload101 创建频道
// 文档地址 https://doc.shengwang.cn/doc/rtc/restful/webhook/events#101-channel-create
type RTCPayload101 struct {
	ChannelName string `json:"channelName"` // 频道名
	Ts          int64  `json:"ts"`          // 该事件在声网业务服务器上发生的 Unix 时间戳 (s)
}

// RTCPayload102 销毁频道
// 文档地址 https://doc.shengwang.cn/doc/rtc/restful/webhook/events#102-channel-destroy
type RTCPayload102 struct {
	ChannelName string `json:"channelName"` // 频道名
	Ts          int64  `json:"ts"`          // 该事件在声网业务服务器上发生的 Unix 时间戳 (s)
	LastUid     uint   `json:"lastUid"`     // 最后一个离开频道的用户 ID。 如果频道中有多人同时最后离开，则声网消息通知可能返回不同的 lastUid，此时任选其一即可。
}

// RTCPayload103 直播场景下，主播加入频道
// 文档地址 https://doc.shengwang.cn/doc/rtc/restful/webhook/events#103-broadcaster-join-channel
type RTCPayload103 struct {
	ChannelName string        `json:"channelName"` // 频道名
	Uid         uint          `json:"uid"`         // 主播在频道内的用户 ID。
	Platform    AgoraPlatform `json:"platform"`    // 主播设备所属平台
	ClientSeq   int64         `json:"clientSeq"`   // 序列号，标识该事件在 App 客户端上发生的顺序，可用于对同一用户的事件进行排序  https://doc.shengwang.cn/doc/rtc/restful/best-practice/online-user-status
	ClientType  int64         `json:"clientType"`  // 该字段仅在 platform 为 6 时返回 Linux 平台的主播端使用的业务类型，常见的返回值包括： 3：本地服务端录制, 8：小程序, 10：云录制
	Ts          int64         `json:"ts"`          // 该事件在声网业务服务器上发生的 Unix 时间戳 (s)
	Account     string        `json:"account"`     // String 类型的用户 ID
}

// RTCPayload104 直播场景下，主播离开频道
// 文档地址 https://doc.shengwang.cn/doc/rtc/restful/webhook/events#104-broadcaster-leave-channel
type RTCPayload104 struct {
	ChannelName string        `json:"channelName"` // 频道名
	Uid         uint          `json:"uid"`         // 主播在频道内的用户 ID。
	Platform    AgoraPlatform `json:"platform"`    // 主播设备所属平台
	ClientSeq   int64         `json:"clientSeq"`   // 序列号，标识该事件在 App 客户端上发生的顺序，可用于对同一用户的事件进行排序
	ClientType  int64         `json:"clientType"`  // 该字段仅在 platform 为 6 时返回 Linux 平台的主播端使用的业务类型，常见的返回值包括： 3：本地服务端录制, 8：小程序, 10：云录制
	Reason      int           `json:"reason"`      // 主播离开频道的原因
	Ts          int64         `json:"ts"`          // 该事件在声网业务服务器上发生的 Unix 时间戳 (s)
	Duration    int           `json:"duration"`    // 主播在频道内的时长 (s)
	Account     string        `json:"account"`     // String 类型的用户 ID
}

// RTCPayload105 直播场景下，观众加入频道
// 文档地址 https://doc.shengwang.cn/doc/rtc/restful/webhook/events#105-audience-join-channel
type RTCPayload105 struct {
	ChannelName string        `json:"channelName"` // 频道名
	Uid         uint          `json:"uid"`         // 观众在频道内的用户 ID。
	Platform    AgoraPlatform `json:"platform"`    // 观众设备所属平台
	ClientSeq   int64         `json:"clientSeq"`   // 序列号，标识该事件在 App 客户端上发生的顺序，可用于对同一用户的事件进行排序
	ClientType  int64         `json:"clientType"`  // 该字段仅在 platform 为 6 时返回 Linux 平台的主播端使用的业务类型，常见的返回值包括： 3：本地服务端录制, 8：小程序, 10：云录制
	Ts          int64         `json:"ts"`          // 该事件在声网业务服务器上发生的 Unix 时间戳 (s)
	Account     string        `json:"account"`     // String 类型的用户 ID
}

// RTCPayload106 直播场景下，观众离开频道
// 文档地址 https://doc.shengwang.cn/doc/rtc/restful/webhook/events#106-audience-leave-channel
type RTCPayload106 struct {
	ChannelName string        `json:"channelName"` // 频道名
	Uid         uint          `json:"uid"`         // 观众在频道内的用户 ID。
	Platform    AgoraPlatform `json:"platform"`    // 观众设备所属平台
	ClientSeq   int64         `json:"clientSeq"`   // 序列号，标识该事件在 App 客户端上发生的顺序，可用于对同一用户的事件进行排序
	ClientType  int64         `json:"clientType"`  // 该字段仅在 platform 为 6 时返回 Linux 平台的主播端使用的业务类型，常见的返回值包括： 3：本地服务端录制, 8：小程序, 10：云录制
	Reason      int           `json:"reason"`      // 观众离开频道的原因
	Ts          int64         `json:"ts"`          // 该事件在声网业务服务器上发生的 Unix 时间戳 (s)
	Duration    int           `json:"duration"`    // 观众在频道内的时长 (s)
	Account     string        `json:"account"`     // String 类型的用户 ID
}

// RTCPayload107 通信场景下用户加入频道
// 文档地址 https://docportal.shengwang.cn/cn/voice-legacy/rtc_channel_event?platform=All%20Platforms#107-user-join-channel-with-communication-mode
type RTCPayload107 struct {
	ChannelName string        `json:"channelName"` // 频道名
	Uid         uint          `json:"uid"`         // 观众在频道内的用户 ID。
	Platform    AgoraPlatform `json:"platform"`    // 观众设备所属平台
	ClientSeq   int64         `json:"clientSeq"`   // 序列号，标识该事件在 App 客户端上发生的顺序，可用于对同一用户的事件进行排序
	Ts          int64         `json:"ts"`          // 该事件在声网业务服务器上发生的 Unix 时间戳 (s)
	Account     string        `json:"account"`     // String 类型的用户 ID
}

// RTCPayload108 通信场景下用户离开频道
// 文档地址 https://docportal.shengwang.cn/cn/voice-legacy/rtc_channel_event?platform=All%20Platforms#108-user-leave-channel-with-communication-mode
type RTCPayload108 struct {
	ChannelName string        `json:"channelName"` // 频道名
	Uid         uint          `json:"uid"`         // 用户在频道内的用户 ID。
	Platform    AgoraPlatform `json:"platform"`    // 用户设备所属平台
	ClientSeq   int64         `json:"clientSeq"`   // 序列号，标识该事件在 App 客户端上发生的顺序，可用于对同一用户的事件进行排序
	Reason      int           `json:"reason"`      // 用户离开频道的原因
	Ts          int64         `json:"ts"`          // 该事件在声网业务服务器上发生的 Unix 时间戳 (s)
	Duration    int           `json:"duration"`    // 用户在频道内的时长 (s)
	Account     string        `json:"account"`     // String 类型的用户 ID
}

// RTCPayload111 观众将角色切换为主播
// 文档地址 https://doc.shengwang.cn/doc/rtc/restful/webhook/events#111-client-role-change-to-broadcaster
type RTCPayload111 struct {
	ChannelName string `json:"channelName"` // 频道名
	Uid         uint   `json:"uid"`         // 用户在频道内的用户 ID
	ClientSeq   int64  `json:"clientSeq"`   // 序列号，标识该事件在 App 客户端上发生的顺序，可用于对同一用户的事件进行排序
	Ts          int64  `json:"ts"`          // 该事件在声网业务服务器上发生的 Unix 时间戳 (s)
	Account     string `json:"account"`     // String 类型的用户 ID
}

// RTCPayload112 主播将角色切换为观众
// 文档地址 https://doc.shengwang.cn/doc/rtc/restful/webhook/events#112-client-role-change-to-audience
type RTCPayload112 struct {
	ChannelName string `json:"channelName"` // 频道名
	Uid         uint   `json:"uid"`         // 用户在频道内的用户 ID
	ClientSeq   int64  `json:"clientSeq"`   // 序列号，标识该事件在 App 客户端上发生的顺序，可用于对同一用户的事件进行排序
	Ts          int64  `json:"ts"`          // 该事件在声网业务服务器上发生的 Unix 时间戳 (s)
	Account     string `json:"account"`     // String 类型的用户 ID
}
