package request

type UpdateLiveRankReq struct {
	Id        uint     `json:"id"          form:"id"          validate:"required"`
	AiRoleIds []string `json:"ai_role_ids" form:"ai_role_ids" validate:"required"`
}

type CreateLiveInfoReq struct {
	GiftIds    []uint `json:"gift_ids"    form:"gift_ids"`
	ViewerId   uint   `json:"viewer_id"   form:"viewer_id" validate:"required"`
	ArrivalMin int    `json:"arrival_min" form:"arrival_min" validate:"required"`
}

type UpdateLiveInfoReq struct {
	Id            uint                 `json:"id"              form:"id" validate:"required"`
	LiveSecond    int                  `json:"live_second"     form:"live_second"`
	LikeCount     int                  `json:"like_count"      form:"like_count"`
	ActualViewers int                  `json:"actual_viewers"  form:"actual_viewers"`
	CommentCount  int                  `json:"comment_count"   form:"comment_count"`
	GiftItems     []UpdateLiveGiftItem `json:"gift_items"      form:"gift_items"`
}

type LiveAiRoleRankV2Req struct {
	LiveInfoId uint `json:"live_info_id" form:"live_info_id" validate:"required"`
}

type UpdateLiveGiftItem struct {
	Id  uint `json:"id"  form:"id"`
	Num int  `json:"num" form:"num"`
}
