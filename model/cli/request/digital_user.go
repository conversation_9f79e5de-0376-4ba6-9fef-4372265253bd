package request

import (
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/datatypes"
)

type SocialLoginReq struct {
	LoginType   model.LoginType `json:"login_type" validate:"required"`
	DeviceToken string          `json:"device_token"`
	ThirdToken  string          `json:"third_token"`
	DeviceId    string          `json:"device_id" validate:"required"`
	Username    string          `json:"username"`
	Password    string          `json:"password"`
}

type RegisterReq struct {
	RegType  model.LoginType `json:"reg_type"`
	Username string          `json:"username"`
	Password string          `json:"password"`
}

type ReceiveFlowersV2Req struct {
	Source string `json:"source"`
}

type SendMsgReq struct {
	MessageType    model.MsgType `json:"message_type" validate:"required"`
	ReceiverId     string        `json:"receiver_id" validate:"required"`
	MessageContent string        `json:"message_content" validate:"required"`
	GiftId         uint          `json:"gift_id"`
}

type UserQuickReplyReq struct {
	MessageId string `json:"message_id" form:"message_id" validate:"required"`
	IsReset   bool   `json:"is_reset" form:"is_reset"`   // 是否是重新生成
	IsCancel  bool   `json:"is_cancel" form:"is_cancel"` // 取消快捷回复
}

// UserQuickReplyChargingReq 12-31 增加快捷回复消息下发付费
type UserQuickReplyChargingReq struct {
	AiRoleId       string `json:"ai_role_id" form:"ai_role_id" binding:"required"`
	QuickReplyType int    `json:"quick_reply_type" form:"quick_reply_type" binding:"required"`
}

type ImgBlindBoxReq struct {
	AiRoleId string `json:"ai_role_id" form:"ai_role_id" binding:"required"`
}

type GetAiFuncSwitchReq struct {
	AiRoleId string `json:"ai_role_id" form:"ai_role_id" binding:"required"`
}

type GetAiFuncContentReq struct {
	AiRoleId  string `json:"ai_role_id" form:"ai_role_id" binding:"required"`
	FuncType  int    `json:"func_type" form:"func_type" binding:"required"`
	GiftId    uint   `json:"gift_id" form:"gift_id"`
	BodyImgId uint   `json:"body_img_id" form:"body_img_id"`
}

type GetAiFirstMsgReq struct {
	AiRoleId string `json:"ai_role_id" binding:"required"`
}

type GetAiFirstVoiceReq struct {
	AiRoleId string `json:"ai_role_id" form:"ai_role_id" binding:"required"`
}

type UnlockDataReq struct {
	ConsumeType model.ConsumeType `json:"consume_type" form:"consume_type" binding:"required"`
	AiRoleId    string            `json:"ai_role_id" form:"ai_role_id" binding:"required"`
	RelationId  string            `json:"relation_id" form:"relation_id" binding:"required"`
	Type        int               `json:"type"` //  1 广告解锁
}

type GenMsgVoiceReq struct {
	MessageId string `json:"message_id" form:"message_id" binding:"required"`
	SenderId  string `json:"sender_id" form:"sender_id" binding:"required"`
}

type GetAsmrVoiceReq struct {
	MessageId string `json:"message_id" form:"message_id" binding:"required"`
}

type GenMsgVideoReq struct {
	MessageId string `json:"message_id" form:"message_id" binding:"required"`
	SenderId  string `json:"sender_id" form:"sender_id" binding:"required"`
	Type      int    `json:"type"`
}

type ResetChatReq struct {
	RoleId string `json:"role_id" form:"role_id" binding:"required"`
}

type FcmTokenReq struct {
	FcmToken string `json:"fcm_token" form:"fcm_token" validate:"required"`
}

type SetNicknameReq struct {
	Nickname string `json:"nickname" form:"nickname" validate:"required"`
}

type ChangePasswordReq struct {
	Password string `json:"password" form:"password" validate:"required"`
}

type SetUsernameReq struct {
	Username string `json:"username" form:"username" validate:"required"`
}

type WebUnlockGameReq struct {
	GameId uint `json:"game_id" form:"game_id" validate:"required"`
}

type RoleIdRequest struct {
	RoleId string `json:"role_id" form:"role_id" binding:"required"`
}

type UploadFileUrlReq struct {
	FileType    int    `json:"file_type"    form:"file_type"    validate:"required"`
	FileName    string `json:"file_name"    form:"file_name"    validate:"required"`
	FileHash    string `json:"file_hash"    form:"file_hash"    validate:"required"`
	ContentType string `json:"content_type" form:"content_type" validate:"required"`
}

type SaveFeedbackRecordReq struct {
	Content string `json:"content" form:"content" validate:"required"`
	Star    uint   `json:"star"    form:"star"`
}

type GenAiRoleReq struct {
	Nickname    string                                `json:"nickname"`
	Gender      model.GenderType                      `json:"gender"`
	Describe    string                                `json:"describe"`
	FirstMsg    string                                `json:"first_msg"`
	RoleLabels  []string                              `json:"role_labels"`
	Avatar      string                                `json:"avatar"`
	AvatarType  int                                   `json:"avatar_type"`
	ImgId       uint                                  `json:"img_id"`
	ImgTaskId   uint                                  `json:"img_task_id"`
	VoiceInfoId uint                                  `json:"voice_info_id"`
	EleVoiceReq datatypes.JSONType[model.EleVoiceReq] `json:"ele_voice_req"`
}

type AiRoleFaceImgCheckReq struct {
	FileKey string `json:"file_key" form:"file_key" validate:"required"`
}

type CompleteUserInfoReq struct {
	Nickname   string                                `json:"nickname"    form:"nickname" validate:"required"`
	Avatar     string                                `json:"avatar"      form:"avatar"`
	Gender     model.GenderType                      `json:"gender"      form:"gender"`
	Birthday   string                                `json:"birthday"    form:"birthday"`
	LikeGender datatypes.JSONSlice[model.GenderType] `json:"like_gender" form:"like_gender"`
	AgeRange   model.AgeRange                        `json:"age_range"   form:"age_range"`
}

type GenVideoTaskReq struct {
	TxtId   uint   `json:"txt_id"   form:"txt_id"   validate:"required"`
	UserImg string `json:"user_img" form:"user_img" validate:"required"`
}

type GenVideoHistoryReq struct {
	Page     int  `json:"page"      form:"page"      validate:"required"`
	PageSize int  `json:"page_size" form:"page_size" validate:"required"`
	TaskId   uint `json:"task_id"   form:"task_id"`
}
