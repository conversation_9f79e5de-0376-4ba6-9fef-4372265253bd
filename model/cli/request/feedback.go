package request

type FeedbackAddReq struct {
	RoleID        string `json:"role_id" binding:"required"`    // 角色 ID，可为 0 表示无角色
	MessageID     string `json:"message_id" binding:"required"` // 消息 ID (UUID)
	FeedbackFlags []uint `json:"feedback_flags"`                // 反馈标志，前端以数组传递
	Type          string `json:"type" binding:"required,oneof=like dislike"`
	UserId        uint
}

type FeedbackListReq struct{}

type FeedbackBatchReq struct {
	RoleID     string `json:"role_id" binding:"required"` // 角色 ID，可为 0 表示无角色
	UserId     uint
	MessageIDs []string `json:"message_ids" binding:"required"`
}
