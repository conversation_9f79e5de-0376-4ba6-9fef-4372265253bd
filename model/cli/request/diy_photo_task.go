package request

import "git.costnovel.com/cashbox-ai/ai-par-model/model"

type GetDiyConfigReq struct {
	AiRoleId string        `json:"ai_role_id" form:"ai_role_id"`
	DiyType  model.DiyType `json:"diy_type" form:"diy_type"`
}

type CreateDiyPhotoTaskReq struct {
	AiRoleId    string        `json:"ai_role_id"`
	DiyType     model.DiyType `json:"diy_type" form:"diy_type"`
	UploadImg   string        `json:"upload_img" form:"upload_img"`
	DiyConfigId uint          `json:"diy_config_id"  validate:"required,gte=0"`
	ImgCount    int           `json:"img_count"  validate:"required,gte=0"`
	Email       string        `json:"email" form:"email"`
}

type CliDiyPhotoTaskHistoryReq struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
}
