package response

import "git.costnovel.com/cashbox-ai/ai-par-model/model"

type ImgGenRecordItem struct {
	Id              uint            `json:"id"`
	ImgRecordId     uint            `json:"img_record_id"`
	ImgRecordName   string          `json:"img_record_name"`
	ImgCategoryName string          `json:"img_category_name"`
	DigitalUserId   uint            `json:"digital_user_id"`
	UserImgUrl      string          `json:"user_img_url"`
	UserImgFullUrl  string          `json:"user_img_full_url"`
	ResImgUrl       string          `json:"res_img_url"`
	ResImgFullUrl   string          `json:"res_img_full_url"`
	GenStatus       model.GenStatus `json:"gen_status"`
	AiId            uint            `json:"ai_id"`
	AiRoleId        string          `json:"ai_role_id"`
}
