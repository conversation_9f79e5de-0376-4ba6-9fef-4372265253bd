package response

type AiMsgRes struct {
	Results []struct {
		History struct {
			Internal [][]string `json:"internal"`
			Visible  [][]string `json:"visible"`
		} `json:"history"`
	} `json:"results"`
}

type AiMsgResV2 struct {
	Id      string `json:"id"`
	Object  string `json:"object"`
	Created int    `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index        int    `json:"index"`
		FinishReason string `json:"finish_reason"`
		Message      struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
}

type AiMsgResNew struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Answer string `json:"answer"`
	} `json:"data"`
}

type AiMsgResMiniMax struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Answer string `json:"answer"`
	} `json:"data"`
}

type MsgNewStartCfg struct {
	StartRes string `json:"start_res"`
}
