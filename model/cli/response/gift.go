package response

type GiftRes struct {
	Id        uint   `json:"id" form:"id" gorm:"primaryKey"` // 主键ID
	FreeCount int    `json:"free_count"`
	Name      string `json:"name"      form:"name"      gorm:"column:name;not null;type:string;size:10;index;comment:礼物名称"`
	Icon      string `json:"icon"      form:"icon"      gorm:"column:icon;not null;type:string;size:256;comment:礼物图标"`
	Consume   int    `json:"consume"   form:"consume"   gorm:"column:consume;not null;type:int;comment:消耗数量"`
	Msg       string `json:"msg"       form:"msg"       gorm:"column:msg;not null;type:string;size:512;comment:消息"`
}
