package response

import (
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
)

type DiyPhotoTaskHistoryItem struct {
	// model.DiyPhotoTask
	Id            uint                    `json:"id"`
	CreatedAt     string                  `json:"created_at"`
	Tittle        string                  `json:"tittle"`
	DiyConfigName string                  `json:"diy_config_name"`
	ImgCount      int                     `json:"img_count"`
	TaskState     model.DiyPhotoTaskState `json:"task_state"`
	FaceImg       string                  `json:"face_img"`
	ResImgs       []string                `json:"res_imgs"`
}

type DiyPhotoTaskHistoryRes struct {
	List  []*DiyPhotoTaskHistoryItem `json:"list"`
	Total int64                      `json:"total"`
}
