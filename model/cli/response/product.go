package response

import (
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/shopspring/decimal"
)

// 订阅页的轮播图数据
type SlideData struct {
	SlideType int    `json:"slide_type"`
	Title     string `json:"title"`
	Subtitle  string `json:"subtitle"`
	BannerUrl string `json:"banner_url"`
}

type SubProductV2 struct {
	Id                uint              `json:"id"`           // 主键ID
	Name              string            `json:"name"`         // 名称
	Description       string            `json:"description"`  // 描述
	GooID             string            `json:"goo_id"`       // 谷歌商品ID
	IOSID             string            `json:"ios_id"`       // IOS商品ID
	ProductType       model.ProductType `json:"product_type"` // 商品类型
	OriginPrice       decimal.Decimal   `json:"origin_price"` // 原价
	USDPrice          decimal.Decimal   `json:"usd_price"`    // 美元价格
	TopLabel          string            `json:"top_label"`
	Bonus             string            `json:"bonus"`
	Hot               bool              `json:"hot"`
	DayCost           decimal.Decimal   `json:"day_cost"`
	PriceTip          string            `json:"price_tip"`
	PriceOffLefText   string            `json:"price_off_left_text"`
	PriceOffRightText string            `json:"price_off_right_text"`
	PayBtnText        string            `json:"pay_btn_text"` // 支付按钮的文案
}

type TipsV2Item struct {
	Content  string `json:"content"`
	FontBold bool   `json:"font_bold"`
}

type BenefitsItem struct {
	Content string `json:"content"`
	Icon    string `json:"icon"`
}

type TipsV2Cfg struct {
	BgIcon       string         `json:"bg_icon"`
	AddCoinIcon  string         `json:"add_coin_icon"`
	ContentList  []TipsV2Item   `json:"content_list"`
	BenefitsList []BenefitsItem `json:"benefits_list"`
}

type VipDialogTip struct {
	Title      string    `json:"title"`
	Tips       []string  `json:"tips"`
	MonthTips  []string  `json:"month_tips"`
	YearTips   []string  `json:"year_tips"`
	TipWeek    string    `json:"tip_week"`
	TipMonth   string    `json:"tip_month"`
	TipYear    string    `json:"tip_year"`
	WeekTipV2  TipsV2Cfg `json:"week_tip_v2"`
	MonthTipV2 TipsV2Cfg `json:"month_tip_v2"`
	YearTipV2  TipsV2Cfg `json:"year_tip_v2"`
}

type GetSubListRespV2 struct {
	DialogPlan           int            `json:"dialog_plan"`
	Tip                  VipDialogTip   `json:"tip"`
	TipV2                VipDialogV2Cfg `json:"tip_v2"`
	HalfDialog           bool           `json:"half_dialog"`
	TipV2Index           int            `json:"tip_v2_index"`
	PageBgPlan           int            `json:"page_bg_plan"`
	PageBgUrl            string         `json:"page_bg_url"`
	SlideData            []SlideData    `json:"slide_data"`
	ProductList          []SubProductV2 `json:"product_list"`
	WaitSeconds          uint           `json:"wait_seconds"`
	CountdownLeftText    string         `json:"countdown_left_text"`
	CountdownRightSecond int64          `json:"countdown_right_second"`
}

type ContentItem struct {
	Icon    string `json:"icon"`
	Content string `json:"content"`
}

type ShopTip struct {
	Content   string `json:"content"`
	ButtonTxt string `json:"button_txt"`
}

type FlowerProductListItem struct {
	Id          uint              `json:"id"`
	Name        string            `json:"name"`
	Description string            `json:"description"`
	GooID       string            `json:"goo_id"`
	IOSID       string            `json:"ios_id"`
	ProductType model.ProductType `json:"product_type"`
	Amount      int               `json:"amount"`
	OriginPrice decimal.Decimal   `json:"origin_price"`
	USDPrice    decimal.Decimal   `json:"usd_price"`
	TopLabel    string            `json:"top_label"`
	Hot         bool              `json:"hot"`
}

type FlowerProductListRes struct {
	Title         string                  `json:"title"`
	FlowerAmount  int                     `json:"flower_amount"`
	ShopTip       ShopTip                 `json:"shop_tip"`
	FlowerCostTip string                  `json:"flower_cost_tip"`
	ShowAds       bool                    `json:"show_ads"`
	List          []FlowerProductListItem `json:"list"`
}

type ChatProductListItem struct {
	Id          uint              `json:"id"`
	Name        string            `json:"name"`
	Description string            `json:"description"`
	GooID       string            `json:"goo_id"`
	IOSID       string            `json:"ios_id"`
	ProductType model.ProductType `json:"product_type"`
	Amount      int               `json:"amount"`
	OriginPrice decimal.Decimal   `json:"origin_price"`
	USDPrice    decimal.Decimal   `json:"usd_price"`
	TopLabel    string            `json:"top_label"`
	Hot         bool              `json:"hot"`
	PerTip      string            `json:"per_tip"`
}

type ChatProductListRes struct {
	List []ChatProductListItem `json:"list"`
}

type FlowerShopItem struct {
	Id           uint            `json:"id"`
	Name         string          `json:"name"`
	Description  string          `json:"description"`
	GooID        string          `json:"goo_id"`
	IOSID        string          `json:"ios_id"`
	Amount       int             `json:"amount"`
	RewardAmount int             `json:"reward_amount"`
	OriginPrice  decimal.Decimal `json:"origin_price"`
	USDPrice     decimal.Decimal `json:"usd_price"`
	Hot          bool            `json:"hot"`
}

type FlowerShopRes struct {
	FlowerAmount int              `json:"flower_amount"`
	List         []FlowerShopItem `json:"list"`
}

type FlowerShopV2Res struct {
	GetFreeFloShow bool             `json:"get_free_flo_show"`
	FlowerAmount   int              `json:"flower_amount"`
	FloCount       int              `json:"flo_count"`
	List           []FlowerShopItem `json:"list"`
	SVipInfo       *SVipProductInfo `json:"svip_info"`
}

type WebCoinShopRes struct {
	List []FlowerShopItem `json:"list"`
}

type SVipProductInfo struct {
	Id           uint              `json:"id"`
	Name         string            `json:"name"`
	Description  string            `json:"description"`
	GooID        string            `json:"goo_id"`
	IOSID        string            `json:"ios_id"`
	Amount       int               `json:"amount"`
	GoodType     model.GoodType    `json:"good_type"`
	ProductType  model.ProductType `json:"product_type"`
	RewardAmount int               `json:"reward_amount"`
	OriginPrice  decimal.Decimal   `json:"origin_price"`
	USDPrice     decimal.Decimal   `json:"usd_price"`
	TextList     []string          `json:"text_list"`
}

type ProductItem struct {
	Id           uint            `json:"id"`
	Name         string          `json:"name"`
	Description  string          `json:"description"`
	GooID        string          `json:"goo_id"`
	IOSID        string          `json:"ios_id"`
	Amount       int             `json:"amount"`
	RewardAmount int             `json:"reward_amount"`
	OriginPrice  decimal.Decimal `json:"origin_price"`
	USDPrice     decimal.Decimal `json:"usd_price"`
	Bonus        string          `json:"bonus"`
	Hot          bool            `json:"hot"`
}
type ClearSubListResV1 struct {
	ProductList   []ProductItemV1 `json:"product_list"`
	VipEquityList []VipEquity     `json:"vip_equity_list"`
}
type ProductItemV1 struct {
	Id           uint            `json:"id"`
	Name         string          `json:"name"`
	Description  string          `json:"description"`
	GooID        string          `json:"goo_id"`
	IOSID        string          `json:"ios_id"`
	Amount       int             `json:"amount"`
	RewardAmount int             `json:"reward_amount"`
	OriginPrice  decimal.Decimal `json:"origin_price"`
	USDPrice     decimal.Decimal `json:"usd_price"`
	Bonus        string          `json:"bonus"`
	Hot          bool            `json:"hot"`
	RenewDate    string          `json:"renew_date"`
	Tag          string          `json:"Tag"`
	Free         decimal.Decimal `json:"free"`
	SubTitle     string          `json:"sub_title"`
}
type VipEquity struct {
	IsHave  bool   `json:"is_have"`
	Content string `json:"content"`
}

type VoiceProductList struct {
	Msg      string        `json:"msg"`
	MsgVoice string        `json:"msg_voice"`
	List     []ProductItem `json:"list"`
}

type SVipDialogRes struct {
	Title    string              `json:"title"`
	FloCount int                 `json:"flo_count"`
	TipList  []SVipDialogTipItem `json:"tip_list"`
	BgList   []string            `json:"bg_list"`
	SVipInfo SVipProductInfo     `json:"svip_info"`
}

type SVIPProduct struct {
	Id          uint              `json:"id"`           // 主键ID
	Name        string            `json:"name"`         // 名称
	Description string            `json:"description"`  // 描述
	GooID       string            `json:"goo_id"`       // 谷歌商品ID
	IOSID       string            `json:"ios_id"`       // IOS商品ID
	ProductType model.ProductType `json:"product_type"` // 商品类型
	OriginPrice decimal.Decimal   `json:"origin_price"` // 原价
	USDPrice    decimal.Decimal   `json:"usd_price"`    // 美元价格
	TopLabel    string            `json:"top_label"`
	Bonus       string            `json:"bonus"`
	Hot         bool              `json:"hot"`
	DayCost     decimal.Decimal   `json:"day_cost"`
	PriceTip    string            `json:"price_tip"`
	PayBtnText  string            `json:"pay_btn_text"`
}

type TipItem struct {
	Icon    string `json:"icon"`
	Content string `json:"content"`
}

type SVipProductListRes struct {
	Tip         SVipDialogTip   `json:"tip"`
	TipV2       SVipDialogV2Cfg `json:"tip_v2"`
	TipV2Index  int             `json:"tip_v2_index"`
	HalfDialog  bool            `json:"half_dialog"`
	BgList      []string        `json:"bg_list"`
	ProductList []SVIPProduct   `json:"product_list"`
}

type SVipDialogTip struct {
	Title      string    `json:"title"`
	TipWeek    []TipItem `json:"tip_week"`
	TipMonth   []TipItem `json:"tip_month"`
	TipYear    []TipItem `json:"tip_year"`
	WeekTipV2  TipsV2Cfg `json:"week_tip_v2"`
	MonthTipV2 TipsV2Cfg `json:"month_tip_v2"`
	YearTipV2  TipsV2Cfg `json:"year_tip_v2"`
}

type ChatVipDialogTip struct {
	Title    string    `json:"title"`
	TipWeek  []TipItem `json:"tip_week"`
	TipMonth []TipItem `json:"tip_month"`
	TipYear  []TipItem `json:"tip_year"`
}

type ChatVipDialogV2Cfg struct {
	WeekList  []DialogBannerCfgItem `json:"week_list"`
	MonthList []DialogBannerCfgItem `json:"month_list"`
	YearList  []DialogBannerCfgItem `json:"year_list"`
}

type ChatVIPProduct struct {
	Id          uint              `json:"id"`           // 主键ID
	Name        string            `json:"name"`         // 名称
	Description string            `json:"description"`  // 描述
	GooID       string            `json:"goo_id"`       // 谷歌商品ID
	IOSID       string            `json:"ios_id"`       // IOS商品ID
	ProductType model.ProductType `json:"product_type"` // 商品类型
	OriginPrice decimal.Decimal   `json:"origin_price"` // 原价
	USDPrice    decimal.Decimal   `json:"usd_price"`    // 美元价格
	TopLabel    string            `json:"top_label"`
	Hot         bool              `json:"hot"`
	DayCost     decimal.Decimal   `json:"day_cost"`
	PriceTip    string            `json:"price_tip"`
	PayBtnText  string            `json:"pay_btn_text"`
}

type ChatVipProductListRes struct {
	Tip         ChatVipDialogTip   `json:"tip"`
	TipV2       ChatVipDialogV2Cfg `json:"tip_v2"`
	TipV2Index  int                `json:"tip_v2_index"`
	HalfDialog  bool               `json:"half_dialog"`
	BgList      []string           `json:"bg_list"`
	ProductList []ChatVIPProduct   `json:"product_list"`
}

type ContentVipDialogTip struct {
	Title    string    `json:"title"`
	TipWeek  []TipItem `json:"tip_week"`
	TipMonth []TipItem `json:"tip_month"`
	TipYear  []TipItem `json:"tip_year"`
}

type ContentVipDialogV2Cfg struct {
	WeekList  []DialogBannerCfgItem `json:"week_list"`
	MonthList []DialogBannerCfgItem `json:"month_list"`
	YearList  []DialogBannerCfgItem `json:"year_list"`
}

type ContentVIPProduct struct {
	Id          uint              `json:"id"`           // 主键ID
	Name        string            `json:"name"`         // 名称
	Description string            `json:"description"`  // 描述
	GooID       string            `json:"goo_id"`       // 谷歌商品ID
	IOSID       string            `json:"ios_id"`       // IOS商品ID
	ProductType model.ProductType `json:"product_type"` // 商品类型
	OriginPrice decimal.Decimal   `json:"origin_price"` // 原价
	USDPrice    decimal.Decimal   `json:"usd_price"`    // 美元价格
	TopLabel    string            `json:"top_label"`
	Hot         bool              `json:"hot"`
	DayCost     decimal.Decimal   `json:"day_cost"`
	PriceTip    string            `json:"price_tip"`
	PayBtnText  string            `json:"pay_btn_text"`
}

type ContentVipProductListRes struct {
	Tip         ContentVipDialogTip   `json:"tip"`
	TipV2       ContentVipDialogV2Cfg `json:"tip_v2"`
	TipV2Index  int                   `json:"tip_v2_index"`
	HalfDialog  bool                  `json:"half_dialog"`
	BgList      []string              `json:"bg_list"`
	ProductList []ContentVIPProduct   `json:"product_list"`
}

type ClearSubListRes struct {
	ProductList []ProductItem `json:"product_list"`
}

type DialogBannerCfgItem struct {
	Img      string         `json:"img"`
	Icon     string         `json:"icon"`
	Title    string         `json:"title"`
	Content  string         `json:"content"`
	Position model.Position `json:"position"`
	JsonFile string         `json:"json_file"`
}

type SVipDialogV2Cfg struct {
	BgIcon    string                `json:"bg_icon"`
	WeekList  []DialogBannerCfgItem `json:"week_list"`
	MonthList []DialogBannerCfgItem `json:"month_list"`
	YearList  []DialogBannerCfgItem `json:"year_list"`
}

type VipDialogV2Cfg struct {
	BgIcon    string                `json:"bg_icon"`
	WeekList  []DialogBannerCfgItem `json:"week_list"`
	MonthList []DialogBannerCfgItem `json:"month_list"`
	YearList  []DialogBannerCfgItem `json:"year_list"`
}
