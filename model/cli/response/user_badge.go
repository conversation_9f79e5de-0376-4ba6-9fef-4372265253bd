package response

type MyBadgeItem struct {
	Id               uint   `json:"id"`
	Name             string `json:"name"`
	TriggerCondition string `json:"trigger_condition"`
	Icon             string `json:"icon"`
	Switch           bool   `json:"switch"`
	Single           bool   `json:"single"`
	Count            uint   `json:"count"`
	CountStr         string `json:"count_str"`
}

type SeriesSortItem struct {
	SeriesName string `json:"series_name"`
	Sort       int    `json:"sort"`
}

type BadgeItem struct {
	Id             uint   `json:"id" form:"id" gorm:"primaryKey"` // 主键ID
	Name           string `json:"name"              form:"name"              gorm:"column:name;type:string;size:32;comment:名称"`
	OnIcon         string `json:"on_icon"           form:"on_icon"           gorm:"column:on_icon;type:string;size:256;comment:点亮图标"`
	FloRewardCount uint   `json:"flo_reward_count"  form:"flo_reward_count"  gorm:"column:flo_reward_count;type:int;comment:鲜花奖励数量"`
	SuccessContent string `json:"success_content"   form:"success_content"   gorm:"column:success_content;type:string;size:512;comment:达成提示信息"`
}
