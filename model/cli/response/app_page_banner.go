package response

import "git.costnovel.com/cashbox-ai/ai-par-model/model"

type AppPageBannerItem struct {
	ID       uint                 `json:"id"`
	TurnPage model.TurnPage       `json:"turn_page"`
	AiRoleId string               `json:"ai_role_id"`
	FuncType model.BannerFuncType `json:"func_type"`
	ImgId    uint                 `json:"img_id"`
	SceneId  uint                 `json:"scene_id"`
	Banner   string               `json:"banner"`
}
