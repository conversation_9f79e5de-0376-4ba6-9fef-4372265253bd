package response

import (
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"time"
)

type VideoSimplifySubListRes struct {
	ProductList []VideoSimplifyProductInfo `json:"product_list"`
}
type VideoSimplifyVipRes struct {
	ProductTypes []VideoSimplifyProductListRes `json:"product_types"`
	UserDes      string                        `json:"user_des"`
}

// type VideoSimplifyProductTypeRes struct {
// 	ProductTypes []VideoSimplifyProductListRes `json:"product_types"`
// }

type VideoSimplifyProductListRes struct {
	TypeTitle   string                     `json:"type_title"`
	ProductList []VideoSimplifyProductInfo `json:"product_list"`
	Hot         bool                       `json:"hot"`
}

type VideoSimplifyProductInfo struct {
	Id                uint               `json:"id"`
	Name              string             `json:"name"`
	Description       string             `json:"description"`
	GooID             string             `json:"goo_id"`
	IOSID             string             `json:"ios_id"`
	Amount            int                `json:"amount"`
	RewardAmount      int                `json:"reward_amount"`
	OriginPrice       decimal.Decimal    `json:"origin_price"`
	USDPrice          decimal.Decimal    `json:"usd_price"`
	Bonus             string             `json:"bonus"`
	Hot               bool               `json:"hot"`
	ByMonthAmount     string             `json:"by_monthAmount"`
	MemberEquity      []MemberEquityItem `json:"member_equity"`
	VidifyProductType int                `json:"vidify_product_type"`
}

type MemberEquityItem struct {
	Description string `json:"description"`
	IsOpen      bool   `json:"is_open"`
}

type VideoSimplifyShopRes struct {
	List []VideoSimplifyShopItem `json:"list"`
}

type VideoSimplifyShopItem struct {
	Id           uint            `json:"id"`
	Name         string          `json:"name"`
	Description  string          `json:"description"`
	GooID        string          `json:"goo_id"`
	IOSID        string          `json:"ios_id"`
	Amount       int             `json:"amount"`
	RewardAmount int             `json:"reward_amount"`
	OriginPrice  decimal.Decimal `json:"origin_price"`
	USDPrice     decimal.Decimal `json:"usd_price"`
	Hot          bool            `json:"hot"`
	Bonus        string          `json:"bonus"`
}

type KLingRequestPayload struct {
	ModelName      string             `json:"model_name,omitempty"`
	Mode           string             `json:"mode,omitempty"`
	Duration       string             `json:"duration,omitempty"`
	ImageTail      string             `json:"image_tail,omitempty"`
	Image          string             `json:"image"`
	Prompt         string             `json:"prompt"`
	CfgScale       float64            `json:"cfg_scale,string"`
	StaticMask     string             `json:"static_mask,omitempty"`
	NegativePrompt string             `json:"negative_prompt,omitempty"`
	DynamicMasks   []KLingDynamicMask `json:"dynamic_masks,omitempty"`
	CallbackUrl    string             `json:"callback_url"`
}
type KLingDynamicMask struct {
	Mask         string            `json:"mask"`
	Trajectories []KLingTrajectory `json:"trajectories"`
}

type KLingTrajectory struct {
	X int `json:"x"`
	Y int `json:"y"`
}

type KLingResponseData struct {
	Code      int    `json:"code"`
	Message   string `json:"message"`
	RequestID string `json:"request_id"`
	Data      struct {
		TaskID   string `json:"task_id"`
		TaskInfo struct {
			ExternalTaskID string `json:"external_task_id"`
		} `json:"task_info"`
		TaskStatus string `json:"task_status"`
		CreatedAt  int64  `json:"created_at"`
		UpdatedAt  int64  `json:"updated_at"`
	} `json:"data"`
}

type KLingTaskResult struct {
	ID       string `json:"id"`
	URL      string `json:"url"`
	Duration string `json:"duration"`
}

type KLingTaskResponseData struct {
	TaskID        string `json:"task_id"`
	TaskStatus    string `json:"task_status"`
	TaskStatusMsg string `json:"task_status_msg"`
	TaskResult    struct {
		Videos []KLingTaskResult `json:"videos"`
	} `json:"task_result"`
	CreatedAt int64 `json:"created_at"`
	UpdatedAt int64 `json:"updated_at"`
}

type KLingAPIResponse struct {
	Code      int                   `json:"code"`
	Message   string                `json:"message"`
	RequestID string                `json:"request_id"`
	Data      KLingTaskResponseData `json:"data"`
}

type TwoImgResponse struct {
	Code int `json:"code"` // 返回码
	Data struct {
		URL string `json:"url"` // 合成后的图片 URL
	} `json:"data"` // 数据部分
	Msg string `json:"msg"` // 返回消息
}

type KLingModelConfigResponse struct {
	ModelName      string  `json:"model_name"`
	Mode           string  `json:"mode"`
	Duration       string  `json:"duration"`
	Prompt         string  `json:"prompt"`
	NegativePrompt string  `json:"negative_prompt"`
	ImageTail      string  `json:"image_tail"`
	CfgScale       float64 `json:"cfg_scale"`
}

type KLingMsg struct {
	//可灵的参数
	ModelName      string  `json:"model_name"`
	Mode           string  `json:"mode"`
	Duration       string  `json:"duration"`
	ImgUrL         string  `json:"img_url"`
	Prompt         string  `json:"prompt"`
	NegativePrompt string  `json:"negative_prompt"`
	CfgScale       float64 `json:"cfg_scale"`
	ImageTail      string  `json:"image_tail"`
	// 私有
	TemplateType  uint `json:"template_type"`
	TemplateId    uint `json:"template_id"`
	DigitalUserId uint `json:"digital_user_id"`
	UserVideoId   uint `json:"user_video_id"`
	Cost          int  `json:"cost"`

	//新增-------------------KlingaiEffectsOne 或 KlingaiEffectsTwo
	KlingaiType int `gorm:"column:klingai_type;type:tinyint;not null;default:0;comment:0-图生视频, 1-单图特效, 2-双图特效" json:"klingai_type"`
	// 支持 KlingaiEffectsOne 或 KlingaiEffectsTwo
	KlingaiEffectsData json.RawMessage `json:"klingai_effects_data"`
	ImgList            []string        `json:"img_list"`
}

type KlingaiEffectsOne struct {
	ModelName string `json:"model_name"`
	Image     string `json:"image"`
	Duration  string `json:"duration"`
}
type KlingaiEffectsTwo struct {
	ModelName string    `json:"model_name"`
	Mode      string    `json:"mode"`
	Images    [2]string `json:"images"`
	Duration  string    `json:"duration"`
}

type KlingaiText2Video struct {
	Prompt      string `json:"prompt"`
	CallbackUrl string `json:"callback_url,omitempty"`
}

type KlingaiEffectsInput interface {
	KlingaiEffectsOne | KlingaiEffectsTwo
}
type KlingaiEffects[T KlingaiEffectsInput] struct {
	EffectScene    string `json:"effect_scene"`
	Input          T      `json:"input"`
	CallbackUrl    string `json:"callback_url,omitempty"`
	ExternalTaskId string `json:"external_task_id,omitempty"`
}

type KLingUploadMsg struct {
	VideoUrl            string `json:"video_url"`
	IsVip               bool   `json:"is_vip"`
	UserVideoTemplateId uint   `json:"user_video_template_id"`
}

// TaskInfo 任务创建时的参数信息
type TaskInfo struct {
	ExternalTaskID string `json:"external_task_id"` // 客户自定义任务ID
}

// ImageResult 图片类任务的结果
type ImageResult struct {
	Index int    `json:"index"` // 图片编号
	URL   string `json:"url"`   // 图片的URL
}

// VideoResult 视频类任务的结果
type VideoResult struct {
	ID       string `json:"id"`       // 视频ID，全局唯一
	URL      string `json:"url"`      // 视频的URL
	Duration string `json:"duration"` // 视频总时长，单位s
}

// TaskResult 任务结果
type TaskResult struct {
	Images []ImageResult `json:"images"` // 图片结果列表
	Videos []VideoResult `json:"videos"` // 视频结果列表
}

// CallbackResponse 可灵回调的响应结构
type CallbackResponse struct {
	TaskID        string     `json:"task_id"`         // 任务ID，系统生成
	TaskStatus    string     `json:"task_status"`     // 任务状态，枚举值：submitted、processing、succeed、failed
	TaskStatusMsg string     `json:"task_status_msg"` // 任务状态信息，当任务失败时展示失败原因
	TaskInfo      TaskInfo   `json:"task_info"`       // 任务创建时的参数信息
	CreatedAt     int64      `json:"created_at"`      // 任务创建时间，Unix时间戳，单位ms
	UpdatedAt     int64      `json:"updated_at"`      // 任务更新时间，Unix时间戳，单位ms
	TaskResult    TaskResult `json:"task_result"`     // 任务结果
}

type TemplateInfoRes struct {
	VideoUrL      string              `gorm:"column:video_url;type:varchar(255);not null;comment:模版视频地址" json:"video_url"`
	VideoCoverUrL string              `gorm:"column:video_cover_url;type:varchar(255);not null;comment:模版视频封面地址" json:"video_cover_url"`
	Title         string              `gorm:"column:title;type:varchar(100);not null;comment:模版标题" json:"title"`
	Description   string              `gorm:"column:description;type:varchar(255);comment:模版描述" json:"description"`
	TemplateType  uint                `gorm:"column:template_type;type:int;not null;comment:模版类型" json:"template_type"`
	Cost          int                 `gorm:"column:cost;type:int;not null;comment:花费" json:"cost"`
	ModelConfig   string              `gorm:"column:model_config;type:varchar(255);not null;comment:kling模型配置相关" json:"model_config"`
	DurationList  []map[string]string `json:"duration_list"`
	ModelType     []map[string]string `json:"model_type"`
	TagList       []string            `json:"tag_list"  form:"-"  gorm:"-" default:"0"`
}

type VideoSimplifyNoticeMsgBody struct {
	UserVideoId uint   `json:"user_video_id"`
	Content     string `json:"content"`
	Title       string `json:"title"`
	ImgUrl      string `json:"img_url"`
}

type VideoSimplifyBannerResponse struct {
	Id            uint               `json:"id"         form:"id"         gorm:"primaryKey"`
	CreatedAt     time.Time          `json:"created_at" form:"created_at" gorm:"autoCreateTime;not null;comment:创建时间;"`
	UpdatedAt     time.Time          `json:"updated_at" form:"updated_at" gorm:"autoUpdateTime;not null;comment:更新时间;"`
	DeletedAt     gorm.DeletedAt     `gorm:"index" json:"-"`
	VideoUrL      string             `gorm:"column:video_url;type:varchar(255);not null;comment:视频地址" json:"video_url"`
	VideoCoverUrL string             `gorm:"column:video_cover_url;type:varchar(255);not null;comment:视频封面地址" json:"video_cover_url"`
	Title         string             `gorm:"column:title;type:varchar(100);not null;comment:视频标题" json:"title"`
	Description   string             `gorm:"column:description;type:varchar(255);comment:描述" json:"description"`
	RedirectUrL   string             `gorm:"column:redirect_url;type:varchar(255);comment:跳转地址" json:"redirect_url"`
	RedirectType  model.RedirectType `gorm:"column:redirect_type;type:int;not null;comment:跳转类型" json:"redirect_type"`
	TemplateType  uint               `gorm:"-" json:"template_type"`
	Cost          int                `gorm:"-" json:"cost"`
}

type UserVideoListResp struct {
	List     []*UserVideoListItem `json:"list"`
	Total    int64                `json:"total"`
	Page     int                  `json:"page"`
	PageSize int                  `json:"pageSize"`
}

type UserVideoListItem struct {
	Title string `json:"title"`
	*model.VideoSimplifyUserVideo
}

type CreateIndexResp struct {
	List []*CreateIndexItem `json:"list"`
	Cost int                `json:"cost"`
}
type CreateIndexItem struct {
	Id            int    `json:"id"`
	VideoUrL      string `gorm:"column:video_url;type:varchar(255);not null;comment:模版视频地址" json:"video_url"`
	VideoCoverUrL string `gorm:"column:video_cover_url;type:varchar(255);not null;comment:模版视频封面地址" json:"video_cover_url"`
	Title         string `gorm:"column:title;type:varchar(100);not null;comment:模版标题" json:"title"`
	Description   string `gorm:"column:description;type:varchar(255);comment:模版描述" json:"description"`
}
