package response

// AppVoiceCallCfg app语音通话配置
type AppVoiceCallCfg struct {
	FirstMsgs       []string `json:"first_msgs"`
	IdleMsgs        []string `json:"idle_msgs"`
	FloNoEnoughMsgs []string `json:"flo_no_enough_msgs"`
}

type MsgReqMapCfg struct {
	MaxNewTokens      int     `json:"max_new_tokens"`
	Temperature       float64 `json:"temperature"`
	TopP              float64 `json:"top_p"`
	TopK              float64 `json:"top_k"`
	RepetitionPenalty float64 `json:"repetition_penalty"`
}

type DiyPhotoLv2TacticsCfg struct {
	UserIsSub bool  `json:"user_is_sub"`
	RegDays   int   `json:"reg_days"`
	DiyCount  int64 `json:"diy_count"`
	Nsfw      bool  `json:"nsfw"`
	ChatCount int   `json:"chat_count"`
}

type FlowerShopSVipTextListCfg struct {
	Tips []string `json:"tips"`
}

type SVipDialogTipItem struct {
	Tip       string `json:"tip"`
	IsPremier bool   `json:"is_premier"`
}

type SVipDialogCfg struct {
	Title   string              `json:"title"`
	TipList []SVipDialogTipItem `json:"tip_list"`
}

type SplashAdCfg struct {
	Days           int `json:"days"`
	StepSecond     int `json:"step_second"`
	LowDialogCount int `json:"low_dialog_count"`
}

type CountryListCfg struct {
	Countrys []string `json:"countrys"`
}

type SetIntimateLevelCfgReq struct {
	List []IntimateLevelCfg `json:"list"`
}

type IntimateLevelCfg struct {
	Level uint `json:"level"`
	Left  uint `json:"left"`
	Right uint `json:"right"`
}

type IntimateUpgradeCfg struct {
	Free       int `json:"free"`
	Vip        int `json:"vip"`
	SVip       int `json:"svip"`
	ChatVip    int `json:"chat_vip"`
	ContentVip int `json:"content_vip"`
}

type IntimateUpgradeDialogCfg struct {
	Title         string `json:"title"`
	Content       string `json:"content"`
	BottomContent string `json:"bottom_content"`
	Icon          string `json:"icon"`
	LevelIcon     string `json:"level_icon"`
	RequireType   int    `json:"require_type"` // 需要的类型(1,免费)(2,需要VIP)
}

type IntimateDialogCfg struct {
	Describe   string                  `json:"describe"`
	UpgradeTip string                  `json:"upgrade_tip"`
	LevelList  []IntimateLevelListItem `json:"level_list"`
}

type IntimateUpgradeMsgCfg struct {
	LevelOne   []string `json:"level_one"`
	LevelTwo   []string `json:"level_two"`
	LevelThree []string `json:"level_three"`
	LevelFour  []string `json:"level_four"`
	LevelFive  []string `json:"level_five"`
}

type IntimateLevelListItem struct {
	Level      uint   `json:"level"`
	Title      string `json:"title"`
	Content    string `json:"content"`
	Icon       string `json:"icon"`
	Btn        string `json:"btn"`
	State      int    `json:"state"`      // (1, 已解锁), (2, 进行中), (3, 未解锁)
	Percentage int    `json:"percentage"` // 百分比
}

type DiyPhotoCountRangeCfg struct {
	CountRange []int `json:"count_range"`
}

type AiSendUserGiftTxtCfg struct {
	TxtList []AiSendUserGiftTxtItem `json:"txt_list"`
}

type AiSendUserGiftTxtItem struct {
	Content string `json:"content"`
}

type AiSendUserGiftImgCfg struct {
	GiftList []AiSendUserGiftImgItem `json:"gift_list"`
}

type AiSendUserGiftImgItem struct {
	Icon string `json:"icon"`
}

type MsgModelStrageCfg struct {
	Open bool `json:"open"`
}

type ReSubRewardCoinCfg struct {
	WeekVip  int `json:"week_vip"`
	MonthVip int `json:"month_vip"`
	YearVip  int `json:"year_vip"`
}

type FirstMsgItem struct {
	Label string `json:"label"`
	Msg   string `json:"msg"`
}

type GenAiPreCfg struct {
	LabelList    []string       `json:"label_list"`
	FirstMsgList []FirstMsgItem `json:"first_msg_list"`
}

type RequireImgBoxCfg struct {
	MsgStep   int      `json:"msg_step"`   // 消息步长
	MsgList   []string `json:"msg_list"`   // 消息列表
	MainTitle string   `json:"main_title"` // 主标题
	Subtitle  string   `json:"subtitle"`   // 副标题
	ImgUrl    string   `json:"img_url"`    // 图标地址
}

type RequireImgSubMsgCfg struct {
	Step      int      `json:"step"`
	MaskImg   string   `json:"mask_img"`
	ProductID uint     `json:"product_id"`
	MsgList   []string `json:"msg_list"`
}

// GuidedGiftGivingCfgReq 新增礼物引导 孙宏伟需求
type GuidedGiftGivingCfgReq struct {
	Start int `json:"start"`
	End   int `json:"end"`
	Round int `json:"round"`
}

// ReplyChargeCfgReq 12-31 增加快捷回复消息下发付费
type ReplyChargeCfgReq struct {
	Normal int `json:"normal"`
	Nsfw   int `json:"nsfw"`
}
