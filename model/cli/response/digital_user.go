package response

import (
	reqBtc "aimsg-server/model/backend/request/btc"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/datatypes"
)

type DeviceToken struct {
	DeviceId string `json:"device_id"`
}

type SocialLoginRtm struct {
	Token         string `json:"token"`
	ExpireSeconds int    `json:"expire_seconds"`
}

type SocialLoginRes struct {
	Token string          `json:"token"`
	Rtm   SocialLoginRtm  `json:"rtm"`
	User  DigitalUserInfo `json:"user"`
	IsNew bool            `json:"is_new"`
}

type DialogType int

const (
	DialogTypeLowPriceCoin DialogType = 210 // 跳转到低价鲜花弹窗
	DialogTypeVIP          DialogType = 410 // 跳转到VIP订阅
	DialogTypeCoinShop     DialogType = 440 // 跳转到金币商城
	DialogTypeSVIP         DialogType = 460 // 跳转到SVIP
	DialogTypeContentVIP   DialogType = 601 // 跳转到内容VIP
	DialogTypeChatVIP      DialogType = 602 // 跳转到畅聊VIP
)

type DigitalUserInfo struct {
	Id               uint                                  `json:"id"`
	AppId            uint                                  `json:"app_id"`
	UserGroup        int                                   `json:"user_group"`
	Nickname         *string                               `json:"nickname"`
	Avatar           string                                `json:"avatar"`
	Username         string                                `json:"username"`
	Password         string                                `json:"password"`
	LoginType        model.LoginType                       `json:"login_type"`
	Gender           model.GenderType                      `json:"gender"`
	LikeGender       datatypes.JSONSlice[model.GenderType] `json:"like_gender"`
	AgeRange         model.AgeRange                        `json:"age_range"`
	Birthday         string                                `json:"birthday"`
	SubStatus        model.SubStatus                       `json:"sub_status"`
	SubType          model.SubType                         `json:"sub_type"`
	UserStatus       model.UserStatus                      `json:"user_status"`
	SubExpire        string                                `json:"sub_expire"`
	SVipStatus       model.SubStatus                       `json:"svip_status"`
	SVipExpire       string                                `json:"svip_expire"`
	ChatVipStatus    model.SubStatus                       `json:"chat_vip_status"`
	ChatVipExpire    string                                `json:"chat_vip_expire"`
	ChatVipType      model.SubType                         `json:"chat_vip_type"`
	ContentVipStatus model.SubStatus                       `json:"content_vip_status"`
	ContentVipExpire string                                `json:"content_vip_expire"`
	ContentVipType   model.SubType                         `json:"content_vip_type"`
	CreatedAt        string                                `json:"created_at"`
	FlowerAmount     int                                   `json:"flower_amount"`
	Nsfw             bool                                  `json:"nsfw"`
	ChatAds          bool                                  `json:"chat_ads"`
	SplashAd         bool                                  `json:"splash_ads"`
	OldUser          bool                                  `json:"old_user"`
	ShowDialogType   DialogType                            `json:"show_dialog_type"`
	SplashSecond     int                                   `json:"splash_second"`
	WebOne           model.SwitchStatus                    `json:"web_one"`
	WebTwo           model.SwitchStatus                    `json:"web_two"`
	WebThree         model.SwitchStatus                    `json:"web_three"`
	MinPrice         int                                   `json:"min_price"`
	ResetQuickReply  int                                   `json:"reset_quick_reply"` // 用户重置快捷回复所需金币数
	BtcData          reqBtc.UserInfo                       `json:"btc_data"`
	CallAssets       CallAssets                            `json:"call_assets"`
	IsNew            bool                                  `json:"is_new"`
	App4IsOld        int                                   `json:"app4_is_old"`
}

type CallAssets struct {
	FreeTime int `json:"free_time"`
}

type ReceiveFlowersCoinsResp struct {
	Coins int `json:"coins"`
}

type ReceiveFlowersDialogRes struct {
	Title      string `json:"title"`
	FloCount   int    `json:"flo_count"`
	Exist      int64  `json:"exist"`
	Content    string `json:"content"`
	BtnContent string `json:"btn_content"`
}

type ReceiveFlowersDialogV2Res struct {
	Title      string `json:"title"`
	FloCount   int    `json:"flo_count"`
	Exist      int64  `json:"exist"`
	Content    string `json:"content"`
	BtnContent string `json:"btn_content"`
}

type ReceiveFlowersDialogV3Res struct {
	TodayIsSigin int               `json:"today_is_sigin"`
	List         []SiginRewardData `json:"list"`
}

type SiginInfo struct {
	TodayIsSign     bool   `json:"today_is_sign"`
	NextSignSeconds int    `json:"next_sign_seconds"`
	Tip1            string `json:"tip1"`
	Tip2            string `json:"tip2"`
}

type SiginRewardData struct {
	FlowerNum    int    `json:"flower_num"`
	FlowerNumSub int    `json:"flower_num_sub"`
	Date         string `json:"date"`
	IsSigin      int    `json:"is_sigin"` // 1:已签到、2:过期漏签、3:可签
	IsToday      bool   `json:"is_today"` // 当前是否是今天的
}
type WatchVideoInfo struct {
	Title              string `json:"title"`
	FlowerCountForHour int    `json:"flower_count_for_hour"`
	MaxCountForHour    int    `json:"max_count_for_hour"`
}

type SignInListRes struct {
	MyFlowerCount   int               `json:"my_flower_count"`
	SiginInfo       SiginInfo         `json:"sigin_info"`
	SiginRewardData []SiginRewardData `json:"sigin_reward_data"`
	WatchVideoInfo  WatchVideoInfo    `json:"watch_video_info"`
}

type AiFuncItem struct {
	FuncType     int         `json:"func_type"`
	Icon         string      `json:"icon"`
	Msg          string      `json:"msg"`
	MinPrice     int         `json:"min_price"`
	TouchOptions interface{} `json:"touch_options"`
	GiftList     interface{} `json:"gift_list"`
	BodyList     interface{} `json:"body_list"`
	Dot          bool        `json:"dot"`
}

type GenVideoHistoryItem struct {
	Id             uint            `json:"id"`
	GenStatus      model.GenStatus `json:"gen_status"`
	VideoFullUrl   string          `json:"video_full_url"`
	VideoUrl       string          `json:"video_url"`
	UserImgFullUrl string          `json:"user_img_full_url"`
	UserImgUrl     string          `json:"user_img_url"`
}

type VideoTxtListItem struct {
	Id          uint   `json:"id"`
	Txt         string `json:"txt"`
	FileFullUrl string `json:"file_key"`
	Source      string `json:"source"`
	Price       int    `json:"price"`
}

type ReceiveAiRoleFlowersRes struct {
	DeepFlo int `json:"deep_flo"`
}

type ImgBlindBoxRes struct {
	MainTitle  string `json:"main_title"` // 主标题
	Subtitle   string `json:"subtitle"`   // 副标题
	BodyImgArr []AiBodyImgSwitchRes
}

type UserRewardResp struct {
	List []UserRewardListItem `json:"list"`
}

type UserRewardListItem struct {
	Type  string   `json:"type"`
	Items []string `json:"items"`
}
