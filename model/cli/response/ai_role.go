package response

import (
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/datatypes"
)

type AiRoleProfileInfo struct {
	Id          uint                 `json:"id"`
	RoleID      string               `json:"role_id"`
	RoleType    model.AiRoleType     `json:"role_type"`
	RoleLabels  []string             `json:"role_labels"`
	Nickname    string               `json:"nickname"`
	CanUndress  bool                 `json:"can_undress"`
	ChatCount   int                  `json:"chat_count"`
	CountStr    string               `json:"count_str"`
	Describe    string               `json:"describe"`
	Gender      model.GenderType     `json:"gender"`
	Avatar      string               `json:"avatar"`
	PhotoList   []*model.AiSendMedia `json:"photo_list"`
	AsmrList    []*model.AiSendMedia `json:"asmr_list"`
	VideoList   []*model.AiSendMedia `json:"video_list"`
	ContactLock bool                 `json:"contact_lock"`
	ContactList []*AiContactInfoItem `json:"contact_list"`
	ProfileInfo *model.AiProfileInfo `json:"profile_info"`
	LabelList   []AiRoleLabelAndIcon `json:"label_list"`
}

type AiRoleListV2Res struct {
	Id           uint                        `json:"id" form:"id" gorm:"primaryKey"` // 主键ID
	RoleID       string                      `json:"role_id"         form:"role_id"         gorm:"column:role_id;type:string;size:32;uniqueIndex;comment:角色ID"`
	RoleType     model.AiRoleType            `json:"role_type"       form:"role_type"       gorm:"column:role_type;type:smallint;default:1;comment:角色类型"`
	RoleLabels   datatypes.JSONSlice[string] `json:"role_labels"     form:"role_labels"     gorm:"column:role_labels;type:json;default:'[]';comment:角色标签"`
	Nickname     string                      `json:"nickname"        form:"nickname"        gorm:"column:nickname;type:string;size:32;comment:昵称"`
	Describe     string                      `json:"describe"        form:"describe"        gorm:"column:describe;type:text;comment:描述"`
	Gender       model.GenderType            `json:"gender"          form:"gender"          gorm:"column:gender;type:smallint;default:2;comment:性别"`
	Avatar       string                      `json:"avatar"          form:"avatar"          gorm:"column:avatar;type:string;size:512;comment:头像地址"`
	GifUrl       string                      `json:"gif_url"         form:"gif_url"         gorm:"column:gif_url;type:string;size:512;comment:GIF地址"`
	WebThumbnail string                      `json:"web_thumbnail"   form:"web_thumbnail"   gorm:"column:web_thumbnail;type:string;size:512;comment:Web缩略图"`
	ChatBG       string                      `json:"chat_bg"         form:"chat_bg"         gorm:"column:chat_bg;type:string;size:512;comment:聊天背景地址"`
	FirstMsg     string                      `json:"first_msg"       form:"first_msg"       gorm:"column:first_msg;type:text;comment:第一句问候语"`
	FirstVoice   string                      `json:"first_voice"     form:"first_voice"     gorm:"column:first_voice;type:string;size:512;comment:第一句问候语的语音"`
	FirstImg     string                      `json:"first_img"       form:"first_img"       gorm:"column:first_img;type:string;size:512;comment:第一句图片"`
	ChatCount    int                         `json:"chat_count"      form:"chat_count"      gorm:"column:chat_count;type:int;default:0;comment:聊天次数"`
	RolePlay     model.SwitchStatus          `json:"role_play"       form:"role_play"       gorm:"column:role_play;type:int;default:2;comment:role_play开关"`
	RealAnchorId uint                        `json:"real_anchor_id"  form:"real_anchor_id"  gorm:"column:real_anchor_id;type:int;comment:真人主播ID"`
	OnlineStatus model.OnlineStatus          `json:"online_status"   form:"online_status"   gorm:"column:online_status;type:int;comment:在线状态"`
	CountStr     string                      `json:"count_str"       form:"count_str"       gorm:"-"`
	LabelList    []AiRoleLabelAndIcon        `json:"label_list" form:"-" gorm:"-"`
	VideoUrl     string                      `json:"video_url"         form:"video_url"         gorm:"column:video_url;type:string;size:512;comment:mp4地址"`
	RoleDesc     string                      `json:"role_desc"       form:"role_desc"       gorm:"column:role_desc;type:text;comment:角色简介"`
}

type AiRoleLabelAndIcon struct {
	RoleLabel string `json:"role_label"`
	LabelIcon string `json:"label_icon"`
}

type AiFuncShowRes struct {
}

type UnlockProfileContentResp struct {
	Status int `json:"status"` // 0 付费解锁 1广告解锁
}
type UnlockProfileContentResp2 struct {
}
