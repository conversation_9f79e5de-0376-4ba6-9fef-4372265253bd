package response

import "git.costnovel.com/cashbox-ai/ai-par-model/model"

type PrivateSpaceListItem struct {
	Id           uint            `json:"id"`
	Name         string          `json:"name"         form:"name"         gorm:"column:name;type:string;size:64;comment:名称;"`
	MediaType    model.MediaType `json:"media_type"   form:"media_type"   gorm:"column:media_type;type:int;default:1;comment:媒体类型"`
	MediaSize    string          `json:"media_size"   form:"media_size"   gorm:"column:media_size;type:string;size:32;comment:文件大小"`
	ThumbnailURL string          `json:"thumbnail_url" form:"thumbnail_url" gorm:"column:thumbnail_url;type:string;size:512;comment:缩略图地址"`
	ImgCount     int             `json:"img_count"    form:"img_count"    gorm:"column:img_count;type:int;comment:图片数量"`
	VideoSecond  int             `json:"video_second" form:"video_second" gorm:"column:video_second;type:int;comment:视频秒数"`
	Price        int             `json:"price"        form:"price"        gorm:"column:price;type:int;comment:解锁价格"`
	BuyTime      string          `json:"buy_time"`
	IsBuy        bool            `json:"is_buy"`
}
