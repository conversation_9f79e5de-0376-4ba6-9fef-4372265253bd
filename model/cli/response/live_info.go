package response

type LiveConfigRes struct {
	ViewerList    []LiveViewerItem `json:"viewer_list"`
	GiftList      []LiveGiftItem   `json:"gift_list"`
	MinMinute     int              `json:"min_minute"`
	MaxMinute     int              `json:"max_minute"`
	DayLimitCount int              `json:"day_limit_count"`
	DayUsedCount  int              `json:"day_used_count"`
	RemainCount   int              `json:"remain_count"`
}

type LiveAiRoleRankRes struct {
	List []*AiRoleListV2Res `json:"list"`
}

type LiveViewerItem struct {
	Id       uint   `json:"id"`
	CountStr string `json:"count_str"`
	Count    int    `json:"count"`
	Price    int    `json:"price"`
	Free     bool   `json:"free"`
}
