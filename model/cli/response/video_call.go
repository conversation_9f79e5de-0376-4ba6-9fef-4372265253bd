package response

import "git.costnovel.com/cashbox-ai/ai-par-model/model"

type MicroUserOnlineRes struct {
	Data struct {
		Id     int                `json:"id"`
		Online model.OnlineStatus `json:"online"`
	} `json:"data"`
	Msg    string `json:"msg"`
	Status int    `json:"status"`
}

type MicroCallRes struct {
	Data   MicroCallDataRes `json:"data"`
	Msg    string           `json:"msg"`
	Status int              `json:"status"`
}

type MicroCallDataRes struct {
	RoomId    string `json:"roomId"`
	Msg       string `json:"msg"`
	RemoteUid uint   `json:"remoteUid"`
	AgoId     string `json:"agoId"`
}

type MicroVideoCallCloseRes struct {
	Data   interface{} `json:"data"`
	Msg    string      `json:"msg"`
	Status int         `json:"status"`
}

type MicroVideoCallCancelRes struct {
	Data   interface{} `json:"data"`
	Msg    string      `json:"msg"`
	Status int         `json:"status"`
}

type RealAnchorInfoRes struct {
	Online  model.OnlineStatus `json:"online"`
	CanCall bool               `json:"can_call"`
}

type CallStartRes struct {
	VideoCallId    uint   `json:"video_call_id"`
	RoomId         string `json:"room_id"`
	JoinRoomUserId int    `json:"join_room_user_id"`
	RtcToken       string `json:"rtc_token"`
}

type CallDeductionRes struct {
	CoinAmount int `json:"coin_amount"`  // 余额
	MinPrice   int `json:"min_price"`    // 每分钟价格
	CanCallMin int `json:"can_call_min"` // 可通话分钟数
}
