package response

type OrderPayRes struct {
	Id      uint   `json:"id"`
	OrderNo string `json:"order_no"`
	PayUrl  string `json:"pay_url"`
}

type PayMethodItemRes struct {
	Id   uint   `json:"id"`
	Name string `json:"name"`
	Icon string `json:"icon"`
}

type PayMethodListRes struct {
	List []PayMethodItemRes `json:"list"`
}

type PayCenterMethodListResponse struct {
	Code int `json:"code"`
	Data struct {
		List []PayCenterMethodListRes `json:"list"`
	} `json:"data"`
	Msg string `json:"msg"`
}

type PayCenterPayResponse struct {
	Code int `json:"code"`
	Data struct {
		OrderNo    string `json:"order_no"`
		PayURL     string `json:"pay_url"`
		QrCode     string `json:"qr_code"`
		Commission string `json:"commission"`
		LossFee    string `json:"loss_fee"`
	} `json:"data"`
	Msg string `json:"msg"`
}

type PayCenterSubPayResponse struct {
	Code int `json:"code"`
	Data struct {
		Commission string `json:"commission"` // 手续费
		LossFee    string `json:"loss_fee"`   // 汇损手续费
		OrderNo    string `json:"order_no"`   // 订单号
		PayUrl     string `json:"pay_url"`    // 渠道返回的支付web链接
		QrCode     string `json:"qr_code"`    // 渠道返回的支付二维码字符串
	} `json:"data"`
	Msg string `json:"msg"`
}

type PayCenterMethodListRes struct {
	Id         uint   `json:"id"`
	Name       string `json:"name"`
	Icon       string `json:"icon"`
	ErrorCount uint   `json:"error_count"`
	PayRate    uint   `json:"pay_rate"`
	Alias      string `json:"alias"`
}

type SubPayCenterMethodListResponse struct {
	Code int `json:"code"`
	Data struct {
		List []SubPayCenterMethodListRes `json:"list"`
	} `json:"data"`
	Msg string `json:"msg"`
}

type SubPayCenterMethodListRes struct {
	Id         uint   `json:"id"`
	Name       string `json:"name"`
	Icon       string `json:"icon"`
	PayRate    int64  `json:"pay_rate"`
	ErrorCount int64  `json:"error_count"`
	Alias      string `json:"alias"`
	SupportSS  bool   `json:"support_ss"` // 是否支持订阅
}
