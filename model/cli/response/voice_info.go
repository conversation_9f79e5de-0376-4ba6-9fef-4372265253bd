package response

import (
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/datatypes"
)

type VoiceListItem struct {
	ID       uint                                  `json:"id"`
	AppID    uint                                  `json:"app_id"`
	Gender   model.GenderType                      `json:"gender"`
	Title    string                                `json:"title"`
	VoiceReq datatypes.JSONType[model.EleVoiceReq] `json:"voice_req"`
	Example  string                                `json:"example"`
	UseCount int                                   `json:"use_count"`
}
