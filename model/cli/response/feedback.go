package response

import "git.costnovel.com/cashbox-ai/ai-par-model/model"

type FeedbackAddResp struct {
}
type FeedbackListResp struct {
	List []*FeedbackFlagItem `json:"list"`
}

type FeedbackFlagItem struct {
	ID      int    `json:"id"`
	Content string `json:"content"`
}

type FeedbackBatchResp struct {
	List []*FeedbackBatchItem `json:"list"`
}

type FeedbackBatchItem struct {
	*model.MsgFeedback
	FeedbackFlags []*FeedbackFlagItem `json:"feedback_flags"`
}
