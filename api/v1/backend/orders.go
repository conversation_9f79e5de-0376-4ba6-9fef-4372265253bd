package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type OrdersApi struct{}

var ordersService = service.ServiceGroupApp.BackendServiceGroup.OrdersService

func (m *OrdersApi) Create(c *gin.Context) {
	var reqData model.Order
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := ordersService.Create(&reqData); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *OrdersApi) AdjustTest(c *gin.Context) {
	var reqData req.AdjustTestReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := ordersService.AdjustTest(c, &reqData); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *OrdersApi) DeleteById(c *gin.Context) {
	var reqData model.Order
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := ordersService.DeleteById(reqData.Id); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *OrdersApi) DeleteByIds(c *gin.Context) {
	var reqData request.IdsReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := ordersService.DeleteByIds(reqData); err != nil {
		global.LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *OrdersApi) Update(c *gin.Context) {
	var reqData model.Order
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := ordersService.Update(&reqData); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *OrdersApi) GetById(c *gin.Context) {
	var reqData model.Order
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := ordersService.GetById(reqData.Id); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *OrdersApi) GetAll(c *gin.Context) {
	if res, err := ordersService.GetAll(); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *OrdersApi) SyncPriceCount(c *gin.Context) {
	go ordersService.SyncPriceCount()
	response.Ok(c)
}

func (m *OrdersApi) FixReSubOrderSVIP(c *gin.Context) {
	go ordersService.FixReSubOrderSVIP()
	response.Ok(c)
}

func (m *OrdersApi) FixReSubOrderVIP(c *gin.Context) {
	go ordersService.FixReSubOrderVIP()
	response.Ok(c)
}

func (m *OrdersApi) FixOrderSVIP(c *gin.Context) {
	go ordersService.FixOrderSVIP()
	response.Ok(c)
}

func (m *OrdersApi) FixOrderVIP(c *gin.Context) {
	go ordersService.FixOrderVIP()
	response.Ok(c)
}

func (m *OrdersApi) GetList(c *gin.Context) {
	var reqData req.OrdersSearch
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := ordersService.GetList(reqData); err != nil {
		global.LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     reqData.Page,
			PageSize: reqData.PageSize,
		}, "成功", c)
	}
}

func (m *OrdersApi) TestTdPointer(c *gin.Context) {
	var (
		err      error
		reqData  req.TestTdPointerReq
		userInfo *model.DigitalUser
	)
	if err = c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if userInfo, err = digitalUserService.GetById(c, reqData.UserId); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	ordersService.SendTdIapServer(userInfo, reqData.Properties)
	response.Ok(c)
}

func (m *OrdersApi) ExecSubNoBuyFlo(c *gin.Context) {
	var (
		err          error
		reqDataBytes []byte
		reqData      req.SubNoBuyFloQueueItem
	)
	if err = c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = ordersService.ExecSubNoBuyFlo(c, string(reqDataBytes)); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}
