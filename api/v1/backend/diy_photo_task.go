package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"aimsg-server/utils"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DiyPhotoTaskApi struct{}

var diyPhotoTaskService = service.ServiceGroupApp.BackendServiceGroup.DiyPhotoTaskService

func (m *DiyPhotoTaskApi) Create(c *gin.Context) {
	var reqData model.DiyPhotoTask
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.<PERSON>rror(), c)
		return
	}
	if err := diyPhotoTaskService.Create(&reqData); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *DiyPhotoTaskApi) DeleteById(c *gin.Context) {
	var reqData model.DiyPhotoTask
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := diyPhotoTaskService.DeleteById(c, reqData.Id); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *DiyPhotoTaskApi) DeleteByIds(c *gin.Context) {
	var reqData request.IdsReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := diyPhotoTaskService.DeleteByIds(reqData); err != nil {
		global.LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *DiyPhotoTaskApi) Update(c *gin.Context) {
	var reqData model.DiyPhotoTask
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := diyPhotoTaskService.Update(c, &reqData); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *DiyPhotoTaskApi) GetById(c *gin.Context) {
	var reqData model.DiyPhotoTask
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := diyPhotoTaskService.GetById(reqData.Id); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *DiyPhotoTaskApi) GetAll(c *gin.Context) {
	if res, err := diyPhotoTaskService.GetAll(); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *DiyPhotoTaskApi) GetList(c *gin.Context) {
	var reqData req.DiyPhotoTaskSearch
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := diyPhotoTaskService.GetList(reqData); err != nil {
		global.LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     reqData.Page,
			PageSize: reqData.PageSize,
		}, "成功", c)
	}
}

func (m *DiyPhotoTaskApi) RestartTask(c *gin.Context) {
	var reqData req.RestartTaskReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := diyPhotoTaskService.RestartTask(c, &reqData); err != nil {
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *DiyPhotoTaskApi) ReSendEmail(c *gin.Context) {
	var reqData req.ReSendEmailReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := diyPhotoTaskService.ReSendEmail(c, &reqData); err != nil {
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *DiyPhotoTaskApi) ReSendPushMsg(c *gin.Context) {
	var reqData req.ReSendPushMsgReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := diyPhotoTaskService.ReSendPushMsg(c, &reqData); err != nil {
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *DiyPhotoTaskApi) SwapFace(c *gin.Context) {
	var (
		reqData     req.SwapFaceReq
		imgTrainSer = service.ServiceGroupApp.BackendServiceGroup.ImgToImgTrainService
	)
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if imgPubPath, err := imgTrainSer.Img2ImgReplaceFaceV2(reqData.AvatarS3Url, reqData.MaskS3Url); err != nil {
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithData(utils.S3Url(imgPubPath, 1), c)
	}
}
