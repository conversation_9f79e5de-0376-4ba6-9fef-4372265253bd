package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
)

type LiveInfoApi struct{}

var liveInfoService = service.ServiceGroupApp.BackendServiceGroup.LiveInfoService

func (m *LiveInfoApi) Create(c *gin.Context) {
	var reqData model.LiveInfo
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := liveInfoService.Create(c, &reqData); err != nil {
		response.FailWithMessage(err.<PERSON><PERSON><PERSON>(), c)
	} else {
		response.Ok(c)
	}
}

func (m *LiveInfoApi) DeleteById(c *gin.Context) {
	var reqData model.LiveInfo
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := liveInfoService.DeleteById(c, reqData.Id); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *LiveInfoApi) DeleteByIds(c *gin.Context) {
	var reqData request.IdsReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := liveInfoService.DeleteByIds(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *LiveInfoApi) Update(c *gin.Context) {
	var reqData model.LiveInfo
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := liveInfoService.Update(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *LiveInfoApi) GetById(c *gin.Context) {
	var reqData model.LiveInfo
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := liveInfoService.GetById(c, reqData.Id); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *LiveInfoApi) GetAll(c *gin.Context) {
	if res, err := liveInfoService.GetAll(c); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *LiveInfoApi) GetList(c *gin.Context) {
	var reqData req.LiveInfoSearch
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := liveInfoService.GetList(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(response.PageResult{
			List:     list,
			Total:    total,
			Page:     reqData.Page,
			PageSize: reqData.PageSize,
		}, c)
	}
}
