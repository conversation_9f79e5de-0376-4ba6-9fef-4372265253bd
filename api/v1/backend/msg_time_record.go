package backend

import (
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"github.com/gin-gonic/gin"
)

type MsgTimeRecordApi struct{}

var msgTimeRecordService = service.ServiceGroupApp.BackendServiceGroup.MsgTimeRecordService

func (m *MsgTimeRecordApi) GetList(c *gin.Context) {
	var reqData req.MsgTimeRecordSearchReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := msgTimeRecordService.Find(c, reqData); err != nil {
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *MsgTimeRecordApi) GetCount(c *gin.Context) {
	var reqData req.MsgTimeRecordSearchReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.<PERSON>rror(), c)
		return
	}
	if res, err := msgTimeRecordService.Count(c, reqData); err != nil {
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}
