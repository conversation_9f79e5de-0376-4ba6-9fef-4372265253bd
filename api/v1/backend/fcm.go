package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/backend/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type FcmApi struct{}

var fcmService = service.ServiceGroupApp.BackendServiceGroup.FcmService

func (m *FcmApi) SendPushPushReceiveFlowerTest(c *gin.Context) {
	var reqData request.SendPushPushReceiveFlowerTestReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := fcmService.SendPushPushReceiveFlowerTest(c, &reqData); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *FcmApi) PushCommonMsgSingle(c *gin.Context) {
	var reqData request.PushCommonMsgSingleReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := fcmService.PushCommonMsgSingle(c, reqData.UserId, reqData.Title, reqData.Body); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}
