package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
)

type UserAiMsgCountApi struct{}

var userAiMsgCountService = service.ServiceGroupApp.BackendServiceGroup.UserAiMsgCountService

func (m *UserAiMsgCountApi) Create(c *gin.Context) {
	var reqData model.UserAiMsgCount
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := userAiMsgCountService.Create(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *UserAiMsgCountApi) DeleteById(c *gin.Context) {
	var reqData model.UserAiMsgCount
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := userAiMsgCountService.DeleteById(c, reqData.Id); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *UserAiMsgCountApi) DeleteByIds(c *gin.Context) {
	var reqData request.IdsReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := userAiMsgCountService.DeleteByIds(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *UserAiMsgCountApi) Update(c *gin.Context) {
	var reqData model.UserAiMsgCount
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := userAiMsgCountService.Update(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *UserAiMsgCountApi) GetById(c *gin.Context) {
	var reqData model.UserAiMsgCount
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := userAiMsgCountService.GetById(c, reqData.Id); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *UserAiMsgCountApi) GetAll(c *gin.Context) {
	if res, err := userAiMsgCountService.GetAll(c); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *UserAiMsgCountApi) GetList(c *gin.Context) {
	var reqData req.UserAiMsgCountSearch
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := userAiMsgCountService.GetList(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(response.PageResult{
			List:     list,
			Total:    total,
			Page:     reqData.Page,
			PageSize: reqData.PageSize,
		}, c)
	}
}
