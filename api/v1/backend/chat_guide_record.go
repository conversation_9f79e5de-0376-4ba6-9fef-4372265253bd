package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ChatGuideRecordApi struct{}

var chatGuideRecordService = service.ServiceGroupApp.BackendServiceGroup.ChatGuideRecordService

func (m *ChatGuideRecordApi) Create(c *gin.Context) {
	var reqData model.ChatGuideRecord
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := chatGuideRecordService.Create(&reqData); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *ChatGuideRecordApi) DeleteById(c *gin.Context) {
	var reqData model.ChatGuideRecord
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := chatGuideRecordService.DeleteById(reqData.Id); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *ChatGuideRecordApi) DeleteByIds(c *gin.Context) {
	var reqData request.IdsReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := chatGuideRecordService.DeleteByIds(reqData); err != nil {
		global.LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *ChatGuideRecordApi) Update(c *gin.Context) {
	var reqData model.ChatGuideRecord
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := chatGuideRecordService.Update(&reqData); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *ChatGuideRecordApi) GetById(c *gin.Context) {
	var reqData model.ChatGuideRecord
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := chatGuideRecordService.GetById(reqData.Id); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *ChatGuideRecordApi) GetAll(c *gin.Context) {
	if res, err := chatGuideRecordService.GetAll(); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *ChatGuideRecordApi) GetList(c *gin.Context) {
	var reqData req.ChatGuideRecordSearch
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := chatGuideRecordService.GetList(reqData); err != nil {
		global.LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     reqData.Page,
			PageSize: reqData.PageSize,
		}, "成功", c)
	}
}
