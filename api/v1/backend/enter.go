package backend

type ApiGroup struct {
	MsgTimeRecordApi
	AmazonS3Api
	AppApi
	AiRoleApi
	AiRoleImgInfoApi
	AiRoleMediaApi
	AdjustLogApi
	DigitalUserApi
	DigitalUserProfileApi
	FeedbackRecordApi
	FlowerConsumeRecordApi
	OrdersApi
	ProductApi
	ReSubOrdersApi
	UsersIntimateApi
	ChatGuideRecordApi
	UserAiRoleFuncApi
	MsgJobApi
	PayMethodsApi
	ConfigPlanApi
	AiSendMediaApi
	AiCommonReplyApi
	FcmApi
	ProductInfoApi
	TouchOptionApi
	SyncDataApi
	UndressRecordApi
	ImgConfigApi
	DailyReportApi
	WithdrawHistoryApi
	KocUserInfoApi
	SysUserKocApi
	AiScenesApi
	UserUseScenesApi
	PushMsgApi
	GiftApi
	GiftAiImgApi
	DiyPhotoTaskApi
	BadgeApi
	UserBadgeApi
	AiProfileInfoApi
	AiContactInfoApi
	AppPageBannerApi
	DiyConfigApi
	ImgTemplateCategoryApi
	ImgTemplateRecordApi
	ImgGenRecordApi
	VoiceCallApi
	VideoTxtApi
	ImgToVideoApi
	LiveGiftApi
	LiveInfoApi
	LivePeopleApi
	LiveReceiveGiftApi
	VoiceInfoApi
	LiveReceiveFansApi
	PrivateSpaceApi
	PrivateSpaceUserApi
	UserAiMsgCountApi
	AiSortStatisticsApi
	AiBodyImgApi
	AiBodyImgCacheApi
	VideoCallApi
	UserFeedbackBackendApi
	VideoSimplifyBackendApi
	BtcBackendApi
	SpeedBalanceApi
}
