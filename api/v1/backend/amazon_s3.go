package backend

import (
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"github.com/gin-gonic/gin"
)

type AmazonS3Api struct{}

var amazonS3Service = service.ServiceGroupApp.BackendServiceGroup.AmazonS3Service

func (s *AmazonS3Api) CredentialsData(c *gin.Context) {
	res, err := amazonS3Service.CredentialsData()
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(res, c)
}

func (s *AmazonS3Api) UploadFile(c *gin.Context) {
	business := c.PostForm("business")
	file, err := c.FormFile("file")
	if err != nil {
		response.FailWithMessage("文件上传失败", c)
		return
	}
	if business == "" {
		business = "image"
	}
	url, key, err := amazonS3Service.UploadFile(c, file, business)
	if err != nil {
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithDetailed(gin.H{
			"url": url,
			"key": key,
		}, "上传成功", c)
	}
}
