package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/backend/request"
	cliRes "aimsg-server/model/cli/response"
	"aimsg-server/model/common/response"
	"aimsg-server/service/backend"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"strconv"
)

type ConfigPlanApi struct{}

func (m *ConfigPlanApi) GetPlanConfig(c *gin.Context) {
	var (
		appId uint
	)
	plan, planExist := c.GetQuery("plan")
	if !planExist {
		response.FailWithMessage("plan is required", c)
		return
	}
	appIdStr, appIdExist := c.Get<PERSON>uery("app_id")
	if appIdExist {
		appIdInt, appIdAtoiErr := strconv.Atoi(appIdStr)
		if appIdAtoiErr != nil {
			response.FailWithMessage(appIdAtoiErr.Error(), c)
			return
		}
		appId = uint(appIdInt)
	}
	switch plan {
	case "1":
		msgAdsPlan := backend.GetMsgAdsPlanOneConfig(c)
		response.OkWithData(msgAdsPlan, c)
		return
	case "2":
		msgAdsPlan := backend.GetMsgAdsPlanTwoConfig(c)
		response.OkWithData(msgAdsPlan, c)
		return
	case "3":
		msgPlan := backend.GetMsgSendTimeOutConfig(c)
		response.OkWithData(msgPlan, c)
		return
	case "4":
		res := backend.GetTextMaskMsgList(c)
		response.OkWithData(res, c)
		return
	case "5":
		res := backend.GetFlowerDialogTipConfig(c)
		response.OkWithData(res, c)
		return
	case "6":
		res := backend.GetReplyMsgRatioFree(c)
		response.OkWithData(res, c)
		return
	case "7":
		res := backend.GetReplyMsgRatioSub(c)
		response.OkWithData(res, c)
		return
	case "8":
		res := backend.GetSeeuRatioFree(c)
		response.OkWithData(res, c)
		return
	case "9":
		res := backend.GetSeeuRatioSub(c)
		response.OkWithData(res, c)
		return
	case "10":
		res := backend.GetVipDialogPlanConfig(c)
		response.OkWithData(res, c)
		return
	case "11":
		res := backend.GetIPWhiteList(c)
		response.OkWithData(res, c)
		return
	case "12":
		res := backend.GetVipDialogPlanTwoTip(c, appId)
		response.OkWithData(res, c)
		return
	case "13":
		res := backend.GetSubLowCountry(c)
		response.OkWithData(res, c)
		return
	case "14":
		res := backend.GetVipDialogDownConfig(c)
		response.OkWithData(res, c)
		return
	case "15":
		res := backend.GetAppOtherConfig(c)
		response.OkWithData(res, c)
		return
	case "16":
		res := backend.GetSignInNoticeCfg(c)
		response.OkWithData(res, c)
		return
	case "17":
		res := backend.GetSubNoBuyFloCfg(c)
		response.OkWithData(res, c)
		return
	// case "18":
	// 	res := backend.GetDiyPhotoCfg(c)
	// 	response.OkWithData(res, c)
	// 	return
	case "19":
		res := backend.GetAllAiFuncSwitchCfg(c)
		response.OkWithData(res, c)
		return
	case "20":
		res := backend.GetDiyPhotoConfig(c, appId)
		response.OkWithData(res, c)
		return
	case "21":
		res := backend.GetVipDialogPlanTwoAuditTip(c, appId)
		response.OkWithData(res, c)
		return
	case "22":
		res := backend.GetAppVoiceCallCfg(c, appId)
		response.OkWithData(res, c)
		return
	case "23":
		res := backend.GetMsgReqMapCfg(c)
		response.OkWithData(res, c)
		return
	case "24":
		res := backend.GetDiyPhotoLv2TacticsCfg(c, appId)
		response.OkWithData(res, c)
		return
	case "25":
		res := backend.GetFlowerShopSVipTextListCfg(c, appId)
		response.OkWithData(res, c)
		return
	case "26":
		res := backend.GetFlowerShopSVipTextListAuditCfg(c, appId)
		response.OkWithData(res, c)
		return
	case "27":
		res := backend.GetSVipDialogCfg(c, appId)
		response.OkWithData(res, c)
		return
	case "28":
		res := backend.GetSVipDialogAuditCfg(c, appId)
		response.OkWithData(res, c)
		return
	case "29":
		res := backend.GetSplashAdCfg(c, appId)
		response.OkWithData(res, c)
		return
	case "30":
		res := backend.GetToPayCenterCountryGood(c, appId)
		response.OkWithData(res, c)
		return
	case "31":
		res := backend.GetToPayCenterCountrySub(c, appId)
		response.OkWithData(res, c)
		return
	case "32":
		res := backend.GetSVipDialogTip(c, appId)
		response.OkWithData(res, c)
		return
	case "33":
		res := backend.GetSVipDialogTipAudit(c, appId)
		response.OkWithData(res, c)
		return
	case "34":
		res := backend.GetMsgNewStart()
		response.OkWithData(res, c)
		return
	case "35":
		res := backend.GetVipDialogV2Cfg(c, appId)
		response.OkWithData(res, c)
		return
	case "36":
		res := backend.GetSVipDialogV2Cfg(c, appId)
		response.OkWithData(res, c)
		return
	case "37":
		res := backend.GetIntimateLevelCfg(c, appId)
		response.OkWithData(cliRes.SetIntimateLevelCfgReq{
			List: res,
		}, c)
		return
	case "38":
		res := backend.GetIntimateUpgradeCfg(c)
		response.OkWithData(res, c)
		return
	case "39":
		res := backend.GetIntimateDialogCfg(c, appId)
		response.OkWithData(res, c)
		return
	case "40":
		res := backend.GetIntimateUpgradeDialogCfg(c, appId)
		response.OkWithData(res, c)
		return
	case "41":
		res := backend.GetIntimateUpgradeDialogAuditCfg(c, appId)
		response.OkWithData(res, c)
		return
	case "42":
		res := backend.GetDiyPhotoCountRangeCfg(c, appId)
		response.OkWithData(res, c)
		return
	case "43":
		res := backend.GetAiSendUserGiftImgCfg(c, appId)
		response.OkWithData(res, c)
		return
	case "44":
		res := backend.GetAiSendUserGiftTxtCfg(c, appId)
		response.OkWithData(res, c)
		return
	case "45":
		res := backend.GetMsgModelStrageCfg(c)
		response.OkWithData(res, c)
		return
	case "46":
		res := backend.GetReSubRewardCoinCfg(c, appId)
		response.OkWithData(res, c)
		return
	case "47":
		res := backend.GetChatVipDialogTip(c, appId)
		response.OkWithData(res, c)
		return
	case "48":
		res := backend.GetChatVipDialogTipAudit(c, appId)
		response.OkWithData(res, c)
		return
	case "49":
		res := backend.GetChatVipDialogV2Cfg(c, appId)
		response.OkWithData(res, c)
		return
	case "50":
		res := backend.GetContentVipDialogTip(c, appId)
		response.OkWithData(res, c)
		return
	case "51":
		res := backend.GetContentVipDialogTipAudit(c, appId)
		response.OkWithData(res, c)
		return
	case "52":
		res := backend.GetContentVipDialogV2Cfg(c, appId)
		response.OkWithData(res, c)
		return
	case "53":
		res := backend.GetGenAiPreCfg(c, appId)
		response.OkWithData(res, c)
		return
	case "54":
		res := backend.GetVipDialogV390Cfg(c, appId)
		response.OkWithData(res, c)
		return
	case "55":
		res := backend.GetSVipDialogV390Cfg(c, appId)
		response.OkWithData(res, c)
		return
	case "56":
		res := backend.GetIntimateUpgradeMsgCfg(c, appId)
		response.OkWithData(res, c)
		return
	case "57":
		res := backend.GetRequireImgBoxCfg(c, appId)
		response.OkWithData(res, c)
		return
	case "58":
		res := backend.GetRequireImgSubMsgCfg(c, appId)
		response.OkWithData(res, c)
		return
	case "59":
		// 新增礼物引导 孙宏伟需求
		res := backend.GetGuidedGiftGivingCfg(c, appId)
		response.OkWithData(res, c)
		return
	case "60":
		// 12-31 增加快捷回复消息下发付费
		res := backend.GetReplyChargeCfg(c, appId)
		response.OkWithData(res, c)
		return
	default:
		response.FailWithMessage("plan is invalid", c)
	}
}

func (m *ConfigPlanApi) UpdatePlanConfig(c *gin.Context) {
	var (
		err             error
		dataBytes       []byte
		reqData         request.MsgAdsPlanUpdateReq
		planConfigOne   model.MsgPlanOneConfig
		planConfigTwo   model.MsgPlanTwoConfig
		msgTimeConfig   model.MsgSendTimeOutConfig
		tipConfig       model.FlowerDialogTipConfig
		ratioConfig     model.ReplyMsgRatio
		vDPlanConfig    model.VipDialogPlanConfig
		dPlanTxtCfg     cliRes.VipDialogTip
		diyLv2Cfg       cliRes.DiyPhotoLv2TacticsCfg
		fShopSvipCfg    cliRes.FlowerShopSVipTextListCfg
		sVipCfg         cliRes.SVipDialogCfg
		vipDownCfg      model.VipDialogDownConfig
		appOtherCfg     model.AppOtherConfig
		signInNoticeCfg request.SignInNoticeCfg
		subNoBuyFloCfg  request.SubNoBuyFloCfg
		diyPhotoCfg     request.DiyPhotoConfig
		voiceCallCfg    cliRes.AppVoiceCallCfg
		msgReqMapCfg    cliRes.MsgReqMapCfg
		sAdCfg          cliRes.SplashAdCfg
		countrysCfg     cliRes.CountryListCfg
		funcCfg         map[uint]request.AiFuncSwitchCfg
		msgArr          []string
		ipArr           []string
		subLowCountry   []string
	)
	if err = c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err = global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if dataBytes, err = json.Marshal(reqData.Data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	switch reqData.Plan {
	case "1":
		if err = json.Unmarshal(dataBytes, &planConfigOne); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetMsgAdsPlanOneConfig(c, planConfigOne); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "2":
		if err = json.Unmarshal(dataBytes, &planConfigTwo); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetMsgAdsPlanTwoConfig(c, planConfigTwo); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "3":
		if err = json.Unmarshal(dataBytes, &msgTimeConfig); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetMsgSendTimeOutConfig(c, msgTimeConfig); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "4":
		if err = json.Unmarshal(dataBytes, &msgArr); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetTextMaskMsgList(c, msgArr); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "5":
		if err = json.Unmarshal(dataBytes, &tipConfig); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetFlowerDialogTipConfig(c, tipConfig); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "6":
		if err = json.Unmarshal(dataBytes, &ratioConfig); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetReplyMsgRatioFree(c, ratioConfig); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "7":
		if err = json.Unmarshal(dataBytes, &ratioConfig); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetReplyMsgRatioSub(c, ratioConfig); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "8":
		if err = json.Unmarshal(dataBytes, &ratioConfig); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetSeeuRatioFree(c, ratioConfig); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "9":
		if err = json.Unmarshal(dataBytes, &ratioConfig); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetSeeuRatioSub(c, ratioConfig); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "10":
		if err = json.Unmarshal(dataBytes, &vDPlanConfig); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetVipDialogPlanConfig(c, vDPlanConfig); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "11":
		if err = json.Unmarshal(dataBytes, &ipArr); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetIPWhiteList(c, ipArr); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "12":
		if err = json.Unmarshal(dataBytes, &dPlanTxtCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetVipDialogPlanTwoTip(c, reqData.AppId, dPlanTxtCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "13":
		if err = json.Unmarshal(dataBytes, &subLowCountry); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetSubLowCountry(c, subLowCountry); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "14":
		if err = json.Unmarshal(dataBytes, &vipDownCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetVipDialogDownConfig(c, vipDownCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "15":
		if err = json.Unmarshal(dataBytes, &appOtherCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetAppOtherConfig(c, appOtherCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		// 同步AppOtherConfig
		appService.SyncAppOtherConfig()
	case "16":
		if err = json.Unmarshal(dataBytes, &signInNoticeCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetSignInNoticeCfg(c, signInNoticeCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "17":
		if err = json.Unmarshal(dataBytes, &subNoBuyFloCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetSubNoBuyFloCfg(c, subNoBuyFloCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	// case "18":
	// 	if err = json.Unmarshal(dataBytes, &diyPhotoPriceCfg); err != nil {
	// 		response.FailWithMessage(err.Error(), c)
	// 		return
	// 	}
	// 	if err = backend.SetDiyPhotoCfg(c, diyPhotoPriceCfg); err != nil {
	// 		response.FailWithMessage(err.Error(), c)
	// 		return
	// 	}
	case "19":
		if err = json.Unmarshal(dataBytes, &funcCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetAllAiFuncSwitchCfg(c, funcCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "20":
		if err = json.Unmarshal(dataBytes, &diyPhotoCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetDiyPhotoConfig(c, reqData.AppId, diyPhotoCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "21":
		if err = json.Unmarshal(dataBytes, &dPlanTxtCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetVipDialogPlanTwoAuditTip(c, reqData.AppId, dPlanTxtCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "22":
		if err = json.Unmarshal(dataBytes, &voiceCallCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetAppVoiceCallCfg(c, reqData.AppId, voiceCallCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "23":
		if err = json.Unmarshal(dataBytes, &msgReqMapCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetMsgReqMapConfig(c, msgReqMapCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "24":
		if err = json.Unmarshal(dataBytes, &diyLv2Cfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetDiyPhotoLv2TacticsCfg(c, reqData.AppId, diyLv2Cfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "25":
		if err = json.Unmarshal(dataBytes, &fShopSvipCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetFlowerShopSVipTextListCfg(c, reqData.AppId, fShopSvipCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "26":
		if err = json.Unmarshal(dataBytes, &fShopSvipCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetFlowerShopSVipTextListAuditCfg(c, reqData.AppId, fShopSvipCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "27":
		if err = json.Unmarshal(dataBytes, &sVipCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetSVipDialogCfg(c, reqData.AppId, sVipCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "28":
		if err = json.Unmarshal(dataBytes, &sVipCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetSVipDialogAuditCfg(c, reqData.AppId, sVipCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "29":
		if err = json.Unmarshal(dataBytes, &sAdCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetSplashAdCfg(c, reqData.AppId, sAdCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "30":
		if err = json.Unmarshal(dataBytes, &countrysCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetToPayCenterCountryGood(c, reqData.AppId, countrysCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "31":
		if err = json.Unmarshal(dataBytes, &countrysCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetToPayCenterCountrySub(c, reqData.AppId, countrysCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "32":
		var svipDialogCfg cliRes.SVipDialogTip
		if err = json.Unmarshal(dataBytes, &svipDialogCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetSVipDialogTip(c, reqData.AppId, svipDialogCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "33":
		var svipDialogCfg cliRes.SVipDialogTip
		if err = json.Unmarshal(dataBytes, &svipDialogCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetSVipDialogTipAudit(c, reqData.AppId, svipDialogCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "34":
		var msgNewStartCfg cliRes.MsgNewStartCfg
		if err = json.Unmarshal(dataBytes, &msgNewStartCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetMsgNewStart(msgNewStartCfg.StartRes); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "35":
		var svipDialogCfg cliRes.VipDialogV2Cfg
		if err = json.Unmarshal(dataBytes, &svipDialogCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetVipDialogV2Cfg(c, reqData.AppId, svipDialogCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "36":
		var svipDialogCfg cliRes.SVipDialogV2Cfg
		if err = json.Unmarshal(dataBytes, &svipDialogCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetSVipDialogV2Cfg(c, reqData.AppId, svipDialogCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "37":
		var lCfg cliRes.SetIntimateLevelCfgReq
		if err = json.Unmarshal(dataBytes, &lCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetIntimateLevelCfg(c, reqData.AppId, lCfg.List); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "38":
		var lCfg map[model.IntimateSubType]cliRes.IntimateUpgradeCfg
		if err = json.Unmarshal(dataBytes, &lCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetIntimateUpgradeCfg(c, lCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "39":
		var lCfg cliRes.IntimateDialogCfg
		if err = json.Unmarshal(dataBytes, &lCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetIntimateDialogCfg(c, reqData.AppId, lCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "40":
		var lCfg map[uint]cliRes.IntimateUpgradeDialogCfg
		if err = json.Unmarshal(dataBytes, &lCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetIntimateUpgradeDialogCfg(c, reqData.AppId, lCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "41":
		var lCfg map[uint]cliRes.IntimateUpgradeDialogCfg
		if err = json.Unmarshal(dataBytes, &lCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetIntimateUpgradeDialogAuditCfg(c, reqData.AppId, lCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "42":
		var sCfg cliRes.DiyPhotoCountRangeCfg
		if err = json.Unmarshal(dataBytes, &sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetDiyPhotoCountRangeCfg(c, reqData.AppId, sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "43":
		var sCfg cliRes.AiSendUserGiftImgCfg
		if err = json.Unmarshal(dataBytes, &sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetAiSendUserGiftImgCfg(c, reqData.AppId, sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "44":
		var sCfg cliRes.AiSendUserGiftTxtCfg
		if err = json.Unmarshal(dataBytes, &sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetAiSendUserGiftTxtCfg(c, reqData.AppId, sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "45":
		var sCfg cliRes.MsgModelStrageCfg
		if err = json.Unmarshal(dataBytes, &sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetMsgModelStrageCfg(c, sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "46":
		var sCfg cliRes.ReSubRewardCoinCfg
		if err = json.Unmarshal(dataBytes, &sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetReSubRewardCoinCfg(c, reqData.AppId, sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "47":
		var sCfg cliRes.ChatVipDialogTip
		if err = json.Unmarshal(dataBytes, &sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetChatVipDialogTip(c, reqData.AppId, sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "48":
		var sCfg cliRes.ChatVipDialogTip
		if err = json.Unmarshal(dataBytes, &sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetChatVipDialogTipAudit(c, reqData.AppId, sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "49":
		var sCfg cliRes.ChatVipDialogV2Cfg
		if err = json.Unmarshal(dataBytes, &sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetChatVipDialogV2Cfg(c, reqData.AppId, sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "50":
		var sCfg cliRes.ContentVipDialogTip
		if err = json.Unmarshal(dataBytes, &sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetContentVipDialogTip(c, reqData.AppId, sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "51":
		var sCfg cliRes.ContentVipDialogTip
		if err = json.Unmarshal(dataBytes, &sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetContentVipDialogTipAudit(c, reqData.AppId, sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "52":
		var sCfg cliRes.ContentVipDialogV2Cfg
		if err = json.Unmarshal(dataBytes, &sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetContentVipDialogV2Cfg(c, reqData.AppId, sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "53":
		var sCfg cliRes.GenAiPreCfg
		if err = json.Unmarshal(dataBytes, &sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetGenAiPreCfg(c, reqData.AppId, sCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "54":
		var svipDialogCfg cliRes.VipDialogV2Cfg
		if err = json.Unmarshal(dataBytes, &svipDialogCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetVipDialogV390Cfg(c, reqData.AppId, svipDialogCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "55":
		var svipDialogCfg cliRes.SVipDialogV2Cfg
		if err = json.Unmarshal(dataBytes, &svipDialogCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetSVipDialogV390Cfg(c, reqData.AppId, svipDialogCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "58":
		var itemCfg cliRes.RequireImgSubMsgCfg
		if err = json.Unmarshal(dataBytes, &itemCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetRequireImgSubMsgCfg(c, reqData.AppId, itemCfg); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "56":
		var cfgItem cliRes.IntimateUpgradeMsgCfg
		if err = json.Unmarshal(dataBytes, &cfgItem); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetIntimateUpgradeMsgCfg(c, reqData.AppId, cfgItem); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "57":
		var cfgItem cliRes.RequireImgBoxCfg
		if err = json.Unmarshal(dataBytes, &cfgItem); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetRequireImgBoxCfg(c, reqData.AppId, cfgItem); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "59":
		var cfgItem cliRes.GuidedGiftGivingCfgReq
		if err = json.Unmarshal(dataBytes, &cfgItem); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetGuidedGiftGivingCfg(c, reqData.AppId, cfgItem); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	case "60":
		// 12-31 增加快捷回复消息下发付费
		var cfgItem cliRes.ReplyChargeCfgReq
		if err = json.Unmarshal(dataBytes, &cfgItem); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err = backend.SetReplyChargeCfg(c, reqData.AppId, cfgItem.Normal, cfgItem.Nsfw); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	default:
		response.FailWithMessage("plan is invalid", c)
		return
	}
	response.Ok(c)
}

func (m *ConfigPlanApi) MapList(c *gin.Context) {
	res := gin.H{
		"ConsumeTypeMap":       model.ConsumeTypeMap,
		"GuideTypeMap":         model.GuideTypeMap,
		"MediaTypeMap":         model.MediaTypeMap,
		"AddFlowerMap":         model.AddFlowerMap,
		"AppTypeMap":           model.AppTypeMap,
		"ExamineTypeMap":       model.ExamineTypeMap,
		"AiRoleTypeMap":        model.AiRoleTypeMap,
		"WithdrawPlatformMap":  model.WithdrawPlatformMap,
		"ReplyTypeMap":         model.ReplyTypeMap,
		"ImgConfigTypeMap":     model.ImgConfigTypeMap,
		"SceneTypeMap":         model.SceneTypeMap,
		"GenderTypeMap":        model.GenderTypeMap,
		"GoodTypeMap":          model.GoodTypeMap,
		"ProductTypeMap":       model.ProductTypeDescriptions,
		"InfoTypeMap":          model.ProductInfoTypeDescriptions,
		"IntimateSubTypeMap":   model.IntimateSubTypeMap,
		"IntimateTypeMap":      model.IntimateTypeMap,
		"OpenTypeMap":          model.OpenTypeMap,
		"OrderStatusMap":       model.OrderStatusMap,
		"SceneMap":             model.SceneMap,
		"SubTypeMap":           model.SubTypeMap,
		"SubStatusMap":         model.SubStatusMap,
		"SwitchStatusMap":      model.SwitchStatusMap,
		"UnlockStateMap":       model.UnlockStateMap,
		"UserStatusMap":        model.UserStatusMap,
		"TouchTypeMap":         model.TouchTypeMap,
		"DiyPhotoTaskStateMap": model.DiyPhotoTaskStateMap,
		"TriggerTypeMap":       model.TriggerTypeMap,
		"PeriodTypeMap":        model.PeriodTypeMap,
		"ContactTypeMap":       model.ContactTypeMap,
		"TurnPageMap":          model.TurnPageMap,
		"BannerFuncTypeMap":    model.BannerFuncTypeMap,
		"GenStatusMap":         model.GenStatusMap,
		"DiyTypeMap":           model.DiyTypeMap,
		"AgeRangeMap":          model.AgeRangeMap,
		"PositionMap":          model.PositionMap,
		"DiyConfigTypeMap":     model.DiyConfigTypeMap,
		"CallStatusMap":        model.CallStatusMap,
		"CallTypeMap":          model.CallTypeMap,
	}
	response.OkWithData(res, c)
}
