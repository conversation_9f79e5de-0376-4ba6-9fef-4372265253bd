package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AppPageBannerApi struct{}

var appPageBannerService = service.ServiceGroupApp.BackendServiceGroup.AppPageBannerService

func (m *AppPageBannerApi) Create(c *gin.Context) {
	var reqData model.AppPageBanner
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := appPageBannerService.Create(&reqData); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.<PERSON>rror(), c)
	} else {
		response.Ok(c)
	}
}

func (m *AppPageBannerApi) DeleteById(c *gin.Context) {
	var reqData model.AppPageBanner
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := appPageBannerService.DeleteById(reqData.Id); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *AppPageBannerApi) DeleteByIds(c *gin.Context) {
	var reqData request.IdsReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := appPageBannerService.DeleteByIds(reqData); err != nil {
		global.LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *AppPageBannerApi) Update(c *gin.Context) {
	var reqData model.AppPageBanner
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := appPageBannerService.Update(&reqData); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *AppPageBannerApi) GetById(c *gin.Context) {
	var reqData model.AppPageBanner
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := appPageBannerService.GetById(reqData.Id); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *AppPageBannerApi) GetAll(c *gin.Context) {
	if res, err := appPageBannerService.GetAll(); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *AppPageBannerApi) GetList(c *gin.Context) {
	var reqData req.AppPageBannerSearch
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := appPageBannerService.GetList(reqData); err != nil {
		global.LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     reqData.Page,
			PageSize: reqData.PageSize,
		}, "成功", c)
	}
}
