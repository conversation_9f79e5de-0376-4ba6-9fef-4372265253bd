package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DigitalUserApi struct{}

var digitalUserService = service.ServiceGroupApp.BackendServiceGroup.DigitalUserService

func (m *DigitalUserApi) Create(c *gin.Context) {
	var reqData model.DigitalUser
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := digitalUserService.Create(c, &reqData); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *DigitalUserApi) DeleteById(c *gin.Context) {
	var reqData model.DigitalUser
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := digitalUserService.DeleteById(c, reqData.Id); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *DigitalUserApi) DeleteByIds(c *gin.Context) {
	var reqData request.IdsReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := digitalUserService.DeleteByIds(c, reqData); err != nil {
		global.LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *DigitalUserApi) Update(c *gin.Context) {
	var reqData model.DigitalUser
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := digitalUserService.Update(c, &reqData); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *DigitalUserApi) GetById(c *gin.Context) {
	var reqData model.DigitalUser
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := digitalUserService.GetById(c, reqData.Id); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *DigitalUserApi) GetList(c *gin.Context) {
	var reqData req.DigitalUserSearch
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := digitalUserService.GetList(reqData); err != nil {
		global.LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithData(list, c)
	}
}

func (m *DigitalUserApi) GetTotal(c *gin.Context) {
	var reqData req.DigitalUserSearch
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if total, err := digitalUserService.GetTotal(reqData); err != nil {
		global.LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithData(total, c)
	}
}

func (m *DigitalUserApi) SetContinuousActiveDay(c *gin.Context) {
	var reqData req.SetContinuousActiveDayReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := digitalUserService.SetContinuousActiveDay(c, reqData); err != nil {
		global.LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}
