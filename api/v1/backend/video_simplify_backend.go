package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"github.com/gin-gonic/gin"
)

type VideoSimplifyBackendApi struct{}

var videoSimplifyService = service.ServiceGroupApp.BackendServiceGroup.VideoSimplifyImgVideoService

func (m *VideoSimplifyBackendApi) GetVideoSimplifyList(c *gin.Context) {
	var reqData request.VideoBackendListReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, total, err := videoSimplifyService.GetVideoSimplifyList(reqData.Page, reqData.PageSize, reqData.KlingaiType, reqData.Id); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(response.PageResult{
			List:     res,
			Total:    total,
			Page:     reqData.Page,
			PageSize: reqData.PageSize,
		}, c)
	}

}

func (m *VideoSimplifyBackendApi) AddVideoSimplify(c *gin.Context) {
	var reqData request.AddTemplateReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := videoSimplifyService.AddVideoSimplify(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.Ok(c)
	}

}

func (m *VideoSimplifyBackendApi) UpdateVideoSimplify(c *gin.Context) {
	var reqData request.AddTemplateReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := videoSimplifyService.AddVideoSimplify(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.Ok(c)
	}

}

func (m *VideoSimplifyBackendApi) DeleteVideoSimplify(c *gin.Context) {
	var reqData request.DeleteVideoSimplifyReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := videoSimplifyService.DeleteVideoSimplify(reqData.Id, reqData.Title); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.Ok(c)
	}

}

func (m *VideoSimplifyBackendApi) SetBanner(c *gin.Context) {
	var reqData request.CommonVideoSimplifyReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := videoSimplifyService.SetBanner(reqData.Id); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.Ok(c)
	}

}

func (m *VideoSimplifyBackendApi) DeleteBanner(c *gin.Context) {
	var reqData request.CommonVideoSimplifyReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := videoSimplifyService.DeleteBanner(reqData.Id); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.Ok(c)
	}

}

func (m *VideoSimplifyBackendApi) GetBannerList(c *gin.Context) {
	var reqData request.VideoBackendListReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, total, err := videoSimplifyService.GetBannerList(reqData.Page, reqData.PageSize); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(response.PageResult{
			List:     res,
			Total:    total,
			Page:     reqData.Page,
			PageSize: reqData.PageSize,
		}, c)
	}

}

func (m *VideoSimplifyBackendApi) GetVideoSimplifyInfo(c *gin.Context) {
	var reqData request.CommonVideoSimplifyReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := videoSimplifyService.GetVideoSimplifyInfo(reqData.Id); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}

}
