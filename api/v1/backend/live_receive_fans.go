package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
)

type LiveReceiveFansApi struct{}

var liveReceiveFansService = service.ServiceGroupApp.BackendServiceGroup.LiveReceiveFansService

func (m *LiveReceiveFansApi) Create(c *gin.Context) {
	var reqData model.LiveReceiveFans
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := liveReceiveFansService.Create(c, &reqData); err != nil {
		response.FailWithMessage(err.<PERSON>rror(), c)
	} else {
		response.Ok(c)
	}
}

func (m *LiveReceiveFansApi) DeleteById(c *gin.Context) {
	var reqData model.LiveReceiveFans
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := liveReceiveFansService.DeleteById(c, reqData.Id); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *LiveReceiveFansApi) DeleteByIds(c *gin.Context) {
	var reqData request.IdsReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := liveReceiveFansService.DeleteByIds(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *LiveReceiveFansApi) Update(c *gin.Context) {
	var reqData model.LiveReceiveFans
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := liveReceiveFansService.Update(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *LiveReceiveFansApi) GetById(c *gin.Context) {
	var reqData model.LiveReceiveFans
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := liveReceiveFansService.GetById(c, reqData.Id); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *LiveReceiveFansApi) GetAll(c *gin.Context) {
	if res, err := liveReceiveFansService.GetAll(c); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *LiveReceiveFansApi) GetList(c *gin.Context) {
	var reqData req.LiveReceiveFansSearch
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := liveReceiveFansService.GetList(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(response.PageResult{
			List:     list,
			Total:    total,
			Page:     reqData.Page,
			PageSize: reqData.PageSize,
		}, c)
	}
}
