package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/datatypes"
	"strings"
)

type AiRoleApi struct{}

var aiRoleService = service.ServiceGroupApp.BackendServiceGroup.AiRoleService

func (m *AiRoleApi) Create(c *gin.Context) {
	var reqData model.AiRole
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := aiRoleService.Create(c, &reqData); err != nil {
		response.FailWithMessage("失败"+err.<PERSON>rror(), c)
	} else {
		response.Ok(c)
	}
}

func (m *AiRoleApi) DeleteById(c *gin.Context) {
	var reqData model.AiRole
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := aiRoleService.DeleteById(reqData.Id); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *AiRoleApi) DeleteByIds(c *gin.Context) {
	var reqData request.IdsReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := aiRoleService.DeleteByIds(reqData); err != nil {
		global.LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *AiRoleApi) Update(c *gin.Context) {
	var reqData model.AiRole
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	// 2025-01-06 孙宏伟需求 ai_label
	if len(reqData.RoleLabels) > 0 {
		var test datatypes.JSONSlice[string]
		for _, v := range reqData.RoleLabels {

			// 检查是否包含逗号
			if !strings.Contains(v, ",") {
				test = append(test, v)
				continue
			}
			// 分割字符串，提取 x 和值
			parts := strings.SplitN(v, ",", 2)
			if len(parts) < 2 {
				test = append(test, v)
				continue // 确保有足够的分割部分
			}
			x := strings.TrimSpace(parts[0])     // 去除前后空格
			value := strings.TrimSpace(parts[1]) // 去除前后空格
			test = append(test, x)
			global.REDIS.Set(c, fmt.Sprintf(global.AI_ROLE_LABEL_KEY, x), value, 0)
		}
		reqData.RoleLabels = test
	}

	if err := aiRoleService.Update(c, &reqData); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *AiRoleApi) GetById(c *gin.Context) {
	var reqData model.AiRole
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := aiRoleService.GetById(c, reqData.Id); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *AiRoleApi) GetAll(c *gin.Context) {
	if res, err := aiRoleService.GetAll(); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *AiRoleApi) GetList(c *gin.Context) {
	var reqData req.AiRoleSearch
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	//  2025-01-06 孙宏伟需求 list ai
	if list, total, err := aiRoleService.GetList(reqData, false); err != nil {
		global.LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     reqData.Page,
			PageSize: reqData.PageSize,
		}, "成功", c)
	}
}

func (m *AiRoleApi) ChangeSort(c *gin.Context) {
	aiRoleService.ChangePublicRoleSort()
	response.Ok(c)
}
