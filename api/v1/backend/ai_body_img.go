package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
)

type AiBodyImgApi struct{}

var aiBodyImgService = service.ServiceGroupApp.BackendServiceGroup.AiBodyImgService

func (m *AiBodyImgApi) Create(c *gin.Context) {
	var reqData model.AiBodyImg
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := aiBodyImgService.Create(c, &reqData); err != nil {
		response.FailWithMessage(err.<PERSON>rror(), c)
	} else {
		response.Ok(c)
	}
}

func (m *AiBodyImgApi) DeleteById(c *gin.Context) {
	var reqData model.AiBodyImg
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := aiBodyImgService.DeleteById(c, reqData.Id); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *AiBodyImgApi) DeleteByIds(c *gin.Context) {
	var reqData request.IdsReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := aiBodyImgService.DeleteByIds(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *AiBodyImgApi) Update(c *gin.Context) {
	var reqData model.AiBodyImg
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := aiBodyImgService.Update(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *AiBodyImgApi) GetById(c *gin.Context) {
	var reqData model.AiBodyImg
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := aiBodyImgService.GetById(c, reqData.Id); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *AiBodyImgApi) GetAll(c *gin.Context) {
	if res, err := aiBodyImgService.GetAll(); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *AiBodyImgApi) GetList(c *gin.Context) {
	var reqData req.AiBodyImgSearch
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := aiBodyImgService.GetList(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(response.PageResult{
			List:     list,
			Total:    total,
			Page:     reqData.Page,
			PageSize: reqData.PageSize,
		}, c)
	}
}
