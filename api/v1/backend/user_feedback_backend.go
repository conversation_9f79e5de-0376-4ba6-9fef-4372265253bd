package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"github.com/gin-gonic/gin"
)

type UserFeedbackBackendApi struct{}

var userFeedbackService = service.ServiceGroupApp.BackendServiceGroup.UserFeedbackService

func (m *UserFeedbackBackendApi) GetUserFeedbackList(c *gin.Context) {
	var reqData request.UserFeedbackListReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, total, err := userFeedbackService.GetUserFeedbackList(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(response.PageResult{
			List:     res,
			Total:    total,
			Page:     reqData.Page,
			PageSize: reqData.PageSize,
		}, c)
	}

}

func (m *UserFeedbackBackendApi) Reply(c *gin.Context) {
	var reqData request.UserFeedbackReply
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := userFeedbackService.Reply(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.Ok(c)
	}
}

func (m *UserFeedbackBackendApi) DeleteSession(c *gin.Context) {
	var reqData request.UserFeedbackDeleteSessionReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := userFeedbackService.DeleteSession(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.Ok(c)
	}
}
