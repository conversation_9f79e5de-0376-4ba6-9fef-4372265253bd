package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ImgTemplateRecordApi struct{}

var imgTemplateRecordService = service.ServiceGroupApp.BackendServiceGroup.ImgTemplateRecordService

func (m *ImgTemplateRecordApi) Create(c *gin.Context) {
	var reqData model.ImgTemplateRecord
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.<PERSON>rror(), c)
		return
	}
	if err := imgTemplateRecordService.Create(&reqData); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *ImgTemplateRecordApi) DeleteById(c *gin.Context) {
	var reqData model.ImgTemplateRecord
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := imgTemplateRecordService.DeleteById(reqData.Id); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *ImgTemplateRecordApi) DeleteByIds(c *gin.Context) {
	var reqData request.IdsReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := imgTemplateRecordService.DeleteByIds(reqData); err != nil {
		global.LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *ImgTemplateRecordApi) Update(c *gin.Context) {
	var reqData model.ImgTemplateRecord
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := imgTemplateRecordService.Update(&reqData); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *ImgTemplateRecordApi) GetById(c *gin.Context) {
	var reqData model.ImgTemplateRecord
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := imgTemplateRecordService.GetById(reqData.Id); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *ImgTemplateRecordApi) GetAll(c *gin.Context) {
	if res, err := imgTemplateRecordService.GetAll(); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *ImgTemplateRecordApi) GetList(c *gin.Context) {
	var reqData req.ImgTemplateRecordSearch
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := imgTemplateRecordService.GetList(reqData); err != nil {
		global.LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     reqData.Page,
			PageSize: reqData.PageSize,
		}, "成功", c)
	}
}
