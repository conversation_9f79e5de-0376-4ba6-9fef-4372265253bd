package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/common/response"
	"aimsg-server/service/backend"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type SpeedBalanceApi struct{}

// GetBalanceStatus 获取 Speed API 余额状态
// @Tags SpeedBalance
// @Summary 获取 Speed API 余额状态
// @Description 获取 Speed API 当前余额状态和配置信息
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=map[string]interface{}} "成功"
// @Router /speedBalance/status [get]
func (s *SpeedBalanceApi) GetBalanceStatus(c *gin.Context) {
	speedMonitor := &backend.SpeedBalanceMonitorService{}
	
	status, err := speedMonitor.GetBalanceStatus()
	if err != nil {
		global.LOG.Error("获取 Speed API 余额状态失败", zap.Error(err))
		response.FailWithMessage("获取余额状态失败: "+err.Error(), c)
		return
	}
	
	response.OkWithData(status, c)
}

// CheckBalanceNow 立即检查余额并发送通知（如果需要）
// @Tags SpeedBalance
// @Summary 立即检查余额
// @Description 立即检查 Speed API 余额，如果低于阈值则发送通知
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=map[string]interface{}} "成功"
// @Router /speedBalance/check [post]
func (s *SpeedBalanceApi) CheckBalanceNow(c *gin.Context) {
	speedMonitor := &backend.SpeedBalanceMonitorService{}
	
	// 先获取当前状态
	status, err := speedMonitor.GetBalanceStatus()
	if err != nil {
		global.LOG.Error("获取 Speed API 余额状态失败", zap.Error(err))
		response.FailWithMessage("获取余额状态失败: "+err.Error(), c)
		return
	}
	
	// 执行检查
	err = speedMonitor.CheckBalance()
	if err != nil {
		global.LOG.Error("检查 Speed API 余额失败", zap.Error(err))
		response.FailWithMessage("检查余额失败: "+err.Error(), c)
		return
	}
	
	// 添加检查结果信息
	result := map[string]interface{}{
		"status":  status,
		"message": "余额检查完成",
	}
	
	if status["is_low"].(bool) {
		result["message"] = "余额不足，已发送通知"
	}
	
	response.OkWithData(result, c)
}
