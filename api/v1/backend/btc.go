package backend

import (
	reqBtc "aimsg-server/model/backend/request/btc"
	"aimsg-server/model/common/response"
	"aimsg-server/service"

	"github.com/gin-gonic/gin"
)

type BtcBackendApi struct{}

var BtcService = service.ServiceGroupApp.BackendServiceGroup.BtcService

func (m *BtcBackendApi) GetBtcWithdrawalRecords(c *gin.Context) {
	var reqData reqBtc.GetBtcWithdrawalRecordsReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := BtcService.GetBtcWithdrawalRecords(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}

}

func (m *BtcBackendApi) UpdateBtcWithdrawalStatus(c *gin.Context) {
	var reqData reqBtc.UpdateBtcWithdrawalStatus
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := BtcService.UpdateBtcWithdrawalStatus(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(nil, c)
	}

}

func (m *BtcBackendApi) GetBtcTrades(c *gin.Context) {
	var reqData reqBtc.GetBtcTradesRequest
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if reqData.Page <= 0 {
		reqData.Page = 1
	}
	if reqData.PageSize <= 0 {
		reqData.PageSize = 10
	}

	if res, err := BtcService.GetBtcTrades(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}
