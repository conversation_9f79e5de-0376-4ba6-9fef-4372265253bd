package cli

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"errors"
	"github.com/gin-gonic/gin"
)

type CliAiRoleApi struct{}

var aiRoleService = service.ServiceGroupApp.BackendServiceGroup.AiRoleService

func (m *CliAiRoleApi) Profile(c *gin.Context) {
	var reqData request.AiRoleIdReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := aiRoleService.GetProfileInfo(c, &reqData); err != nil {
		response.FailWithMessage(err.<PERSON>rror(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliAiRoleApi) UndressImg(c *gin.Context) {
	var reqData request.UndressImgReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := aiRoleService.UndressImg(c, &reqData); err != nil {
		if errors.Is(err, errs.RequireSubErr) {
			response.RequireSub(c, nil)
			return
		}
		if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		}
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		if errors.Is(err, errs.RequireBuyFloErr) {
			response.RequireBuyFlo(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliAiRoleApi) UnlockProfileContent(c *gin.Context) {
	var reqData request.UnlockProfileContentReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := aiRoleService.UnlockProfileContent(c, &reqData); err != nil {
		if errors.Is(err, errs.RequireSubErr) {
			response.RequireSub(c, nil)
			return
		}
		if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		}
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		if errors.Is(err, errs.RequireBuyFloErr) {
			response.RequireBuyFlo(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(resp, c)
	}
}

func (m *CliAiRoleApi) AiRoleListV2(c *gin.Context) {
	var reqData request.AiRoleListV2Req
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, total, err := aiRoleService.IndexPage(c, &request.IndexReq{
		RoleType: reqData.RoleType,
		Page:     reqData.Page,
		PageSize: reqData.PageSize,
		AiType:   1,
	}); err != nil {
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		go msgService.CheckPushMsg(c)
		response.OkWithData(response.PageResult{
			List:     res,
			Total:    total,
			Page:     reqData.Page,
			PageSize: reqData.PageSize,
		}, c)
	}
}

func (m *CliAiRoleApi) AiRoleListV3(c *gin.Context) {
	var reqData request.AiRoleListV2Req
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, total, err := aiRoleService.IndexPage(c, &request.IndexReq{
		RoleType: reqData.RoleType,
		Page:     reqData.Page,
		PageSize: reqData.PageSize,
		AiType:   reqData.AiType,
	}); err != nil {
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		go msgService.CheckPushMsg(c)
		response.OkWithData(response.PageResult{
			List:     res,
			Total:    total,
			Page:     reqData.Page,
			PageSize: reqData.PageSize,
		}, c)
	}
}

func (m *CliAiRoleApi) AiRoleListPub(c *gin.Context) {
	var reqData request.AiRoleListPubReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, total, err := aiRoleService.AiRoleListPub(c, &reqData); err != nil {
		if errors.Is(err, errs.RequireLoginErr) {
			response.JWTFail(c, err.Error())
			return
		}
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(response.PageResult{
			List:     res,
			Total:    total,
			Page:     reqData.Page,
			PageSize: reqData.PageSize,
		}, c)
	}
}

func (m *CliAiRoleApi) AiFansList(c *gin.Context) {
	var reqData request.AiFansListReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, total, err := aiRoleService.IndexPage(c, &request.IndexReq{
		RoleType: reqData.RoleType,
		Page:     reqData.Page,
		PageSize: reqData.PageSize,
	}); err != nil {
		response.FailWithMessage("失败"+err.Error(), c)
	} else {
		go msgService.CheckPushMsg(c)
		response.OkWithData(response.PageResult{
			List:     res,
			Total:    total,
			Page:     reqData.Page,
			PageSize: reqData.PageSize,
		}, c)
	}
}

func (m *CliAiRoleApi) AiRoleInfo(c *gin.Context) {
	var reqData request.AiRoleIdReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := aiRoleService.AiRoleInfo(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliAiRoleApi) DelAiRole(c *gin.Context) {
	var reqData request.AiRoleIdReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := aiRoleService.DelAiRole(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *CliAiRoleApi) RequireImgSubMsg(c *gin.Context) {
	var reqData request.RequireImgSubMsgReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := aiRoleService.RequireImgSubMsg(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}
