package cli

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/response"
	"aimsg-server/utils"
	"github.com/gin-gonic/gin"
	"time"
)

type SystemApi struct{}

func (s *SystemApi) Ping(c *gin.Context) {
	// 检查DB连接是否可用
	if err := global.DB.Exec("SELECT 1").Error; err != nil {
		c.Status(585)
		return
	}
	// 检查Redis连接是否可用
	if _, err := global.REDIS.Ping(c).Result(); err != nil {
		c.Status(586)
		return
	}
	// 检查MongoDB链接是否可用.
	if err := global.MONGO.Ping(3); err != nil {
		c.Status(589)
		return
	}
	response.Ok(c)
}

func (s *SystemApi) Headers(c *gin.Context) {
	// aInt := c.GetInt("a")
	// a := 1 / aInt
	// fmt.Println(a)
	clientIp := c.<PERSON>("X-Forwarded-For")
	resMap := gin.H{
		"ip":       c.ClientIP(),
		"headers":  c.Request.Header,
		"clientIp": clientIp,
	}
	response.OkWithData(resMap, c)
}

func (s *SystemApi) ServerTimeRes() gin.H {
	var now = time.Now().UTC()
	zoneName, zoneOffset := now.Zone()
	return gin.H{
		"ZoneName":   zoneName,
		"ZoneOffset": zoneOffset,
		"DateOnly":   now.Format(time.DateOnly),
		"DateTime":   now.Format(time.DateTime),
		"Unix":       now.Unix(),
		"UnixMilli":  now.UnixMilli(),
		"UnixMicro":  now.UnixMicro(),
		"UnixNano":   now.UnixNano(),
	}
}

func (s *SystemApi) ServerTime(c *gin.Context) {
	response.OkWithData(s.ServerTimeRes(), c)
}

func (s *SystemApi) SetExchangeRate(c *gin.Context) {
	var (
		reqData req.ExchangeRateRes
	)
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	ratesMap := utils.StructToMap(reqData.ConversionRates)
	global.REDIS.HSet(c, global.EXCHANGE_RATE_KEY, ratesMap)
	response.Ok(c)
}
