package cli

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"errors"
	"github.com/gin-gonic/gin"
)

type CliAiContactInfoApi struct{}

var aiContactInfoService = service.ServiceGroupApp.BackendServiceGroup.AiContactInfoService

func (cli *CliAiContactInfoApi) UnlockAiContactInfo(c *gin.Context) {
	var reqData request.UnlockAiContactInfoReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := aiContactInfoService.UnlockAiContactInfo(c, &reqData); err != nil {
		if errors.Is(err, errs.RequireSubErr) {
			response.RequireSub(c, nil)
			return
		}
		if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		}
		if errors.Is(err, errs.RequireBuyFloErr) {
			response.RequireBuyFlo(c)
			return
		}
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}
