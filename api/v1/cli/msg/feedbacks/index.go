package feedbacks

import (
	"aimsg-server/model/cli/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service/msg/feedback"
	"github.com/gin-gonic/gin"
)

var APi FeedbackApi

type FeedbackApi struct{}

func (m *FeedbackApi) Add(c *gin.Context) {
	var reqData request.FeedbackAddReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := feedback.Service.Add(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *FeedbackApi) List(c *gin.Context) {
	if res, err := feedback.Service.List(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *FeedbackApi) Batch(c *gin.Context) {
	var reqData request.FeedbackBatchReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := feedback.Service.Batch(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}
