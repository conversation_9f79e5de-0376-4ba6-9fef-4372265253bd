package cli

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"errors"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type VideoSimplifyImgVideoApi struct{}

var videoSimplifyImgVideoService = service.ServiceGroupApp.BackendServiceGroup.VideoSimplifyImgVideoService

func (m *VideoSimplifyImgVideoApi) VideoSimplifyConsumeMsg(c *gin.Context) {
	videoSimplifyImgVideoService.VideoSimplifyConsumeMsg()
	response.Ok(c)
}

func (m *VideoSimplifyImgVideoApi) VideoSimplifyUploadVideoConsumeMsg(c *gin.Context) {
	videoSimplifyImgVideoService.VideoSimplifyUploadVideoConsumeMsg()
	response.Ok(c)
}

func (m *VideoSimplifyImgVideoApi) GetApiTaskStatus(c *gin.Context) {
	videoSimplifyImgVideoService.GetApiTaskStatus(c)
	response.Ok(c)
}

func (m *VideoSimplifyImgVideoApi) AgainCreateVideo(c *gin.Context) {
	var reqData request.AgainCreateVideoReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	req := request.UserCreateVideoReq2{
		VideoTemplateId: reqData.VideoTemplateId,
		Duration:        reqData.Duration,
		ModelType:       reqData.ModelType,
		Text:            reqData.Text,
	}
	if err := videoSimplifyImgVideoService.AgainCreateVideo(c, &req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.Ok(c)
	}
}

func (m *VideoSimplifyImgVideoApi) DeleteUserVideo(c *gin.Context) {
	var reqData request.DeleteUserVideoReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := videoSimplifyImgVideoService.DeleteUserVideo(reqData.UserVideoId); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.Ok(c)
	}
}

func (m *VideoSimplifyImgVideoApi) UserMsgList(c *gin.Context) {
	if res, err := videoSimplifyImgVideoService.UserMsgList(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		tmpMap := map[string]interface{}{
			"list": res,
		}
		response.OkWithData(tmpMap, c)
	}
}

func (m *VideoSimplifyImgVideoApi) UserMsgNotify(c *gin.Context) {
	if res, err := videoSimplifyImgVideoService.UserMsgNotify(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		var mapRes = make(map[string]interface{})
		mapRes["is_have"] = res
		response.OkWithData(mapRes, c)
	}

}

func (m *VideoSimplifyImgVideoApi) UserViewRecord(c *gin.Context) {
	var reqData request.UserViewRecordReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := videoSimplifyImgVideoService.UserViewRecord(c, reqData.UserVideoId); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		var mapRes = make(map[string]interface{})
		mapRes["is_show"] = res
		response.OkWithData(mapRes, c)
	}
}

func (m *VideoSimplifyImgVideoApi) VideoSimplifyCallBack(c *gin.Context) {

	var reqData request.VideoSimplifyCallbackResponse
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := videoSimplifyImgVideoService.VideoSimplifyCallBack(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.Ok(c)
	}

}

func (m *VideoSimplifyImgVideoApi) BannerList(c *gin.Context) {
	if res, err := videoSimplifyImgVideoService.BannerList(); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		tmpMap := map[string]interface{}{
			"list": res,
		}
		response.OkWithData(tmpMap, c)
	}
}

func (m *VideoSimplifyImgVideoApi) VideoTemplateList(c *gin.Context) {
	var reqData request.VideoTemplateListReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, total, err := videoSimplifyImgVideoService.VideoTemplateList(reqData.Page, reqData.PageSize); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(response.PageResult{
			List:     res,
			Total:    total,
			Page:     reqData.Page,
			PageSize: reqData.PageSize,
		}, c)
	}
}

func (m *VideoSimplifyImgVideoApi) UserVideoList(c *gin.Context) {
	var reqData request.UserVideoListReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := videoSimplifyImgVideoService.UserVideoList(c, reqData.Page, reqData.PageSize); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *VideoSimplifyImgVideoApi) TemplateInfo(c *gin.Context) {
	var reqData request.TemplateInfo
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if res, err := videoSimplifyImgVideoService.VideoTemplateInfo(c, reqData.VideoTemplate); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}

}

func (m *VideoSimplifyImgVideoApi) UserVideoInfo(c *gin.Context) {
	var reqData request.UserVideoInfoReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if res, err := videoSimplifyImgVideoService.UserVideoInfo(c, reqData.UserVideoId); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *VideoSimplifyImgVideoApi) UserCreateVideo(c *gin.Context) {
	var reqData request.UserCreateVideoReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	req := request.UserCreateVideoReq2{
		ImgUrl:          reqData.ImgUrl,
		VideoTemplateId: reqData.VideoTemplate,
		Duration:        "5",
		ModelType:       "std",
		Text:            reqData.Text,
		Title:           reqData.Title,
	}
	if err := videoSimplifyImgVideoService.UserCreateVideo(c, &req); err != nil {

		if errors.Is(err, errs.RequireSubErr) {
			response.RequireSub(c, nil)
			return
		}
		if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		}
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		if errors.Is(err, errs.RequireBuyFloErr) {
			response.RequireBuyFlo(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		global.LOG.Info("test_login", zap.Error(err))
		if err.Error() == "LIMIT" {
			response.CreateLimit(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.Ok(c)
	}
}

func (m *VideoSimplifyImgVideoApi) CreateIndex(c *gin.Context) {
	if res, err := videoSimplifyImgVideoService.CreateIndex(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *VideoSimplifyImgVideoApi) GetConsumeRecordList(c *gin.Context) {

	var reqData request.ConsumeRecordReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, total, err := videoSimplifyImgVideoService.GetConsumeRecordList(c, reqData.Page, reqData.PageSize); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(response.PageResult{
			List:     res,
			Total:    total,
			Page:     reqData.Page,
			PageSize: reqData.PageSize,
		}, c)
	}

}

func (m *VideoSimplifyImgVideoApi) VipList(c *gin.Context) {
	if res, err := videoSimplifyImgVideoService.VipList(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *VideoSimplifyImgVideoApi) FlowerShop(c *gin.Context) {

	if res, err := videoSimplifyImgVideoService.FlowerShop(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}
