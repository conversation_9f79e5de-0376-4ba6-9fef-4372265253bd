package cli

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"github.com/gin-gonic/gin"
)

type CliS3Api struct{}

var amazonS3Service = service.ServiceGroupApp.BackendServiceGroup.AmazonS3Service

func (m *CliS3Api) S3UploadFileUrl(c *gin.Context) {
	var reqData request.S3UploadFileUrlReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := amazonS3Service.CliS3UploadFileUrl(c, reqData); err != nil {
		response.FailWithMessage(err.<PERSON>rror(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliS3Api) S3UploadFileUrlOrigin(c *gin.Context) {
	var reqData request.S3UploadFileUrlReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := amazonS3Service.S3UploadFileUrlOrigin(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}
