package cli

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"errors"
	"github.com/gin-gonic/gin"
)

type CliImgGenRecordApi struct{}

var imgGenRecordService = service.ServiceGroupApp.BackendServiceGroup.ImgGenRecordService

func (m *CliImgGenRecordApi) RemainingGenCount(c *gin.Context) {
	var reqData request.RemainingGenCountReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := imgGenRecordService.RemainingGenCount(c, &reqData); err != nil {
		response.FailWithMessage(err.<PERSON><PERSON>r(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliImgGenRecordApi) GenHistory(c *gin.Context) {
	var reqData req.ImgGenRecordSearch
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := imgGenRecordService.GenHistory(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliImgGenRecordApi) GenImgTask(c *gin.Context) {
	var reqData request.GenImgTaskReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := imgGenRecordService.GenImgTask(c, &reqData); err != nil {
		if errors.Is(err, errs.RequireSubErr) {
			response.RequireSub(c, nil)
			return
		} else if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		} else if errors.Is(err, errs.RequireBuyFloErr) {
			response.RequireBuyFlo(c)
			return
		} else if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		} else if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		} else if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}
