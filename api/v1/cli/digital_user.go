package cli

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"aimsg-server/utils"
	"errors"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"

	"github.com/gin-gonic/gin"
)

type CliDigitalUserApi struct{}

var digitalUserService = service.ServiceGroupApp.BackendServiceGroup.DigitalUserService
var flowerConsumeRecordService = service.ServiceGroupApp.BackendServiceGroup.FlowerConsumeRecordService
var chatGuideRecordService = service.ServiceGroupApp.BackendServiceGroup.ChatGuideRecordService
var msgService = service.ServiceGroupApp.BackendServiceGroup.MsgService
var liveInfoService = service.ServiceGroupApp.BackendServiceGroup.LiveInfoService
var fireBaseUtilSer = service.ServiceGroupApp.BackendServiceGroup.FireBaseUtilSer

func (m *CliDigitalUserApi) SocialLogin(c *gin.Context) {
	var data request.SocialLoginReq
	appId := c.GetUint(global.GinContextAppId)
	if err := c.ShouldBind(&data); err != nil {
		if appId == 129 {
			global.LOG.Info("SocialLogin ShouldBind error", zap.Error(err))
		}
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(data); err != nil {
		if appId == 129 {
			global.LOG.Info("SocialLogin ShouldBind error", zap.Error(err))
		}
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := digitalUserService.SocialLogin(c, data); err != nil {
		if appId == 129 {
			global.LOG.Info("SocialLogin ShouldBind error", zap.Error(err))
		}
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) Register(c *gin.Context) {
	var data request.RegisterReq
	if err := c.ShouldBind(&data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := digitalUserService.Register(c, data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) UserInfo(c *gin.Context) {
	userInfo, err := digitalUserService.UserInfo(c)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(gin.H{
			"user": userInfo,
		}, c)
	}
}

func (m *CliDigitalUserApi) CompleteUserInfo(c *gin.Context) {
	var reqData request.CompleteUserInfoReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := digitalUserService.CompleteUserInfo(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.Ok(c)
	}
}

func (m *CliDigitalUserApi) RtmToken(c *gin.Context) {
	res, err := digitalUserService.RtmToken(c)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) ReceiveFlowersDialog(c *gin.Context) {
	res, err := digitalUserService.ReceiveFlowersDialog(c)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) ReceiveFlowersDialogV2(c *gin.Context) {
	res, err := digitalUserService.ReceiveFlowersDialogV2(c)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) ReceiveFlowersDialogV3(c *gin.Context) {
	res, err := digitalUserService.ReceiveFlowersDialogV3(c)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

// ReviewFlowers 领取评价奖励鲜花
func (m *CliDigitalUserApi) ReviewFlowers(c *gin.Context) {
	if err := flowerConsumeRecordService.ReviewFlowers(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.Ok(c)
	}
}

// ReceiveFlowers 签到领取鲜花
func (m *CliDigitalUserApi) ReceiveFlowers(c *gin.Context) {
	if res, err := flowerConsumeRecordService.ReceiveFlowers(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) ReceiveFlowersVideoSimplify(c *gin.Context) {
	if res, err := flowerConsumeRecordService.ReceiveFlowersVideoSimplify(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) ReceiveFlowersCoins(c *gin.Context) {
	if res, err := flowerConsumeRecordService.ReceiveFlowersCoins(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

// ReceiveFlowers 签到领取鲜花
func (m *CliDigitalUserApi) ReceiveAdFlowers(c *gin.Context) {
	if res, err := flowerConsumeRecordService.ReceiveAdFlowers(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) SignInList(c *gin.Context) {
	if res, err := flowerConsumeRecordService.SignInList(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) ChatGuideListV2(c *gin.Context) {
	if res, err := chatGuideRecordService.ChatGuideListV2(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) SendMsg(c *gin.Context) {
	var data request.SendMsgReq
	if err := c.ShouldBind(&data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if res, err := digitalUserService.SendMsg(c, data); err != nil {
		if errors.Is(err, errs.RequireSubErr) {
			response.RequireSub(c, res)
			return
		}
		if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		}
		if errors.Is(err, errs.RequireBuyChatErr) {
			response.RequireBuyChat(c)
			return
		}
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		if errors.Is(err, errs.RequireBuyFloErr) {
			response.RequireBuyFlo(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) GetAiFuncSwitch(c *gin.Context) {
	var data request.GetAiFuncSwitchReq
	if err := c.ShouldBind(&data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := digitalUserService.GetAiFuncSwitch(c, data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) GetAiFuncContent(c *gin.Context) {
	var data request.GetAiFuncContentReq
	if err := c.ShouldBind(&data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := digitalUserService.GetAiFuncContent(c, data); err != nil {
		if errors.Is(err, errs.RequireSubErr) {
			response.RequireSub(c, nil)
			return
		}
		if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		}
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		if errors.Is(err, errs.RequireBuyFloErr) {
			response.RequireBuyFlo(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.Ok(c)
	}
}

func (m *CliDigitalUserApi) GetAiFirstMsg(c *gin.Context) {
	var data request.GetAiFirstMsgReq
	if err := c.ShouldBind(&data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if data.AiRoleId == "" {
		response.FailWithMessage("ai_role_id required", c)
		return
	}

	if res, err := digitalUserService.GetAiFirstMsg(c, data.AiRoleId); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) GetAiFirstVoice(c *gin.Context) {
	var data request.GetAiFirstVoiceReq
	if err := c.ShouldBind(&data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := digitalUserService.GetAiFirstVoice(c, data); err != nil {
		if errors.Is(err, errs.RequireSubErr) {
			response.RequireSub(c, nil)
			return
		}
		if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		}
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		if errors.Is(err, errs.RequireBuyFloErr) {
			response.RequireBuyFlo(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) UnlockData(c *gin.Context) {
	var data request.UnlockDataReq
	if err := c.ShouldBind(&data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := digitalUserService.UnlockData(c, data); err != nil {
		if errors.Is(err, errs.RequireSubErr) {
			response.RequireSub(c, nil)
			return
		}
		if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		}
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		if errors.Is(err, errs.RequireBuyFloErr) {
			response.RequireBuyFlo(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) GenMsgVoice(c *gin.Context) {
	var data request.GenMsgVoiceReq
	if err := c.ShouldBind(&data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := digitalUserService.GenMsgVoice(c, data); err != nil {
		if errors.Is(err, errs.RequireSubErr) {
			response.RequireSub(c, nil)
			return
		}
		if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		}
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		if errors.Is(err, errs.RequireBuyFloErr) {
			response.RequireBuyFlo(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) GetAsmrVoice(c *gin.Context) {
	var data request.GetAsmrVoiceReq
	if err := c.ShouldBind(&data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := digitalUserService.GetAsmrVoice(c, data); err != nil {
		if errors.Is(err, errs.RequireSubErr) {
			response.RequireSub(c, nil)
			return
		}
		if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		}
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		if errors.Is(err, errs.RequireBuyFloErr) {
			response.RequireBuyFlo(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) UnlockVideo(c *gin.Context) {
	var data request.GenMsgVideoReq
	if err := c.ShouldBind(&data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := digitalUserService.UnlockVideo(c, data); err != nil {
		if errors.Is(err, errs.RequireSubErr) {
			response.RequireSub(c, nil)
			return
		}
		if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		}
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		if errors.Is(err, errs.RequireBuyFloErr) {
			response.RequireBuyFlo(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) ResetChat(c *gin.Context) {
	var data request.ResetChatReq
	if err := c.ShouldBind(&data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := digitalUserService.ResetChat(c, data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.Ok(c)
	}
}

func (m *CliDigitalUserApi) FcmToken(c *gin.Context) {
	var reqData request.FcmTokenReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	digitalUserService.UpdateFcmToken(c, &reqData)
	response.Ok(c)
}

func (m *CliDigitalUserApi) AdjustIdData(c *gin.Context) {
	var reqData model.AdjustIdData
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := digitalUserService.UpdateAdjustIdData(c, &reqData); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *CliDigitalUserApi) SetNickname(c *gin.Context) {
	var reqData request.SetNicknameReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := digitalUserService.SetNickname(c, &reqData); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *CliDigitalUserApi) ChangePassword(c *gin.Context) {
	var reqData request.ChangePasswordReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := digitalUserService.ChangePassword(c, &reqData); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *CliDigitalUserApi) SetUsername(c *gin.Context) {
	var reqData request.SetUsernameReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := digitalUserService.SetUsername(c, &reqData); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *CliDigitalUserApi) WebUnlockGame(c *gin.Context) {
	var reqData request.WebUnlockGameReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := digitalUserService.WebUnlockGame(c, &reqData); err != nil {
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *CliDigitalUserApi) UserDel(c *gin.Context) {
	if err := digitalUserService.UserDel(c); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *CliDigitalUserApi) AiRoleTypeList(c *gin.Context) {
	res, err := digitalUserService.AiRoleTypeList(c)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(res, c)
}

func (m *CliDigitalUserApi) AiRoleTypeListPub(c *gin.Context) {
	res, err := digitalUserService.AiRoleTypeListPub(c)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(res, c)
}

func (m *CliDigitalUserApi) FansTypeList(c *gin.Context) {
	res, err := digitalUserService.FansTypeList(c)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(res, c)
}

func (m *CliDigitalUserApi) CloseFlowerDialog(c *gin.Context) {
	var reqData request.RoleIdRequest
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := digitalUserService.CloseFlowerDialog(c, &reqData); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *CliDigitalUserApi) CloseChatDialog(c *gin.Context) {
	var reqData request.RoleIdRequest
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := digitalUserService.CloseChatDialog(c, &reqData); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *CliDigitalUserApi) LowFlowersDialog(c *gin.Context) {
	if res, err := digitalUserService.LowFlowersDialog(c); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) GenAiRolePreInfo(c *gin.Context) {
	if res, err := digitalUserService.GenAiRolePreInfo(c); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) ReviewDialogInfo(c *gin.Context) {
	if res, err := digitalUserService.ReviewDialogInfo(c); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) UserFlowerAmount(c *gin.Context) {
	if res, err := digitalUserService.UserFlowerAmount(c); err != nil {
		global.LOG.Error("失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) AiRoleMediaList(c *gin.Context) {
	response.OkWithData(gin.H{"list": []string{}}, c)
}

func (m *CliDigitalUserApi) Digital(c *gin.Context) {
	response.OkWithData(gin.H{"list": []string{}, "total": 0}, c)
}

func (m *CliDigitalUserApi) UploadFileUrl(c *gin.Context) {
	var reqData request.UploadFileUrlReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := digitalUserService.UploadFileUrl(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) SaveFeedbackRecord(c *gin.Context) {
	var reqData request.SaveFeedbackRecordReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := digitalUserService.SaveFeedbackRecord(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) GenAiRole(c *gin.Context) {
	var reqData request.GenAiRoleReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := digitalUserService.GenAiRole(c, &reqData); err != nil {
		if errors.Is(err, errs.RequireSubErr) {
			response.RequireSub(c, nil)
			return
		}
		if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		}
		if errors.Is(err, errs.RequireBuyChatErr) {
			response.RequireBuyChat(c)
			return
		}
		if errors.Is(err, errs.RequireBuyFloErr) {
			response.RequireBuyFlo(c)
			return
		}
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) AiRoleFaceImgCheck(c *gin.Context) {
	var reqData request.AiRoleFaceImgCheckReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := digitalUserService.AiRoleFaceImgCheck(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *CliDigitalUserApi) VideoTxtList(c *gin.Context) {
	if res, err := digitalUserService.VideoTxtList(c); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) GenVideoTask(c *gin.Context) {
	var reqData request.GenVideoTaskReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := digitalUserService.GenVideoTask(c, &reqData); err != nil {
		if errors.Is(err, errs.RequireSubErr) {
			response.RequireSub(c, nil)
			return
		}
		if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		}
		if errors.Is(err, errs.RequireBuyChatErr) {
			response.RequireBuyChat(c)
			return
		}
		if errors.Is(err, errs.RequireBuyFloErr) {
			response.RequireBuyFlo(c)
			return
		}
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) GenVideoHistory(c *gin.Context) {
	var reqData request.GenVideoHistoryReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if listRes, total, err := digitalUserService.GenVideoHistory(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(gin.H{
			"list":  listRes,
			"total": total,
		}, c)
	}
}

func (m *CliDigitalUserApi) ReceiveAiRoleFlowers(c *gin.Context) {
	var reqData request.AiRoleIdReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := flowerConsumeRecordService.ReceiveAiRoleFlowers(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) LiveConfig(c *gin.Context) {
	if res, err := liveInfoService.LiveConfig(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) LiveAiRoleRank(c *gin.Context) {
	if res, err := liveInfoService.LiveAiRoleRank(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) LiveAiRoleRankV2(c *gin.Context) {
	var reqData request.LiveAiRoleRankV2Req
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := liveInfoService.LiveAiRoleRankV2(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) UpdateLiveRank(c *gin.Context) {
	var reqData request.UpdateLiveRankReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := liveInfoService.UpdateLiveRank(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.Ok(c)
	}
}

func (m *CliDigitalUserApi) CreateLiveInfo(c *gin.Context) {
	var reqData request.CreateLiveInfoReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := liveInfoService.CreateLiveInfo(c, &reqData); err != nil {
		if errors.Is(err, errs.RequireSubErr) {
			response.RequireSub(c, nil)
			return
		}
		if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		}
		if errors.Is(err, errs.RequireBuyFloErr) {
			response.RequireBuyFlo(c)
			return
		}
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) UpdateLiveInfo(c *gin.Context) {
	var reqData request.UpdateLiveInfoReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := liveInfoService.UpdateLiveInfo(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.Ok(c)
	}
}

func (m *CliDigitalUserApi) LiveRandomConfig(c *gin.Context) {
	var appId = utils.GetAppID(c)
	var cList []global.PeopleInfo

	peoList := utils.RandSelect(global.AllPeopleInfoList, 100)
	conList := utils.RandSelect(global.ContentList, 100)

	for _, peopleInfo := range peoList {
		cList = append(cList, global.PeopleInfo{
			Name:   peopleInfo.Name,
			Avatar: utils.S3Url(peopleInfo.Avatar, appId),
		})
	}
	response.OkWithData(gin.H{
		"people_list":  cList,
		"content_list": conList,
	}, c)
}

func (m *CliDigitalUserApi) GetFirebaseReport(c *gin.Context) {
	if res, err := fireBaseUtilSer.GetFirebaseReport(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalUserApi) GetFirebaseReportEventList(c *gin.Context) {
	userId := utils.GetDigitalUserID(c)
	if res, err := fireBaseUtilSer.GetFirebaseReportEvent(c, userId); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(gin.H{
			"list": res,
		}, c)
	}
}

func (m *CliDigitalUserApi) FinishFirebaseReportEvent(c *gin.Context) {
	var reqData request.FinishFirebaseReportEventReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := fireBaseUtilSer.FinishFirebaseReportEvent(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}

// UserQuickReplyCharging 12-31 增加快捷回复消息下发付费
func (m *CliDigitalUserApi) UserQuickReplyCharging(c *gin.Context) {
	var reqData request.UserQuickReplyChargingReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := digitalUserService.UserQuickReplyCharging(c, &reqData)
	if err != nil {
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)

}

func (m *CliDigitalUserApi) UserQuickReply(c *gin.Context) {
	var reqData request.UserQuickReplyReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := digitalUserService.UserQuickReply(c, &reqData); err != nil {
		if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		}
		if errors.Is(err, errs.RequireBuyChatErr) {
			response.RequireBuyChat(c)
			return
		}
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		if errors.Is(err, errs.RequireBuyFloErr) {
			response.RequireBuyFlo(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}

func (m *CliDigitalUserApi) ImgBlindBox(c *gin.Context) {
	var reqData request.ImgBlindBoxReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := digitalUserService.ImgBlindBox(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}
