package cli

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"github.com/gin-gonic/gin"
)

type AgoraApi struct{}

var agoraService = service.ServiceGroupApp.BackendServiceGroup.AgoraService

func (m *AgoraApi) RTCWebhook(c *gin.Context) {
	var reqData request.RTCWebhookReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.<PERSON>rror(), c)
		return
	}
	if err := agoraService.RTCWebhook(c, &reqData); err != nil {
		response.FailWithMessage(err.<PERSON>rror(), c)
	} else {
		response.Ok(c)
	}
}
