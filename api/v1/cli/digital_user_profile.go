package cli

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"errors"
	"github.com/gin-gonic/gin"
)

type CliDigitalUserProfileApi struct{}

var digitalUserProfileService = service.ServiceGroupApp.BackendServiceGroup.DigitalUserProfileService

func (m *CliDigitalUserProfileApi) SetNSFW(c *gin.Context) {
	var data request.SetNSFWReq
	if err := c.ShouldBind(&data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := digitalUserProfileService.SetNSFW(c, &data); err != nil {
		if errors.Is(err, errs.RequireSubErr) {
			response.RequireSub(c, nil)
			return
		}
		if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		// global.LOG.Error("SetNSFW error", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.Ok(c)
	}
}

func (m *CliDigitalUserProfileApi) AddWatchAdMsgCount(c *gin.Context) {
	var reqData request.AiRoleIdReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := digitalUserProfileService.AddWatchAdMsgCount(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}
