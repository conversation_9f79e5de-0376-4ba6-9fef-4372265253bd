package cli

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"aimsg-server/utils"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
)

type CliUsersIntimateApi struct{}

var usersIntimateService = service.ServiceGroupApp.BackendServiceGroup.UsersIntimateService

func (m *CliUsersIntimateApi) Intimate(c *gin.Context) {
	var (
		err        error
		res        *model.UsersIntimate
		aiRoleInfo *model.AiRole
		reqData    request.AiRoleIdReq
	)
	if err = c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err = global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.<PERSON>rror(), c)
		return
	}
	userId := utils.GetDigitalUserID(c)
	// 获取AI角色信息
	if aiRoleInfo, err = aiRoleService.GetByRoleId(reqData.AiRoleId); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	res, err = usersIntimateService.GetByUidAndRoleid(userId, aiRoleInfo.Id)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	// 孙宏伟需求 2024-12-24 新增app_id 4号包数据 亲密度功能 1
	percentage, err := usersIntimateService.IntimatePercentage(c, reqData.AiRoleId)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithData(gin.H{
		"intimate":       res.Intimate,
		"intimate_level": res.Level,
		"percentage":     percentage,
	}, c)
}

func (m *CliUsersIntimateApi) IntimateLevelList(c *gin.Context) {
	var (
		reqData request.AiRoleIdReq
	)
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := usersIntimateService.IntimateLevelList(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliUsersIntimateApi) IntimateUpgradeDialog(c *gin.Context) {
	var (
		reqData request.AiRoleIdReq
	)
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := usersIntimateService.IntimateUpgradeDialog(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}
