package cli

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"errors"
	"github.com/gin-gonic/gin"
)

type CliUserUseScenesApi struct{}

var userUseScenesService = service.ServiceGroupApp.BackendServiceGroup.UserUseScenesService

func (m *CliUserUseScenesApi) Check(c *gin.Context) {
	var reqData request.CliUserUseScenesCheckReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := userUseScenesService.CliCheck(c, &reqData); err != nil {
		if errors.Is(err, errs.RequireSubErr) {
			response.RequireSub(c, nil)
			return
		}
		if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *CliUserUseScenesApi) GetChecked(c *gin.Context) {
	var reqData request.AiRoleIdReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := userUseScenesService.GetChecked(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}
