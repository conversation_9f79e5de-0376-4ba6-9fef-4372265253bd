package cli

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"errors"
	"github.com/gin-gonic/gin"
)

type CliDiyPhotoTaskApi struct{}

var diyPhotoTaskService = service.ServiceGroupApp.BackendServiceGroup.DiyPhotoTaskService

func (m *CliDiyPhotoTaskApi) GetDiyConfig(c *gin.Context) {
	var reqData request.GetDiyConfigReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := diyPhotoTaskService.GetDiyConfig(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDiyPhotoTaskApi) CreateDiyPhotoTask(c *gin.Context) {
	var reqData request.CreateDiyPhotoTaskReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := diyPhotoTaskService.CliCreateDiyPhotoTask(c, &reqData); err != nil {
		if errors.Is(err, errs.RequireSubErr) {
			response.RequireSub(c, nil)
			return
		}
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDiyPhotoTaskApi) DiyPhotoTaskHistory(c *gin.Context) {
	var reqData req.DiyPhotoTaskSearch
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := diyPhotoTaskService.CliDiyPhotoTaskHistory(c, reqData); err != nil {
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}
