package cli

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"errors"
	"github.com/gin-gonic/gin"
)

type CliGiftApi struct{}

var giftService = service.ServiceGroupApp.BackendServiceGroup.GiftService

func (m *CliGiftApi) SendGift(c *gin.Context) {
	var reqData request.SendGiftReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := msgService.SendGift(c, &reqData); err != nil {
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}
