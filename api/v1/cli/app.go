package cli

import (
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"github.com/gin-gonic/gin"
)

type CliAppApi struct{}

var appService = service.ServiceGroupApp.BackendServiceGroup.AppService

func (m *CliAppApi) AppConfig(c *gin.Context) {
	res := appService.AppConfig(c)
	response.OkWithData(res, c)
}

func (m *CliAppApi) IconConfig(c *gin.Context) {
	res := appService.IconConfig(c)
	response.OkWithData(res, c)
}

func (m *CliAppApi) BannerList(c *gin.Context) {
	res := appService.BannerList(c)
	response.OkWithData(res, c)
}

func (m *CliAppApi) ImgDemoList(c *gin.Context) {
	res := appService.ImgDemoList(c)
	response.OkWithData(res, c)
}
