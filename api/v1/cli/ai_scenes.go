package cli

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type CliAiScenesApi struct{}

var aiScenesService = service.ServiceGroupApp.BackendServiceGroup.AiScenesService

func (m *CliAiScenesApi) GetList(c *gin.Context) {
	var reqData req.CliAiScenesSearch
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := aiScenesService.CliGetList(c, reqData); err != nil {
		global.LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage(err.<PERSON>rror(), c)
	} else {
		response.OkWithData(response.PageResult{
			List:     list,
			Total:    total,
			Page:     reqData.Page,
			PageSize: reqData.PageSize,
		}, c)
	}
}
