package cli

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"github.com/gin-gonic/gin"
)

type CliImgTemplateRecordApi struct{}

var imgTemplateRecordService = service.ServiceGroupApp.BackendServiceGroup.ImgTemplateRecordService

func (m *CliImgTemplateRecordApi) CliList(c *gin.Context) {
	if res, err := imgTemplateRecordService.CliList(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliImgTemplateRecordApi) CliFindOne(c *gin.Context) {
	var reqData request.ImgTemplateRecordFindOneReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := imgTemplateRecordService.CliFindOne(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}
