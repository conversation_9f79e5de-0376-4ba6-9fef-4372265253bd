package cli

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/service"
	"github.com/gin-gonic/gin"
	"net/http"
)

type CallbackApi struct{}

var callbackService = service.ServiceGroupApp.BackendServiceGroup.CallbackService

func (m *CallbackApi) PayCenter(c *gin.Context) {
	var reqData request.PayCenterCallbackReq
	if err := c.ShouldBind(&reqData); err != nil {
		c.String(http.StatusBadRequest, err.Error())
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		c.String(http.StatusBadRequest, err.Error())
		return
	}
	if err := callbackService.PayCenter(c, &reqData); err != nil {
		c.String(http.StatusBadRequest, err.Error())
		return
	}
	c.JSON(http.StatusOK, gin.H{"code": 200, "msg": "success"})
}

func (m *CallbackApi) PayCenterSub(c *gin.Context) {
	var reqData request.PayCenterSubCallbackReq
	if err := c.ShouldBind(&reqData); err != nil {
		c.String(http.StatusBadRequest, err.Error())
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		c.String(http.StatusBadRequest, err.Error())
		return
	}
	if err := callbackService.PayCenterSub(c, &reqData); err != nil {
		c.String(http.StatusBadRequest, err.Error())
		return
	}
	c.JSON(http.StatusOK, gin.H{"code": 200, "msg": "success"})
}

func (m *CallbackApi) VideoSimplifyCallBack(c *gin.Context) {
	var reqData request.VideoSimplifyCallbackResponse
	if err := c.ShouldBind(&reqData); err != nil {
		c.String(http.StatusBadRequest, err.Error())
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		c.String(http.StatusBadRequest, err.Error())
		return
	}

	if err := videoSimplifyImgVideoService.VideoSimplifyCallBack(c, reqData); err != nil {
		c.String(http.StatusBadRequest, err.Error())
		return
	}
	c.JSON(http.StatusOK, gin.H{"code": 200, "msg": "success"})
}
