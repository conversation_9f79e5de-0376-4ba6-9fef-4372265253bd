package cli

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	req "aimsg-server/model/common/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"errors"
	"github.com/gin-gonic/gin"
)

type CliPrivateSpaceUserApi struct{}

var privateSpaceUserService = service.ServiceGroupApp.BackendServiceGroup.PrivateSpaceUserService

func (m *CliPrivateSpaceUserApi) UserPrivateSpaceList(c *gin.Context) {
	if res, err := privateSpaceUserService.UserPrivateSpaceList(c); err != nil {
		response.FailWithMessage(err.<PERSON>rror(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliPrivateSpaceUserApi) UnlockPrivateSpace(c *gin.Context) {
	var reqData req.IdReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := privateSpaceUserService.UnlockPrivateSpace(c, &reqData); err != nil {
		if errors.Is(err, errs.RequireSubErr) {
			response.RequireSub(c, nil)
			return
		}
		if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		}
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		if errors.Is(err, errs.RequireBuyFloErr) {
			response.RequireBuyFlo(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}
