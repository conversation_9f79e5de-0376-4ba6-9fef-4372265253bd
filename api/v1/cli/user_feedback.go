package cli

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"github.com/gin-gonic/gin"
)

type UserFeedbackApi struct{}

var userFeedbackService = service.ServiceGroupApp.BackendServiceGroup.UserFeedbackService

func (m *UserFeedbackApi) Test(c *gin.Context) {
	userFeedbackService.Test()
	response.Ok(c)
}

func (m *UserFeedbackApi) GetAIRobotInfoByAppId(c *gin.Context) {
	if res, err := userFeedbackService.GetAIRobotInfoByAppId(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *UserFeedbackApi) GetFeedbackList(c *gin.Context) {
	if res, err := userFeedbackService.GetFeedbackList(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *UserFeedbackApi) SendMsg(c *gin.Context) {
	var reqData request.UserFeedbackSendMsg
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := userFeedbackService.SendMsg(c, reqData); err != nil {
		if err.Error() == "email is null" {
			response.EmailIsNull(c)
			return
		}
		response.OkWithData(res, c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *UserFeedbackApi) Reply(c *gin.Context) {
	var reqData request.UserFeedbackReply
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := userFeedbackService.Reply(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.Ok(c)
	}
}

func (m *UserFeedbackApi) SetUserEmail(c *gin.Context) {
	var reqData request.UserFeedbackSetEmail
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := userFeedbackService.SetUserEmail(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *UserFeedbackApi) GetUserFeedbackList(c *gin.Context) {
	var reqData request.UserFeedbackListReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, total, err := userFeedbackService.GetUserFeedbackList(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(response.PageResult{
			List:     res,
			Total:    total,
			Page:     reqData.Page,
			PageSize: reqData.PageSize,
		}, c)
	}
}
