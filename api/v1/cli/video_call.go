package cli

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"errors"
	"github.com/gin-gonic/gin"
)

type CliVideoCallApi struct{}

var videoCallService = service.ServiceGroupApp.BackendServiceGroup.VideoCallService

func (m *CliVideoCallApi) Connected(c *gin.Context) {
	var reqData request.ConnectedReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := videoCallService.Connected(c, &reqData); err != nil {
		response.FailWithMessage(err.<PERSON>r(), c)
	} else {
		response.Ok(c)
	}
}

func (m *CliVideoCallApi) HangUp(c *gin.Context) {
	var reqData request.HangUpReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := videoCallService.HangUp(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *CliVideoCallApi) Msg(c *gin.Context) {
	var reqData request.MsgReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := videoCallService.Msg(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *CliVideoCallApi) GetRealAnchorInfo(c *gin.Context) {
	var reqData request.GetRealAnchorInfoReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := videoCallService.GetRealAnchorInfo(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliVideoCallApi) CallStart(c *gin.Context) {
	var reqData request.CallStartReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := videoCallService.CallStart(c, &reqData); err != nil {
		if errors.Is(err, errs.RequireSubErr) {
			response.RequireSub(c, nil)
			return
		}
		if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		}
		if errors.Is(err, errs.RequireBuyChatErr) {
			response.RequireBuyChat(c)
			return
		}
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		if errors.Is(err, errs.RequireBuyFloErr) {
			response.RequireBuyFlo(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliVideoCallApi) CallCancel(c *gin.Context) {
	var reqData request.CallCancelReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := videoCallService.CallCancel(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *CliVideoCallApi) CallClose(c *gin.Context) {
	var reqData request.CallCloseReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := videoCallService.CallClose(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

// CallDeduction 通话扣费
func (m *CliVideoCallApi) CallDeduction(c *gin.Context) {
	var reqData request.CallDeductionReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := videoCallService.CallDeduction(c, &reqData); err != nil {
		if errors.Is(err, errs.RequireSubErr) {
			response.RequireSub(c, nil)
			return
		}
		if errors.Is(err, errs.RequireSVipErr) {
			response.RequireSVip(c)
			return
		}
		if errors.Is(err, errs.RequireBuyChatErr) {
			response.RequireBuyChat(c)
			return
		}
		if errors.Is(err, errs.FloShopErr) {
			response.TurnToFloShop(c)
			return
		}
		if errors.Is(err, errs.RequireBuyFloErr) {
			response.RequireBuyFlo(c)
			return
		}
		if errors.Is(err, errs.ContentVipErr) {
			response.RequireContentVip(c)
			return
		}
		if errors.Is(err, errs.ChatVipErr) {
			response.RequireChatVip(c)
			return
		}
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}
