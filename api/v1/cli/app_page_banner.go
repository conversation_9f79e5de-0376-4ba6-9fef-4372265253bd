package cli

import (
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"github.com/gin-gonic/gin"
)

type CliAppPageBannerApi struct{}

var appPageBannerService = service.ServiceGroupApp.BackendServiceGroup.AppPageBannerService

func (m *CliAppPageBannerApi) GetAppPageBanner(c *gin.Context) {
	if res, err := appPageBannerService.GetAppPageBanner(c); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}
