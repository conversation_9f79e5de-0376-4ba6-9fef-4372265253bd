package cli

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"github.com/gin-gonic/gin"
)

type CliPrivateSpaceApi struct{}

var privateSpaceService = service.ServiceGroupApp.BackendServiceGroup.PrivateSpaceService

func (m *CliPrivateSpaceApi) PrivateSpaceList(c *gin.Context) {
	var reqData request.PrivateSpaceListReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := privateSpaceService.PrivateSpaceList(c, &reqData); err != nil {
		response.FailWithMessage(err.<PERSON><PERSON><PERSON>(), c)
	} else {
		response.OkWithData(res, c)
	}
}
