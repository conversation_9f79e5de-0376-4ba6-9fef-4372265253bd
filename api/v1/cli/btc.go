package cli

import (
	reqBtc "aimsg-server/model/backend/request/btc"
	"aimsg-server/model/common/response"
	"aimsg-server/service"

	"github.com/gin-gonic/gin"
)

type CliBtcApi struct{}

var btcService = service.ServiceGroupApp.BackendServiceGroup.BtcService

func (m *CliBtcApi) Index(c *gin.Context) {
	if res, err := btcService.Index(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliBtcApi) ProductDetails(c *gin.Context) {
	if res, err := btcService.ProductDetails(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliBtcApi) MyNFT(c *gin.Context) {
	var data reqBtc.NFTReq
	if err := c.ShouldBind(&data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := btcService.MyNFT(c, &data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliBtcApi) IncomeDetails(c *gin.Context) {
	if res, err := btcService.IncomeDetails(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliBtcApi) Wallet(c *gin.Context) {
	if res, err := btcService.Wallet(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliBtcApi) Withdraw(c *gin.Context) {
	if res, err := btcService.Withdraw(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliBtcApi) CashOut(c *gin.Context) {
	var data reqBtc.CashOutReq
	if err := c.ShouldBind(&data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := btcService.CashOut(c, &data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliBtcApi) ClaimDailyRewards(c *gin.Context) {
	var data reqBtc.ClaimDailyRewardsReq
	if err := c.ShouldBind(&data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := btcService.ClaimDailyRewards(c, &data); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliBtcApi) GetRateByUser(c *gin.Context) {
	if res, err := btcService.GetRateByUser(c, true); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliBtcApi) GetTotalByUser(c *gin.Context) {
	if res, err := btcService.GetTotalByUser(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliBtcApi) SelectNetwork(c *gin.Context) {
	if res, err := btcService.SelectNetwork(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliBtcApi) ADDetail(c *gin.Context) {
	if res, err := btcService.ADDetail(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliBtcApi) SignDetail(c *gin.Context) {
	if res, err := btcService.SignDetail(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliBtcApi) GiftPack(c *gin.Context) {
	if res, err := btcService.GiftPack(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}
