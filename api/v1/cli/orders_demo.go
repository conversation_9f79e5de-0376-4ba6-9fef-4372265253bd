package cli

import (
	"aimsg-server/model/cli/request"
	"github.com/gin-gonic/gin"
	"net/http"
)

type CliOrdersDemoApi struct{}

func (m *CliOrdersApi) GoogleNotifyDemo(c *gin.Context) {
	var reqData request.GoogleNotifyReq
	if err := c.ShouldBind(&reqData); err != nil {
		c.Status(http.StatusBadRequest)
		return
	}
	if err := ordersService.GoogleNotifyNew(c, &reqData); err != nil {
		c.Status(http.StatusBadRequest)
	} else {
		c.Status(http.StatusOK)
	}
}
