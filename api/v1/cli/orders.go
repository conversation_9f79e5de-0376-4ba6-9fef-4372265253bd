package cli

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"github.com/awa/go-iap/appstore"
	"github.com/gin-gonic/gin"
	"net/http"
)

type CliOrdersApi struct{}

var ordersService = service.ServiceGroupApp.BackendServiceGroup.OrdersService

func (m *CliOrdersApi) OrderPay(c *gin.Context) {
	var reqData request.OrderPayReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := ordersService.OrderPay(c, &reqData); err != nil {
		response.FailWithMessage(err.<PERSON>r(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliOrdersApi) PayResult(c *gin.Context) {
	c.HTML(200, "result.html", gin.H{})
}

func (m *CliOrdersApi) GoogleQuery(c *gin.Context) {
	var reqData request.GoogleQueryReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := ordersService.GoogleQuery(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliOrdersApi) AppleQuery(c *gin.Context) {
	var reqData request.AppleQueryReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := ordersService.AppleQuery(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliOrdersApi) AppleNotify(c *gin.Context) {
	var reqData appstore.SubscriptionNotificationV2SignedPayload
	if err := c.ShouldBind(&reqData); err != nil {
		c.Status(http.StatusBadRequest)
		return
	}
	if err := ordersService.AppleNotify(c, &reqData); err != nil {
		c.Status(http.StatusBadRequest)
	} else {
		c.Status(http.StatusOK)
	}
}

func (m *CliOrdersApi) GoogleNotify(c *gin.Context) {
	var reqData request.GoogleNotifyReq
	if err := c.ShouldBind(&reqData); err != nil {
		c.Status(http.StatusBadRequest)
		return
	}
	if err := ordersService.GoogleNotifyNew(c, &reqData); err != nil {
		c.Status(http.StatusBadRequest)
	} else {
		c.Status(http.StatusOK)
	}
}

func (m *CliOrdersApi) PayMethodList(c *gin.Context) {
	var reqData request.PayMethodListReq
	if err := c.ShouldBind(&reqData); err != nil {
		c.Status(http.StatusBadRequest)
		return
	}
	if res, err := ordersService.PayMethodList(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliOrdersApi) SubPayMethodList(c *gin.Context) {
	var reqData request.SubPayMethodListReq
	if err := c.ShouldBind(&reqData); err != nil {
		c.Status(http.StatusBadRequest)
		return
	}
	if res, err := ordersService.SubPayMethodList(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliOrdersApi) WebMethodList(c *gin.Context) {
	var reqData request.PayMethodListReq
	if err := c.ShouldBind(&reqData); err != nil {
		c.Status(http.StatusBadRequest)
		return
	}
	if res, err := ordersService.WebMethodList(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}
