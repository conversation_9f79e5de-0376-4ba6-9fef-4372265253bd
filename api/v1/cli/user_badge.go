package cli

import (
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"github.com/gin-gonic/gin"
)

type CliUserBadgeApi struct{}

var userBadgeService = service.ServiceGroupApp.BackendServiceGroup.UserBadgeService

func (m *CliUserBadgeApi) MyBadgeList(c *gin.Context) {
	if res, err := userBadgeService.MyBadgeList(c); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}
