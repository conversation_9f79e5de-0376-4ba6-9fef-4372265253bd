package cli

import (
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"github.com/gin-gonic/gin"
)

type VoiceInfoApi struct{}

var voiceInfoService = service.ServiceGroupApp.BackendServiceGroup.VoiceInfoService

func (m *VoiceInfoApi) VoiceList(c *gin.Context) {
	if res, err := voiceInfoService.VoiceList(c); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}
