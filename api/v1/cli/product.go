package cli

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/common/response"
	"aimsg-server/service"
	"github.com/gin-gonic/gin"
)

type CliDigitalProductApi struct{}

var digitalProductService = service.ServiceGroupApp.BackendServiceGroup.ProductService

func (m *CliDigitalProductApi) FlowerProductList(c *gin.Context) {
	source, sourceExist := c.Get<PERSON>uery("source")
	if !sourceExist {
		source = "send_msg"
	}
	if res, err := digitalProductService.FlowerProductList(c, source); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalProductApi) ChatProductList(c *gin.Context) {
	if res, err := digitalProductService.ChatProductList(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalProductApi) FlowerShop(c *gin.Context) {
	if res, err := digitalProductService.FlowerShop(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalProductApi) FlowerShopV2(c *gin.Context) {
	if res, err := digitalProductService.FlowerShopV2(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalProductApi) WebCoinShop(c *gin.Context) {
	if res, err := digitalProductService.WebCoinShop(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalProductApi) SubProductList(c *gin.Context) {
	var reqData request.GetSubListReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := digitalProductService.SubProductListV3(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalProductApi) VoiceProductList(c *gin.Context) {
	var reqData request.AiRoleIdReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := digitalProductService.VoiceProductList(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalProductApi) SVipProductList(c *gin.Context) {
	var reqData request.SVipProductListReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := digitalProductService.SVipProductList(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalProductApi) ChatVipProductList(c *gin.Context) {
	var reqData request.ChatVipProductListReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := digitalProductService.ChatVipProductList(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalProductApi) ContentVipProductList(c *gin.Context) {
	var reqData request.ContentVipProductListReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := digitalProductService.ContentVipProductList(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalProductApi) ClearSubList(c *gin.Context) {
	var reqData request.ClearSubReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := digitalProductService.ClearSubList(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalProductApi) ClearSubListV1(c *gin.Context) {
	var reqData request.ClearSubReq
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := digitalProductService.ClearSubListV1(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}

func (m *CliDigitalProductApi) SVipDialog(c *gin.Context) {
	if res, err := digitalProductService.SVipDialog(c); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else {
		response.OkWithData(res, c)
	}
}
