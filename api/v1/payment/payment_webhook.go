package payment

import (
	"aimsg-server/global"
	"aimsg-server/model/paymentwebhook/request"
	"bytes"
	"io"
	"log"
	"net/http"
	"slices"

	"github.com/gin-gonic/gin"

	"aimsg-server/service"
)

type PaymentWebhookSpeed struct{}

var paymentWebhookSpeedService = service.ServiceGroupApp.PaymentWebhookServiceGroup.PaymentWebhookSpeedService

// SpeedWebhook 处理Speed API的webhook回调
func (sw *PaymentWebhookSpeed) Webhook(c *gin.Context) {
	var err error
	var body []byte
	{
		body, err = io.ReadAll(c.Request.Body)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": "Cannot read request body"})
			return
		}
		c.Request.Body = io.NopCloser(bytes.NewBuffer(body))
	}

	// --- 官方文档第 1 步: 从 Header 中提取信息 ---
	webhookID := c.Request.Header.Get("webhook-id")
	webhookTimestamp := c.Request.Header.Get("webhook-timestamp")
	signatureHeader := c.Request.Header.Get("webhook-signature")

	if webhookID == "" || webhookTimestamp == "" || signatureHeader == "" {
		log.Println("Webhook Error: Missing required headers (webhook-id, webhook-timestamp, or webhook-signature)")
		c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "Missing required headers"})
		return
	}

	if err := paymentWebhookSpeedService.VerifySignature(body, signatureHeader, webhookID, webhookTimestamp); err != nil {
		c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
		return
	}

	var webhook request.SpeedWebhookRequest
	if err := c.ShouldBindJSON(&webhook); err != nil {
		global.LOG.Sugar().Errorf("invalid webhook format: %v", err.Error())
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid webhook format"})
		return
	}

	// 从metadata中提取withdrawal_id
	withdrawalID := webhook.Data.Object.ReferenceID
	if withdrawalID == "" {
		global.LOG.Sugar().Errorf("missing withdrawal_id in webhook metadata")
		c.JSON(http.StatusBadRequest, gin.H{"error": "missing withdrawal_id"})
		return
	}

	if !slices.Contains([]string{
		"withdraw.paid",
		"withdraw.failed",
	}, webhook.EventType) {
		global.LOG.Sugar().Errorf("event not support, %s", webhook.EventType)
		c.JSON(http.StatusBadRequest, gin.H{"error": "event not support"})
		return
	}

	if !slices.Contains([]string{
		"paid",
		"failed",
	}, webhook.Data.Object.Status) {
		global.LOG.Sugar().Errorf("status is not support, %s", webhook.Data.Object.Status)
		c.JSON(http.StatusBadRequest, gin.H{"error": "status is not support"})
		return
	}

	if err := paymentWebhookSpeedService.HandleWebhook(c,
		webhook.Data.Object.ReferenceID,
		webhook.EventType,
		webhook.Data.Object.Status,
		webhook.Data.Object.FailureReason); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true})
}
