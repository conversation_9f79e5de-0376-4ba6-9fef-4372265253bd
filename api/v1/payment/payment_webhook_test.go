package payment

import (
	"aimsg-server/global"
	"bytes"
	"encoding/json"
	"log"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	gormLogger "gorm.io/gorm/logger"
)

func setup() (*gorm.DB, *redis.Client, *zap.Logger) {
	logger := zap.New(zapcore.NewTee(
		zapcore.NewCore(zapcore.NewConsoleEncoder(zap.NewDevelopmentEncoderConfig()), zapcore.AddSync(os.Stdout), zapcore.DebugLevel),
	))
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: gormLogger.Default.LogMode(gormLogger.Info),
	})
	if err != nil {
		log.Fatalf("failed to open database: %v", err)
	}
	db.AutoMigrate(
		&model.BtcWithdrawalRecord{},
		&model.BtcTrade{},
		&model.BtcChannel{},
	)
	redis := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
	})

	return db, redis, logger
}

func TestPaymentWebhookSpeed(t *testing.T) {
	db, redis, logger := setup()
	global.DB = db
	global.REDIS = redis
	global.LOG = logger

	paymentWebhookSpeed := &PaymentWebhookSpeed{}

	// call paymentWebhookSpeed.webhook
	// 创建测试数据
	btcChannel := &model.BtcChannel{
		ChannelCode: "speed",
		ChannelName: "Speed",
		AppId:       "test_app_id",
		AppKey:      "wsec_ij2wwOvkDUuQ+s2UBn2kSQZ5/PTG2kHJ",
	}
	if err := db.Create(btcChannel).Error; err != nil {
		t.Fatalf("创建channel失败: %v", err)
	}

	btcWithdrawal := &model.BtcWithdrawalRecord{
		ID:               1,
		UserID:           1,
		Amount:           "0.001",
		WithdrawalMethod: 2,
		WithdrawalStatus: model.BtcWithdrawStatusReviewSuccess,
	}
	if err := db.Create(btcWithdrawal).Error; err != nil {
		t.Fatalf("创建withdrawal记录失败: %v", err)
	}

	btcTrade := &model.BtcTrade{
		MODEL:          model.MODEL{Id: 1},
		WithdrawalID:   1,
		Amount:         "0.001",
		ChannelOrderNo: "is_mc3h2ltcj42uweJw",
		TradeStatus:    model.BtcTradeStatusPending,
	}
	if err := db.Create(btcTrade).Error; err != nil {
		t.Fatalf("创建trade记录失败: %v", err)
	}

	// 测试webhook处理
	webhookBody := `{"api_version":"2022-10-15","data":{"object":{"amount":38.78410698,"created":1750343071749,"currency":"SATS","est_fees":0,"exchange_rate":1,"failure_reason":"ALREADY_EXISTS: invoice is already paid","id":"wi_mc3h2ltxHWAFXOXN","modified":1750343072700,"object":"withdraw","reference_id":"is_mc3h2ltcj42uweJw","reference_type":"instant_send","speed_fee":{"amount":0,"percentage":0},"statement_descriptor":"17207","status":"failed","target_amount":38,"target_amount_paid":0,"target_currency":"SATS","withdraw_method":"lightning","withdraw_request":"lnbc1p598m2npp5pg9xw79l94cypayvng9u7630368dansk69ed5pvpdgs5dw6wf2sqdqqcqzzsxqyz5vqsp5v7pupxuz8xh900juswmm274g5lnqqg4wf4p5lpmya32k5eqx20wq9p4gqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqpqysgqnkfz2vyjj46zu56mngdfvzgqgcl4umdazvktm5xjvsxz9tmpg9vhk7ner3fzdyhhypnmth7qs020hslfg7y8a73swz0vkrrrz8nc7kcp8ckvlv"}},"event_type":"withdraw.failed","id":"evt_mc3h2ml4EvWH7PWI","livemode":true,"object":"events","request":{"id":"mc3h2lsikotf0DcnDEm"}}`

	w := httptest.NewRecorder()

	ctx, _ := gin.CreateTestContext(w)

	httpReq, _ := http.NewRequest("POST", "/api/v1/payment/webhook/speed", bytes.NewBufferString(webhookBody))
	httpReq.Header.Add("webhook-id", "msg_2yjLXCB3ULC7vc4GvhmJBRPCqPH")
	httpReq.Header.Add("webhook-timestamp", "1750343072")
	httpReq.Header.Add("webhook-signature", "v1,S1pe1hfq9zL2Nwi8U2sN+hhbv4IhVAMzi36PlrxzpRk=")
	ctx.Request = httpReq
	log.Printf("ctx request headers: %+v", httpReq.Header.Values("webhook-timestamp"))
	paymentWebhookSpeed.Webhook(ctx)

	if w.Code != http.StatusOK {
		t.Errorf("期望状态码 %d, 实际得到 %d, body: %s", http.StatusOK, w.Code, w.Body.String())
	}

	updatedBtcWithdrawal := &model.BtcWithdrawalRecord{}
	if err := global.DB.First(updatedBtcWithdrawal, "id = ?", 1).Error; err != nil {
		t.Fatalf("查询withdrawal记录失败: %v", err)
	}
	prettyJson, _ := json.MarshalIndent(updatedBtcWithdrawal, "", "  ")
	t.Logf("updatedBtcWithdrawal: %s", string(prettyJson))

	updatedBtcTrade := &model.BtcTrade{}
	if err := global.DB.First(updatedBtcTrade, "id = ?", 1).Error; err != nil {
		t.Fatalf("查询trade记录失败: %v", err)
	}

	// 打印json数据 pretty
	prettyJson, _ = json.MarshalIndent(updatedBtcTrade, "", "  ")
	t.Logf("updatedBtcTrade: %s", string(prettyJson))
}
