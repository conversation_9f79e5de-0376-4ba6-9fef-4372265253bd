vita:
  url: https://bakbak.cashboxtest.com
#  url: https://api.okjannu.com
autocode:
  server-model: /model/%s
  server-router: /router/%s
  server: /aimsg-server
  server-api: /api/v1/%s
  server-plug: /plugin/%s
  server-initialize: /initialize
  root: /Users/<USER>/code/kxcl/cashbox-ai
  web-table: /view/backend
  web: /aimsg-web/src
  server-service: /service/%s
  server-request: /model/%s/request/
  web-api: /api/backend
  web-form: /view/backend
  transfer-restart: true
aws-s3:
  bucket: cashbox-ai
  region: us-west-2
  endpoint: ""
  secret-id: ********************
  secret-key: pQKuk9WRNdgwWf6WF3HPISNd6aGsbWBhGxTI5aMw
  base-url: https://images.aipersona.cloud/
  path-prefix: aimsg-server
  s3-force-path-style: false
  disable-ssl: false
captcha:
  key-long: 4
  img-width: 240
  img-height: 80
  open-captcha: 0
  open-captcha-timeout: 3600
cors:
  mode: strict-whitelist
  whitelist:
    - allow-origin: example1.com
      allow-methods: POST, GET
      allow-headers: Content-Type,AccessToken,X-CSRF-Token, Authorization, Token,X-Token,X-User-Id
      expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
      allow-credentials: true
    - allow-origin: example2.com
      allow-methods: GET, POST
      allow-headers: content-type
      expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
      allow-credentials: true
db-list:
  - type: ""
    alias-name: ""
    prefix: ""
    port: ""
    config: ""
    db-name: ""
    username: ""
    password: ""
    path: ""
    engine: ""
    log-mode: ""
    max-idle-conns: 10
    max-open-conns: 100
    singular: false
    log-zap: false
    disable: true
dd_notify:
  token: "b79580c71f6218e15c17074a831526fad8dda07bec89ea3d9bf6948cd77629e9"
  secret: "SEC5a41acfb5b0409f0fc589c3d2ed01b02a7dfc0be246fe3e80070354b135fa1f1"
  payout-bot-id: "89ef3581-b2ee-4768-b4d0-bea91901efef"
diy_dd:
  token: "b79580c71f6218e15c17074a831526fad8dda07bec89ea3d9bf6948cd77629e9"
  secret: "SEC5a41acfb5b0409f0fc589c3d2ed01b02a7dfc0be246fe3e80070354b135fa1f1"
excel:
  dir: ./resource/excel/
jwt:
  signing-key: IvQHEpQDKaFAnJGbwVcJN1hrXXkrRCZJuWCqWvHprjhWlgZX4tugbDns55KRUejN
  expires-time: 30d
  buffer-time: 1d
  issuer: qmPlus
local:
  path: uploads/file
  store-path: uploads/file
mongo:
  coll: msg_history
  options: "directConnection=true"
  database: ai-msg
  username: root
  password: W3whq!Qx
  auth-source: ""
  min-pool-size: 0
  max-pool-size: 100
  socket-timeout-ms: 0
  connect-timeout-ms: 0
  is-zap: true
  hosts:
    - host: sg-test-openresty-1-eb1df4be24f9a155.elb.ap-southeast-1.amazonaws.com
      port: 27017
mongoLine:
  coll: "msg_history_new"
  options: ""
  database: "ai-msg"
  username: "cashbox"
  password: "cashbox"
  auth-source: ""
  min-pool-size: 0
  max-pool-size: 100
  socket-timeout-ms: 0
  connect-timeout-ms: 0
  is-zap: false
  hosts:
    - host: "127.0.0.1"
      port: "27016"
mysql:
  prefix: ""
  port: "3306"
  config: charset=utf8mb4&parseTime=True&loc=Local
  db-name: ai_cloud
  username: root
  password: Ty_2mzgheUsv
  path: test-mysql.ctcgwg6ow6ky.ap-southeast-1.rds.amazonaws.com
  engine: ""
  log-mode: info
  max-idle-conns: 10
  max-open-conns: 100
  singular: false
  log-zap: true
mysqlLine:
  prefix: ""
  port: 7781
  config: charset=utf8mb4&parseTime=True&loc=Local
  db-name: ai_cloud
  username: admin
  password: DuA9tSic0xrxlRpabD8Q
  path: 127.0.0.1
  engine: ""
  log-mode: info
  max-idle-conns: 10
  max-open-conns: 100
  singular: false
  log-zap: true
pgsql:
  prefix: ""
  port: ""
  config: ""
  db-name: ""
  username: ""
  password: ""
  path: ""
  engine: ""
  log-mode: ""
  max-idle-conns: 10
  max-open-conns: 100
  singular: false
  log-zap: false
redis:
  addr: *************:6379
  password: "test2NCGMtBf"
  db: 0
#redis:
#  addr: **********:6379
#  password: ""
#  db: 0
system:
  env: dev
  name: "Cloud-Server"
  url_prefix: https://aimsg-server-test.cashboxtest.com/api
  db-type: mysql
  oss-type: local
  router-prefix: "api"
  addr: 8400
  iplimit-count: 15000
  iplimit-time: 3600
  use-multipoint: false
  use-redis: false
  use-mongo: false
zap:
  level: info
  prefix: '[aimsg-server]'
  format: console
  director: log
  encode-level: LowercaseColorLevelEncoder
  stacktrace-key: stacktrace
  max-age: 0
  show-line: true
  log-in-console: true
kafka:
  host:
    - **********:9092
    - **********:9092
    - **********:9092
ab_test:
  fix_hash_rate_register_from:  1953254800

# Speed API 余额监控配置
speed_api:
  enabled: true                                                    # 是否启用监控
  base_url: "https://api.tryspeed.com"                            # Speed API 基础 URL
  authorization: "Basic c2tfdGVzdF9tYmthamk2bVhGTjV1Q29IbWJrYWptbnZtZ25rNWF2YW1ia2FqbW53S2Ztc3pDRlY6"  # API 授权头
  threshold: 100000                                                # 余额阈值，单位：SATS (10万)
  feishu_bot_id: "your-feishu-bot-id-here"                       # 飞书机器人 ID
