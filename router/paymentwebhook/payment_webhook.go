package paymentwebhook

import (
	v1 "aimsg-server/api/v1"

	"github.com/gin-gonic/gin"
)

type PaymentWebhookRouter struct{}

func (s *PaymentWebhookRouter) InitPaymentWebhookSpeedRouter(Router *gin.RouterGroup) {
	webhookRouter := Router.Group("webhook")
	var paymentWebhookSpeedApi = v1.ApiGroupApp.PaymentWebhookGroup.PaymentWebhookSpeed
	webhookRouter.POST("speed", paymentWebhookSpeedApi.Webhook)
}
