package cli

import (
	"aimsg-server/api/v1"
	"github.com/gin-gonic/gin"
)

type CliAppRouter struct{}

func (s *CliAppRouter) InitCliAppPubRouter(Router *gin.RouterGroup) {
	var (
		r   = Router.Group("App")
		api = v1.ApiGroupApp.CliApiGroup.CliAppApi
	)
	r.GET("Config", api.AppConfig)
	r.GET("IconConfig", api.IconConfig)
	r.GET("BannerList", api.BannerList)
	r.GET("ImgDemoList", api.ImgDemoList)
}
