package cli

import (
	v1 "aimsg-server/api/v1"

	"github.com/gin-gonic/gin"
)

type BtcRouter struct{}

func (s *CliDigitalProductRouter) InitBtcRouter(Router *gin.RouterGroup) {
	var (
		r         = Router.Group("btc")
		CliBtcApi = v1.ApiGroupApp.CliApiGroup.CliBtcApi
	)
	r.GET("index", CliBtcApi.Index)
	r.GET("productDetails", CliBtcApi.ProductDetails)
	r.GET("myNFT", CliBtcApi.MyNFT)
	r.GET("incomeDetails", CliBtcApi.IncomeDetails)
	r.GET("wallet", CliBtcApi.Wallet)
	r.GET("withdraw", CliBtcApi.Withdraw)
	r.POST("cashOut", CliBtcApi.CashOut)
	r.POST("claimDailyRewards", CliBtcApi.ClaimDailyRewards)
	r.GET("getRateByUser", CliBtcApi.GetRateByUser)
	r.GET("getTotalByUser", CliBtcApi.GetTotalByUser)
	r.GET("selectNetwork", CliBtcApi.SelectNetwork)
	r.GET("ADDetail", CliBtcApi.ADDetail)
	r.GET("SignDetail", CliBtcApi.SignDetail)
	r.GET("GiftPack", CliBtcApi.GiftPack)
}
