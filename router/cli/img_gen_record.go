package cli

import (
	v1 "aimsg-server/api/v1"
	"github.com/gin-gonic/gin"
)

type CliImgGenRecordRouter struct{}

func (s *CliImgGenRecordRouter) InitCliImgGenRecordRouter(Router *gin.RouterGroup) {
	var (
		r   = Router.Group("CliImgGenRecord")
		api = v1.ApiGroupApp.CliApiGroup.CliImgGenRecordApi
	)
	r.GET("RemainingGenCount", api.RemainingGenCount)
	r.POST("GenHistory", api.GenHistory)
	r.POST("GenImgTask", api.GenImgTask)
}
