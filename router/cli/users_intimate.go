package cli

import (
	v1 "aimsg-server/api/v1"
	"github.com/gin-gonic/gin"
)

type CliUsersIntimateRouter struct{}

func (s *CliUsersIntimateRouter) InitCliUsersIntimateRouter(Router *gin.RouterGroup) {
	var (
		r   = Router.Group("UsersIntimate")
		api = v1.ApiGroupApp.CliApiGroup.CliUsersIntimateApi
	)
	r.GET("Intimate", api.Intimate)
	r.GET("IntimateLevelList", api.IntimateLevelList)
	r.GET("IntimateUpgradeDialog", api.IntimateUpgradeDialog)
}
