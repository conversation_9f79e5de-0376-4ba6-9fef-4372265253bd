package cli

import (
	v1 "aimsg-server/api/v1"
	"github.com/gin-gonic/gin"
)

type CliVoiceCallRouter struct{}

func (s *CliVoiceCallRouter) InitCliVoiceCallRouter(Router *gin.RouterGroup) {
	var (
		r   = Router.Group("CliVoiceCall")
		api = v1.ApiGroupApp.CliApiGroup.CliVoiceCallApi
	)
	r.POST("CreateVoiceCall", api.CreateVoiceCall)
	r.POST("VoiceCallDeduction", api.VoiceCallDeduction)
	r.POST("VoiceCallMsg", api.VoiceCallMsg)
}
