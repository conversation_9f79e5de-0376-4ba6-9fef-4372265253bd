package cli

import (
	v1 "aimsg-server/api/v1"
	"aimsg-server/middleware"

	"github.com/gin-gonic/gin"
)

type CallBackRouter struct{}

func (s *CallBackRouter) InitCallBackRouter(Router *gin.RouterGroup) {
	var (
		r   = Router.Group("Callback").Use(middleware.ReqMiddleware())
		api = v1.ApiGroupApp.CliApiGroup.CallbackApi
	)
	r.POST("PayCenter", api.PayCenter)
	r.POST("PayCenterSub", api.PayCenterSub)
	r.POST("VideoSimplifyCallBack", api.VideoSimplifyCallBack)
}
