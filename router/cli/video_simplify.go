package cli

import (
	v1 "aimsg-server/api/v1"

	"github.com/gin-gonic/gin"
)

type VideoSimplifyImgToVideoRouter struct{}

func (s *VideoSimplifyImgToVideoRouter) InitVideoSimplifyImgToVideoRouter(Router *gin.RouterGroup) {
	var (
		r           = Router.Group("video")
		ImgVideoApi = v1.ApiGroupApp.CliApiGroup.VideoSimplifyImgVideoApi
	)
	r.GET("bannerList", ImgVideoApi.BannerList)
	r.GET("videoTemplateList", ImgVideoApi.VideoTemplateList)
	r.GET("userVideoList", ImgVideoApi.UserVideoList)
	r.POST("userCreateVideo", ImgVideoApi.UserCreateVideo)
	r.GET("createIndex", ImgVideoApi.CreateIndex)
	r.GET("getConsumeList", ImgVideoApi.GetConsumeRecordList)
	r.GET("vipList", ImgVideoApi.VipList)
	r.GET("flowerShop", ImgVideoApi.FlowerShop)
	r.GET("templateInfo", ImgVideoApi.TemplateInfo)
	r.GET("userVideoInfo", ImgVideoApi.UserVideoInfo)
	r.GET("VideoSimplifyConsumeMsg", ImgVideoApi.VideoSimplifyConsumeMsg)
	r.GET("GetApiTaskStatus", ImgVideoApi.GetApiTaskStatus)
	r.GET("VideoSimplifyUploadVideoConsumeMsg", ImgVideoApi.VideoSimplifyUploadVideoConsumeMsg)
	r.POST("againCreateVideo", ImgVideoApi.AgainCreateVideo)
	r.POST("deleteUserVideo", ImgVideoApi.DeleteUserVideo)
	r.GET("userMsgList", ImgVideoApi.UserMsgList)
	r.GET("userMsgNotify", ImgVideoApi.UserMsgNotify)
	r.POST("userViewRecord", ImgVideoApi.UserViewRecord)
	r.POST("VideoSimplifyCallBack", ImgVideoApi.VideoSimplifyCallBack)

}
