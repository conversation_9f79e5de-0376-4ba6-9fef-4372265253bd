package cli

import (
	v1 "aimsg-server/api/v1"
	"github.com/gin-gonic/gin"
)

type CliDiyPhotoTaskRouter struct{}

func (s *CliDiyPhotoTaskRouter) InitCliDiyPhotoTaskRouter(Router *gin.RouterGroup) {
	var (
		r   = Router.Group("CliDiyPhotoTask")
		api = v1.ApiGroupApp.CliApiGroup.CliDiyPhotoTaskApi
	)
	r.GET("GetDiyConfig", api.GetDiyConfig)
	r.POST("CreateDiyPhotoTask", api.CreateDiyPhotoTask)
	r.GET("DiyPhotoTaskHistory", api.DiyPhotoTaskHistory)
}
