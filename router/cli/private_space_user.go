package cli

import (
	v1 "aimsg-server/api/v1"
	"github.com/gin-gonic/gin"
)

type CliPrivateSpaceUserRouter struct{}

func (s *CliPrivateSpaceUserRouter) InitCliPrivateSpaceUserRouter(Router *gin.RouterGroup) {
	var (
		r   = Router.Group("CliPrivateSpaceUser")
		api = v1.ApiGroupApp.CliApiGroup.CliPrivateSpaceUserApi
	)
	r.GET("UserPrivateSpaceList", api.UserPrivateSpaceList)
	r.POST("UnlockPrivateSpace", api.UnlockPrivateSpace)
}
