package cli

import (
	v1 "aimsg-server/api/v1"
	"aimsg-server/middleware"

	"github.com/gin-gonic/gin"
)

type CliVideoCallRouter struct{}

func (s *CliVideoCallRouter) InitCliVideoCallPubRouter(Router *gin.RouterGroup) {
	var (
		r   = Router.Group("CliVideoCall").Use(middleware.ReqMiddleware())
		api = v1.ApiGroupApp.CliApiGroup.CliVideoCallApi
	)
	r.POST("Connected", api.Connected)
	r.POST("HangUp", api.HangUp)
	r.POST("Msg", api.Msg)
}

func (s *CliVideoCallRouter) InitCliVideoCallPrivateRouter(Router *gin.RouterGroup) {
	var (
		r   = Router.Group("CliVideoCall")
		api = v1.ApiGroupApp.CliApiGroup.CliVideoCallApi
	)
	r.POST("GetRealAnchorInfo", api.GetRealAnchorInfo)
	r.POST("CallStart", api.CallStart)
	r.POST("CallCancel", api.CallCancel)
	r.POST("CallClose", api.CallClose)
	r.POST("CallDeduction", api.CallDeduction)
}
