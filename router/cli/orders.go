package cli

import (
	v1 "aimsg-server/api/v1"
	"aimsg-server/middleware"

	"github.com/gin-gonic/gin"
)

type CliOrdersRouter struct{}

func (s *CliOrdersRouter) InitOrdersPubRouter(Router *gin.RouterGroup) {
	var (
		r   = Router.Group("Orders").Use(middleware.ReqMiddleware())
		api = v1.ApiGroupApp.CliApiGroup.CliOrdersApi
	)
	r.POST("AppleNotify", api.AppleNotify)
	r.POST("GoogleNotify", api.GoogleNotify)
}

func (s *CliOrdersRouter) InitOrdersDemoRouter(Router *gin.RouterGroup) {
	var (
		r   = Router.Group("OrdersDemo").Use(middleware.ReqMiddleware())
		api = v1.ApiGroupApp.CliApiGroup.CliOrdersApi
	)
	r.POST("GoogleNotifyDemo", api.GoogleNotifyDemo)
}

func (s *CliOrdersRouter) InitOrdersResPubRouter(Router *gin.RouterGroup) {
	var (
		r   = Router.Group("Orders")
		api = v1.ApiGroupApp.CliApiGroup.CliOrdersApi
	)
	r.GET("PayResult", api.PayResult)
}

func (s *CliOrdersRouter) InitCliOrdersRouter(Router *gin.RouterGroup) {
	var (
		r   = Router.Group("Orders").Use(middleware.ReqMiddleware())
		api = v1.ApiGroupApp.CliApiGroup.CliOrdersApi
	)
	r.POST("OrderPay", api.OrderPay)
	r.POST("GoogleQuery", api.GoogleQuery)
	r.POST("AppleQuery", api.AppleQuery)
	r.POST("PayMethodList", api.PayMethodList)
	r.POST("SubPayMethodList", api.SubPayMethodList)
	r.POST("WebMethodList", api.WebMethodList)
}

func (s *CliOrdersRouter) InitCliOrdersPythonRouter(Router *gin.RouterGroup) {
	var (
		r   = Router.Group("Orders").Use(middleware.ReqMiddleware())
		api = v1.ApiGroupApp.CliApiGroup.CliOrdersApi
	)
	r.POST("OrderPay", api.OrderPay)
	r.POST("GoogleQuery", api.GoogleQuery)
	r.POST("AppleQuery", api.AppleQuery)
}
