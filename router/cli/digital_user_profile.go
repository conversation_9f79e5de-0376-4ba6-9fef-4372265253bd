package cli

import (
	v1 "aimsg-server/api/v1"

	"github.com/gin-gonic/gin"
)

type CliDigitalUserProfileRouter struct{}

func (s *CliDigitalUserRouter) InitCliDigitalUserProfileRouter(Router *gin.RouterGroup) {
	var (
		r   = Router.Group("DigitalUserProfile")
		api = v1.ApiGroupApp.CliApiGroup.CliDigitalUserProfileApi
	)
	r.PUT("SetNSFW", api.SetNSFW)
	r.POST("AddWatchAdMsgCount", api.AddWatchAdMsgCount)
}
