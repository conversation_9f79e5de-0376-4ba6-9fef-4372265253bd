package cli

import (
	v1 "aimsg-server/api/v1"
	"aimsg-server/api/v1/cli/user"
	"aimsg-server/middleware"

	"github.com/gin-gonic/gin"
)

type CliDigitalUserRouter struct{}

func (s *CliDigitalUserRouter) InitCliDigitalUserPubRouter(Router *gin.RouterGroup) {
	var (
		r       = Router.Group("Users").Use(middleware.CliHeader())
		userApi = v1.ApiGroupApp.CliApiGroup.CliDigitalUserApi
	)
	r.POST("Login", userApi.SocialLogin)
	r.POST("Register", userApi.Register)
	r.POST("FcmToken", userApi.FcmToken)
}

func (s *CliDigitalUserRouter) InitCliDigitalUserFileRouter(Router *gin.RouterGroup) {
	// /DigitalModel/Digital/
	var (
		r       = Router.Group("Users").Use(middleware.CliHeader()).Use(middleware.JWTCliAuth())
		r2      = Router.Group("DigitalModel").Use(middleware.CliHeader()).Use(middleware.JWTCliAuth())
		s3Api   = v1.ApiGroupApp.CliApiGroup.CliS3Api
		userApi = v1.ApiGroupApp.CliApiGroup.CliDigitalUserApi
	)
	r.POST("UploadFileUrl", s3Api.S3UploadFileUrlOrigin)
	r.GET("UserFlowerAmount", userApi.UserFlowerAmount)
	r.GET("AiRoleMediaList", userApi.AiRoleMediaList)
	r2.GET("Digital/", userApi.Digital)
}

func (s *CliDigitalUserRouter) InitCliDigitalUserRouter(Router *gin.RouterGroup) {
	var (
		r       = Router.Group("Users")
		userApi = v1.ApiGroupApp.CliApiGroup.CliDigitalUserApi
	)
	r.POST("CompleteUserInfo", userApi.CompleteUserInfo)
	r.GET("UserInfo", userApi.UserInfo)
	r.GET("RtmToken", userApi.RtmToken)
	r.GET("ReceiveFlowersDialog", userApi.ReceiveFlowersDialog)
	r.GET("ReceiveFlowersDialogV2", userApi.ReceiveFlowersDialogV2)
	r.GET("ReceiveFlowersDialogV3", userApi.ReceiveFlowersDialogV3)
	r.GET("ReviewFlowers", userApi.ReviewFlowers)
	r.GET("ReceiveFlowersVideoSimplify", userApi.ReceiveFlowersVideoSimplify)
	r.GET("ReceiveFlowersCoins", userApi.ReceiveFlowersCoins)
	r.GET("ReceiveFlowers", userApi.ReceiveFlowers)
	r.GET("ReceiveAdFlowers", userApi.ReceiveAdFlowers)
	r.GET("signInList", userApi.SignInList)
	r.POST("SendMsg", userApi.SendMsg)
	r.GET("GetAiFuncSwitch", userApi.GetAiFuncSwitch)
	r.POST("GetAiFuncContent", userApi.GetAiFuncContent)
	r.GET("ChatGuideListV2", userApi.ChatGuideListV2)
	r.GET("GetAiFirstVoice", userApi.GetAiFirstVoice)
	r.POST("UnlockData", userApi.UnlockData)
	r.POST("GenMsgVoice", userApi.GenMsgVoice)
	r.POST("GetAsmrVoice", userApi.GetAsmrVoice)
	r.POST("getAiFirstMsg", userApi.GetAiFirstMsg)
	r.POST("UnlockVideo", userApi.UnlockVideo)
	r.POST("ResetChat", userApi.ResetChat)
	r.POST("AdjustIdData", userApi.AdjustIdData)
	r.DELETE("UserDel", userApi.UserDel)
	r.POST("SetNickname", userApi.SetNickname)
	r.POST("ChangePassword", userApi.ChangePassword)
	r.POST("SetUsername", userApi.SetUsername)
	r.POST("WebUnlockGame", userApi.WebUnlockGame)
	r.GET("AiRoleTypeList", userApi.AiRoleTypeList)
	r.GET("FansTypeList", userApi.FansTypeList)
	r.POST("CloseFlowerDialog", userApi.CloseFlowerDialog)
	r.POST("CloseChatDialog", userApi.CloseChatDialog)
	r.GET("LowFlowersDialog", userApi.LowFlowersDialog)
	r.GET("GenAiRolePreInfo", userApi.GenAiRolePreInfo)
	r.POST("GenAiRole", userApi.GenAiRole)
	r.POST("AiRoleFaceImgCheck", userApi.AiRoleFaceImgCheck)
	r.GET("ReviewDialogInfo", userApi.ReviewDialogInfo)
	r.GET("AiRoleMediaList", userApi.AiRoleMediaList)
	r.GET("UserFlowerAmount", userApi.UserFlowerAmount)
	r.POST("SaveFeedbackRecord", userApi.SaveFeedbackRecord)
	r.POST("UploadFileUrl", userApi.UploadFileUrl)
	r.GET("VideoTxtList", userApi.VideoTxtList)
	r.POST("GenVideoTask", userApi.GenVideoTask)
	r.GET("GenVideoHistory", userApi.GenVideoHistory)
	r.POST("ReceiveAiRoleFlowers", userApi.ReceiveAiRoleFlowers)
	r.GET("LiveConfig", userApi.LiveConfig)
	r.GET("LiveAiRoleRank", userApi.LiveAiRoleRank)
	r.GET("LiveAiRoleRankV2", userApi.LiveAiRoleRankV2)
	r.GET("LiveRandomConfig", userApi.LiveRandomConfig)
	r.POST("CreateLiveInfo", userApi.CreateLiveInfo)
	r.POST("UpdateLiveRank", userApi.UpdateLiveRank)
	r.POST("UpdateLiveInfo", userApi.UpdateLiveInfo)
	r.GET("GetFirebaseReport", userApi.GetFirebaseReport)
	r.GET("GetFirebaseReportEventList", userApi.GetFirebaseReportEventList)
	r.GET("UserQuickReply", userApi.UserQuickReply)
	r.POST("UserQuickReplyCharging", userApi.UserQuickReplyCharging) // 12-31 增加快捷回复消息下发付费
	r.GET("ImgBlindBox", userApi.ImgBlindBox)
	r.POST("FinishFirebaseReportEvent", userApi.FinishFirebaseReportEvent)
	r.GET("reward/info", user.RewardCon.Info)
}
