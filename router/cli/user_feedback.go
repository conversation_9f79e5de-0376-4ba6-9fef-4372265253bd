package cli

import (
	v1 "aimsg-server/api/v1"

	"github.com/gin-gonic/gin"
)

type UserFeedbackRouter struct{}

func (s *UserFeedbackRouter) InitUserFeedbackRouter(Router *gin.RouterGroup) {
	var (
		r               = Router.Group("user_feedback")
		UserFeedbackApi = v1.ApiGroupApp.CliApiGroup.UserFeedbackApi
	)
	r.GET("Test", UserFeedbackApi.Test)
	r.GET("getAIRobotInfoByAppId", UserFeedbackApi.GetAIRobotInfoByAppId)
	r.GET("getFeedbackList", UserFeedbackApi.GetFeedbackList)
	r.POST("sendMsg", UserFeedbackApi.SendMsg)
	r.POST("reply", UserFeedbackApi.Reply)
	r.POST("setUserEmail", UserFeedbackApi.SetUserEmail)
	r.GET("GetUserFeedbackList", UserFeedbackApi.GetUserFeedbackList)
}
