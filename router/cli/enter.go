package cli

import (
	"aimsg-server/middleware"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
)

type RouterGroup struct {
	CliVideoCallRouter
	CliVoiceInfoRouter
	CliPrivateSpaceRouter
	CliPrivateSpaceUserRouter
	AgoraRouter
	CallBackRouter
	CliVoiceCallRouter
	CliImgGenRecordRouter
	CliImgTemplateRecordRouter
	CliS3Router
	CliAiProfileInfoRouter
	CliUserBadgeRouter
	CliDiyPhotoTaskRouter
	CliGiftRouter
	CliUserUseScenesRouter
	CliAiScenesRouter
	CliAppRouter
	CliAiRoleRouter
	CliUsersIntimateRouter
	CliDigitalUserProfileRouter
	CliDigitalUserRouter
	CliDigitalProductRouter
	CliOrdersRouter
	CliSystemRouter
	VideoSimplifyImgToVideoRouter
	UserFeedbackRouter
	Msg<PERSON><PERSON>
}

func (r RouterGroup) InitCliRouter(PublicGroup *gin.RouterGroup, appInfo model.App) {
	// APP特定路由前缀
	appPrefix := ""
	if appInfo.Prefix != "" {
		appPrefix = appInfo.Prefix
	}
	CliPublicGroup := PublicGroup.Group(appPrefix)
	if appInfo.Prefix != "" {
		CliPublicGroup.Use(middleware.AppReqMiddleware(appInfo))
	}
	CliPublicGroup.Use(middleware.CliHeader())

	CliGroup := CliPublicGroup.Group("")
	CliGroup.Use(middleware.JWTCliAuth())

	// appInfo可能会为空,注意判断
	r.InitCliVideoCallPrivateRouter(CliGroup)
	r.InitCliPrivateSpaceRouter(CliGroup)
	r.InitCliPrivateSpaceUserRouter(CliGroup)
	r.InitCliVoiceInfoRouter(CliGroup)
	r.InitCliVoiceCallRouter(CliGroup)
	r.InitCliImgGenRecordRouter(CliGroup)
	r.InitCliImgTemplateRecordRouter(CliGroup)
	r.InitCliS3PubRouter(CliPublicGroup)
	r.InitCliAiProfileInfoRouter(CliGroup)
	r.InitCliDigitalUserPubRouter(CliPublicGroup)
	r.InitCliAiRolePubRouter(CliPublicGroup)
	r.InitCliProductPubRouter(CliPublicGroup)
	r.InitCliAppPubRouter(CliPublicGroup)
	r.InitCliUserBadgeRouter(CliGroup)
	r.InitCliDiyPhotoTaskRouter(CliGroup)
	r.InitCliGiftRouter(CliGroup)
	r.InitCliUserUseScenesRouter(CliGroup)
	r.InitCliAiScenesRouter(CliGroup)
	r.InitCliAiRoleRouter(CliGroup)
	r.InitCliDigitalUserRouter(CliGroup)
	r.InitCliDigitalProductRouter(CliGroup)
	r.InitVideoSimplifyImgToVideoRouter(CliGroup)
	r.InitUserFeedbackRouter(CliGroup)
	r.InitCliOrdersRouter(CliGroup)
	r.InitCliUsersIntimateRouter(CliGroup)
	r.InitCliDigitalUserProfileRouter(CliGroup)

	r.InitBtcRouter(CliGroup)
	r.InitMsgRouter(CliGroup)
}
