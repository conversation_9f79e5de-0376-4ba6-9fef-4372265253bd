package cli

import (
	v1 "aimsg-server/api/v1"

	"github.com/gin-gonic/gin"
)

type CliDigitalProductRouter struct{}

func (s *CliDigitalUserRouter) InitCliProductPubRouter(Router *gin.RouterGroup) {
	var (
		r   = Router.Group("product")
		api = v1.ApiGroupApp.CliApiGroup.CliDigitalProductApi
	)
	r.GET("WebCoinShop", api.WebCoinShop)
}

func (s *CliDigitalProductRouter) InitCliDigitalProductRouter(Router *gin.RouterGroup) {
	var (
		r          = Router.Group("product")
		productApi = v1.ApiGroupApp.CliApiGroup.CliDigitalProductApi
	)

	r.GET("ChatProductList", productApi.ChatProductList)
	r.GET("FlowerProductList", productApi.FlowerProductList)
	r.GET("FlowerShop", productApi.FlowerShop)
	r.GET("FlowerShopV2", productApi.FlowerShopV2)
	r.GET("VoiceProductList", productApi.VoiceProductList)
	r.GET("SVipDialog", productApi.SVipDialog)
	r.GET("SVipProductList", productApi.SVipProductList)
	r.GET("subList", productApi.SubProductList)
	r.GET("ChatVipProductList", productApi.ChatVipProductList)
	r.GET("ContentVipProductList", productApi.ContentVipProductList)
	r.GET("ClearSubList", productApi.ClearSubList)
	r.GET("ClearSubListV1", productApi.ClearSubListV1)

}
