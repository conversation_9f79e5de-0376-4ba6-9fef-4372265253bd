package cli

import (
	v1 "aimsg-server/api/v1"

	"github.com/gin-gonic/gin"
)

type CliAiRoleRouter struct{}

func (s *CliAiRoleRouter) InitCliAiRolePubRouter(Router *gin.RouterGroup) {
	var (
		r       = Router.Group("AiRolePub")
		api     = v1.ApiGroupApp.CliApiGroup.CliAiRoleApi
		userApi = v1.ApiGroupApp.CliApiGroup.CliDigitalUserApi
	)
	r.GET("AiRoleTypeListPub", userApi.AiRoleTypeListPub)
	r.GET("AiRoleListPub", api.AiRoleListPub)
}

func (s *CliAiRoleRouter) InitCliAiRoleRouter(Router *gin.RouterGroup) {
	var (
		r          = Router.Group("AiRole")
		api        = v1.ApiGroupApp.CliApiGroup.CliAiRoleApi
		contactApi = v1.ApiGroupApp.CliApiGroup.CliAiContactInfoApi
		bannerApi  = v1.ApiGroupApp.CliApiGroup.CliAppPageBannerApi
	)
	r.GET("AppPageBanner", bannerApi.GetAppPageBanner)
	r.GET("AiRoleInfo", api.AiRoleInfo)
	r.GET("AiRoleListV2", api.AiRoleListV2)
	r.GET("AiRoleListV3", api.AiRoleListV3)
	r.GET("AiFansList", api.AiFansList)
	r.GET("Profile", api.Profile)
	r.POST("UndressImg", api.UndressImg)
	r.POST("UnlockProfileContent", api.UnlockProfileContent)
	r.POST("UnlockAiContactInfo", contactApi.UnlockAiContactInfo)
	r.DELETE("DelAiRole", api.DelAiRole)
	r.POST("RequireImgSubMsg", api.RequireImgSubMsg)
}
