package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type ProductRouter struct{}

func (s *ProductRouter) InitProductRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("product").Use(middleware.OperationRecord())
	aRouter := Router.Group("product")
	var productApi = v1.ApiGroupApp.BackendApiGroup.ProductApi
	opRouter.POST("Create", productApi.Create)
	opRouter.DELETE("DeleteById", productApi.DeleteById)
	opRouter.DELETE("DeleteByIds", productApi.DeleteByIds)
	opRouter.PUT("Update", productApi.Update)

	aRouter.GET("GetById", productApi.GetById)
	aRouter.GET("GetAll", productApi.GetAll)
	aRouter.GET("GetList", productApi.GetList)
	aRouter.GET("ProductTypeMap", productApi.ProductTypeMap)
}
