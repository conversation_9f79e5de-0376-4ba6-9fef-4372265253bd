package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type DigitalUserRouter struct{}

func (s *DigitalUserRouter) InitDigitalUserRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("digitalUser").Use(middleware.OperationRecord())
	aRouter := Router.Group("digitalUser")
	var digitalUserApi = v1.ApiGroupApp.BackendApiGroup.DigitalUserApi
	opRouter.POST("Create", digitalUserApi.Create)
	opRouter.DELETE("DeleteById", digitalUserApi.DeleteById)
	opRouter.DELETE("DeleteByIds", digitalUserApi.DeleteByIds)
	opRouter.PUT("Update", digitalUserApi.Update)

	aRouter.GET("GetById", digitalUserApi.GetById)
	aRouter.GET("GetTotal", digitalUserApi.GetTotal)
	aRouter.GET("GetList", digitalUserApi.GetList)
}

func (s *DigitalUserRouter) InitDigitalUserPubRouter(Router *gin.RouterGroup) {
	aRouter := Router.Group("digitalUser")
	var digitalUserApi = v1.ApiGroupApp.BackendApiGroup.DigitalUserApi
	aRouter.POST("SetContinuousActiveDay", digitalUserApi.SetContinuousActiveDay)
}
