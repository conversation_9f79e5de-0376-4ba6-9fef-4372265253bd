package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type AiScenesRouter struct{}

func (s *AiScenesRouter) InitAiScenesRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("aiScenes").Use(middleware.OperationRecord())
	aRouter := Router.Group("aiScenes")
	var aiScenesApi = v1.ApiGroupApp.BackendApiGroup.AiScenesApi
	opRouter.POST("Create", aiScenesApi.Create)
	opRouter.DELETE("DeleteById", aiScenesApi.DeleteById)
	opRouter.DELETE("DeleteByIds", aiScenesApi.DeleteByIds)
	opRouter.PUT("Update", aiScenesApi.Update)

	aRouter.GET("GetById", aiScenesApi.GetById)
	aRouter.GET("GetAll", aiScenesApi.GetAll)
	aRouter.GET("GetList", aiScenesApi.GetList)
}
