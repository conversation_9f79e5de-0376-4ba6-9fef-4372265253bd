package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type MsgJobRouter struct{}

func (s *MsgJobRouter) InitMsgJobRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("msgJob").Use(middleware.OperationRecord())
	aRouter := Router.Group("msgJob")
	var msgJobApi = v1.ApiGroupApp.BackendApiGroup.MsgJobApi
	opRouter.POST("Create", msgJobApi.Create)
	opRouter.DELETE("DeleteById", msgJobApi.DeleteById)
	opRouter.DELETE("DeleteByIds", msgJobApi.DeleteByIds)
	opRouter.PUT("Update", msgJobApi.Update)
	opRouter.POST("SendJumpMsg", msgJobApi.SendJumpMsg)

	aRouter.GET("GetById", msgJobApi.GetById)
	aRouter.GET("GetAll", msgJobApi.GetAll)
	aRouter.GET("GetList", msgJobApi.GetList)
	aRouter.GET("GetMsgQueueCount", msgJobApi.GetMsgQueueCount)
}
