package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type AiSendMediaRouter struct{}

func (s *AiSendMediaRouter) InitAiSendMediaRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("aiSendMedia").Use(middleware.OperationRecord())
	aRouter := Router.Group("aiSendMedia")
	var aiSendMediaApi = v1.ApiGroupApp.BackendApiGroup.AiSendMediaApi
	opRouter.POST("Create", aiSendMediaApi.Create)
	opRouter.DELETE("DeleteById", aiSendMediaApi.DeleteById)
	opRouter.DELETE("DeleteByIds", aiSendMediaApi.DeleteByIds)
	opRouter.PUT("Update", aiSendMediaApi.Update)

	aRouter.GET("GetById", aiSendMediaApi.GetById)
	aRouter.GET("GetAll", aiSendMediaApi.GetAll)
	aRouter.GET("GetList", aiSendMediaApi.GetList)
}
