package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type AiRoleRouter struct{}

func (s *AiRoleRouter) InitAiRoleRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("aiRole").Use(middleware.OperationRecord())
	aRouter := Router.Group("aiRole")
	var aiRoleApi = v1.ApiGroupApp.BackendApiGroup.AiRoleApi
	opRouter.POST("Create", aiRoleApi.Create)
	opRouter.DELETE("DeleteById", aiRoleApi.DeleteById)
	opRouter.DELETE("DeleteByIds", aiRoleApi.DeleteByIds)
	opRouter.PUT("Update", aiRoleApi.Update)

	aRouter.GET("GetById", aiRoleApi.GetById)
	aRouter.GET("GetAll", aiRoleApi.GetAll)
	aRouter.GET("GetList", aiRoleApi.GetList)
	aRouter.GET("ChangeSort", aiRoleApi.ChangeSort)
}
