package backend

type RouterGroup struct {
	MsgTimeRecordRouter
	AmazonS3Router
	AppRouter
	AiRoleRouter
	AiRoleImgInfoRouter
	AiRoleMediaRouter
	AdjustLogRouter
	DigitalUserRouter
	DigitalUserProfileRouter
	FeedbackRecordRouter
	FlowerConsumeRecordRouter
	OrdersRouter
	ProductRouter
	ReSubOrdersRouter
	UsersIntimateRouter
	ChatGuideRecordRouter
	UserAiRoleFuncRouter
	MsgJobRouter
	PayMethodsRouter
	ConfigPlanRouter
	AiSendMediaRouter
	AiCommonReplyRouter
	FcmRouter
	ProductInfoRouter
	TouchOptionRouter
	SyncDataRouter
	UndressRecordRouter
	ImgConfigRouter
	DailyReportRouter
	WithdrawHistoryRouter
	KocUserInfoRouter
	SysUserKocRouter
	AiScenesRouter
	UserUseScenesRouter
	PushMsgRouter
	GiftRouter
	GiftAiImgRouter
	DiyPhotoTaskRouter
	BadgeRouter
	UserBadgeRouter
	AiProfileInfoRouter
	AiContactInfoRouter
	AppPageBannerRouter
	DiyConfigRouter
	ImgTemplateCategoryRouter
	ImgTemplateRecordRouter
	ImgGenRecordRouter
	VoiceCallRouter
	VideoTxtRouter
	ImgToVideoRouter
	LiveGiftRouter
	LiveInfoRouter
	LivePeopleRouter
	LiveReceiveGiftRouter
	VoiceInfoRouter
	LiveReceiveFansRouter
	PrivateSpaceRouter
	PrivateSpaceUserRouter
	UserAiMsgCountRouter
	AiSortStatisticsRouter
	AiBodyImgRouter
	AiBodyImgCacheRouter
	VideoCallRouter
	UserFeedbackBackendRouter
	VideoSimplifyBackendRouter
	BtcBackendRouter
}
