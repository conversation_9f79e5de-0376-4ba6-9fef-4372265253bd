package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type KocUserInfoRouter struct{}

func (s *KocUserInfoRouter) InitKocUserInfoRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("kocUserInfo").Use(middleware.OperationRecord())
	aRouter := Router.Group("kocUserInfo")
	var kocUserInfoApi = v1.ApiGroupApp.BackendApiGroup.KocUserInfoApi
	opRouter.POST("Create", kocUserInfoApi.Create)
	opRouter.POST("RegCreate", kocUserInfoApi.RegCreate)
	opRouter.DELETE("DeleteById", kocUserInfoApi.DeleteById)
	opRouter.DELETE("DeleteByIds", kocUserInfoApi.DeleteByIds)
	opRouter.PUT("Update", kocUserInfoApi.Update)

	aRouter.GET("GetById", kocUserInfoApi.GetById)
	aRouter.GET("GetCurrentInfo", kocUserInfoApi.GetCurrentInfo)
	aRouter.GET("GetAll", kocUserInfoApi.GetAll)
	aRouter.GET("GetList", kocUserInfoApi.GetList)
}
