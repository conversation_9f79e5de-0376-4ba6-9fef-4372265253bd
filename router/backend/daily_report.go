package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type DailyReportRouter struct{}

func (s *DailyReportRouter) InitDailyReportRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("dailyReport").Use(middleware.OperationRecord())
	aRouter := Router.Group("dailyReport")
	var dailyReportApi = v1.ApiGroupApp.BackendApiGroup.DailyReportApi
	opRouter.POST("Create", dailyReportApi.Create)
	opRouter.DELETE("DeleteById", dailyReportApi.DeleteById)
	opRouter.DELETE("DeleteByIds", dailyReportApi.DeleteByIds)
	opRouter.PUT("Update", dailyReportApi.Update)

	aRouter.GET("GetById", dailyReportApi.GetById)
	aRouter.GET("GetAll", dailyReportApi.GetAll)
	aRouter.GET("GetList", dailyReportApi.GetList)
	aRouter.GET("GetSum", dailyReportApi.GetSum)
}
