package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type AppRouter struct{}

func (s *AppRouter) InitAppRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("app").Use(middleware.OperationRecord())
	aRouter := Router.Group("app")
	var appApi = v1.ApiGroupApp.BackendApiGroup.AppApi
	opRouter.POST("Create", appApi.Create)
	opRouter.DELETE("DeleteById", appApi.DeleteById)
	opRouter.DELETE("DeleteByIds", appApi.DeleteByIds)
	opRouter.PUT("Update", appApi.Update)

	aRouter.GET("GetById", appApi.GetById)
	aRouter.GET("GetAll", appApi.GetAll)
	aRouter.GET("GetList", appApi.GetList)
	aRouter.POST("SetAuditPlan", appApi.SetAuditPlan)
	aRouter.GET("GetAuditPlan", appApi.GetAuditPlan)
	aRouter.POST("CopyAppData", appApi.CopyAppData)
}
