package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type DigitalUserProfileRouter struct{}

func (s *DigitalUserProfileRouter) InitDigitalUserProfileRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("digitalUserProfile").Use(middleware.OperationRecord())
	aRouter := Router.Group("digitalUserProfile")
	var digitalUserProfileApi = v1.ApiGroupApp.BackendApiGroup.DigitalUserProfileApi
	opRouter.POST("Create", digitalUserProfileApi.Create)
	opRouter.DELETE("DeleteById", digitalUserProfileApi.DeleteById)
	opRouter.DELETE("DeleteByIds", digitalUserProfileApi.DeleteByIds)
	opRouter.PUT("Update", digitalUserProfileApi.Update)

	aRouter.GET("GetById", digitalUserProfileApi.GetById)
	aRouter.GET("GetAll", digitalUserProfileApi.GetAll)
	aRouter.GET("GetList", digitalUserProfileApi.GetList)
}
