package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type DiyPhotoTaskRouter struct{}

func (s *DiyPhotoTaskRouter) InitDiyPhotoTaskRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("diyPhotoTask").Use(middleware.OperationRecord())
	aRouter := Router.Group("diyPhotoTask")
	var diyPhotoTaskApi = v1.ApiGroupApp.BackendApiGroup.DiyPhotoTaskApi
	opRouter.POST("ReSendEmail", diyPhotoTaskApi.ReSendEmail)
	opRouter.POST("ReSendPushMsg", diyPhotoTaskApi.ReSendPushMsg)
	opRouter.POST("RestartTask", diyPhotoTaskApi.RestartTask)
	opRouter.POST("Create", diyPhotoTaskApi.Create)
	opRouter.DELETE("DeleteById", diyPhotoTaskApi.DeleteById)
	opRouter.DELETE("DeleteByIds", diyPhotoTaskApi.DeleteByIds)
	opRouter.PUT("Update", diyPhotoTaskApi.Update)

	aRouter.GET("GetById", diyPhotoTaskApi.GetById)
	aRouter.GET("GetAll", diyPhotoTaskApi.GetAll)
	aRouter.GET("GetList", diyPhotoTaskApi.GetList)
}

func (s *DiyPhotoTaskRouter) InitDiyPhotoTaskPubRouter(Router *gin.RouterGroup) {
	aRouter := Router.Group("diyPhotoTaskPub")
	var diyPhotoTaskApi = v1.ApiGroupApp.BackendApiGroup.DiyPhotoTaskApi
	aRouter.POST("SwapFace", diyPhotoTaskApi.SwapFace)

}
