package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type UndressRecordRouter struct{}

func (s *UndressRecordRouter) InitUndressRecordRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("undressRecord").Use(middleware.OperationRecord())
	aRouter := Router.Group("undressRecord")
	var undressRecordApi = v1.ApiGroupApp.BackendApiGroup.UndressRecordApi
	opRouter.POST("Create", undressRecordApi.Create)
	opRouter.DELETE("DeleteById", undressRecordApi.DeleteById)
	opRouter.DELETE("DeleteByIds", undressRecordApi.DeleteByIds)
	opRouter.PUT("Update", undressRecordApi.Update)

	aRouter.GET("GetById", undressRecordApi.GetById)
	aRouter.GET("GetAll", undressRecordApi.GetAll)
	aRouter.GET("GetList", undressRecordApi.GetList)
}
