package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type AppPageBannerRouter struct{}

func (s *AppPageBannerRouter) InitAppPageBannerRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("appPageBanner").Use(middleware.OperationRecord())
	aRouter := Router.Group("appPageBanner")
	var appPageBannerApi = v1.ApiGroupApp.BackendApiGroup.AppPageBannerApi
	opRouter.POST("Create", appPageBannerApi.Create)
	opRouter.DELETE("DeleteById", appPageBannerApi.DeleteById)
	opRouter.DELETE("DeleteByIds", appPageBannerApi.DeleteByIds)
	opRouter.PUT("Update", appPageBannerApi.Update)

	aRouter.GET("GetById", appPageBannerApi.GetById)
	aRouter.GET("GetAll", appPageBannerApi.GetAll)
	aRouter.GET("GetList", appPageBannerApi.GetList)
}
