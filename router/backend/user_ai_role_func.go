package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type UserAiRoleFuncRouter struct{}

func (s *UserAiRoleFuncRouter) InitUserAiRoleFuncRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("userAiRoleFunc").Use(middleware.OperationRecord())
	aRouter := Router.Group("userAiRoleFunc")
	var userAiRoleFuncApi = v1.ApiGroupApp.BackendApiGroup.UserAiRoleFuncApi
	opRouter.POST("Create", userAiRoleFuncApi.Create)
	opRouter.DELETE("DeleteById", userAiRoleFuncApi.DeleteById)
	opRouter.DELETE("DeleteByIds", userAiRoleFuncApi.DeleteByIds)
	opRouter.PUT("Update", userAiRoleFuncApi.Update)

	aRouter.GET("GetById", userAiRoleFuncApi.GetById)
	aRouter.GET("GetAll", userAiRoleFuncApi.GetAll)
	aRouter.GET("GetList", userAiRoleFuncApi.GetList)
}
