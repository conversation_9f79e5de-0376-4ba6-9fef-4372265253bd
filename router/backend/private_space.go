package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type PrivateSpaceRouter struct{}

func (s *PrivateSpaceRouter) InitPrivateSpaceRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("privateSpace").Use(middleware.OperationRecord())
	aRouter := Router.Group("privateSpace")
	var privateSpaceApi = v1.ApiGroupApp.BackendApiGroup.PrivateSpaceApi
	opRouter.POST("Create", privateSpaceApi.Create)
	opRouter.DELETE("DeleteById", privateSpaceApi.DeleteById)
	opRouter.DELETE("DeleteByIds", privateSpaceApi.DeleteByIds)
	opRouter.PUT("Update", privateSpaceApi.Update)

	aRouter.GET("GetById", privateSpaceApi.GetById)
	aRouter.GET("GetAll", privateSpaceApi.GetAll)
	aRouter.GET("GetList", privateSpaceApi.GetList)
}
