package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type ConfigPlanRouter struct{}

func (s *ConfigPlanRouter) InitConfigPlanRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("configPlan").Use(middleware.OperationRecord())
	aRouter := Router.Group("configPlan")
	var mApi = v1.ApiGroupApp.BackendApiGroup.ConfigPlanApi
	opRouter.PUT("Update", mApi.UpdatePlanConfig)
	aRouter.GET("getPlanConfig", mApi.GetPlanConfig)
	aRouter.GET("mapList", mApi.MapList)
}
