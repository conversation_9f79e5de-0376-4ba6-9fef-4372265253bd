package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type BadgeRouter struct{}

func (s *BadgeRouter) InitBadgeRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("badge").Use(middleware.OperationRecord())
	aRouter := Router.Group("badge")
	var badgeApi = v1.ApiGroupApp.BackendApiGroup.BadgeApi
	opRouter.POST("Create", badgeApi.Create)
	opRouter.DELETE("DeleteById", badgeApi.DeleteById)
	opRouter.DELETE("DeleteByIds", badgeApi.DeleteByIds)
	opRouter.PUT("Update", badgeApi.Update)

	aRouter.GET("GetById", badgeApi.GetById)
	aRouter.GET("GetAll", badgeApi.GetAll)
	aRouter.GET("GetList", badgeApi.GetList)
}
