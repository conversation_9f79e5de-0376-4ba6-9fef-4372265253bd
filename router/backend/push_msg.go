package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type PushMsgRouter struct{}

func (s *PushMsgRouter) InitPushMsgRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("pushMsg").Use(middleware.OperationRecord())
	aRouter := Router.Group("pushMsg")
	var pushMsgApi = v1.ApiGroupApp.BackendApiGroup.PushMsgApi
	opRouter.POST("Create", pushMsgApi.Create)
	opRouter.DELETE("DeleteById", pushMsgApi.DeleteById)
	opRouter.DELETE("DeleteByIds", pushMsgApi.DeleteByIds)
	opRouter.PUT("Update", pushMsgApi.Update)

	aRouter.GET("GetById", pushMsgApi.GetById)
	aRouter.GET("GetAll", pushMsgApi.GetAll)
	aRouter.GET("GetList", pushMsgApi.GetList)
}
