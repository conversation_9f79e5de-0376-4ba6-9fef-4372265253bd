package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type ImgToVideoRouter struct{}

func (s *ImgToVideoRouter) InitImgToVideoRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("imgToVideo").Use(middleware.OperationRecord())
	aRouter := Router.Group("imgToVideo")
	var imgToVideoApi = v1.ApiGroupApp.BackendApiGroup.ImgToVideoApi
	opRouter.POST("Create", imgToVideoApi.Create)
	opRouter.DELETE("DeleteById", imgToVideoApi.DeleteById)
	opRouter.DELETE("DeleteByIds", imgToVideoApi.DeleteByIds)
	opRouter.PUT("Update", imgToVideoApi.Update)

	aRouter.GET("GetById", imgToVideoApi.GetById)
	aRouter.GET("GetAll", imgToVideoApi.GetAll)
	aRouter.GET("GetList", imgToVideoApi.GetList)
}
