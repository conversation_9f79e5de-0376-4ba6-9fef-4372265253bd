package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type UserBadgeRouter struct{}

func (s *UserBadgeRouter) InitUserBadgeRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("userBadge").Use(middleware.OperationRecord())
	aRouter := Router.Group("userBadge")
	var userBadgeApi = v1.ApiGroupApp.BackendApiGroup.UserBadgeApi
	opRouter.POST("Create", userBadgeApi.Create)
	opRouter.DELETE("DeleteById", userBadgeApi.DeleteById)
	opRouter.DELETE("DeleteByIds", userBadgeApi.DeleteByIds)
	opRouter.PUT("Update", userBadgeApi.Update)

	aRouter.GET("GetById", userBadgeApi.GetById)
	aRouter.GET("GetAll", userBadgeApi.GetAll)
	aRouter.GET("GetList", userBadgeApi.GetList)
}
