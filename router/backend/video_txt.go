package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type VideoTxtRouter struct{}

func (s *VideoTxtRouter) InitVideoTxtRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("videoTxt").Use(middleware.OperationRecord())
	aRouter := Router.Group("videoTxt")
	var videoTxtApi = v1.ApiGroupApp.BackendApiGroup.VideoTxtApi
	opRouter.POST("Create", videoTxtApi.Create)
	opRouter.DELETE("DeleteById", videoTxtApi.DeleteById)
	opRouter.DELETE("DeleteByIds", videoTxtApi.DeleteByIds)
	opRouter.PUT("Update", videoTxtApi.Update)

	aRouter.GET("GetById", videoTxtApi.GetById)
	aRouter.GET("GetAll", videoTxtApi.GetAll)
	aRouter.GET("GetList", videoTxtApi.GetList)
}
