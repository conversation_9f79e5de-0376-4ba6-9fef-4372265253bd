package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type AiBodyImgRouter struct{}

func (s *AiBodyImgRouter) InitAiBodyImgRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("aiBodyImg").Use(middleware.OperationRecord())
	aRouter := Router.Group("aiBodyImg")
	var aiBodyImgApi = v1.ApiGroupApp.BackendApiGroup.AiBodyImgApi
	opRouter.POST("Create", aiBodyImgApi.Create)
	opRouter.DELETE("DeleteById", aiBodyImgApi.DeleteById)
	opRouter.DELETE("DeleteByIds", aiBodyImgApi.DeleteByIds)
	opRouter.PUT("Update", aiBodyImgApi.Update)

	aRouter.GET("GetById", aiBodyImgApi.GetById)
	aRouter.GET("GetAll", aiBodyImgApi.GetAll)
	aRouter.GET("GetList", aiBodyImgApi.GetList)
}
