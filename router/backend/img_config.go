package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type ImgConfigRouter struct{}

func (s *ImgConfigRouter) InitImgConfigRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("imgConfig").Use(middleware.OperationRecord())
	aRouter := Router.Group("imgConfig")
	var imgConfigApi = v1.ApiGroupApp.BackendApiGroup.ImgConfigApi
	opRouter.POST("Create", imgConfigApi.Create)
	opRouter.DELETE("DeleteById", imgConfigApi.DeleteById)
	opRouter.DELETE("DeleteByIds", imgConfigApi.DeleteByIds)
	opRouter.PUT("Update", imgConfigApi.Update)

	aRouter.GET("GetById", imgConfigApi.GetById)
	aRouter.GET("GetAll", imgConfigApi.GetAll)
	aRouter.GET("GetList", imgConfigApi.GetList)
}
