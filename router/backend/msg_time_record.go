package backend

import (
	v1 "aimsg-server/api/v1"
	"github.com/gin-gonic/gin"
)

type MsgTimeRecordRouter struct{}

func (s *MsgTimeRecordRouter) InitMsgTimeRecordRouter(Router *gin.RouterGroup) {
	aRouter := Router.Group("MsgTimeRecord")
	var msgTimeRecordApi = v1.ApiGroupApp.BackendApiGroup.MsgTimeRecordApi

	aRouter.GET("GetCount", msgTimeRecordApi.GetCount)
	aRouter.GET("GetList", msgTimeRecordApi.GetList)
}
