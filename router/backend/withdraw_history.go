package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type WithdrawHistoryRouter struct{}

func (s *WithdrawHistoryRouter) InitWithdrawHistoryRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("withdrawHistory").Use(middleware.OperationRecord())
	aRouter := Router.Group("withdrawHistory")
	var withdrawHistoryApi = v1.ApiGroupApp.BackendApiGroup.WithdrawHistoryApi
	opRouter.POST("Create", withdrawHistoryApi.Create)
	opRouter.DELETE("DeleteById", withdrawHistoryApi.DeleteById)
	opRouter.DELETE("DeleteByIds", withdrawHistoryApi.DeleteByIds)
	opRouter.PUT("Update", withdrawHistoryApi.Update)

	aRouter.GET("GetById", withdrawHistoryApi.GetById)
	aRouter.GET("GetAll", withdrawHistoryApi.GetAll)
	aRouter.GET("GetList", withdrawHistoryApi.GetList)
}
