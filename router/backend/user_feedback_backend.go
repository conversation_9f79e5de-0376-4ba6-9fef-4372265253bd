package backend

import (
	"aimsg-server/api/v1"
	"github.com/gin-gonic/gin"
)

type UserFeedbackBackendRouter struct{}

func (s *UserFeedbackBackendRouter) InitUserFeedbackBackendRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("user_feedback_backend")
	var userFeedBackBackendApi = v1.ApiGroupApp.BackendApiGroup.UserFeedbackBackendApi
	opRouter.GET("GetUserFeedbackList", userFeedBackBackendApi.GetUserFeedbackList)
	opRouter.POST("Reply", userFeedBackBackendApi.Reply)
	opRouter.POST("deleteSession", userFeedBackBackendApi.DeleteSession)

}
