package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type UserUseScenesRouter struct{}

func (s *UserUseScenesRouter) InitUserUseScenesRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("userUseScenes").Use(middleware.OperationRecord())
	aRouter := Router.Group("userUseScenes")
	var userUseScenesApi = v1.ApiGroupApp.BackendApiGroup.UserUseScenesApi
	opRouter.POST("Create", userUseScenesApi.Create)
	opRouter.DELETE("DeleteById", userUseScenesApi.DeleteById)
	opRouter.DELETE("DeleteByIds", userUseScenesApi.DeleteByIds)
	opRouter.PUT("Update", userUseScenesApi.Update)

	aRouter.GET("GetById", userUseScenesApi.GetById)
	aRouter.GET("GetAll", userUseScenesApi.GetAll)
	aRouter.GET("GetList", userUseScenesApi.GetList)
}
