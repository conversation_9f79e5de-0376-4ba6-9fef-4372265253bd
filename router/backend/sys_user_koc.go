package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type SysUserKocRouter struct{}

func (s *SysUserKocRouter) InitSysUserKocRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("sysUserKoc").Use(middleware.OperationRecord())
	aRouter := Router.Group("sysUserKoc")
	var sysUserKocApi = v1.ApiGroupApp.BackendApiGroup.SysUserKocApi
	opRouter.POST("Create", sysUserKocApi.Create)
	opRouter.POST("CreateOrUpdate", sysUserKocApi.CreateOrUpdate)
	opRouter.DELETE("DeleteById", sysUserKocApi.DeleteById)
	opRouter.DELETE("DeleteByIds", sysUserKocApi.DeleteByIds)
	opRouter.PUT("Update", sysUserKocApi.Update)

	aRouter.GET("GetById", sysUserKocApi.GetById)
	aRouter.GET("GetAll", sysUserKocApi.GetAll)
	aRouter.GET("GetList", sysUserKocApi.GetList)
}
