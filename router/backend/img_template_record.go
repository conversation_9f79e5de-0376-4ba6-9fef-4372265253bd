package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type ImgTemplateRecordRouter struct{}

func (s *ImgTemplateRecordRouter) InitImgTemplateRecordRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("imgTemplateRecord").Use(middleware.OperationRecord())
	aRouter := Router.Group("imgTemplateRecord")
	var imgTemplateRecordApi = v1.ApiGroupApp.BackendApiGroup.ImgTemplateRecordApi
	opRouter.POST("Create", imgTemplateRecordApi.Create)
	opRouter.DELETE("DeleteById", imgTemplateRecordApi.DeleteById)
	opRouter.DELETE("DeleteByIds", imgTemplateRecordApi.DeleteByIds)
	opRouter.PUT("Update", imgTemplateRecordApi.Update)

	aRouter.GET("GetById", imgTemplateRecordApi.GetById)
	aRouter.GET("GetAll", imgTemplateRecordApi.GetAll)
	aRouter.GET("GetList", imgTemplateRecordApi.GetList)
}
