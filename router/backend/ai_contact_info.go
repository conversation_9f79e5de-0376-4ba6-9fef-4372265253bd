package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type AiContactInfoRouter struct{}

func (s *AiContactInfoRouter) InitAiContactInfoRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("aiContactInfo").Use(middleware.OperationRecord())
	aRouter := Router.Group("aiContactInfo")
	var aiContactInfoApi = v1.ApiGroupApp.BackendApiGroup.AiContactInfoApi
	opRouter.POST("Create", aiContactInfoApi.Create)
	opRouter.DELETE("DeleteById", aiContactInfoApi.DeleteById)
	opRouter.DELETE("DeleteByIds", aiContactInfoApi.DeleteByIds)
	opRouter.PUT("Update", aiContactInfoApi.Update)

	aRouter.GET("GetById", aiContactInfoApi.GetById)
	aRouter.GET("GetAll", aiContactInfoApi.GetAll)
	aRouter.GET("GetList", aiContactInfoApi.GetList)
}
