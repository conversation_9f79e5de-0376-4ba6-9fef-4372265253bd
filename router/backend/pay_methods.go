package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type PayMethodsRouter struct{}

func (s *PayMethodsRouter) InitPayMethodsRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("payMethods").Use(middleware.OperationRecord())
	aRouter := Router.Group("payMethods")
	var payMethodsApi = v1.ApiGroupApp.BackendApiGroup.PayMethodsApi
	opRouter.POST("Create", payMethodsApi.Create)
	opRouter.DELETE("DeleteById", payMethodsApi.DeleteById)
	opRouter.DELETE("DeleteByIds", payMethodsApi.DeleteByIds)
	opRouter.PUT("Update", payMethodsApi.Update)

	aRouter.GET("GetById", payMethodsApi.GetById)
	aRouter.GET("GetAll", payMethodsApi.GetAll)
	aRouter.GET("GetList", payMethodsApi.GetList)
}
