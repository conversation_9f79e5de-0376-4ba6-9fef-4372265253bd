package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type DiyConfigRouter struct{}

func (s *DiyConfigRouter) InitDiyConfigRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("diyConfig").Use(middleware.OperationRecord())
	aRouter := Router.Group("diyConfig")
	var diyConfigApi = v1.ApiGroupApp.BackendApiGroup.DiyConfigApi
	opRouter.POST("Create", diyConfigApi.Create)
	opRouter.DELETE("DeleteById", diyConfigApi.DeleteById)
	opRouter.DELETE("DeleteByIds", diyConfigApi.DeleteByIds)
	opRouter.PUT("Update", diyConfigApi.Update)

	aRouter.GET("GetById", diyConfigApi.GetById)
	aRouter.GET("GetAll", diyConfigApi.GetAll)
	aRouter.GET("GetList", diyConfigApi.GetList)
}
