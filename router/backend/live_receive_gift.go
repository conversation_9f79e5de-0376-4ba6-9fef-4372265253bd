package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type LiveReceiveGiftRouter struct{}

func (s *LiveReceiveGiftRouter) InitLiveReceiveGiftRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("liveReceiveGift").Use(middleware.OperationRecord())
	aRouter := Router.Group("liveReceiveGift")
	var liveReceiveGiftApi = v1.ApiGroupApp.BackendApiGroup.LiveReceiveGiftApi
	opRouter.POST("Create", liveReceiveGiftApi.Create)
	opRouter.DELETE("DeleteById", liveReceiveGiftApi.DeleteById)
	opRouter.DELETE("DeleteByIds", liveReceiveGiftApi.DeleteByIds)
	opRouter.PUT("Update", liveReceiveGiftApi.Update)

	aRouter.GET("GetById", liveReceiveGiftApi.GetById)
	aRouter.GET("GetAll", liveReceiveGiftApi.GetAll)
	aRouter.GET("GetList", liveReceiveGiftApi.GetList)
}
