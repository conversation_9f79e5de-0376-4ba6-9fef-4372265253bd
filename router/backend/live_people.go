package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type LivePeopleRouter struct{}

func (s *LivePeopleRouter) InitLivePeopleRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("livePeople").Use(middleware.OperationRecord())
	aRouter := Router.Group("livePeople")
	var livePeopleApi = v1.ApiGroupApp.BackendApiGroup.LivePeopleApi
	opRouter.POST("Create", livePeopleApi.Create)
	opRouter.DELETE("DeleteById", livePeopleApi.DeleteById)
	opRouter.DELETE("DeleteByIds", livePeopleApi.DeleteByIds)
	opRouter.PUT("Update", livePeopleApi.Update)

	aRouter.GET("GetById", livePeopleApi.GetById)
	aRouter.GET("GetAll", livePeopleApi.GetAll)
	aRouter.GET("GetList", livePeopleApi.GetList)
}
