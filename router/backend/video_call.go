package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type VideoCallRouter struct{}

func (s *VideoCallRouter) InitVideoCallRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("videoCall").Use(middleware.OperationRecord())
	aRouter := Router.Group("videoCall")
	var videoCallApi = v1.ApiGroupApp.BackendApiGroup.VideoCallApi
	opRouter.POST("Create", videoCallApi.Create)
	opRouter.DELETE("DeleteById", videoCallApi.DeleteById)
	opRouter.DELETE("DeleteByIds", videoCallApi.DeleteByIds)
	opRouter.PUT("Update", videoCallApi.Update)

	aRouter.GET("GetById", videoCallApi.GetById)
	aRouter.GET("GetAll", videoCallApi.GetAll)
	aRouter.GET("GetList", videoCallApi.GetList)
}
