package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type LiveGiftRouter struct{}

func (s *LiveGiftRouter) InitLiveGiftRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("liveGift").Use(middleware.OperationRecord())
	aRouter := Router.Group("liveGift")
	var liveGiftApi = v1.ApiGroupApp.BackendApiGroup.LiveGiftApi
	opRouter.POST("Create", liveGiftApi.Create)
	opRouter.DELETE("DeleteById", liveGiftApi.DeleteById)
	opRouter.DELETE("DeleteByIds", liveGiftApi.DeleteByIds)
	opRouter.PUT("Update", liveGiftApi.Update)

	aRouter.GET("GetById", liveGiftApi.GetById)
	aRouter.GET("GetAll", liveGiftApi.GetAll)
	aRouter.GET("GetList", liveGiftApi.GetList)
}
