package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type ChatGuideRecordRouter struct{}

func (s *ChatGuideRecordRouter) InitChatGuideRecordRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("chatGuideRecord").Use(middleware.OperationRecord())
	aRouter := Router.Group("chatGuideRecord")
	var chatGuideRecordApi = v1.ApiGroupApp.BackendApiGroup.ChatGuideRecordApi
	opRouter.POST("Create", chatGuideRecordApi.Create)
	opRouter.DELETE("DeleteById", chatGuideRecordApi.DeleteById)
	opRouter.DELETE("DeleteByIds", chatGuideRecordApi.DeleteByIds)
	opRouter.PUT("Update", chatGuideRecordApi.Update)

	aRouter.GET("GetById", chatGuideRecordApi.GetById)
	aRouter.GET("GetAll", chatGuideRecordApi.GetAll)
	aRouter.GET("GetList", chatGuideRecordApi.GetList)
}
