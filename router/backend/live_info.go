package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type LiveInfoRouter struct{}

func (s *LiveInfoRouter) InitLiveInfoRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("liveInfo").Use(middleware.OperationRecord())
	aRouter := Router.Group("liveInfo")
	var liveInfoApi = v1.ApiGroupApp.BackendApiGroup.LiveInfoApi
	opRouter.POST("Create", liveInfoApi.Create)
	opRouter.DELETE("DeleteById", liveInfoApi.DeleteById)
	opRouter.DELETE("DeleteByIds", liveInfoApi.DeleteByIds)
	opRouter.PUT("Update", liveInfoApi.Update)

	aRouter.GET("GetById", liveInfoApi.GetById)
	aRouter.GET("GetAll", liveInfoApi.GetAll)
	aRouter.GET("GetList", liveInfoApi.GetList)
}
