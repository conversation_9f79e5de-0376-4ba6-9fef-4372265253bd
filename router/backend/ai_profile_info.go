package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type AiProfileInfoRouter struct{}

func (s *AiProfileInfoRouter) InitAiProfileInfoRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("aiProfileInfo").Use(middleware.OperationRecord())
	aRouter := Router.Group("aiProfileInfo")
	var aiProfileInfoApi = v1.ApiGroupApp.BackendApiGroup.AiProfileInfoApi
	opRouter.POST("Create", aiProfileInfoApi.Create)
	opRouter.DELETE("DeleteById", aiProfileInfoApi.DeleteById)
	opRouter.DELETE("DeleteByIds", aiProfileInfoApi.DeleteByIds)
	opRouter.PUT("Update", aiProfileInfoApi.Update)

	aRouter.GET("GetById", aiProfileInfoApi.GetById)
	aRouter.GET("GetAll", aiProfileInfoApi.GetAll)
	aRouter.GET("GetList", aiProfileInfoApi.GetList)
}
