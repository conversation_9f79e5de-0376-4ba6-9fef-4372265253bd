package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type AiBodyImgCacheRouter struct{}

func (s *AiBodyImgCacheRouter) InitAiBodyImgCacheRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("aiBodyImgCache").Use(middleware.OperationRecord())
	aRouter := Router.Group("aiBodyImgCache")
	var aiBodyImgCacheApi = v1.ApiGroupApp.BackendApiGroup.AiBodyImgCacheApi
	opRouter.POST("Create", aiBodyImgCacheApi.Create)
	opRouter.DELETE("DeleteById", aiBodyImgCacheApi.DeleteById)
	opRouter.DELETE("DeleteByIds", aiBodyImgCacheApi.DeleteByIds)
	opRouter.PUT("Update", aiBodyImgCacheApi.Update)

	aRouter.GET("GetById", aiBodyImgCacheApi.GetById)
	aRouter.GET("GetAll", aiBodyImgCacheApi.GetAll)
	aRouter.GET("GetList", aiBodyImgCacheApi.GetList)
}
