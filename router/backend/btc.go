package backend

import (
	v1 "aimsg-server/api/v1"

	"github.com/gin-gonic/gin"
)

type BtcBackendRouter struct{}

func (s *BtcBackendRouter) InitBtcBackendRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("btc_backend")
	var btcBackendApi = v1.ApiGroupApp.BackendApiGroup.BtcBackendApi
	opRouter.POST("GetBtcWithdrawalRecords", btcBackendApi.GetBtcWithdrawalRecords)
	opRouter.POST("UpdateBtcWithdrawalStatus", btcBackendApi.UpdateBtcWithdrawalStatus)
	opRouter.POST("GetBtcTrades", btcBackendApi.GetBtcTrades)
}
