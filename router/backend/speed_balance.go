package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"

	"github.com/gin-gonic/gin"
)

type SpeedBalanceRouter struct{}

// InitSpeedBalanceRouter 初始化 Speed Balance 路由信息
func (s *SpeedBalanceRouter) InitSpeedBalanceRouter(Router *gin.RouterGroup) {
	speedBalanceRouter := Router.Group("speedBalance").Use(middleware.OperationRecord())
	speedBalanceRouterWithoutRecord := Router.Group("speedBalance")
	speedBalanceApi := v1.ApiGroupApp.BackendApiGroup.SpeedBalanceApi
	{
		speedBalanceRouter.POST("check", speedBalanceApi.CheckBalanceNow)   // 立即检查余额
	}
	{
		speedBalanceRouterWithoutRecord.GET("status", speedBalanceApi.GetBalanceStatus) // 获取余额状态
	}
}
