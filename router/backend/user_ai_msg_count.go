package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type UserAiMsgCountRouter struct{}

func (s *UserAiMsgCountRouter) InitUserAiMsgCountRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("userAiMsgCount").Use(middleware.OperationRecord())
	aRouter := Router.Group("userAiMsgCount")
	var userAiMsgCountApi = v1.ApiGroupApp.BackendApiGroup.UserAiMsgCountApi
	opRouter.POST("Create", userAiMsgCountApi.Create)
	opRouter.DELETE("DeleteById", userAiMsgCountApi.DeleteById)
	opRouter.DELETE("DeleteByIds", userAiMsgCountApi.DeleteByIds)
	opRouter.PUT("Update", userAiMsgCountApi.Update)

	aRouter.GET("GetById", userAiMsgCountApi.GetById)
	aRouter.GET("GetAll", userAiMsgCountApi.GetAll)
	aRouter.GET("GetList", userAiMsgCountApi.GetList)
}
