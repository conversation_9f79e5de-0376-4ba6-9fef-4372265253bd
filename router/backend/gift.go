package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type GiftRouter struct{}

func (s *GiftRouter) InitGiftRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("gift").Use(middleware.OperationRecord())
	aRouter := Router.Group("gift")
	var giftApi = v1.ApiGroupApp.BackendApiGroup.GiftApi
	opRouter.POST("Create", giftApi.Create)
	opRouter.DELETE("DeleteById", giftApi.DeleteById)
	opRouter.DELETE("DeleteByIds", giftApi.DeleteByIds)
	opRouter.PUT("Update", giftApi.Update)

	aRouter.GET("GetById", giftApi.GetById)
	aRouter.GET("GetAll", giftApi.GetAll)
	aRouter.GET("GetList", giftApi.GetList)
}
