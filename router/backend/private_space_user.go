package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type PrivateSpaceUserRouter struct{}

func (s *PrivateSpaceUserRouter) InitPrivateSpaceUserRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("privateSpaceUser").Use(middleware.OperationRecord())
	aRouter := Router.Group("privateSpaceUser")
	var privateSpaceUserApi = v1.ApiGroupApp.BackendApiGroup.PrivateSpaceUserApi
	opRouter.POST("Create", privateSpaceUserApi.Create)
	opRouter.DELETE("DeleteById", privateSpaceUserApi.DeleteById)
	opRouter.DELETE("DeleteByIds", privateSpaceUserApi.DeleteByIds)
	opRouter.PUT("Update", privateSpaceUserApi.Update)

	aRouter.GET("GetById", privateSpaceUserApi.GetById)
	aRouter.GET("GetAll", privateSpaceUserApi.GetAll)
	aRouter.GET("GetList", privateSpaceUserApi.GetList)
}
