package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type LiveReceiveFansRouter struct{}

func (s *LiveReceiveFansRouter) InitLiveReceiveFansRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("liveReceiveFans").Use(middleware.OperationRecord())
	aRouter := Router.Group("liveReceiveFans")
	var liveReceiveFansApi = v1.ApiGroupApp.BackendApiGroup.LiveReceiveFansApi
	opRouter.POST("Create", liveReceiveFansApi.Create)
	opRouter.DELETE("DeleteById", liveReceiveFansApi.DeleteById)
	opRouter.DELETE("DeleteByIds", liveReceiveFansApi.DeleteByIds)
	opRouter.PUT("Update", liveReceiveFansApi.Update)

	aRouter.GET("GetById", liveReceiveFansApi.GetById)
	aRouter.GET("GetAll", liveReceiveFansApi.GetAll)
	aRouter.GET("GetList", liveReceiveFansApi.GetList)
}
