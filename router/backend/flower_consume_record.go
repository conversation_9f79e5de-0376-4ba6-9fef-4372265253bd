package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type FlowerConsumeRecordRouter struct{}

func (s *FlowerConsumeRecordRouter) InitFlowerConsumeRecordRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("flowerConsumeRecord").Use(middleware.OperationRecord())
	aRouter := Router.Group("flowerConsumeRecord")
	var flowerConsumeRecordApi = v1.ApiGroupApp.BackendApiGroup.FlowerConsumeRecordApi
	opRouter.POST("Create", flowerConsumeRecordApi.Create)
	opRouter.DELETE("DeleteById", flowerConsumeRecordApi.DeleteById)
	opRouter.DELETE("DeleteByIds", flowerConsumeRecordApi.DeleteByIds)
	opRouter.PUT("Update", flowerConsumeRecordApi.Update)

	aRouter.GET("GetById", flowerConsumeRecordApi.GetById)
	aRouter.GET("GetAll", flowerConsumeRecordApi.GetAll)
	aRouter.GET("GetList", flowerConsumeRecordApi.GetList)
	aRouter.GET("ConsumeTypeMap", flowerConsumeRecordApi.ConsumeTypeMap)
}
