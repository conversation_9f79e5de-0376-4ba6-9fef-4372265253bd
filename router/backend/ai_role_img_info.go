package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type AiRoleImgInfoRouter struct{}

func (s *AiRoleImgInfoRouter) InitAiRoleImgInfoRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("aiRoleImgInfo").Use(middleware.OperationRecord())
	aRouter := Router.Group("aiRoleImgInfo")
	var aiRoleImgInfoApi = v1.ApiGroupApp.BackendApiGroup.AiRoleImgInfoApi
	opRouter.POST("Create", aiRoleImgInfoApi.Create)
	opRouter.DELETE("DeleteById", aiRoleImgInfoApi.DeleteById)
	opRouter.DELETE("DeleteByIds", aiRoleImgInfoApi.DeleteByIds)
	opRouter.PUT("Update", aiRoleImgInfoApi.Update)

	aRouter.GET("GetById", aiRoleImgInfoApi.GetById)
	aRouter.GET("GetAll", aiRoleImgInfoApi.GetAll)
	aRouter.GET("GetList", aiRoleImgInfoApi.GetList)
}
