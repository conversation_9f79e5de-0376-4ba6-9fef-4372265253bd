package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type AiSortStatisticsRouter struct{}

func (s *AiSortStatisticsRouter) InitAiSortStatisticsRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("aiSortStatistics").Use(middleware.OperationRecord())
	aRouter := Router.Group("aiSortStatistics")
	var aiSortStatisticsApi = v1.ApiGroupApp.BackendApiGroup.AiSortStatisticsApi
	opRouter.POST("Create", aiSortStatisticsApi.Create)
	opRouter.DELETE("DeleteById", aiSortStatisticsApi.DeleteById)
	opRouter.DELETE("DeleteByIds", aiSortStatisticsApi.DeleteByIds)
	opRouter.PUT("Update", aiSortStatisticsApi.Update)

	aRouter.GET("GetById", aiSortStatisticsApi.GetById)
	aRouter.GET("GetAll", aiSortStatisticsApi.GetAll)
	aRouter.GET("GetList", aiSortStatisticsApi.GetList)
}
