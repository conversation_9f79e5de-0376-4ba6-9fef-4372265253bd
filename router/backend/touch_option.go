package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type TouchOptionRouter struct{}

func (s *TouchOptionRouter) InitTouchOptionRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("touchOption").Use(middleware.OperationRecord())
	aRouter := Router.Group("touchOption")
	var touchOptionApi = v1.ApiGroupApp.BackendApiGroup.TouchOptionApi
	opRouter.POST("Create", touchOptionApi.Create)
	opRouter.DELETE("DeleteById", touchOptionApi.DeleteById)
	opRouter.DELETE("DeleteByIds", touchOptionApi.DeleteByIds)
	opRouter.PUT("Update", touchOptionApi.Update)

	aRouter.GET("GetById", touchOptionApi.GetById)
	aRouter.GET("GetAll", touchOptionApi.GetAll)
	aRouter.GET("GetList", touchOptionApi.GetList)
}
