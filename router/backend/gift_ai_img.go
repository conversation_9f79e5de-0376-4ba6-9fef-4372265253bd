package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type GiftAiImgRouter struct{}

func (s *GiftAiImgRouter) InitGiftAiImgRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("giftAiImg").Use(middleware.OperationRecord())
	aRouter := Router.Group("giftAiImg")
	var giftAiImgApi = v1.ApiGroupApp.BackendApiGroup.GiftAiImgApi
	opRouter.POST("Create", giftAiImgApi.Create)
	opRouter.DELETE("DeleteById", giftAiImgApi.DeleteById)
	opRouter.DELETE("DeleteByIds", giftAiImgApi.DeleteByIds)
	opRouter.PUT("Update", giftAiImgApi.Update)

	aRouter.GET("GetById", giftAiImgApi.GetById)
	aRouter.GET("GetAll", giftAiImgApi.GetAll)
	aRouter.GET("GetList", giftAiImgApi.GetList)
}
