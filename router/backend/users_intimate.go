package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type UsersIntimateRouter struct{}

func (s *UsersIntimateRouter) InitUsersIntimateRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("usersIntimate").Use(middleware.OperationRecord())
	aRouter := Router.Group("usersIntimate")
	var usersIntimateApi = v1.ApiGroupApp.BackendApiGroup.UsersIntimateApi
	opRouter.POST("Create", usersIntimateApi.Create)
	opRouter.DELETE("DeleteById", usersIntimateApi.DeleteById)
	opRouter.DELETE("DeleteByIds", usersIntimateApi.DeleteByIds)
	opRouter.PUT("Update", usersIntimateApi.Update)

	aRouter.GET("GetById", usersIntimateApi.GetById)
	aRouter.GET("GetAll", usersIntimateApi.GetAll)
	aRouter.GET("GetList", usersIntimateApi.GetList)
}
