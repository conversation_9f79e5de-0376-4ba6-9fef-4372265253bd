package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type AdjustLogRouter struct{}

func (s *AdjustLogRouter) InitAdjustLogRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("adjustLog").Use(middleware.OperationRecord())
	aRouter := Router.Group("adjustLog")
	var adjustLogApi = v1.ApiGroupApp.BackendApiGroup.AdjustLogApi
	opRouter.POST("Create", adjustLogApi.Create)
	opRouter.DELETE("DeleteById", adjustLogApi.DeleteById)
	opRouter.DELETE("DeleteByIds", adjustLogApi.DeleteByIds)
	opRouter.PUT("Update", adjustLogApi.Update)

	aRouter.GET("GetById", adjustLogApi.GetById)
	aRouter.GET("GetAll", adjustLogApi.GetAll)
	aRouter.GET("GetList", adjustLogApi.GetList)
}
