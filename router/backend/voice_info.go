package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type VoiceInfoRouter struct{}

func (s *VoiceInfoRouter) InitVoiceInfoRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("voiceInfo").Use(middleware.OperationRecord())
	aRouter := Router.Group("voiceInfo")
	var voiceInfoApi = v1.ApiGroupApp.BackendApiGroup.VoiceInfoApi
	opRouter.POST("Create", voiceInfoApi.Create)
	opRouter.DELETE("DeleteById", voiceInfoApi.DeleteById)
	opRouter.DELETE("DeleteByIds", voiceInfoApi.DeleteByIds)
	opRouter.PUT("Update", voiceInfoApi.Update)

	aRouter.GET("GetById", voiceInfoApi.GetById)
	aRouter.GET("GetAll", voiceInfoApi.GetAll)
	aRouter.GET("GetList", voiceInfoApi.GetList)
}
