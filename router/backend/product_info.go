package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type ProductInfoRouter struct{}

func (s *ProductInfoRouter) InitProductInfoRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("productInfo").Use(middleware.OperationRecord())
	aRouter := Router.Group("productInfo")
	var productInfoApi = v1.ApiGroupApp.BackendApiGroup.ProductInfoApi
	opRouter.POST("Create", productInfoApi.Create)
	opRouter.DELETE("DeleteById", productInfoApi.DeleteById)
	opRouter.DELETE("DeleteByIds", productInfoApi.DeleteByIds)
	opRouter.PUT("Update", productInfoApi.Update)

	aRouter.GET("SyncAll", productInfoApi.SyncAll)
	aRouter.GET("GetById", productInfoApi.GetById)
	aRouter.GET("GetAll", productInfoApi.GetAll)
	aRouter.GET("GetList", productInfoApi.GetList)
}
