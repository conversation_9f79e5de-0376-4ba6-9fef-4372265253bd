package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type ImgGenRecordRouter struct{}

func (s *ImgGenRecordRouter) InitImgGenRecordRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("imgGenRecord").Use(middleware.OperationRecord())
	aRouter := Router.Group("imgGenRecord")
	var imgGenRecordApi = v1.ApiGroupApp.BackendApiGroup.ImgGenRecordApi
	opRouter.POST("Create", imgGenRecordApi.Create)
	opRouter.DELETE("DeleteById", imgGenRecordApi.DeleteById)
	opRouter.DELETE("DeleteByIds", imgGenRecordApi.DeleteByIds)
	opRouter.PUT("Update", imgGenRecordApi.Update)

	aRouter.GET("GetById", imgGenRecordApi.GetById)
	aRouter.GET("GetAll", imgGenRecordApi.GetAll)
	aRouter.GET("GetList", imgGenRecordApi.GetList)
}
