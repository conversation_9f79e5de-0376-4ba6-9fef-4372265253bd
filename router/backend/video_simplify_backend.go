package backend

import (
	"aimsg-server/api/v1"
	"github.com/gin-gonic/gin"
)

type VideoSimplifyBackendRouter struct{}

func (s *VideoSimplifyBackendRouter) InitVideoSimplyBackendRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("video_simply_backend")
	var videoSimplifyBackendApi = v1.ApiGroupApp.BackendApiGroup.VideoSimplifyBackendApi
	opRouter.GET("GetVideoSimplifyList", videoSimplifyBackendApi.GetVideoSimplifyList)
	opRouter.POST("AddVideoSimplify", videoSimplifyBackendApi.AddVideoSimplify)
	opRouter.POST("UpdateVideoSimplify", videoSimplifyBackendApi.UpdateVideoSimplify)
	opRouter.POST("DeleteVideoSimplify", videoSimplifyBackendApi.DeleteVideoSimplify)
	opRouter.POST("SetBanner", videoSimplifyBackendApi.SetBanner)
	opRouter.POST("DeleteBanner", videoSimplifyBackendApi.DeleteBanner)
	opRouter.GET("GetBannerList", videoSimplifyBackendApi.GetBannerList)
	opRouter.GET("GetVideoSimplifyInfo", videoSimplifyBackendApi.GetVideoSimplifyInfo)
}
