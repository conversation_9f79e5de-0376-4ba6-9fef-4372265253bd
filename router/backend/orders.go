package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type OrdersRouter struct{}

func (s *OrdersRouter) InitOrdersRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("orders").Use(middleware.OperationRecord())
	aRouter := Router.Group("orders")
	var ordersApi = v1.ApiGroupApp.BackendApiGroup.OrdersApi
	opRouter.POST("Create", ordersApi.Create)
	opRouter.POST("AdjustTest", ordersApi.AdjustTest)
	opRouter.DELETE("DeleteById", ordersApi.DeleteById)
	opRouter.DELETE("DeleteByIds", ordersApi.DeleteByIds)
	opRouter.PUT("Update", ordersApi.Update)

	aRouter.GET("GetById", ordersApi.GetById)
	aRouter.GET("GetAll", ordersApi.GetAll)
	aRouter.GET("GetList", ordersApi.GetList)
	aRouter.GET("SyncPriceCount", ordersApi.SyncPriceCount)
	aRouter.GET("FixReSubOrderSVIP", ordersApi.FixReSubOrderSVIP)
	aRouter.GET("FixReSubOrderVIP", ordersApi.FixReSubOrderVIP)
	aRouter.GET("FixOrderSVIP", ordersApi.FixOrderSVIP)
	aRouter.GET("FixOrderVIP", ordersApi.FixOrderVIP)
}

func (s *OrdersRouter) InitOrdersTestPubRouter(Router *gin.RouterGroup) {
	aRouter := Router.Group("Orders")
	var ordersApi = v1.ApiGroupApp.BackendApiGroup.OrdersApi
	aRouter.POST("TestTdPointer", ordersApi.TestTdPointer)
	aRouter.POST("ExecSubNoBuyFlo", ordersApi.ExecSubNoBuyFlo)
}
