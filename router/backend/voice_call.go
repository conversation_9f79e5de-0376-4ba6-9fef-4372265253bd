package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type VoiceCallRouter struct{}

func (s *VoiceCallRouter) InitVoiceCallRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("voiceCall").Use(middleware.OperationRecord())
	aRouter := Router.Group("voiceCall")
	var voiceCallApi = v1.ApiGroupApp.BackendApiGroup.VoiceCallApi
	opRouter.POST("Create", voiceCallApi.Create)
	opRouter.DELETE("DeleteById", voiceCallApi.DeleteById)
	opRouter.DELETE("DeleteByIds", voiceCallApi.DeleteByIds)
	opRouter.PUT("Update", voiceCallApi.Update)

	aRouter.GET("GetById", voiceCallApi.GetById)
	aRouter.GET("GetAll", voiceCallApi.GetAll)
	aRouter.GET("GetList", voiceCallApi.GetList)
}
