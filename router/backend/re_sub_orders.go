package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type ReSubOrdersRouter struct{}

func (s *ReSubOrdersRouter) InitReSubOrdersRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("reSubOrders").Use(middleware.OperationRecord())
	aRouter := Router.Group("reSubOrders")
	var reSubOrdersApi = v1.ApiGroupApp.BackendApiGroup.ReSubOrdersApi
	opRouter.POST("Create", reSubOrdersApi.Create)
	opRouter.DELETE("DeleteById", reSubOrdersApi.DeleteById)
	opRouter.DELETE("DeleteByIds", reSubOrdersApi.DeleteByIds)
	opRouter.PUT("Update", reSubOrdersApi.Update)

	aRouter.GET("GetById", reSubOrdersApi.GetById)
	aRouter.GET("GetAll", reSubOrdersApi.GetAll)
	aRouter.GET("GetList", reSubOrdersApi.GetList)
}
