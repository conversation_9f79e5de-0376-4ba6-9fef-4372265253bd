package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type FcmRouter struct{}

func (s *FcmRouter) InitFcmRouterPublic(Router *gin.RouterGroup) {
	aRouter := Router.Group("fcm")
	api := v1.ApiGroupApp.BackendApiGroup.FcmApi
	aRouter.GET("SendPushPushReceiveFlowerTest", api.SendPushPushReceiveFlowerTest)
}

func (s *FcmRouter) InitFcmRouterPrivate(Router *gin.RouterGroup) {
	aRouter := Router.Group("fcm").Use(middleware.OperationRecord())
	api := v1.ApiGroupApp.BackendApiGroup.FcmApi
	aRouter.POST("PushCommonMsgSingle", api.PushCommonMsgSingle)
}
