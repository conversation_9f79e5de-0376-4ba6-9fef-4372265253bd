package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type ImgTemplateCategoryRouter struct{}

func (s *ImgTemplateCategoryRouter) InitImgTemplateCategoryRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("imgTemplateCategory").Use(middleware.OperationRecord())
	aRouter := Router.Group("imgTemplateCategory")
	var imgTemplateCategoryApi = v1.ApiGroupApp.BackendApiGroup.ImgTemplateCategoryApi
	opRouter.POST("Create", imgTemplateCategoryApi.Create)
	opRouter.DELETE("DeleteById", imgTemplateCategoryApi.DeleteById)
	opRouter.DELETE("DeleteByIds", imgTemplateCategoryApi.DeleteByIds)
	opRouter.PUT("Update", imgTemplateCategoryApi.Update)

	aRouter.GET("GetById", imgTemplateCategoryApi.GetById)
	aRouter.GET("GetAll", imgTemplateCategoryApi.GetAll)
	aRouter.GET("GetList", imgTemplateCategoryApi.GetList)
}
