package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type FeedbackRecordRouter struct{}

func (s *FeedbackRecordRouter) InitFeedbackRecordRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("feedbackRecord").Use(middleware.OperationRecord())
	aRouter := Router.Group("feedbackRecord")
	var feedbackRecordApi = v1.ApiGroupApp.BackendApiGroup.FeedbackRecordApi
	opRouter.POST("Create", feedbackRecordApi.Create)
	opRouter.DELETE("DeleteById", feedbackRecordApi.DeleteById)
	opRouter.DELETE("DeleteByIds", feedbackRecordApi.DeleteByIds)
	opRouter.PUT("Update", feedbackRecordApi.Update)

	aRouter.GET("GetById", feedbackRecordApi.GetById)
	aRouter.GET("GetAll", feedbackRecordApi.GetAll)
	aRouter.GET("GetList", feedbackRecordApi.GetList)
}
