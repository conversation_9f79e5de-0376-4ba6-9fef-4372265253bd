package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type AiRoleMediaRouter struct{}

func (s *AiRoleMediaRouter) InitAiRoleMediaRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("aiRoleMedia").Use(middleware.OperationRecord())
	aRouter := Router.Group("aiRoleMedia")
	var aiRoleMediaApi = v1.ApiGroupApp.BackendApiGroup.AiRoleMediaApi
	opRouter.POST("Create", aiRoleMediaApi.Create)
	opRouter.DELETE("DeleteById", aiRoleMediaApi.DeleteById)
	opRouter.DELETE("DeleteByIds", aiRoleMediaApi.DeleteByIds)
	opRouter.PUT("Update", aiRoleMediaApi.Update)

	aRouter.GET("GetById", aiRoleMediaApi.GetById)
	aRouter.GET("GetAll", aiRoleMediaApi.GetAll)
	aRouter.GET("GetList", aiRoleMediaApi.GetList)
}
