package backend

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type AiCommonReplyRouter struct{}

func (s *AiCommonReplyRouter) InitAiCommonReplyRouter(Router *gin.RouterGroup) {
	opRouter := Router.Group("aiCommonReply").Use(middleware.OperationRecord())
	aRouter := Router.Group("aiCommonReply")
	var aiCommonReplyApi = v1.ApiGroupApp.BackendApiGroup.AiCommonReplyApi
	opRouter.POST("Create", aiCommonReplyApi.Create)
	opRouter.DELETE("DeleteById", aiCommonReplyApi.DeleteById)
	opRouter.DELETE("DeleteByIds", aiCommonReplyApi.DeleteByIds)
	opRouter.PUT("Update", aiCommonReplyApi.Update)

	aRouter.GET("GetById", aiCommonReplyApi.GetById)
	aRouter.GET("GetAll", aiCommonReplyApi.GetAll)
	aRouter.GET("GetList", aiCommonReplyApi.GetList)
}
