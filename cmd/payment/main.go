package main

import (
	"aimsg-server/core"
	"aimsg-server/global"
	"aimsg-server/initialize"
	"aimsg-server/pkg/payment_queue"
	"database/sql"
	"fmt"
	"runtime"

	"go.uber.org/zap"
)

//go:generate go env -w GO111MODULE=on
//go:generate go env -w GOPROXY=https://goproxy.cn,direct
//go:generate go mod tidy
//go:generate go mod download

// @title                       Gin-Vue-Admin Swagger API接口文档
// @version                     v2.6.0
// @description                 使用gin+vue进行极速开发的全栈开发基础平台
// @securityDefinitions.apikey  ApiKeyAuth
// @in                          header
// @name                        x-token
// @BasePath                    /
func main() {
	global.VIPER = core.Viper() // 初始化Viper
	initialize.OtherInit()
	global.LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.LOG)
	initialize.HttpClient()
	global.DB = initialize.Gorm() // gorm连接数据库
	initialize.DBList()
	initialize.Redis()
	// kafka.InitKafka()
	if global.DB != nil {
		// initialize.RegisterTables() // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.DB.DB()
		defer func(db *sql.DB) {
			if dbErr := db.Close(); dbErr != nil {
			}
		}(db)
	}
	fmt.Printf("GOMAXPROCS: %d\n", runtime.GOMAXPROCS(0))

	// 启动消费者程序
	paymentQueue := payment_queue.NewPaymentQueue()
	paymentQueue.Start()
}
