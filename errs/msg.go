package errs

import (
	"aimsg-server/global"
	"errors"
)

var (
	RequireLoginErr   = errors.New("please login")
	RequireBuyChatErr = errors.New(global.BuyChatMsg)
	RequireBuyFloErr  = errors.New("coin is not enough")
	FloShopErr        = errors.New(global.TurnToFloShop)
	VoiceNoFloErr     = errors.New(global.VoiceNoFloErrMsg)
	RequireSubErr     = errors.New(global.ChatLimitMsg)
	RequireSVipErr    = errors.New(global.SVipMsg)
	ChatVipErr        = errors.New(global.ChatVipMsg)
	ContentVipErr     = errors.New(global.ContentVipMsg)
)
