package core

import (
	"aimsg-server/global"
	"aimsg-server/initialize"
	"fmt"
	"go.uber.org/zap"
	"net/http"
	"time"
)

func RunWindowsServer() {
	Router := initialize.Routers()
	address := fmt.Sprintf(":%d", global.CONFIG.System.Addr)
	httpServer := &http.Server{
		Addr:           address,
		Handler:        Router.Handler(),
		ReadTimeout:    5 * time.Minute,
		WriteTimeout:   5 * time.Minute,
		MaxHeaderBytes: 1 << 20,
	}
	time.Sleep(10 * time.Microsecond)
	addr := global.CONFIG.System.Addr
	routerPrefix := global.CONFIG.System.RouterPrefix
	fmt.Printf(`ping地址检查 http://127.0.0.1:%d/%s/ping
header地址 http://127.0.0.1:%d/%s/headers
服务时间接口 http://127.0.0.1:%d/%s/time
`, addr, routerPrefix, addr, routerPrefix, addr, routerPrefix)
	err := httpServer.ListenAndServe()
	global.LOG.Error("RunWindowsServer error", zap.Any("err", err))
}
