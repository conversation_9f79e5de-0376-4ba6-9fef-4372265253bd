package config

import (
	"strconv"
	"sync"

	"go.uber.org/zap"
)

type BTCConfigs struct {
	HashRateFix int `mapstructure:"hash_rate_fix" json:"hash_rate_fix" yaml:"hash_rate_fix"` //算力衰减系数，放大10000倍
}

var btcConfigsInstance = &BTCConfigs{}
var btcConfigsLock sync.Mutex

func UpdateBTCConfigs(data map[string]string) {
	btcConfigsLock.Lock()
	defer btcConfigsLock.Unlock()
	tmp := &BTCConfigs{}
	if val, ok := data["hash_rate_fix"]; ok {
		if hashRateFix, err := strconv.Atoi(val); err == nil {
			tmp.HashRateFix = hashRateFix
		}
	}
	zap.L().Info("UpdateBTCConfigs", zap.Any("data", tmp))
	btcConfigsInstance = tmp
}

func GetBTCConfigsHashRateFix() float64 {
	btcConfigsLock.Lock()
	defer btcConfigsLock.Unlock()
	return float64(btcConfigsInstance.HashRateFix) / 10000.0
}
