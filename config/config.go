package config

type Server struct {
	DDnotify DDnotify        `mapstructure:"dd_notify" json:"dd_notify" yaml:"dd_notify"`
	DiyDD    DDnotify        `mapstructure:"diy_dd" json:"diy_dd" yaml:"diy_dd"`
	Vita     VitaConfig      `mapstructure:"vita" json:"vita" yaml:"vita"`
	JWT      JWT             `mapstructure:"jwt" json:"jwt" yaml:"jwt"`
	Zap      Zap             `mapstructure:"zap" json:"zap" yaml:"zap"`
	Redis    Redis           `mapstructure:"redis" json:"redis" yaml:"redis"`
	Mongo    Mongo           `mapstructure:"mongo" json:"mongo" yaml:"mongo"`
	System   System          `mapstructure:"system" json:"system" yaml:"system"`
	Captcha  Captcha         `mapstructure:"captcha" json:"captcha" yaml:"captcha"`
	AutoCode Autocode        `mapstructure:"autocode" json:"autocode" yaml:"autocode"`
	Mysql    Mysql           `mapstructure:"mysql" json:"mysql" yaml:"mysql"`
	Pgsql    Pgsql           `mapstructure:"pgsql" json:"pgsql" yaml:"pgsql"`
	DBList   []SpecializedDB `mapstructure:"db-list" json:"db-list" yaml:"db-list"`
	Local    Local           `mapstructure:"local" json:"local" yaml:"local"`
	AwsS3    AwsS3           `mapstructure:"aws-s3" json:"aws-s3" yaml:"aws-s3"`
	Excel    Excel           `mapstructure:"excel" json:"excel" yaml:"excel"`
	Cors     CORS            `mapstructure:"cors" json:"cors" yaml:"cors"`
	Kafka    Kafka           `mapstructure:"kafka" json:"kafka" yaml:"kafka"`
	ABTest   ABTestConfig    `mapstructure:"ab_test" json:"ab_test" yaml:"ab_test"`
	SpeedAPI SpeedAPI        `mapstructure:"speed_api" json:"speed_api" yaml:"speed_api"`
}
