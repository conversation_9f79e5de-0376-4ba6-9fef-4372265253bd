package config

type SpeedAPI struct {
	BaseURL       string `mapstructure:"base_url" json:"base_url" yaml:"base_url"`
	Authorization string `mapstructure:"authorization" json:"authorization" yaml:"authorization"`
	Enabled       bool   `mapstructure:"enabled" json:"enabled" yaml:"enabled"`
	Threshold     int64  `mapstructure:"threshold" json:"threshold" yaml:"threshold"` // 余额阈值，单位：SATS
	FeiShuBotID   string `mapstructure:"feishu_bot_id" json:"feishu_bot_id" yaml:"feishu_bot_id"`
}
