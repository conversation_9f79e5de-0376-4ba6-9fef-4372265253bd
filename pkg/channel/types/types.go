package types

import "github.com/shopspring/decimal"

type TransferStatus int

const (
	TransferStatusSuccess TransferStatus = 1
	TransferStatusFailed  TransferStatus = 2
	TransferStatusPending TransferStatus = 3
)

func (t TransferStatus) IsSuccess() bool {
	return t == TransferStatusSuccess
}

func (t TransferStatus) IsFailed() bool {
	return t == TransferStatusFailed
}

func (t TransferStatus) IsPending() bool {
	return t == TransferStatusPending
}

type InitChannel struct {
	AppId        string
	AppKey       string
	AppSecretId  string
	AppSecretKey string
}

type TransferRequest struct {
	Amount         string
	Method         string
	AccountNo      string
	Currency       string
	TargetCurrency string
	Remark         string
}

type TransferResponse struct {
	Id            string
	Identifier    *string
	Status        TransferStatus
	Message       string
	FailureReason string
}

type TransferQueryRequest struct {
}

type TransferQueryResponse struct {
}

type BalanceRequest struct {
}

type BalanceResponse struct {
	Currency         string          `json:"currency"`          //币种
	Message          string          `json:"message"`           //错误信息
	AvailableBalance decimal.Decimal `json:"available_balance"` //可用余额
}
