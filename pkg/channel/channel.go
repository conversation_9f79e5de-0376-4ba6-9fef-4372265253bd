package channel

import (
	"aimsg-server/pkg/channel/speed"
	"aimsg-server/pkg/channel/types"
	"context"
	"fmt"
)

type ChannelCode string

const (
	ChannelSpeed ChannelCode = "speed"
)

func (c ChannelCode) String() string {
	return string(c)
}

type ChannelInterface interface {
	Transfer(ctx context.Context, req *types.TransferRequest) (*types.TransferResponse, error)
	TransferQuery(ctx context.Context, req *types.TransferQueryRequest) (*types.TransferQueryResponse, error)
	BalanceQuery(ctx context.Context) (*types.BalanceResponse, error)
}

func NewChannel(channel ChannelCode, initChannel *types.InitChannel) (ChannelInterface, error) {
	switch channel {
	case ChannelSpeed:
		return speed.NewSpeed(initChannel), nil
	default:
		return nil, fmt.Errorf("not found channel by code: %v" + string(channel))
	}
}
