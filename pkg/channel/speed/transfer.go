package speed

import (
	"aimsg-server/pkg/channel/types"
	"aimsg-server/utils/httpclient"
	"context"
	"fmt"
	"net/http"

	"github.com/shopspring/decimal"
)

// Speed API 请求结构体
type InstantSendRequest struct {
	Amount          decimal.Decimal `json:"amount"`
	Currency        string          `json:"currency"`
	TargetCurrency  string          `json:"target_currency"`
	WithdrawMethod  string          `json:"withdraw_method"`
	WithdrawRequest string          `json:"withdraw_request"`
	Note            string          `json:"note,omitempty"`
}

type Error struct {
	Type    string `json:"type"`
	Message string `json:"message"`
	Param   string `json:"param"`
}

type InstantSendResponseError struct {
	Errors []Error `json:"errors"`
}

// Speed API 响应结构体
type InstantSendResponse struct {
	ID                      string          `json:"id"`
	Object                  string          `json:"object"`
	Status                  string          `json:"status"`
	WithdrawID              string          `json:"withdraw_id"`
	Amount                  decimal.Decimal `json:"amount"`
	Currency                string          `json:"currency"`
	TargetAmount            decimal.Decimal `json:"target_amount"`
	TargetCurrency          string          `json:"target_currency"`
	Fees                    string          `json:"fees"`
	SpeedFee                SpeedFee        `json:"speed_fee"`
	ExchangeRate            int64           `json:"exchange_rate"`
	Conversion              int64           `json:"conversion"`
	WithdrawMethod          string          `json:"withdraw_method"`
	WithdrawRequest         string          `json:"withdraw_request"`
	WithdrawType            string          `json:"withdraw_type"`
	Note                    string          `json:"note"`
	FailureReason           string          `json:"failure_reason"`
	ExplorerLink            string          `json:"explorer_link"`
	PromoCode               string          `json:"promo_code"`
	ContactPaymentAddressID string          `json:"contactPaymentAddressId"`
	ConversionFee           string          `json:"conversion_fee"`
	Created                 int64           `json:"created"`
	Modified                int64           `json:"modified"`
}

type SpeedFee struct {
	Percentage decimal.Decimal `json:"percentage"`
	Amount     decimal.Decimal `json:"amount"`
}

func (t *Speed) Transfer(ctx context.Context, req *types.TransferRequest) (*types.TransferResponse, error) {
	// 准备请求数据
	amount, err := decimal.NewFromString(req.Amount)
	if err != nil {
		return nil, err
	}
	apiReq := InstantSendRequest{
		Amount:          amount,
		Currency:        req.Currency,
		TargetCurrency:  req.TargetCurrency,
		WithdrawMethod:  req.Method,
		WithdrawRequest: req.AccountNo,
		Note:            req.Remark,
	}

	response, err := httpclient.NewHttp(ctx,
		BaseURL+URLInstantSend,
		http.MethodPost,
		httpclient.WithDebug(),
		httpclient.WithBodyStruct(apiReq),
		httpclient.WithBasicAuth(t.initChannel.AppId, t.initChannel.AppKey),
	).Send()
	if err != nil {
		return nil, err
	}

	if !response.Is2xx() {
		return &types.TransferResponse{
			Status:  types.TransferStatusFailed,
			Message: fmt.Sprintf("API error (status %d): %s", response.StatusCode, response.String()),
		}, nil
	}

	// 解析响应
	var apiResp InstantSendResponse
	if err := response.Unmarshal(&apiResp); err != nil {
		return &types.TransferResponse{
			Status:  types.TransferStatusFailed,
			Message: fmt.Sprintf("unmarshal response failed: %v", err),
		}, nil
	}

	// 根据Speed API响应状态映射到内部状态
	var transferStatus types.TransferStatus
	switch apiResp.Status {
	case "paid":
		transferStatus = types.TransferStatusSuccess
	case "unpaid":
		transferStatus = types.TransferStatusPending
	case "failed":
		transferStatus = types.TransferStatusFailed
	default:
		// 未知状态默认为pending
		transferStatus = types.TransferStatusPending
	}

	return &types.TransferResponse{
		Id:      apiResp.ID,
		Status:  transferStatus,
		Message: apiResp.FailureReason,
	}, nil
}
