package speed

import (
	"aimsg-server/pkg/channel/types"
	"aimsg-server/utils/httpclient"
	"context"
	"fmt"
	"github.com/shopspring/decimal"
	"net/http"
)

type InstantBalanceResponse struct {
	Object    string `json:"object"`
	Available struct {
		Amount         decimal.Decimal `json:"amount"`
		TargetCurrency string          `json:"target_currency"`
	} `json:"available"`
}

func (t *Speed) BalanceQuery(ctx context.Context) (*types.BalanceResponse, error) {

	response, err := httpclient.NewHttp(ctx,
		BaseURL+URLInstantSend,
		http.MethodGet,
		httpclient.WithDebug(),
		httpclient.WithBodyStruct(nil),
		httpclient.WithBasicAuth(t.initChannel.AppId, t.initChannel.AppKey),
	).Send()
	if err != nil {
		return nil, err
	}

	if !response.Is2xx() {
		return &types.BalanceResponse{
			Message: fmt.Sprintf("API error (status %d): %s", response.StatusCode, response.String()),
		}, nil
	}

	// 解析响应
	var apiResp InstantBalanceResponse
	if err := response.Unmarshal(&apiResp); err != nil {
		return &types.BalanceResponse{
			Message: fmt.Sprintf("unmarshal response failed: %v", err),
		}, nil
	}

	return &types.BalanceResponse{
		Currency:         apiResp.Available.TargetCurrency,
		AvailableBalance: apiResp.Available.Amount,
	}, nil
}
