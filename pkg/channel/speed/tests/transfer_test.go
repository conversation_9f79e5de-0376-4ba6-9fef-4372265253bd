package tests

import (
	"aimsg-server/pkg/channel/speed"
	"aimsg-server/pkg/channel/types"
	"context"
	"encoding/json"
	"testing"
)

func TestTransfer(t *testing.T) {
	_, err := speedInstance.Transfer(context.Background(), &types.TransferRequest{
		Amount:         "20",
		Method:         "lightning",
		AccountNo:      "lnbc1p59pkmapp5fe4fdp8vzvjf93fplxg3uvjk5jzxmn7mznvunq2np6eykwgm8t6qdpv2pshjmt9de6zqar0ypxk2unrdpskuapqv93kxmm4de6qcqzzsxqzjhsp5hk3s2hw8s4mwxmdhj0yl8lgkwad8np6ywg790pk2chxg60ya9a6s9qxpqysgqgnlfyqdj8tf6w0rw9shhgv3tgkhrfy5jjzkwht4wtwr2m6j5gjzrmfk484ed35dcmujwf9mq5qp6vaxwmcvfw07w77lucr7hgv2akwcpn6rdhq",
		Currency:       "SATS",
		TargetCurrency: "SATS",
		Remark:         "test",
	})

	if err != nil {
		t.Fatalf("transfer failed: %v", err)
	}

	// t.Logf("transfer response: %+v", transferResponse)

}

func TestResponse(t *testing.T) {
	data := `{"id":"is_mc3a75mt8HYBkCtN","object":"instant_send","status":"unpaid","withdraw_id":"wi_mc3a75ngnoDcjgk","amount":32.********,"currency":"SATS","target_amount":32,"target_currency":"SATS","fees":null,"speed_fee":{"percentage":0,"amount":0},"exchange_rate":1,"conversion":1,"withdraw_method":"lightning","withdraw_request":"lnbc1p598m2npp5pg9xw79l94cypayvng9u7630368dansk69ed5pvpdgs5dw6wf2sqdqqcqzzsxqyz5vqsp5v7pupxuz8xh900juswmm274g5lnqqg4wf4p5lpmya32k5eqx20wq9p4gqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqpqysgqnkfz2vyjj46zu56mngdfvzgqgcl4umdazvktm5xjvsxz9tmpg9vhk7ner3fzdyhhypnmth7qs020hslfg7y8a73swz0vkrrrz8nc7kcp8ckvlv","withdraw_type":"lightning_invoice","note":"17109","failure_reason":null,"explorer_link":null,"promo_code":null,"contactPaymentAddressId":null,"conversion_fee":null,"created":1750331526725,"modified":1750331526792}`
	response := &speed.InstantSendResponse{}
	if err := json.Unmarshal([]byte(data), response); err != nil {
		t.Fatalf("unmarshal failed: %v", err)
	}
	prettyJson, _ := json.MarshalIndent(response, "", "  ")
	t.Logf("response: %s", string(prettyJson))
}
