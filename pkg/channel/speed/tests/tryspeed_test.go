package tests

import (
	"aimsg-server/pkg/channel/speed"
	"aimsg-server/pkg/channel/types"
)

// const accessId = "pk_test_mbuo205imhPwSe2Bmbuo4f71unQBls5hmbuo4f72JefRsF7V"
const prodAccessId = "********************************************************"
const pleamonProdAccessId = "********************************************************"
const accessId = "sk_test_mbuo205imhPwSe2Bmbuo4grgeuE9Iphtmbuo4grhJ6mUy4xy"
const accessKey = ""

var speedInstance = speed.NewSpeed(&types.InitChannel{
	AppId:  prodAccessId,
	AppKey: prodAccessId,
})
