package main

import (
	"aimsg-server/global"
	"aimsg-server/pkg/payment_queue"
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	gormLogger "gorm.io/gorm/logger"
)

func setup() *payment_queue.PaymentQueue {
	var err error
	global.LOG = zap.New(zapcore.NewTee(
		zapcore.NewCore(zapcore.NewConsoleEncoder(zap.NewDevelopmentEncoderConfig()), zapcore.AddSync(os.Stdout), zapcore.DebugLevel),
	))
	global.DB, err = gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: gormLogger.Default.LogMode(gormLogger.Info),
	})
	if err != nil {
		log.Fatalf("failed to open database: %v", err)
	}
	global.DB.AutoMigrate(
		&model.BtcWithdrawalRecord{},
		&model.BtcTrade{},
		&model.BtcChannel{},
	)
	global.REDIS = redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
	})
	paymentQueue := payment_queue.NewPaymentQueue()

	err = global.DB.FirstOrCreate(&model.BtcChannel{
		ChannelCode: "speed",
		ChannelName: "speed",
		AppId:       "sk_test_mbuo205imhPwSe2Bmbuo4grgeuE9Iphtmbuo4grhJ6mUy4xy",
	}).Error
	if err != nil {
		fmt.Printf("create channel failed: %+v", err.Error())
	}

	// for id := range 200 {

	// id := rand.IntN(9999)
	err = global.DB.Create(&model.BtcWithdrawalRecord{
		ID:               1,
		UserID:           1,
		Amount:           "0.0000002",
		WithdrawalMethod: 2,
		WithdrawalTime:   time.Now(),
		WithdrawalStatus: 4,
		Req:              `{"channel_code":"speed","method":{"network":"lightning","address":"lnbc1p5yccckpp5r3vm36aq23ddx883nxz5dgwn5q8vp6a6czq30kwzfcuj8z5ztqpqdqqcqzzsxqyz5vqsp5zrvpgdxn24aq6xyrcqj2ygkrhuxvdyvm72q0lct2ndmkmq8rkktq9qxpqysgq2h4v2lpgl8wl4x5l6cdvvaafkrawxhntcqtfz6vq6xaq4gzmx8nqfvmx2ktelwc8uh9qtxu0qddw9r0gy8rmrhfjzea0gwt8wtaadtgqpgc282"}}`,
	}).Error
	if err != nil {
		panic(fmt.Sprintf("create data failed: %+v", err.Error()))
	}
	if err = payment_queue.PushToQueue(context.Background(), 1); err != nil {
		panic(err)
	}
	// }
	return paymentQueue
}

func main() {
	// testRedis()
	// setup()
	paymentQueue := setup()
	paymentQueue.Start()
}
