package payment_queue

import (
	"aimsg-server/global"
	"aimsg-server/model/backend/request/btc"
	"aimsg-server/pkg/channel"
	"aimsg-server/pkg/channel/types"
	"aimsg-server/utils"
	"aimsg-server/utils/notify"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
)

type PaymentQueue struct {
}

func NewPaymentQueue() *PaymentQueue {
	return &PaymentQueue{}
}

func convertBTCToSATS(amountOrigin string) (string, error) {
	var amount float64
	amount, err := strconv.ParseFloat(amountOrigin, 64)
	if err != nil {
		return "", err
	}
	amount = amount * 1e8
	amountStr := strconv.FormatFloat(amount, 'f', -1, 64)
	return amountStr, nil
}

func (q *PaymentQueue) getPendingWithdrawalRecordById(withdrawalID int) (*model.BtcWithdrawalRecord, error) {
	withdrawalRecord := model.BtcWithdrawalRecord{}
	err := global.DB.Where("id = ?", withdrawalID).Where("withdrawal_status = ?", model.BtcWithdrawStatusReviewSuccess).First(&withdrawalRecord).Error
	if err != nil {
		return nil, fmt.Errorf("read withdrawal record failed: %v", err.Error())
	}

	return &withdrawalRecord, nil
}

// TODO: 目前只支持lightning network
func (q *PaymentQueue) parseTradeInfo(content string) (*btc.CashOutReq, error) {
	var requestInfo btc.CashOutReq
	if err := json.Unmarshal([]byte(content), &requestInfo); err != nil {
		return nil, fmt.Errorf("unmarshal trade record failed: %v", err.Error())
	}

	if requestInfo.Method.Address == "" {
		return nil, errors.New("requestInfo.Method.Address is empty")
	}
	// if requestInfo.Method.Network == "" {
	// 	return nil, errors.New("requestInfo.Method.Network is empty")
	// }
	if requestInfo.ChannelCode == "" {
		return nil, errors.New("requestInfo.ChannelCode is empty")
	}

	// 检查用户提交的network是否在支持列表
	// if slices.Contains([]model.BtcTradeNetwork{
	// 	model.BtcTradeNetworkLightning,
	// 	model.BtcTradeNetworkOnchain,
	// 	model.BtcTradeNetworkEthereum,
	// 	model.BtcTradeNetworkTron,
	// }, model.BtcTradeNetwork(requestInfo.Method.Network)) {
	// 	return &requestInfo, nil
	// }
	// return nil, fmt.Errorf("unsupport network: %v", requestInfo.Method.Network)
	return &requestInfo, nil
}

func (q *PaymentQueue) Start() {
	for {
		ctx := context.Background()
		withdrawalID, err := PopFromQueue(ctx)
		if err != nil {
			global.LOG.Error("parse withdrawal id failed", zap.Error(err))
			continue
		}
		logger := global.LOG.With(zap.Int("withdrawal_id", withdrawalID))
		logger.Info("received withdrawal")

		q.Worker(ctx, logger, withdrawalID)
	}
}

func (q *PaymentQueue) Worker(ctx context.Context, logger *zap.Logger, withdrawalID int) {
	// 防止重复操作
	locked := global.REDIS.SetNX(ctx, QueueLockKey, 1, time.Second*60).Val()
	if !locked {
		logger.Error("queue hurry")
		return
	}
	defer func() {
		logger.Info("clear lock")
		global.REDIS.Del(ctx, QueueLockKey)
	}()

	botId := global.CONFIG.DDnotify.PayoutBotID

	withdrawalRecord, err := q.getPendingWithdrawalRecordById(withdrawalID)
	if err != nil {
		logger.Error("get pending withdrawal record failed", zap.Error(err))
		return
	}

	requestInfo, err := q.parseTradeInfo(withdrawalRecord.Req)
	if err != nil {
		logger.Error("parse trade info failed", zap.Error(err))
		return
	}

	btcChannel := model.BtcChannel{}
	if err := global.DB.First(&btcChannel, "channel_code = ? and channel_status = ?", requestInfo.ChannelCode, model.BtcChannelStatusNormal).Error; err != nil {
		logger.Error("get channel by code failed", zap.String("channel_code", string(requestInfo.ChannelCode)), zap.Error(err))
		return
	}

	ch, err := channel.NewChannel(channel.ChannelCode(btcChannel.ChannelCode), &types.InitChannel{
		AppId:        btcChannel.AppId,
		AppKey:       btcChannel.AppKey,
		AppSecretId:  btcChannel.AppSecretId,
		AppSecretKey: btcChannel.AppSecretKey,
	})
	if err != nil {
		logger.Error("new channel failed", zap.Error(err))
		return
	}

	// 创建extra订单记录
	trade := model.BtcTrade{
		WithdrawalID:   withdrawalID,
		ChannelCode:    string(channel.ChannelSpeed),
		Currency:       model.BtcCurrencySats,
		TargetCurrency: model.BtcCurrencySats,
		// Network:        requestInfo.Method.Network,
		Network:     string(model.BtcTradeNetworkLightning),
		Amount:      withdrawalRecord.Amount,
		AccountNo:   requestInfo.Method.Address,
		Remark:      strconv.Itoa(withdrawalID),
		TradeStatus: model.BtcTradeStatusWaitPay,
	}
	if err := global.DB.Create(&trade).Error; err != nil {
		logger.Error("create trade record failed", zap.Error(err))
		return
	}

	amount, err := convertBTCToSATS(trade.Amount)
	if err != nil {
		logger.Error("convert btc to sats failed", zap.Error(err))
		return
	}

	// 向channel发起拉单请求
	transferResponse, err := ch.Transfer(ctx, &types.TransferRequest{
		Amount:         amount,
		Method:         trade.Network,
		AccountNo:      trade.AccountNo,
		Currency:       trade.Currency,
		TargetCurrency: trade.TargetCurrency,
		Remark:         trade.Remark,
	})
	if err != nil {
		// 更新为失败
		if err := global.DB.Model(&trade).Updates(model.BtcTrade{
			Message: err.Error(),
		}).Error; err != nil {
			logger.Error("update trade record failed", zap.Error(err))
		}

		logger.Info("transfer failed",
			zap.String("error", err.Error()),
		)

		//飞书通知失败
		var sendText = `
## 提现失败
- 订单ID: %d
- AppID: %d
- 用户ID: %d
- 提现金额: %s
- 失败原因: %s
- 失败时间: %s
`
		sendText = fmt.Sprintf(sendText,
			withdrawalRecord.ID,
			withdrawalRecord.AppId,
			withdrawalRecord.UserID,
			withdrawalRecord.Amount,
			err.Error(),
			time.Now().Format("2006-01-02 15:04:05"),
		)
		// 发送飞书通知
		if botId != "" {
			sendErr := notify.SendFeiShuTalkV2WithRetry(botId, sendText, "警告！！！")
			if sendErr != nil {
				logger.Error("send feiShu record failed", zap.Error(sendErr))
			}
		}

		return
	}

	identifier := ""
	if transferResponse.Identifier != nil {
		identifier = *transferResponse.Identifier
	}
	if transferResponse.Status.IsPending() {
		if err := global.DB.Model(&trade).Updates(model.BtcTrade{
			TradeStatus:    model.BtcTradeStatusPending,
			ChannelOrderNo: transferResponse.Id,
			Identifier:     identifier,
			Message:        transferResponse.Message,
		}).Error; err != nil {
			logger.Error("update trade record failed", zap.Error(err))
		}
		logger.Info("transfer pending",
			zap.Int("status", int(transferResponse.Status)),
			zap.String("channel_order_no", transferResponse.Id),
			zap.String("message", transferResponse.Message),
		)
		return
	}

	if transferResponse.Status.IsFailed() {
		if err := global.DB.Model(&trade).Updates(model.BtcTrade{
			TradeStatus:    model.BtcTradeStatusFailed,
			ChannelOrderNo: transferResponse.Id,
			Identifier:     identifier,
			Message:        transferResponse.Message,
		}).Error; err != nil {
			logger.Error("update trade record failed", zap.Error(err))
		}
		if err := global.DB.Model(&withdrawalRecord).Updates(model.BtcWithdrawalRecord{
			WithdrawalStatus: model.BtcWithdrawStatusFailed,
			FailReason:       utils.CheckStrLen(transferResponse.FailureReason, 255),
		}).Error; err != nil {
			logger.Error("update withdrawal record failed", zap.Error(err))
		}
		logger.Info("transfer failed",
			zap.Int("status", int(transferResponse.Status)),
			zap.String("channel_order_no", transferResponse.Id),
			zap.String("message", transferResponse.Message),
			zap.String("failure_reason", transferResponse.FailureReason),
		)
		//飞书通知失败
		var sendText = `
## 提现失败
- 订单ID: %d
- AppID: %d
- 用户ID: %d
- 提现金额: %s
- 失败原因: %s
- 失败时间: %s
`
		sendText = fmt.Sprintf(sendText,
			withdrawalRecord.ID,
			withdrawalRecord.AppId,
			withdrawalRecord.UserID,
			withdrawalRecord.Amount,
			transferResponse.Message,
			time.Now().Format("2006-01-02 15:04:05"),
		)
		// 发送飞书通知
		if botId != "" {
			sendErr := notify.SendFeiShuTalkV2WithRetry(botId, sendText, "警告！！！")
			if sendErr != nil {
				logger.Error("send feiShu record failed", zap.Error(sendErr))
			}
		}

		return
	}
}
