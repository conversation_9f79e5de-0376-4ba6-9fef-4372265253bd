package payment_queue

import (
	"aimsg-server/global"
	"context"
	"fmt"
	"strconv"

	"go.uber.org/zap"
)

const (
	QueueLockKey  = "ai:btc:Queue:lock"
	QueueTransfer = "ai:btc:QueueTransfer"
)

func GetQueueLength(ctx context.Context) (int, error) {
	cmd := global.REDIS.LLen(ctx, QueueTransfer)
	length, err := cmd.Result()
	if err != nil {
		global.LOG.Error("get queue length failed", zap.Error(err))
		return 0, err
	}
	return int(length), nil
}

func PushToQueue(ctx context.Context, withdrawalId int) error {
	global.LOG.Info("starting push to queue", zap.Int("id", withdrawalId))

	// 检查Redis连接是否可用
	if global.REDIS == nil {
		err := fmt.Errorf("redis client is nil")
		global.LOG.Error("redis client not initialized", zap.Error(err))
		return err
	}

	// 测试Redis连接
	_, err := global.REDIS.Ping(ctx).Result()
	if err != nil {
		global.LOG.Error("redis ping failed", zap.Error(err))
		return fmt.Errorf("redis connection failed: %w", err)
	}

	// 执行LPUSH
	n, err := global.REDIS.LPush(ctx, QueueTransfer, withdrawalId).Result()
	if err != nil {
		global.LOG.Error("lpush failed", zap.Error(err), zap.Int("id", withdrawalId))
		return fmt.Errorf("lpush operation failed: %w", err)
	}

	// 再次检查长度
	length, err := GetQueueLength(ctx)
	if err != nil {
		global.LOG.Error("get length after push failed", zap.Error(err))
		return fmt.Errorf("failed to verify queue length: %w", err)
	}

	// 如果长度为0但LPUSH返回大于0，说明有问题
	if length == 0 && n > 0 {
		err := fmt.Errorf("inconsistent state: lpush returned %d but llen returned %d", n, length)
		global.LOG.Error("inconsistent state detected",
			zap.Int64("lpush_result", n),
			zap.Int("llen_result", length),
			zap.Error(err))
		return err
	}

	return nil
}

func PopFromQueue(ctx context.Context) (int, error) {
	global.LOG.Info("starting pop from queue")

	// 先检查队列长度
	length, err := GetQueueLength(ctx)
	if err != nil {
		return 0, err
	}
	global.LOG.Info("queue length before pop", zap.Int("length", length))

	// 因为BRPop返回的是[key, value]数组,所以需要取第二个元素
	cmd := global.REDIS.BRPop(ctx, 0, QueueTransfer)
	value, err := cmd.Result()
	if err != nil {
		global.LOG.Error("brpop failed", zap.Error(err))
		return 0, err
	}

	withdrawalID, err := strconv.Atoi(value[1])
	if err != nil {
		global.LOG.Error("parse withdrawal id failed", zap.Error(err), zap.String("value", value[1]))
		return 0, fmt.Errorf("parse withdrawal id failed: %v", err.Error())
	}

	return withdrawalID, nil
}
