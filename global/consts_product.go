package global

import "git.costnovel.com/cashbox-ai/ai-par-model/model"

// 不同页面的订阅页弹窗
type PageName string

// 非审核模式下的降价配置
type SubPriceOffConf struct {
	DefaultProductInfoType int           // 默认显示的档次
	IsExecutePriceOff      bool          // 是否执行降价的开关
	ProductInfoTypeMap     map[int][]int // key=弹出订阅窗口的次数，val=[上个档位、当前档位]
}

var (
	// 降价逻辑触发时倒计时周期秒,10分钟内显示倒计时
	SubDialogCountdownSeconds int64 = 600

	// 订阅页里按钮的文案数据,一类按钮是直接传文本,一类是需要用价格做变量
	BuyBtnTextForNotAuditAndPriceOff = "Pay %s now" // 非审核模式下触发了降价，%s是价格的值
)

type SubProductListParams struct {
	InfoType      model.ProductInfoType // 商品信息的类型，比如档次
	IsAuditMode   bool                  // 是否是审核模式，控制支付按钮的文案
	IsDialogCount bool                  // 是否是用在弹窗中要去记录次数
	Page          PageName              // 什么页面过来的请求
}
