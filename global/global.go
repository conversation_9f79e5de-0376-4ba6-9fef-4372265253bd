package global

import (
	"aimsg-server/config"
	"aimsg-server/utils/timer"
	"cloud.google.com/go/translate"
	"github.com/go-playground/validator/v10"
	"github.com/qiniu/qmgo"
	"github.com/redis/go-redis/v9"
	"github.com/songzhibin97/gkit/cache/local_cache"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	"golang.org/x/sync/singleflight"
	"gorm.io/gorm"
	"net/http"
	"sync"
)

var (
	// GeoIpCityDb        *geoip2.Reader
	// GeoIpAsnDb         *geoip2.Reader
	DB                 *gorm.DB
	DBList             map[string]*gorm.DB
	REDIS              *redis.Client
	MONGO              *qmgo.QmgoClient
	CONFIG             config.Server
	VIPER              *viper.Viper
	LOG                *zap.Logger
	Ht                 *http.Client
	TranslateClient    *translate.Client
	BlackCache         local_cache.Cache
	lock               sync.RWMutex
	Timer              = timer.NewTimerTask()
	ConcurrencyControl = &singleflight.Group{}
	Validate           = validator.New()
)

// GetGlobalDBByDBName 通过名称获取db list中的db
func GetGlobalDBByDBName(dbname string) *gorm.DB {
	lock.RLock()
	defer lock.RUnlock()
	return DBList[dbname]
}

// MustGetGlobalDBByDBName 通过名称获取db 如果不存在则panic
func MustGetGlobalDBByDBName(dbname string) *gorm.DB {
	lock.RLock()
	defer lock.RUnlock()
	db, ok := DBList[dbname]
	if !ok || db == nil {
		panic("db no init")
	}
	return db
}
