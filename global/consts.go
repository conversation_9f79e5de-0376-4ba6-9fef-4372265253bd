package global

import (
	"aimsg-server/model/cli/response"
	"aimsg-server/model/common/request"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"net"
	"time"
)

const PayCenterProjectId = 16
const PayCenterPayUrl = "https://pay-center.51payment.live/pay-center/pay/pay"
const PayCenterSubPayUrlProd = "https://pay-center.51payment.live/pay-center/pay/subscription"
const PayCenterSubPayUrlTest = "https://pay-center-test.cashboxtest.com/pay-center/pay/subscription"

var PayCenterSubUrl = PayCenterSubPayUrlProd

const PayCenterMethodListUrl = "https://pay-center.51payment.live/pay-center/pay_method/list"
const PayCenterSubMethodListUrlProd = "https://pay-center.51payment.live/pay-center/pay_method/ssList"
const PayCenterSubMethodListUrlTest = "https://pay-center-test.cashboxtest.com/pay-center/pay_method/ssList"

var PayCenterSubMethodListUrl = PayCenterSubMethodListUrlProd

var OldSubTime = time.Date(2024, 10, 31, 0, 0, 0, 0, time.UTC)

var AdjustTimeOne = time.Date(2024, 11, 12, 0, 0, 0, 0, time.UTC)

var (

	// 普通用户周一到周五签到奖励鲜花的配置信息
	RewardFlowersFreeUserConfig = []int{
		2, // 星期一的，下标是0但是周日的weekday是1
		2, // 星期二的
		2, // 星期三的
		2, // 星期四的
		2, // 星期五的
		5, // 星期六的
		5, // 星期天的
	}

	// VIP用户周一到周五签到奖励鲜花的配置信息
	RewardFlowersVipUserConfig = []int{
		4, // 星期一，下标是0但是周日的weekday是1
		4,
		4,
		4,
		4,
		10,
		10, // 星期天，下标是6但是周日的weekday是0
	}

	WeekdayToRewardFlowersKeyMap = map[int]int{ // weekday => 配置的下标
		1: 0,
		2: 1,
		3: 2,
		4: 3,
		5: 4,
		6: 5,
		0: 6,
	}

	FastMsgList = []string{
		"Hi, what’s up?",
		"Do you believe in karma?",
		"Hey babe, how was work?",
		"Hello, my dear friend!",
		"How was your day?",
		"Do you like role-play?",
	}
	GuideMsgList []*model.ChatGuideRecord

	RequireImgMsgList = []string{
		"show me sexy photo",
	}
	TextMaskMsgList = []string{"rape", "porn", "erotic", "fetish", "catcall", "lesbian", "screw", "boobs", "tits", "pussy", "dick", "cock", "nudity", "orgasm", "chest", "climax", "cum", "ass", "blowjob", "handjob", "nude", "naked", "coming", "fuck", "asshole", "sob", "bozo", "shit", "dense", "slut", "junky", "scum", "stink", "bitch", "dickhead", "moron"}

	GoogleIpRanges = []IpRange{
		{net.ParseIP("**********"), net.ParseIP("************")},
		{net.ParseIP("**************"), net.ParseIP("**************")},
		{net.ParseIP("***********"), net.ParseIP("*************")},
		{net.ParseIP("*************"), net.ParseIP("**************")},
		{net.ParseIP("***********"), net.ParseIP("**************")},
		{net.ParseIP("**********"), net.ParseIP("*************")},
	}
)

const (
	GinContextCliHeader  = "GinContextCliHeader"
	GinContextAppId      = "AppId"
	GinContextUserId     = "DigitalUserId"
	GinContextAppVersion = "AppVersion"
	GinContextDeviceId   = "DeviceId"
	GinContextRawData    = "RawData"
)

var ReplyList []string
var IdleReplyList []string
var LiveOneReplyList []string
var LiveManyReplyList []string
var TouchOptionList []*model.TouchOption
var OrdersOptionList []*model.TouchOption
var PushMsgList []*model.PushMsg
var AllVideoTxtMap map[uint]*model.VideoTxt
var AllVideoTxtList []*model.VideoTxt
var AllVoiceInfoMap map[uint]*model.VoiceInfo
var AllVoiceInfoList []*model.VoiceInfo
var AllGiftMap map[uint]*model.Gift
var AllAiBodyImg = map[uint]*model.AiBodyImg{}
var AllAppAiBodyImg = map[uint]map[model.DiyConfigType][]*model.AiBodyImg{}
var AllGiftIdArr = []uint{}
var LiveGiftMap map[uint]*model.LiveGift
var LiveGiftList []*model.LiveGift
var LivePeopleMap map[uint]*model.LivePeople
var LivePeopleList []*model.LivePeople
var AllGiftList []*response.GiftRes
var AiRoleImgInfoList []*model.AiRoleImgInfo
var AiRoleImgInfoIdMap map[uint]*model.AiRoleImgInfo
var AllBannerMap map[uint][]*model.AppPageBanner
var AllProductMap map[uint]*model.Product
var AppProductListMap map[uint][]*model.Product
var AppInfoTypeListMap map[uint]map[model.ProductInfoType]model.ProductInfoSyncItem
var AppImgMap map[uint]map[model.ImgConfigType]request.SyncImgConfigItem
var AppDiyConfigMap map[uint][]model.DiyConfig
var DiyConfigMaskMap map[string]response.MaskImgItem
var DiyConfigMap map[uint]model.DiyConfig
var ImgTemplateCategoryList []*model.ImgTemplateCategory
var ImgTemplateRecordList []*model.ImgTemplateRecord
var ImgTemplateRecordMap map[uint]*model.ImgTemplateRecord
var ImgTemplateCategoryMap map[uint]*model.ImgTemplateCategory
var DefaultMaleAiRole *model.AiRole
var DefaultFemaleAiRole *model.AiRole
var MaleGiftList []*response.GiftRes
var BadgeList []*model.Badge
var TriggerTypeBadgeMap map[model.TriggerType][]*model.Badge
var DiyDDAtMobiles = []string{"15230463042"}

const (
	RtmExpireSec         = 86400
	RtcExpireSec         = 86400
	SIGN_REVIEW_SUB_RATE = 1
	MSG_REVIEW_RATE      = 0.1
	NotifyChargeSec      = 60
	OrderDDMsgLockKey    = "order_dd_msg_lock"
	OrderDDSecret        = "SEC09615904815cff53850f17dd37a922a5320fd06701ef527de125a1051fa1a310"
	OrderDDToken         = "907fe4334081cba4c8b8f9c54a64c1efddb81f30aea9aa28e0e40cc293c540c6"
	AdjustUrl            = "https://s2s.adjust.com/event"
	FcmMaxSize           = 500
	ILiveDataProjectID   = "81000566"
	ILiveDataSecretKey   = "bLxWM/hb74ljUUrKSV+sFt20iKxN8sKveDGL9tB1Y90="
	EleXiApiKey          = "2472c7825f8d333958c22476ec71e38a"
)
