package global

import (
	"aimsg-server/model/backend/request"
	"firebase.google.com/go/v4/auth"
	"firebase.google.com/go/v4/messaging"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
)

var (
	AppMap                = map[uint]model.App{}
	FcmClientMap          = map[uint]*messaging.Client{}
	AuthClientMap         = map[uint]*auth.Client{}
	MsgJobMap             = map[uint]model.MsgJob{}
	AllAiFuncCfgMap       = map[uint]request.AiFuncSwitchCfg{}
	AndroidPackageNameMap = map[string]model.App{}
	APPOtherCfg           model.AppOtherConfig
)
