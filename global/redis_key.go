package global

import (
	"fmt"
	"time"
)

const MSG_NEW_START_KEY = "msg_new_start"

const (
	// 订阅商品页的缓存
	SUB_DIALOG_HASH_KEY             = "dialog_conf:%s:%d"           // hash key 占位符的值为yyyy-mm-dd、uid
	SUB_DIALOG_PAGE_COUNT           = "page:%s:count"               // 二级key 占位符的值为page的枚举值，记录页面出现的次数
	SUB_DIALOG_COUNTDOWN_TIME       = "page:%s:countdown_timestamp" // 二级key 当前页面首次倒计时的时间戳，有值就不覆盖
	SUB_DIALOG_HASH_KEY_EXPIRE      = 24 * 3600                     // 过期时间设置为1天
	FLO_DIALOG_SHOW_KEY             = "flo_dialog_show:%s:%d"
	FLOWER_SHOW_KEY                 = "flower_dialog_show:%s:%d"
	FLOWER_SHOW_KEY_V2              = "flower_dialog_show_v2:%s:%s:%d" // 新版鲜花弹窗 YYYYMMDD YYYYMMDD uid
	REVIEW_TEST_KEY                 = "review:test:user_id"
	AD_HOUR_COUNT_KEY               = "ad:hour:%s:%d"
	AI_CHAT_ROLE_LIMIT_KEY          = "ai_chat_limit:%s:%d"           // YYYY-MM-DD、uid
	REPLY_USER_ALL_COUNT            = "reply_user_all_count:%s:%d"    // YYYY-MM-DD、uid
	USER_WATCH_AD_MSG_COUNT         = "user_watch_ad_msg_count:%s:%d" // YYYY-MM-DD、uid
	IDLE_MSG_QUEUE_KEY              = "idle_msg_queue_go"             // 空闲消息队列
	LIVE_PUSH_MSG_QUEUE_KEY         = "live_push_msg_queue"           // 直播后AI推送用户消息队列
	IMG_TO_IMG_TRAIN_KEY            = "img_to_img_train"              // 图生图训练队列
	IMG_TO_IMG_DELAY_KEY            = "img_to_img_delay"              // 延时图生图训练队列
	REQUIRE_IMG_TRAIN_KEY           = "require_img_train"             // 索要图片队列
	REQUIRE_IMG_RDS_KEY             = "require_img_rds:%s"            // 索要图片队列
	IMG_TO_VIDEO_TRAIN_KEY          = "img_to_video_train"            // 图转视频训练队列
	UserDayGenCountRdsKey           = "user_day_gen_count:%s:%d"      // YYYY-MM-DD、uid
	UserDayLiveCountRdsKey          = "user_day_live_count:%s:%d"     // YYYY-MM-DD、uid
	IP_WHITE_LIST                   = "ip_white_list"                 // ip白名单列表
	MEDIA_IS_COST_FLOWER            = "media_is_cost_flower:%d:%d:%s" // 解锁的资源是否花过钱 uid、aiId、mediaUrl
	MEDIA_IS_COST_FLOWER_KEY_EXPIRE = 10 * 24 * 3600
	MEDIA_SHOW_LIST                 = "media_send_list:%d:%d" // 图片、视频下发的记录列表 uid、aiId
)

const (
	// 消息相关key
	MAIN_QUEUE_AIMSG         = "main_queue:aimsg_main_go"
	MSG_SEND_TIMEOUT_CONFIG  = "msg_send_timeout_config"
	NOTIFY_CHARGE_QUEUE_KEY  = "notify_charge_queue"
	SUB_NO_BUY_FLO_QUEUE_KEY = "sub_no_buy_flo_queue"
	AI_VOICE_GEN_COUNT_KEY   = "ai_voice_gen_count:%s:%s_%s"
	AI_VIDEO_WATCH_COUNT_KEY = "ai_video_watch_count:%s_%s"
	NOTICE_CHARGE_KEY        = "notice:charge:%s:%d_%s"
	MSG_LOCK_KEY             = "msg_lock:%s"
	MSG_SEC_DIFF_LOCK        = "notify_lock:msg_sec_diff"
	MSG_MANY_LOCK            = "notify_lock:msg_many"
	TEXT_MASK_MSG_LIST_KEY   = "text_mask_msg_list"
	APP_AUDIT_PLAN_KEY       = "app_audit_plan:%d"
	UNDRESS_COUNT_KEY        = "undress_count:%s:%d_%s"
	PUSH_MSG_LOCK_KEY        = "push_msg_lock:%d_%s_%s" // 用户ID,AI角色ID,开始时间,结束时间
)

const (
	// 2024-12-26 新增礼物引导 孙宏伟需求 redis Key
	GUIDED_GIFT_ROUND_KEY  = "guided:round:guided_gift_round_uid_%d_%d_%d" // 用户的id ai_role_id app_id 匹配 存储用户的回合数
	GUIDED_GIFT_NUM_KEY    = "guided:num:guided_gift_num_uid_%d_%d_%d"     // 用户的id ai_role_id app_id 匹配 存储用户的次数
	GUIDED_GIFT_CONFIG_KEY = "guided:config:guided_gift_config_%d"         // app_id  匹配 存储引导送礼配置
	GUIDED_GIFT_Record_KEY = "guided:record:guided_gift_record_%d_%d_%d"   // 用户的id app_id ai_role_id 匹配 存储该用户是否达标
	GUIDED_GIFT_GIVING_KEY = "guided_gift_giving"
	GUIDED_GIFT_TXT_KEY    = "guided:guided_gift_txt_%d_%s_%d_%s"  // 用户的id ai_role_id app_id 匹配 存储该用户的文案
	GUIDED_GIFT_ICON_KEY   = "guided:guided_gift_icon_%d_%s_%d_%s" // 用户的id ai_role_id app_id 匹配 存储该用户的文案

)

const VIDIFT_USER_TO_VIDEO_QUEUE = "vidify:user:video:queue"
const VIDIFT_UPLOAD_VIDEO_QUEUE = "vidify:upload:video:queue"

// 2025-01-06 孙宏伟需求 ai_label redis Key
const AI_ROLE_LABEL_KEY = "label:ai_role_label:%s"

const (
	APP_DIY_PHOTO_CONFIG        = "app_diy_photo_config:%d"
	FLOWER_DIALOG_TIP_CONFIG    = "flower_dialog_tip_config"
	VIP_DIALOG_PLAN_CONFIG      = "vip_dialog_plan_config"
	VIP_DIALOG_TWO_TXT_CONFIG   = "vip_dialog_plan_two_txt_config:%d"
	VIP_DIALOG_DOWN_CONFIG      = "vip_dialog_down_config"
	SUB_LOW_COUNTRY_CONFIG      = "sub_low_country_config"
	REPLY_MSG_RATIO_CONFIG_FREE = "reply_msg_ratio_config:free"
	REPLY_MSG_RATIO_CONFIG_SUB  = "reply_msg_ratio_config:sub"
	SEEU_RATIO_CONFIG_FREE      = "seeu_ratio_config:free"
	SEEU_RATIO_CONFIG_SUB       = "seeu_ratio_config:sub"
	APP_OTHER_CONFIG            = "app_other_config"
	MsgReqMapConfigRdsKey       = "msg_req_map_config_rds_key"
)

const (
	// 12-31 增加快捷回复消息下发付费 redis Key
	USER_QUICK_REPLY_COST_RDS_KEY = "quick_reply:user_quick_reply_cost_res_key_%d_%d"
)

const USER_BODY_IMG_ARR_RDS_KEY = "user_body_img_arr:%d:%d"
const VIP_DIALOG_TWO_AUDIT_TXT_CONFIG = "vip_dialog_plan_two_audit_txt_config:%d"
const AppVoiceCallCfgKey = "AppVoiceCallCfg:%d"
const DiyPhotoLv2TacticsCfgRdsKey = "DiyPhotoLv2TacticsCfg:%d"
const FlowerShopSVipTextListCfgRdsKey = "FlowerShopSVipTextListCfg:%d"
const FlowerShopSVipTextListAuditCfgRdsKey = "FlowerShopSVipTextListAuditCfg:%d"
const SVipDialogCfgRdsKey = "SVipDialogCfg:%d"
const SVipDialogAuditCfgRdsKey = "SVipDialogAuditCfg:%d"
const SplashAdCfgRdsKey = "SplashAdCfg:%d"
const ToPayCenterCountryGoodCfgRdsKey = "ToPayCenterCountryGoodCfg:%d"
const ToPayCenterCountrySubCfgRdsKey = "ToPayCenterCountrySubCfg:%d"
const SVipDialogTipCfgRdsKey = "SVipDialogTip:%d"
const ChatVipDialogTipCfgRdsKey = "ChatVipDialogTip:%d"
const ChatVipDialogTipAuditCfgRdsKey = "ChatVipDialogTipAudit:%d"
const ChatVipDialogV2CfgRdsKey = "ChatVipDialogV2Cfg:%d"
const SVipDialogTipAuditCfgRdsKey = "SVipDialogTipAudit:%d"
const ContentVipDialogTipCfgRdsKey = "ContentVipDialogTip:%d"
const ContentVipDialogTipAuditCfgRdsKey = "ContentVipDialogTipAudit:%d"
const ContentVipDialogV2CfgRdsKey = "ContentVipDialogV2Cfg:%d"
const GenAiPreCfgRdsKey = "GenAiPreCfg:%d"
const RequireImgSubMsgCfgRdsKey = "RequireImgSubMsgCfg:%d"
const SVipDialogV2CfgRdsKey = "SVipDialogV2Cfg:%d"
const SVipDialogV390CfgRdsKey = "SVipDialogV390Cfg:%d"
const VipDialogV2CfgRdsKey = "VipDialogV2Cfg:%d"
const VipDialogV390CfgRdsKey = "VipDialogV390Cfg:%d"
const DiyPhotoCountRangeCfgRdsKey = "DiyPhotoCountRangeCfg:%d"
const AiSendUserGiftImgCfgRdsKey = "AiSendUserGiftImgCfg:%d"
const AiSendUserGiftTxtCfgRdsKey = "AiSendUserGiftTxtCfg:%d"
const ReSubRewardCoinCfgRdsKey = "ReSubRewardCoinCfg:%d"
const USER_AI_QUICK_REPLY_RDS_KEY = "UserAiQuickReplyRdsKey:%d:%s" // uid、aiRoleId
const DayCreateUsersRdsKey = "DayCreateUsersRdsKey:%s"             // day
const DayActiveUsersRdsKey = "DayActiveUsersRdsKey:%s"             // day
const RequireImgBoxCfgRdsKey = "RequireImgBoxCfg:%d"

const (
	// 消息广告方案key
	MSG_ADS_SHOW_COUNT_KEY             = "msg_ads_show_count:%s:%d"
	MSG_ADS_PLAN_ONE_CONFIG_KEY        = "msg_ads_plan_one_config"
	MSG_ADS_PLAN_TWO_CONFIG_KEY        = "msg_ads_plan_two_config"
	SignInNoticeCfg                    = "SignInNoticeCfg"
	IntimateLevelCfg                   = "IntimateLevelCfg"
	IntimateLevelAppIdCfg              = "IntimateLevelAppIdCfg:%d"
	IntimateDialogCfg                  = "IntimateDialogCfg"
	IntimateDialogAppIdCfg             = "IntimateDialogAppIdCfg:%d"
	IntimateUpgradeMsgAppIdCfg         = "IntimateUpgradeMsgAppIdCfg:%d"
	IntimateUpgradeCfg                 = "IntimateUpgradeCfg"
	IntimateUpgradeDialogCfg           = "IntimateUpgradeDialogCfg"
	IntimateUpgradeDialogAppIdCfg      = "IntimateUpgradeDialogAppIdCfg:%d"
	IntimateUpgradeDialogAuditCfg      = "IntimateUpgradeDialogAuditCfg"
	IntimateUpgradeDialogAuditAppIdCfg = "IntimateUpgradeDialogAuditAppIdCfg:%d"
	MsgModelStrageCfg                  = "MsgModelStrageCfg"
	RewardUnlockImgKey                 = "Reward:UnlockImg:%d:%d"
	RewardUnlockVideoKey               = "Reward:UnlockVideo:%d:%d"
	RewardVoiceCallKey                 = "Reward:VoiceCall:%d:%d"
	RewardAsmrKey                      = "Reward:Asmr:%d:%d"
	RewardGiftKey                      = "Reward:Gift:%d:%d"
	SubNoBuyFloCfg                     = "SubNoBuyFloCfg"
	AiFuncSwitchCfg                    = "AiFuncSwitchCfg:%d"
)

const (
	EXCHANGE_RATE_KEY      = "order:exchange_rate_data"
	GOOGLE_NOTIFY_LOCK_KEY = "google:subscribe:callback:%s"
	APPLE_NOTIFY_LOCK_KEY  = "apple:subscribe:callback:%s"
	ADJUST_ORDER_LOCK_KEY  = "adjust_order_lock:%s"
	UserDayActiveKey       = "user_day_active:%s:%d"
	UserDayActiveExpire    = time.Hour * 24 * 3
)

const USER_AIROLE_SEND_MSG_COUNT_KEY = "user_role_send_msg_count:%s_%d" // aiRoleId、uid
const USER_TOTAL_AIROLE_KEY = "user_total_airole:%d"                    // uid
const USER_BADGE_KEY = "user_badge:%d:%d"                               // uid, badgeId
const USER_CONTINUOUS_LOGIN_DAY_KEY = "user_continuous_login:%d"        // uid
const FIREBASE_REPORT = "firebase:report:%d"                            // uid
const FIREBASE_REPORT_USER = "firebase_report_user:%d"                  // uid

func GetSubDialogHashKey(userId uint) string {
	now := time.Now()
	dayStr := now.Format(time.DateOnly)
	return fmt.Sprintf(SUB_DIALOG_HASH_KEY, dayStr, userId)
}
