package initialize

import (
	"aimsg-server/global"
	"cloud.google.com/go/translate"
	"context"
	"google.golang.org/api/option"
)

func InitTranslateClient() {
	var (
		err         error
		transClient *translate.Client
		ctx         = context.Background()
	)
	transClient, err = translate.NewClient(ctx, option.WithAPIKey("AIzaSyAUJ7BUN290n4HNUQCmjXE0vTiGUgdVQ3E"))
	if err != nil {
		panic(err)
	}
	global.TranslateClient = transClient
}
