package initialize

import (
	"context"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/utils"
	"time"
)

// NewZapLogger 将 Zap 记录器转换为 gorm 使用的 Logger
func NewZapLogger(logger *zap.Logger) logger.Interface {
	return &ZapLogger{logger: logger}
}

// ZapLogger 实现了 gorm 的 Logger 接口
type ZapLogger struct {
	logger *zap.Logger
}

func (z *ZapLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	z.logger.Warn(msg, zap.Any("context", ctx), zap.Any("data", data))
}

func (z *ZapLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	z.logger.Error(msg, zap.Any("context", ctx), zap.Any("data", data))
}

func (z *ZapLogger) Trace(ctx context.Context, begin time.Time, fc func() (sql string, rowsAffected int64), err error) {
	elapsed := time.Since(begin)
	sql, rows := fc()
	logFields := []zap.Field{
		zap.Any("context", ctx),
		zap.String("caller", utils.FileWithLineNum()),
		zap.String("sql", sql),
		zap.Int64("rows_affected", rows),
		zap.Error(err),
		zap.Duration("elapsed", elapsed),
		zap.String("business", "gorm"),
	}
	if err != nil {
		z.logger.Error("gorm", logFields...)
	} else {
		z.logger.Info("gorm", logFields...)
	}
}

// LogMode 实现了 gorm 的 Logger 接口中的 LogMode 方法
func (z *ZapLogger) LogMode(level logger.LogLevel) logger.Interface {
	// 省略不同日志模式下的配置
	// 注意：根据你的需求修改以下代码
	switch level {
	case logger.Silent:
		return &ZapLogger{logger: z.logger.WithOptions(zap.IncreaseLevel(zapcore.FatalLevel))}
	case logger.Error:
		return &ZapLogger{logger: z.logger.WithOptions(zap.IncreaseLevel(zapcore.ErrorLevel))}
	case logger.Warn:
		return &ZapLogger{logger: z.logger.WithOptions(zap.IncreaseLevel(zapcore.WarnLevel))}
	case logger.Info:
		return &ZapLogger{logger: z.logger.WithOptions(zap.IncreaseLevel(zapcore.InfoLevel))}
	default:
		return &ZapLogger{logger: z.logger.WithOptions(zap.IncreaseLevel(zapcore.InfoLevel))}
	}
}

// LogMode 实现了 gorm 的 Logger 接口中的 LogMode 方法
func (z *ZapLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	z.logger.Info(msg, zap.Any("context", ctx), zap.Any("data", data))
}
