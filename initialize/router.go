package initialize

import (
	"aimsg-server/docs"
	"aimsg-server/global"
	"aimsg-server/middleware"
	"aimsg-server/router"
	"aimsg-server/utils"
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"runtime"
	"strings"
	"time"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"git.costnovel.com/center/middleware-sdk/notice"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// LogEntry 结构体用于存储 JSON 日志数据
type LogEntry struct {
	Timestamp string      `json:"timestamp"`
	Status    int         `json:"status"`
	Latency   string      `json:"latency"`
	ClientIP  string      `json:"client_ip"`
	Method    string      `json:"method"`
	Path      string      `json:"path"`
	Headers   http.Header `json:"headers"` // 直接使用 http.Header 类型
	Body      string      `json:"body"`
	Debug     interface{} `json:"debug"`
}

// CustomLoggerFormatter 以 JSON 格式记录日志
func CustomLoggerFormatter(param gin.LogFormatterParams) string {
	// 获取 Headers（避免类型转换错误）
	headers, _ := param.Keys["Headers"].(http.Header)
	var debugUid interface{}
	if len(headers["Authorization"]) == 1 && len(strings.Split(headers["Authorization"][0], " ")) == 2 {
		authorizationStrArr := strings.Split(headers["Authorization"][0], " ")
		token := authorizationStrArr[1]
		claims, err := utils.JwtCliObj.ParseToken(token)
		if err == nil {
			debugUid, _ = claims["id"]
		}
	}
	body, _ := param.Keys["Body"].(string)
	// 构建 JSON 日志对象
	logEntry := LogEntry{
		Timestamp: param.TimeStamp.Format(time.RFC3339),
		Status:    param.StatusCode,
		Latency:   param.Latency.String(),
		ClientIP:  param.ClientIP,
		Method:    param.Method,
		Path:      param.Path,
		Headers:   headers,
		Body:      body,
		Debug: map[string]interface{}{
			"uid": debugUid,
		},
	}

	// **格式化 JSON**
	//jsonLog, _ := json.MarshalIndent(logEntry, "", "  ") // 每级缩进 2 个空格
	jsonLog, _ := json.Marshal(logEntry)
	// **将日志写入文件**
	global.LOG.Info(string(jsonLog))
	return string(jsonLog) + "\n"
}

// CustomLoggerMiddleware 读取 Header 和 Body，存入 Gin 上下文
func CustomLoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 存储 Headers（直接存 `http.Header` 避免转换问题）
		c.Set("Headers", c.Request.Header)

		// 读取 Body（避免数据丢失，需复制）
		bodyBytes, _ := io.ReadAll(c.Request.Body)
		c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes)) // 重新赋值 Body
		c.Set("Body", string(bodyBytes))                          // 存储 Body 供日志使用

		c.Next() // 继续处理请求
	}
}

// Routers 初始化总路由
func Routers() *gin.Engine {
	// if global.CONFIG.System.Env == "public" {
	// 	gin.SetMode(gin.ReleaseMode)
	// }
	Router := gin.New()
	// pprof.Register(Router, "api/ai_server/debug/pprof")
	Router.Use(gin.LoggerWithFormatter(CustomLoggerFormatter))
	Router.Use(CustomLoggerMiddleware())
	if runtime.GOOS == "linux" {
		Router.Use(notice.RecoveryForGin(global.CONFIG.System.Env, global.CONFIG.System.Name, global.LOG))
	}
	Router.LoadHTMLFiles("template/result.html")
	systemRouter := router.RouterGroupApp.System
	Router.StaticFS(global.CONFIG.Local.StorePath, http.Dir(global.CONFIG.Local.StorePath))
	// CORS
	corsCfg := cors.DefaultConfig()
	corsCfg.AllowAllOrigins = true
	corsCfg.AllowHeaders = []string{"*"}
	corsCfg.AllowMethods = []string{"*"}
	Router.Use(cors.New(corsCfg))
	docs.SwaggerInfo.BasePath = global.CONFIG.System.RouterPrefix
	cliRouter := router.RouterGroupApp.Cli
	PublicGroup := Router.Group(global.CONFIG.System.RouterPrefix)
	PublicNoPrefixGroup := Router.Group("")
	CliPublicGroup := Router.Group("")
	CliPublicGroup.Use(middleware.CliHeader())
	CliGroup := CliPublicGroup.Group("")
	CliGroup.Use(middleware.JWTCliAuth())
	cliRouter.InitCallBackRouter(PublicGroup)
	cliRouter.InitAgoraRouter(PublicGroup)
	cliRouter.InitCliVideoCallPubRouter(PublicGroup)
	cliRouter.InitCliOrdersPythonRouter(PublicNoPrefixGroup)
	cliRouter.InitCliDigitalUserFileRouter(PublicNoPrefixGroup)
	cliRouter.InitOrdersPubRouter(PublicNoPrefixGroup)
	cliRouter.InitCliRouter(PublicGroup, model.App{})
	// 循环创建各个APP的路由
	haseBeenInitMap := map[string]bool{}
	for _, app := range global.AppMap {
		if app.Prefix != "" && !haseBeenInitMap[app.Prefix] {
			cliRouter.InitCliRouter(PublicGroup, app)
			haseBeenInitMap[app.Prefix] = true
		}
	}
	{
		systemRouter.InitBaseRouter(PublicGroup)
		systemRouter.InitInitRouter(PublicGroup)
		cliRouter.InitCliSystemRouter(PublicGroup)
		cliRouter.InitOrdersPubRouter(PublicGroup)
		cliRouter.InitOrdersResPubRouter(PublicGroup)
	}
	{
		paymentWebhookRouter := router.RouterGroupApp.PaymentWebhook
		paymentWebhookRouter.InitPaymentWebhookRouter(PublicGroup)
	}
	PrivateGroup := Router.Group(global.CONFIG.System.RouterPrefix)
	PrivateGroup.Use(middleware.JWTAuth())
	{
		systemRouter.InitApiRouter(PrivateGroup, PublicGroup)
		systemRouter.InitJwtRouter(PrivateGroup)
		systemRouter.InitUserRouter(PrivateGroup)
		systemRouter.InitMenuRouter(PrivateGroup)
		systemRouter.InitSystemRouter(PrivateGroup)
		systemRouter.InitCasbinRouter(PrivateGroup)
		systemRouter.InitAutoCodeRouter(PrivateGroup)
		systemRouter.InitAuthorityRouter(PrivateGroup)
		systemRouter.InitSysDictionaryRouter(PrivateGroup)
		systemRouter.InitAutoCodeHistoryRouter(PrivateGroup)
		systemRouter.InitSysOperationRecordRouter(PrivateGroup)
		systemRouter.InitSysDictionaryDetailRouter(PrivateGroup)
		systemRouter.InitAuthorityBtnRouterRouter(PrivateGroup)
		systemRouter.InitSysExportTemplateRouter(PrivateGroup)
	}
	{
		backendRouter := router.RouterGroupApp.Backend
		backendRouter.InitDiyPhotoTaskPubRouter(PublicGroup)
		backendRouter.InitDigitalUserPubRouter(PublicGroup)
		backendRouter.InitOrdersTestPubRouter(PublicGroup)
		backendRouter.InitMsgTimeRecordRouter(PrivateGroup)
		backendRouter.InitAmazonS3Router(PrivateGroup)
		backendRouter.InitAppRouter(PrivateGroup)
		backendRouter.InitAiRoleRouter(PrivateGroup)
		backendRouter.InitAiRoleImgInfoRouter(PrivateGroup)
		backendRouter.InitAiRoleMediaRouter(PrivateGroup)
		backendRouter.InitAdjustLogRouter(PrivateGroup)
		backendRouter.InitDigitalUserRouter(PrivateGroup)
		backendRouter.InitDigitalUserProfileRouter(PrivateGroup)
		backendRouter.InitFeedbackRecordRouter(PrivateGroup)
		backendRouter.InitFlowerConsumeRecordRouter(PrivateGroup)
		backendRouter.InitOrdersRouter(PrivateGroup)
		backendRouter.InitProductRouter(PrivateGroup)
		backendRouter.InitReSubOrdersRouter(PrivateGroup)
		backendRouter.InitUsersIntimateRouter(PrivateGroup)
		backendRouter.InitChatGuideRecordRouter(PrivateGroup)
		backendRouter.InitUserAiRoleFuncRouter(PrivateGroup)
		backendRouter.InitMsgJobRouter(PrivateGroup)
		backendRouter.InitConfigPlanRouter(PrivateGroup)
		backendRouter.InitPayMethodsRouter(PrivateGroup)
		backendRouter.InitAiSendMediaRouter(PrivateGroup)
		backendRouter.InitAiCommonReplyRouter(PrivateGroup)
		backendRouter.InitFcmRouterPublic(PublicGroup)
		backendRouter.InitFcmRouterPrivate(PrivateGroup)
		backendRouter.InitProductInfoRouter(PrivateGroup)
		backendRouter.InitTouchOptionRouter(PrivateGroup)
		backendRouter.InitSyncDataRouter(PrivateGroup)
		backendRouter.InitUndressRecordRouter(PrivateGroup)
		backendRouter.InitImgConfigRouter(PrivateGroup)
		backendRouter.InitDailyReportRouter(PrivateGroup)
		backendRouter.InitWithdrawHistoryRouter(PrivateGroup)
		backendRouter.InitKocUserInfoRouter(PrivateGroup)
		backendRouter.InitSysUserKocRouter(PrivateGroup)
		backendRouter.InitAiScenesRouter(PrivateGroup)
		backendRouter.InitUserUseScenesRouter(PrivateGroup)
		backendRouter.InitPushMsgRouter(PrivateGroup)
		backendRouter.InitGiftRouter(PrivateGroup)
		backendRouter.InitGiftAiImgRouter(PrivateGroup)
		backendRouter.InitDiyPhotoTaskRouter(PrivateGroup)
		backendRouter.InitBadgeRouter(PrivateGroup)
		backendRouter.InitUserBadgeRouter(PrivateGroup)
		backendRouter.InitAiProfileInfoRouter(PrivateGroup)
		backendRouter.InitAiContactInfoRouter(PrivateGroup)
		backendRouter.InitAppPageBannerRouter(PrivateGroup)
		backendRouter.InitDiyConfigRouter(PrivateGroup)
		backendRouter.InitImgTemplateCategoryRouter(PrivateGroup)
		backendRouter.InitImgTemplateRecordRouter(PrivateGroup)
		backendRouter.InitImgGenRecordRouter(PrivateGroup)
		backendRouter.InitVoiceCallRouter(PrivateGroup)
		backendRouter.InitVideoTxtRouter(PrivateGroup)
		backendRouter.InitImgToVideoRouter(PrivateGroup)
		backendRouter.InitLiveGiftRouter(PrivateGroup)
		backendRouter.InitLiveInfoRouter(PrivateGroup)
		backendRouter.InitLivePeopleRouter(PrivateGroup)
		backendRouter.InitLiveReceiveGiftRouter(PrivateGroup)
		backendRouter.InitVoiceInfoRouter(PrivateGroup)
		backendRouter.InitLiveReceiveFansRouter(PrivateGroup)
		backendRouter.InitPrivateSpaceRouter(PrivateGroup)
		backendRouter.InitPrivateSpaceUserRouter(PrivateGroup)
		backendRouter.InitUserAiMsgCountRouter(PrivateGroup)
		backendRouter.InitAiSortStatisticsRouter(PrivateGroup)
		backendRouter.InitAiBodyImgRouter(PrivateGroup)
		backendRouter.InitAiBodyImgCacheRouter(PrivateGroup)
		backendRouter.InitVideoCallRouter(PrivateGroup)
		backendRouter.InitUserFeedbackBackendRouter(PublicGroup)
		backendRouter.InitVideoSimplyBackendRouter(PublicGroup)
		backendRouter.InitBtcBackendRouter(PrivateGroup)
		backendRouter.InitSpeedBalanceRouter(PrivateGroup)
	}
	return Router
}
