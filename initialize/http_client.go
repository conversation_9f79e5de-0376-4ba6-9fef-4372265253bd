package initialize

import (
	"aimsg-server/global"
	"net/http"
	"time"
)

func HttpClient() {
	// 设置代理服务器的地址和端口
	// proxyURL, err := url.Parse("http://127.0.0.1:7890")
	// if err != nil {
	// 	panic(err)
	// }
	global.Ht = &http.Client{
		Timeout: time.Duration(10) * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        200,
			MaxIdleConnsPerHost: 10,
			MaxConnsPerHost:     10,
			IdleConnTimeout:     time.Duration(60) * time.Second,
			// Proxy:               http.ProxyURL(proxyURL),
		},
	}
}
