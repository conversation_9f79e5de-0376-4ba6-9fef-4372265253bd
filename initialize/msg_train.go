package initialize

import (
	"aimsg-server/service"
	"aimsg-server/service/backend/ai_role"
)

func Msg() {
	objService := service.ServiceGroupApp.BackendServiceGroup.MsgService
	imgToImgTrainService := service.ServiceGroupApp.BackendServiceGroup.ImgToImgTrainService
	orderService := service.ServiceGroupApp.BackendServiceGroup.OrdersService

	guidedGiftGivingService := service.ServiceGroupApp.BackendServiceGroup.GuidedGiftGivingService

	videoSimplifyService := service.ServiceGroupApp.BackendServiceGroup.VideoSimplifyImgVideoService

	go orderService.SubNoBuyFloQueue()
	// 催单消息脚本
	go objService.NotifyChargeTrain()
	// 空闲消息脚本
	go objService.IdleMsgTrain()

	// 直播消息脚本
	go objService.LivePushMsgTrain()

	// 延迟生成私人订制脚本
	go imgToImgTrainService.DelayImg2imgTrain()

	// 索要照片私人订制脚本
	go imgToImgTrainService.RequireImgTrain()

	// 生成图片脚本
	go imgToImgTrainService.ReceiveImg2imgTrain()

	// 生成视频脚本
	go imgToImgTrainService.ReceiveImg2VideoTrain()

	// 消费引导消息脚本 2024-12-26 新增礼物引导 孙宏伟需求
	go guidedGiftGivingService.GuidedGiftGivingMsg()

	// 消费图生视频的创建消息
	go videoSimplifyService.VideoSimplifyConsumeMsg()

	// 消费图生视频的上传upload消息
	go videoSimplifyService.VideoSimplifyUploadVideoConsumeMsg()
	go ai_role.Crontab()

	// 消息训练脚本
	objService.MsgTrain()

	// objService.MsgTrainNew()
	// ctx := context.Background()
	// objService.GetMsgHistory(ctx, "129559", "0000047")
	// lastMsg, err := objService.LastMsg(ctx, "134510", "0000028")
	// if err != nil {
	// 	return
	// }
	// global.GVA_LOG.Info("lastMsg", zap.Any("lastMsg", lastMsg))
}
