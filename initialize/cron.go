package initialize

import (
	"aimsg-server/service"
	"aimsg-server/service/backend/ai_role"
	"github.com/gin-gonic/gin"
	"github.com/robfig/cron/v3"
	"time"
)

func Crontab() {
	// ChangeAiRoleSort{}.Run()
	// return
	var err error
	c := cron.New()
	// 每天12点更新排序
	if _, err = c.<PERSON>("0 12 * * *", ChangeAiRoleSort{}); err != nil {
		panic(err)
	}
	// if _, err = c.<PERSON>d<PERSON>ob("10 7 * * *", ChangeAiRoleSort{}); err != nil {
	// 	panic(err)
	// }

	// 每天00:05统计上一天daily_report数据
	if _, err = c.<PERSON>d<PERSON>ob("5 0 * * *", DailyReportStatistics{}); err != nil {
		panic(err)
	}

	if _, err = c.<PERSON>d<PERSON>ob("0 18 * * *", DailyPushReceiveFlo{}); err != nil {
		panic(err)
	}

	if _, err = c.<PERSON>d<PERSON>("*/3 * * * *", ResetSubStatus{}); err != nil {
		panic(err)
	}

	if _, err = c.<PERSON>d<PERSON>("*/2 * * * *", VideoSimplify{}); err != nil {
		panic(err)
	}
	if _, err = c.AddJob("* * * * *", Btc{}); err != nil {
		panic(err)
	}

	c.Start()
}

type Btc struct{}

func (Btc) Run() {
	service.ServiceGroupApp.BackendServiceGroup.BtcService.Crontab2()
	service.ServiceGroupApp.BackendServiceGroup.BtcService.CrontabCalYestoday()
}

type ChangeAiRoleSort struct{}

func (ChangeAiRoleSort) Run() {
	go ai_role.Crontab()
	AiRoleService := service.ServiceGroupApp.BackendServiceGroup.AiRoleService
	AiRoleService.SortNew()
}

type DailyReportStatistics struct{}

func (DailyReportStatistics) Run() {
	//go ai_role.Crontab()

	dailyReportService := service.ServiceGroupApp.BackendServiceGroup.DailyReportService
	yesterday := time.Now().AddDate(0, 0, -1)
	// 每天凌晨1点统计昨天的数据
	dailyReportService.Statistics(yesterday)
}

type DailyPushReceiveFlo struct{}

func (DailyPushReceiveFlo) Run() {
	fcmSer := service.ServiceGroupApp.BackendServiceGroup.FcmService
	fcmSer.SignInNotice()
}

type ResetSubStatus struct{}

func (ResetSubStatus) Run() {
	objSer := service.ServiceGroupApp.BackendServiceGroup.DigitalUserService
	objSer.ResetSubStatus()
}

type VideoSimplify struct{}

func (VideoSimplify) Run() {
	objSer := service.ServiceGroupApp.BackendServiceGroup.VideoSimplifyImgVideoService
	var c *gin.Context
	objSer.GetApiTaskStatus(c)

}
