package initialize

import (
	"aimsg-server/global"
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"runtime"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

func Redis() {
	redisCfg := global.CONFIG.Redis
	// 设置TLS配置
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true, // 如果不需要验证服务器证书，设置为true
	}
	rdsOptions := &redis.Options{
		Addr:         redisCfg.Addr,
		Password:     redisCfg.Password, // no password set
		DB:           redisCfg.DB,       // use default DB
		ReadTimeout:  time.Second * 3,
		WriteTimeout: time.Second * 3,
	}
	if global.CONFIG.System.Env == "public" {
		rdsOptions.TLSConfig = tlsConfig
	}
	client := redis.NewClient(rdsOptions)

	// 添加 Hook
	// client.AddHook(redisHook{})
	pong, err := client.Ping(context.Background()).Result()
	if err != nil {
		global.LOG.Error("redis connect ping failed, err:", zap.Error(err))
		panic(err)
	} else {
		global.LOG.Info("redis connect ping response:", zap.String("pong", pong))
		global.REDIS = client
	}
}

type redisHook struct{}

func (redisHook) DialHook(hook redis.DialHook) redis.DialHook {
	return func(ctx context.Context, network, addr string) (net.Conn, error) {
		conn, err := hook(ctx, network, addr)
		return conn, err
	}
}

func (redisHook) ProcessHook(hook redis.ProcessHook) redis.ProcessHook {
	return func(ctx context.Context, cmd redis.Cmder) error {
		if runtime.GOOS != "darwin" {
			fmt.Printf("starting processing: %v\n", cmd)
		}
		err := hook(ctx, cmd)
		return err
	}
}

func (redisHook) ProcessPipelineHook(hook redis.ProcessPipelineHook) redis.ProcessPipelineHook {
	return func(ctx context.Context, cmds []redis.Cmder) error {
		fmt.Printf("pipeline starting processing: %v\n", cmds)
		err := hook(ctx, cmds)
		return err
	}
}
