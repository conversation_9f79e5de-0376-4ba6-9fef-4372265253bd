package initialize

import (
	"aimsg-server/global"
	"context"
	"fmt"
	"github.com/redis/go-redis/v9"
	"os"
	"runtime"
	"time"
)

func WatchMem() {
	ctx := context.Background()
	ticker := time.NewTicker(3 * time.Second)
	defer ticker.Stop()
	hostname, err := os.Hostname()
	if err != nil {
		fmt.Println("Error getting hostname:", err)
		hostname = "unknown"
	}

	for range ticker.C {
		memStats := new(runtime.MemStats)
		runtime.ReadMemStats(memStats)
		memoryUsage := memStats.Alloc / 1024 / 1024
		nowTime := time.Now()
		nowTimeStr := nowTime.Format("2006-01-02 15:04:05")
		// 格式化内存占用和时间
		data := fmt.Sprintf("%s_%dM", nowTimeStr, memoryUsage)
		// 当前时间戳（作为分数使用）
		nowUnix := nowTime.Unix()
		rdsKey := fmt.Sprintf("golang_memory_usage_%s", hostname)

		err = global.REDIS.ZAdd(ctx, rdsKey, redis.Z{
			Score:  float64(nowUnix),
			Member: data,
		}).Err()
		if err != nil {
			fmt.Println("Error storing data to Redis:", err)
		}
		// 删除超过一小时的数据（当前时间戳 - 3600）
		oneHourAgo := nowUnix - 3600
		err = global.REDIS.ZRemRangeByScore(ctx, rdsKey, "0", fmt.Sprintf("%d", oneHourAgo)).Err()
		if err != nil {
			fmt.Println("Error removing old data:", err)
		}
	}
}
