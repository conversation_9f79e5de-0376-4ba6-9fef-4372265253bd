package kafka

import (
	"aimsg-server/global"
	"github.com/IBM/sarama"
	"go.uber.org/zap"
	"time"
)

var Client sarama.Client

func InitKafka() {
	config := sarama.NewConfig()
	config.Producer.Timeout = 5 * time.Second
	config.Producer.Return.Successes = false
	config.Producer.Return.Errors = true
	config.Producer.Retry.Max = 3
	// config.Producer.Flush.Frequency = time.Second * 1
	// config.Producer.Flush.MaxMessages = 10

	config.Version = sarama.V2_4_0_0
	config.Consumer.Offsets.AutoCommit.Enable = true
	config.Consumer.Offsets.AutoCommit.Interval = time.Second * 1
	config.Consumer.Offsets.Initial = sarama.OffsetOldest
	config.Consumer.Offsets.Retry.Max = 3
	config.Consumer.Return.Errors = true

	client, err := sarama.NewClient(global.CONFIG.Kafka.Host, config)
	if err != nil {
		global.LOG.Panic("初始化 kafka 客户端失败", zap.Error(err))
	}

	Client = client
	initKafkaProducer()
}
