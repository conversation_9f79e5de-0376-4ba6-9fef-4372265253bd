package kafka

import (
	"aimsg-server/global"
	"github.com/IBM/sarama"
	"go.uber.org/zap"
)

var syncProducer sarama.AsyncProducer

func initKafkaProducer() {
	var (
		err   error
		syncP sarama.AsyncProducer
	)

	syncP, err = sarama.NewAsyncProducerFromClient(Client)
	if err != nil {
		global.LOG.Panic("初始化 kafka 生产者失败", zap.Error(err))
	}
	syncProducer = syncP

	go func() {
		for {
			select {
			case err = <-syncProducer.Errors():
				global.LOG.Error("kafka 生产者发送消息失败", zap.Error(err))
			}
		}
	}()
}

func makeMessage(topic string, msg []byte) *sarama.ProducerMessage {
	msgObj := &sarama.ProducerMessage{
		Topic: topic,
		Value: sarama.ByteEncoder(msg),
	}
	return msgObj
}

func SendRecordMessage(topic string, body []byte) {
	syncProducer.Input() <- makeMessage(topic, body)
}
