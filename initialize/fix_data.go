package initialize

import (
	req "aimsg-server/model/backend/request"
	"aimsg-server/service"
	"fmt"
)

func CopyAppInfo() {
	var (
		ser     = service.ServiceGroup{}.BackendServiceGroup.AppService
		reqData = &req.CopyAppDataReq{
			SourceAppId: 1,
			TargetAppId: 11,
		}
	)
	err := ser.CopyAppData(reqData)
	if err != nil {
		fmt.Println(err.Error())
		return
	}
}

func ImgSwap() {
	var (
		ser         = service.ServiceGroup{}.BackendServiceGroup.ImgToImgTrainService
		avatarS3Url = "public/ai_role/0000050/avatar/2.jpg"
		maskS3Url   = "public/ai_role/theme/Random/0d07a8d85fb11b7fab0d5750e6c7-2288350.jpg"
	)
	imgPubPath, err := ser.Img2ImgReplaceFaceV2(avatarS3Url, maskS3Url)
	if err != nil {
		fmt.Println(err.Error())
		return
	}
	fmt.Println(imgPubPath)
}

func FailTaskAddRds() {
	var (
		ser = service.ServiceGroup{}.BackendServiceGroup.DiyPhotoTaskService
	)
	err := ser.FailTaskAddRds()
	if err != nil {
		fmt.Println(err.Error())
		return
	}
}

func FixAdjust() {
	var adjustSer = service.ServiceGroup{}.BackendServiceGroup.AdjustLogService
	err := adjustSer.FixAdjust()
	if err != nil {
		panic(err)
	}
}

func FixReSub() {
	var oSer = service.ServiceGroup{}.BackendServiceGroup.OrdersService
	oSer.FixReSubOrderVIP()
}

func OrderStaOne() {
	var objSer = service.ServiceGroup{}.BackendServiceGroup.OrdersService
	err := objSer.TmpStaOne()
	if err != nil {
		panic(err)
	}
}
