package initialize

import (
	"aimsg-server/service"
	"runtime"
	"time"
)

func SyncInfo() {
	appService := service.ServiceGroupApp.BackendServiceGroup.AppService
	aiRoleService := service.ServiceGroupApp.BackendServiceGroup.AiRoleService
	msgService := service.ServiceGroupApp.BackendServiceGroup.MsgService
	msgJobService := service.ServiceGroupApp.BackendServiceGroup.MsgJobService
	aiCommonReplyService := service.ServiceGroupApp.BackendServiceGroup.AiCommonReplyService
	chatGuideRecordService := service.ServiceGroupApp.BackendServiceGroup.ChatGuideRecordService
	touchOptionService := service.ServiceGroupApp.BackendServiceGroup.TouchOptionService
	imgConfigService := service.ServiceGroupApp.BackendServiceGroup.ImgConfigService
	pushMsgService := service.ServiceGroupApp.BackendServiceGroup.PushMsgService
	giftService := service.ServiceGroupApp.BackendServiceGroup.GiftService
	aiRoleImgInfoService := service.ServiceGroupApp.BackendServiceGroup.AiRoleImgInfoService
	badgeService := service.ServiceGroupApp.BackendServiceGroup.BadgeService
	appPageBannerService := service.ServiceGroupApp.BackendServiceGroup.AppPageBannerService
	productService := service.ServiceGroupApp.BackendServiceGroup.ProductService
	productInfoService := service.ServiceGroupApp.BackendServiceGroup.ProductInfoService
	diyConfigService := service.ServiceGroupApp.BackendServiceGroup.DiyConfigService
	imgTemplateCategoryService := service.ServiceGroupApp.BackendServiceGroup.ImgTemplateCategoryService
	imgTemplateRecordService := service.ServiceGroupApp.BackendServiceGroup.ImgTemplateRecordService
	videoTxtService := service.ServiceGroupApp.BackendServiceGroup.VideoTxtService
	liveGiftService := service.ServiceGroupApp.BackendServiceGroup.LiveGiftService
	livePeopleService := service.ServiceGroupApp.BackendServiceGroup.LivePeopleService
	voiceInfoService := service.ServiceGroupApp.BackendServiceGroup.VoiceInfoService
	aiBodyImgService := service.ServiceGroupApp.BackendServiceGroup.AiBodyImgService
	btcConfigService := service.ServiceGroupApp.BackendServiceGroup.BtcConfigService
	appService.SyncAppMap()
	// if global.CONFIG.System.Env != "public" {
	// 	global.PayCenterSubUrl = global.PayCenterSubPayUrlTest
	// 	global.PayCenterSubMethodListUrl = global.PayCenterSubMethodListUrlTest
	// }
	if runtime.GOOS == "linux" {
		localInitSerInfo()
	} else {
		go localInitSerInfo()
	}

	// UPDATE digital_user SET sub_status = 3 WHERE sub_status = 200 AND sub_expire < NOW()
	go func() { //todo zwj recover me
		timeObj := time.NewTicker(time.Minute)
		timeTenObj := time.NewTicker(time.Minute * 10)
		timeHourObj := time.NewTicker(time.Hour)
		for {
			select {
			case <-timeObj.C:
				// 同步APP信息
				appService.SyncAppMap()
				// 同步APP其他配置(APP冷启动是否展示订阅弹窗,空闲消息检测秒数,看广告后增加的消息数量)
				appService.SyncAppOtherConfig()
				// 同步所有AI功能开关配置
				appService.SyncAllAiFuncCfg()
				// 同步 消息大模型 请求地址
				msgJobService.SyncMsgJobMap()
				//cloud项目配置
				btcConfigService.SyncAll()
			case <-timeTenObj.C:
				// 订阅弹窗方案1,方案2的图片配置
				imgConfigService.SyncImgConfig()
				// 索要照片数据同步
				aiBodyImgService.SyncAll()
				// 问候消息列表
				pushMsgService.SyncPushMsgList()
				// 礼物数据
				giftService.SyncGiftAll()
				// 创建私有角色的预设置模板
				aiRoleImgInfoService.SyncAll()
				// 初始化默认AI角色
				aiRoleService.InitDefaultAiRole()
				// 徽章列表
				badgeService.SyncAll()
				// banner
				appPageBannerService.SyncAll()
				// 商品
				productService.SyncAll()
				// 商品信息
				productInfoService.SyncAll()
				// 私人定制配置
				diyConfigService.SyncAll()
				// 图片分类
				imgTemplateCategoryService.SyncAll()
				// 图片数据
				imgTemplateRecordService.SyncAll()
				// 视频语音文本
				videoTxtService.SyncAll()
				// 直播礼物
				liveGiftService.SyncAll()
				// 直播人数配置
				livePeopleService.SyncAll()
				// 自建角色语音配置
				voiceInfoService.SyncAll()
			case <-timeHourObj.C:
				// 重新加载文本屏蔽词列表
				msgService.SyncTextMaskMsgList()
				// 重新加载通用回复列表
				aiCommonReplyService.SyncReplyList()
				// 重新加载引导记录列表
				chatGuideRecordService.SyncMsgList()
				// 重新加载快捷回复列表
				chatGuideRecordService.SyncFastMsgList()
				// 重新加载TouchOptionList
				touchOptionService.SyncTouchOptionList()
			}
		}
	}()
}

func localInitSerInfo() {
	appService := service.ServiceGroupApp.BackendServiceGroup.AppService
	aiRoleService := service.ServiceGroupApp.BackendServiceGroup.AiRoleService
	msgService := service.ServiceGroupApp.BackendServiceGroup.MsgService
	msgJobService := service.ServiceGroupApp.BackendServiceGroup.MsgJobService
	aiCommonReplyService := service.ServiceGroupApp.BackendServiceGroup.AiCommonReplyService
	chatGuideRecordService := service.ServiceGroupApp.BackendServiceGroup.ChatGuideRecordService
	touchOptionService := service.ServiceGroupApp.BackendServiceGroup.TouchOptionService
	imgConfigService := service.ServiceGroupApp.BackendServiceGroup.ImgConfigService
	pushMsgService := service.ServiceGroupApp.BackendServiceGroup.PushMsgService
	giftService := service.ServiceGroupApp.BackendServiceGroup.GiftService
	fcmService := service.ServiceGroupApp.BackendServiceGroup.FcmService
	aiRoleImgInfoService := service.ServiceGroupApp.BackendServiceGroup.AiRoleImgInfoService
	badgeService := service.ServiceGroupApp.BackendServiceGroup.BadgeService
	appPageBannerService := service.ServiceGroupApp.BackendServiceGroup.AppPageBannerService
	productService := service.ServiceGroupApp.BackendServiceGroup.ProductService
	productInfoService := service.ServiceGroupApp.BackendServiceGroup.ProductInfoService
	diyConfigService := service.ServiceGroupApp.BackendServiceGroup.DiyConfigService
	imgTemplateCategoryService := service.ServiceGroupApp.BackendServiceGroup.ImgTemplateCategoryService
	imgTemplateRecordService := service.ServiceGroupApp.BackendServiceGroup.ImgTemplateRecordService
	videoTxtService := service.ServiceGroupApp.BackendServiceGroup.VideoTxtService
	liveGiftService := service.ServiceGroupApp.BackendServiceGroup.LiveGiftService
	livePeopleService := service.ServiceGroupApp.BackendServiceGroup.LivePeopleService
	voiceInfoService := service.ServiceGroupApp.BackendServiceGroup.VoiceInfoService
	aiBodyImgService := service.ServiceGroupApp.BackendServiceGroup.AiBodyImgService
	btcConfigService := service.ServiceGroupApp.BackendServiceGroup.BtcConfigService
	appService.SyncAppOtherConfig()
	aiBodyImgService.SyncAll()
	appService.SyncAllAiFuncCfg()
	msgJobService.SyncMsgJobMap()
	msgService.SyncTextMaskMsgList()
	aiCommonReplyService.SyncReplyList()
	giftService.SyncGiftAll()
	chatGuideRecordService.SyncMsgList()
	chatGuideRecordService.SyncFastMsgList()
	touchOptionService.SyncTouchOptionList()
	imgConfigService.SyncImgConfig()
	pushMsgService.SyncPushMsgList()
	aiRoleImgInfoService.SyncAll()
	aiRoleService.InitDefaultAiRole()
	fcmService.InitAllClient()
	badgeService.SyncAll()
	appPageBannerService.SyncAll()
	productService.SyncAll()
	productInfoService.SyncAll()
	go diyConfigService.SyncAll()
	imgTemplateCategoryService.SyncAll()
	imgTemplateRecordService.SyncAll()
	videoTxtService.SyncAll()
	liveGiftService.SyncAll()
	livePeopleService.SyncAll()
	voiceInfoService.SyncAll()
	btcConfigService.SyncAll()
}
