package payment

import (
	"aimsg-server/global"
	"aimsg-server/pkg/channel"
	"aimsg-server/utils"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
)

type PaymentWebhookSpeedService struct{}

func (m *PaymentWebhookSpeedService) VerifySignature(body []byte, signatureHeader, webhookID, webhookTimestamp string) error {
	btcChannel := &model.BtcChannel{}
	if err := global.DB.Where("channel_code = ?", channel.ChannelSpeed.String()).First(btcChannel).Error; err != nil {
		return fmt.Errorf("find app record failed: %v", err.Error())
	}

	// 从 "v1,..." 格式中提取签名部分
	parts := strings.Split(signatureHeader, ",")
	if len(parts) != 2 || parts[0] != "v1" {
		log.Printf("Webhook Error: Invalid signature header format: %s", signatureHeader)
		return errors.New("invalid signature format")
	}
	receivedSignature := parts[1]

	webhookPwd := btcChannel.AppKey

	// --- 官方文档第 2 步: 准备密钥 ---
	// 移除 "wsec_" 前缀
	if !strings.HasPrefix(webhookPwd, "wsec_") {
		log.Println("Webhook Error: Configured secret is not in the correct 'wsec_' format")
		return errors.New("invalid server secret configuration")
	}
	secretWithoutPrefix := strings.TrimPrefix(webhookPwd, "wsec_")

	// 将密钥的剩余部分进行 Base64 解码
	decodedSecret, err := base64.StdEncoding.DecodeString(secretWithoutPrefix)
	if err != nil {
		log.Printf("Webhook Error: Failed to Base64-decode the secret: %v", err)
		return errors.New("server secret decoding failed")
	}

	// --- 官方文档第 3 步: 准备 signed_payload 字符串 ---
	signedPayload := fmt.Sprintf("%s.%s.%s", webhookID, webhookTimestamp, string(body))

	// --- 官方文档第 4 步: 计算预期的签名 ---
	mac := hmac.New(sha256.New, decodedSecret)
	mac.Write([]byte(signedPayload))
	// 将计算结果进行 Base64 编码，而不是 Hex 编码
	calculatedSignature := base64.StdEncoding.EncodeToString(mac.Sum(nil))

	// --- 最后一步: 安全地比较签名 ---
	if !hmac.Equal([]byte(receivedSignature), []byte(calculatedSignature)) {
		log.Printf("Webhook Error: Invalid signature. Received=%s, Calculated=%s", receivedSignature, calculatedSignature)
		return errors.New("invalid signature")
	}
	return nil
}

func (m *PaymentWebhookSpeedService) HandleWebhook(c *gin.Context, webhookChannelOrderNo, webhookEventType, webhookStatus, failureReason string) error {
	// 查找对应的btc trade记录
	var btcTrade model.BtcTrade
	if err := global.DB.Where("channel_order_no = ?", webhookChannelOrderNo).First(&btcTrade).Error; err != nil {
		return fmt.Errorf("find btc trade record failed: %v", err.Error())
	}

	// 根据Speed webhook状态更新记录
	message := failureReason
	switch webhookStatus {
	case "paid":
		// 同时更新主表状态为成功
		if err := global.DB.Model(&model.BtcWithdrawalRecord{}).
			Where("id = ?", btcTrade.WithdrawalID).
			Where("withdrawal_status = ?", model.BtcWithdrawStatusReviewSuccess).
			Update("withdrawal_status", model.BtcWithdrawStatusSuccess).Error; err != nil {
			return fmt.Errorf("update btc withdrawal record failed: %v", err.Error())
		}
		performedAt := time.UnixMilli(btcTrade.CreatedAt.UnixMilli())
		if err := global.DB.Model(&model.BtcTrade{}).
			Where("id = ?", btcTrade.Id).
			Where("trade_status = ?", model.BtcTradeStatusPending).
			Updates(&model.BtcTrade{
				TradeStatus: model.BtcTradeStatusSuccess,
				Message:     message,
				PerformedAt: &performedAt,
			}).Error; err != nil {
			return fmt.Errorf("update btc trade record failed: %v", err.Error())
		}
	case "failed":
		if message == "" {
			message = "Transfer failed"
		}
		// 同时更新主表状态为失败
		if err := global.DB.Model(&model.BtcWithdrawalRecord{}).
			Where("id = ?", btcTrade.WithdrawalID).
			Where("withdrawal_status = ?", model.BtcWithdrawStatusReviewSuccess).
			Updates(&model.BtcWithdrawalRecord{
				WithdrawalStatus: model.BtcWithdrawStatusFailed,
				FailReason:       utils.CheckStrLen(failureReason, 255),
			}).Error; err != nil {
			return fmt.Errorf("update btc withdrawal record failed: %v", err.Error())
		}
		if err := global.DB.Model(&model.BtcTrade{}).
			Where("id = ?", btcTrade.Id).
			Where("trade_status = ?", model.BtcTradeStatusPending).
			Updates(&model.BtcTrade{
				TradeStatus: model.BtcTradeStatusFailed,
				Message:     message,
			}).Error; err != nil {
			return fmt.Errorf("update btc trade record failed: %v", err.Error())
		}
	}

	global.LOG.Sugar().Infof("Speed webhook processed - withdrawal %d, status: %s, message: %s,fail_reason:%s",
		btcTrade.WithdrawalID, webhookStatus, message, failureReason)

	return nil
}
