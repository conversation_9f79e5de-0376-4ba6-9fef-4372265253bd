package feedback

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/utils"
	"encoding/json"
	"errors"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"time"
)

var Service FeedbackService

type FeedbackService struct{}

func buildSingleFeedbackCacheKey(userID uint, roleID string, messageID string) string {
	return fmt.Sprintf("ai:msg_feedback:item:v1%d:%s:%s", userID, roleID, messageID)
}

func (s *FeedbackService) Add(ctx *gin.Context, req *request.FeedbackAddReq) (res *response.FeedbackAddResp, err error) {
	req.UserId = utils.GetDigitalUserID(ctx)
	feedbackFlags := []uint{model.FeedbackFlagLackOfInterest, model.FeedbackFlagToneMismatch, model.FeedbackFlagSlowResponse, model.FeedbackFlagMemoryIssue, model.FeedbackFlagRepeatedContent}
	var flags uint
	if len(req.FeedbackFlags) > 0 {
		for _, v := range req.FeedbackFlags {
			if !lo.Contains(feedbackFlags, v) {
				return nil, errors.New("params err")
			}
			flags += v
		}
	}

	data, err := s.Batch(ctx, &request.FeedbackBatchReq{
		RoleID:     req.RoleID,
		MessageIDs: []string{req.MessageID},
	})
	if err != nil {
		return nil, err
	}
	var record model.MsgFeedback
	_ = copier.Copy(&record, &req)
	record.FeedbackFlags = flags
	if data == nil {
		err = global.DB.Create(&record).Error
		if err != nil {
			return nil, err
		}
	} else {
		key := buildSingleFeedbackCacheKey(req.UserId, req.RoleID, req.MessageID)
		global.REDIS.Del(ctx, key)
		err = global.DB.
			Where("user_id = ?", req.UserId).
			Where("role_id = ?", req.RoleID).
			Where("message_id = ?", req.MessageID).
			Updates(&record).
			Error
		if err != nil {
			return nil, err
		}
	}

	return
}

var feedbackFlagItem = []*response.FeedbackFlagItem{
	{ID: model.FeedbackFlagLackOfInterest, Content: "Lack of fun"},
	{ID: model.FeedbackFlagToneMismatch, Content: "Tone out of character"},
	{ID: model.FeedbackFlagSlowResponse, Content: "Slow response"},
	{ID: model.FeedbackFlagMemoryIssue, Content: "Memory problems"},
	{ID: model.FeedbackFlagRepeatedContent, Content: "Repeated dialogue"},
}

func (s *FeedbackService) List(ctx *gin.Context) (res *response.FeedbackListResp, err error) {
	res = &response.FeedbackListResp{
		List: feedbackFlagItem,
	}
	return res, nil
}

// Batch 批量获取用户对消息的反馈信息
func (s *FeedbackService) Batch(ctx *gin.Context, req *request.FeedbackBatchReq) (*response.FeedbackBatchResp, error) {
	if err := s.validateBatchRequest(ctx, req); err != nil {
		return nil, err
	}

	// 构建缓存 key 与 key → message_id 映射
	cacheKeys, keyToMsgID := buildBatchCacheKeys(req.UserId, req.RoleID, req.MessageIDs)

	// 优先从 Redis 缓存中获取
	cachedItems, missingIDs, err := s.getCachedFeedbacks(ctx, cacheKeys, keyToMsgID)
	if err != nil {
		return nil, err
	}

	// 缓存未命中的 message_id 从数据库查
	dbItems, err := s.getMissingFeedbacksFromDB(req.UserId, req.RoleID, missingIDs)
	if err != nil {
		return nil, err
	}

	// 补充写入 Redis
	s.setFeedbacksToCache(ctx, dbItems)

	// 合并缓存命中项与数据库查到的项
	allItems := append(cachedItems, dbItems...)

	return &response.FeedbackBatchResp{List: allItems}, nil
}

// 校验请求是否合法，并从上下文补充 UserId
func (s *FeedbackService) validateBatchRequest(ctx *gin.Context, req *request.FeedbackBatchReq) error {
	if len(req.MessageIDs) == 0 {
		return fmt.Errorf("messageIDs 不能为空")
	}
	req.UserId = utils.GetDigitalUserID(ctx) // 服务端自动填充用户 ID
	return nil
}

// 构建缓存 key 与 key → message_id 映射
func buildBatchCacheKeys(userID uint, roleID string, messageIDs []string) ([]string, map[string]string) {
	cacheKeys := make([]string, len(messageIDs))
	keyToMsgID := make(map[string]string)
	for i, msgID := range messageIDs {
		key := buildSingleFeedbackCacheKey(userID, roleID, msgID)
		cacheKeys[i] = key
		keyToMsgID[key] = msgID
	}
	return cacheKeys, keyToMsgID
}

// 从 Redis 批量获取缓存数据，并标记哪些 message_id 缺失
func (s *FeedbackService) getCachedFeedbacks(ctx *gin.Context, keys []string, keyToMsgID map[string]string) ([]*response.FeedbackBatchItem, []string, error) {
	cachedResults, err := global.REDIS.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, nil, err
	}

	var result []*response.FeedbackBatchItem
	var missingIDs []string

	for i, item := range cachedResults {
		if item == nil {
			missingIDs = append(missingIDs, keyToMsgID[keys[i]])
			continue
		}
		var parsed *response.FeedbackBatchItem
		if err := json.Unmarshal([]byte(item.(string)), &parsed); err == nil {
			result = append(result, parsed)
		} else {
			missingIDs = append(missingIDs, keyToMsgID[keys[i]])
		}
	}
	return result, missingIDs, nil
}

// 从数据库中查找未命中的 message_id 对应的反馈数据
func (s *FeedbackService) getMissingFeedbacksFromDB(userID uint, roleID string, messageIDs []string) ([]*response.FeedbackBatchItem, error) {
	if len(messageIDs) == 0 {
		return nil, nil
	}
	var feedbacks []*model.MsgFeedback
	if err := global.DB.
		Where("user_id = ? AND role_id = ? AND message_id IN ?", userID, roleID, messageIDs).
		Find(&feedbacks).Error; err != nil {
		return nil, err
	}

	var result []*response.FeedbackBatchItem
	for _, fb := range feedbacks {
		result = append(result, &response.FeedbackBatchItem{
			MsgFeedback:   fb,
			FeedbackFlags: ParseFeedbackFlagItems(int(fb.FeedbackFlags)),
		})
	}
	return result, nil
}

// 将查到的反馈信息缓存入 Redis
func (s *FeedbackService) setFeedbacksToCache(ctx *gin.Context, items []*response.FeedbackBatchItem) {
	for _, item := range items {
		key := buildSingleFeedbackCacheKey(item.UserID, item.RoleID, item.MessageID)
		val, _ := json.Marshal(item)
		_ = global.REDIS.Set(ctx, key, val, 5*time.Minute).Err()
	}
}

func ParseFeedbackFlagItems(flags int) []*response.FeedbackFlagItem {
	var result []*response.FeedbackFlagItem
	for _, item := range feedbackFlagItem {
		if flags&item.ID != 0 {
			result = append(result, item)
		}
	}
	return result
}
