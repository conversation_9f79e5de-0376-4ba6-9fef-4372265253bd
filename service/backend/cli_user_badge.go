package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/response"
	"aimsg-server/model/cli/td"
	"aimsg-server/utils"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"strings"
	"time"
)

func (m *UserBadgeService) formatCountStr(count uint) string {
	/*
		获得1次~99次，显示对应的数量
		获得100~999次，显示100+
		获得1000~9999次，显示1K+
		获得10000~无穷次，显示1W+
	*/
	if count < 100 {
		return fmt.Sprintf("x%d", count)
	} else if count < 1000 {
		return "x100+"
	} else if count < 10000 {
		return fmt.Sprintf("x%dK+", int(count/1000))
	} else {
		return fmt.Sprintf("x%dW+", int(count/10000))
	}
}

func (m *UserBadgeService) MyBadgeList(c *gin.Context) (res gin.H, err error) {
	var (
		userBadges     []*model.UserBadge
		userBadgeMap   = map[uint]*model.UserBadge{}
		seriesBadgeMap = map[string][]response.MyBadgeItem{}
		userId         = utils.GetDigitalUserID(c)
		appId          = utils.GetAppID(c)
	)
	if userBadges, err = m.SearchAll(&model.UserBadge{DigitalUserId: userId}); err != nil {
		return
	}
	for _, uB := range userBadges {
		userBadgeMap[uB.BadgeId] = uB
	}
	var seriesSortList []response.SeriesSortItem
	for _, badge := range global.BadgeList {
		seriesList, seriesListExist := seriesBadgeMap[badge.SeriesName]
		if !seriesListExist {
			seriesSortList = append(seriesSortList, response.SeriesSortItem{
				SeriesName: badge.SeriesName,
				Sort:       badge.Sort,
			})
		}

		nItem := response.MyBadgeItem{
			Id:               badge.Id,
			Name:             badge.Name,
			TriggerCondition: badge.TriggerCondition,
			Icon:             utils.S3Url(badge.OffIcon, appId),
			Switch:           false,
			Single:           badge.Single,
			Count:            0,
			CountStr:         "0",
		}

		userBadgeInfo, userBadgeInfoExist := userBadgeMap[badge.Id]
		if userBadgeInfoExist {
			nItem.Switch = true
			nItem.Icon = utils.S3Url(badge.OnIcon, appId)
			nItem.Count = userBadgeInfo.Count
			nItem.CountStr = m.formatCountStr(userBadgeInfo.Count)
		}

		seriesList = append(seriesList, nItem)
		seriesBadgeMap[badge.SeriesName] = seriesList
	}
	var badgeSeriesArr []gin.H
	for _, seriesSortItem := range seriesSortList {
		badgeList := seriesBadgeMap[seriesSortItem.SeriesName]
		badgeSeriesItem := gin.H{
			"series_name": seriesSortItem.SeriesName,
			"badge_list":  badgeList,
		}
		badgeSeriesArr = append(badgeSeriesArr, badgeSeriesItem)
	}

	res = gin.H{
		"list": badgeSeriesArr,
	}
	return
}

func (m *UserBadgeService) CheckLoginBadge(c *gin.Context, userId uint) (getBadgeList []*response.BadgeItem) {
	var (
		err                  error
		continuousActiveDays int
		userInfo             *model.DigitalUser
		profileInfo          *model.DigitalUserProfile
		profileSer           DigitalUserProfileService
		fcSer                FlowerConsumeRecordService
		userSer              DigitalUserService
		badgeService         BadgeService
		now                  = time.Now()
		todayStr             = now.Format(time.DateOnly)
		appId                = utils.GetAppID(c)
	)
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		return
	}
	if profileInfo, err = profileSer.GetByUserId(userId); err != nil {
		return
	}
	continuousActiveDays = userSer.GetUserContinuousActiveDays(c, userId)
	loginBadgeList := global.TriggerTypeBadgeMap[model.TriggerTypeLogin]
	for _, badge := range loginBadgeList {
		if continuousActiveDays == int(badge.TriggerCount) {
			// 已达到该徽章的要求,查询是否获取过了.
			if !badgeService.LoginBadgeExist(c, userId, badge, todayStr) {
				// 创建FlowerConsumeRecord记录
				var fRecord = &model.FlowerConsumeRecord{
					DigitalUserId: userId,
					AiId:          0,
					AiRoleId:      "",
					ConsumeType:   model.ConsumeTypeBadge,
					RelationID:    fmt.Sprintf("%d", userId),
					OriginAmount:  profileInfo.FlowerAmount,
					ConsumeAmount: int(badge.FloRewardCount),
				}
				if err = fcSer.Create(fRecord); err != nil {
					return
				}
				go TdPointer(userInfo, profileInfo, td.ServerEvent, map[string]interface{}{
					"event_tag":         "coins_consume",
					"coins_consume_num": badge.FloRewardCount,
					"source":            "medals_get",
				})
				updateMap := map[string]interface{}{
					"flower_amount": gorm.Expr("flower_amount + ?", badge.FloRewardCount),
				}
				// 加鲜花
				global.DB.Model(&model.DigitalUserProfile{}).Where("digital_user_id = ?", userId).Updates(updateMap)
				// 保存徽章
				if err = m.SaveUserBadge(userId, badge.Id); err != nil {
					return
				}
				// 设置徽章记录到redis
				if err = badgeService.LoginBadgeSet(c, userId, badge, todayStr); err != nil {
					return
				}
				// 添加到返回列表
				originContent := badge.SuccessContent
				originContent = strings.ReplaceAll(originContent, "{{badge_name}}", badge.Name)
				originContent = strings.ReplaceAll(originContent, "{{flo_reward_count}}", fmt.Sprintf("%d", badge.FloRewardCount))
				badgeItemInfo := &response.BadgeItem{
					Id:             badge.Id,
					Name:           badge.Name,
					OnIcon:         utils.S3Url(badge.OnIcon, appId),
					FloRewardCount: badge.FloRewardCount,
					SuccessContent: originContent,
				}
				getBadgeList = append(getBadgeList, badgeItemInfo)
			}
		}
	}

	return
}

func (m *UserBadgeService) CheckBadge(c *gin.Context, userInfo *model.DigitalUser, profileInfo *model.DigitalUserProfile, aiRoleInfo *model.AiRole) (getBadgeList []*response.BadgeItem) {
	var (
		err          error
		fcSer        FlowerConsumeRecordService
		msgService   MsgService
		badgeService BadgeService
		badgeArrays  []*model.Badge
		appId        = utils.GetAppID(c)
	)
	// 勋章奖励去掉,只保留连续登录的勋章
	return
	// 用户和该AI角色的消息发送总数--->用户发送的数量
	userSendTotal := msgService.GetUserSendMsgTotal(c, userInfo.Id, aiRoleInfo.RoleID)
	// 用户聊过的AI角色总数
	aiRoleTotal := msgService.GetUserTotalAiRole(c, userInfo.Id)

	// 消息徽章列表
	msgBadgeList := global.TriggerTypeBadgeMap[model.TriggerTypeMsgCount]

	// AI角色徽章列表
	roleBrageList := global.TriggerTypeBadgeMap[model.TriggerTypeAiRoleCount]

	for _, msgBadge := range msgBadgeList {
		if userSendTotal >= int(msgBadge.TriggerCount) {
			// 已达到该徽章的要求,查询是否获取过了.
			if !badgeService.BadgeExist(c, userInfo.Id, aiRoleInfo.RoleID, msgBadge) {
				badgeArrays = append(badgeArrays, msgBadge)
			}
		}
	}

	for _, roleBadge := range roleBrageList {
		if aiRoleTotal == int(roleBadge.TriggerCount) {
			// 已达到该徽章的要求,查询是否获取过了.
			if !badgeService.BadgeExist(c, userInfo.Id, aiRoleInfo.RoleID, roleBadge) {
				badgeArrays = append(badgeArrays, roleBadge)
			}
		}
	}

	for _, badge := range badgeArrays {
		// 创建FlowerConsumeRecord记录
		var fRecord = &model.FlowerConsumeRecord{
			DigitalUserId: userInfo.Id,
			AiId:          0,
			AiRoleId:      "",
			ConsumeType:   model.ConsumeTypeBadge,
			RelationID:    fmt.Sprintf("%d", userInfo.Id),
			OriginAmount:  profileInfo.FlowerAmount,
			ConsumeAmount: int(badge.FloRewardCount),
		}
		if err = fcSer.Create(fRecord); err != nil {
			return
		}
		updateMap := map[string]interface{}{
			"flower_amount": gorm.Expr("flower_amount + ?", badge.FloRewardCount),
		}
		go TdPointer(userInfo, profileInfo, td.ServerEvent, map[string]interface{}{
			"event_tag":         "coins_consume",
			"coins_consume_num": badge.FloRewardCount,
			"ai_role_id":        aiRoleInfo.RoleID,
			"ai_role_type":      aiRoleInfo.RoleType,
			"source":            "medals_get",
		})
		// 加鲜花
		global.DB.Model(&model.DigitalUserProfile{}).Where("digital_user_id = ?", userInfo.Id).Updates(updateMap)
		// 保存徽章
		if err = m.SaveUserBadge(userInfo.Id, badge.Id); err != nil {
			return
		}
		// 设置徽章记录到redis
		if err = badgeService.BadgeSet(c, userInfo.Id, aiRoleInfo.RoleID, badge); err != nil {
			return
		}
		// 添加到返回列表
		originContent := badge.SuccessContent
		originContent = strings.ReplaceAll(originContent, "{{badge_name}}", badge.Name)
		originContent = strings.ReplaceAll(originContent, "{{flo_reward_count}}", fmt.Sprintf("%d", badge.FloRewardCount))
		badgeItemInfo := &response.BadgeItem{
			Id:             badge.Id,
			Name:           badge.Name,
			OnIcon:         utils.S3Url(badge.OnIcon, appId),
			FloRewardCount: badge.FloRewardCount,
			SuccessContent: originContent,
		}
		getBadgeList = append(getBadgeList, badgeItemInfo)
	}

	return
}
