package backend

import (
	"aimsg-server/global"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"git.costnovel.com/center/middleware-sdk/thinkdata"
	"go.uber.org/zap"
)

func TdPointer(userInfo *model.DigitalUser, profileInfo *model.DigitalUserProfile, eventName string, properties map[string]interface{}) {
	// return
	defer func() {
		if aErr := recover(); aErr != nil {
			global.LOG.Error("TdPointer panic", zap.Any("err", aErr))
		}
	}()
	appInfo, appInfoExist := global.AppMap[userInfo.AppID]
	if !appInfoExist {
		global.LOG.Error("TdPointer appInfo not exist", zap.Any("userInfo", userInfo))
		return
	}
	properties["user_id"] = userInfo.Id
	properties["sub_status"] = userInfo.SubStatus
	properties["coins_num"] = profileInfo.FlowerAmount
	properties["network_name"] = userInfo.NetworkName
	properties["country_code"] = userInfo.CountryCode
	properties["app_version"] = userInfo.Version

	sendTdData := thinkdata.SendThinkData{
		IsDebug:    global.CONFIG.System.Env != "public",
		ServerURL:  "https://ta.aipersona.cloud",
		AppId:      appInfo.ThinkData.Data().AppId,
		EventName:  eventName,
		AccountId:  fmt.Sprintf("%d", userInfo.Id),
		DistinctId: "",
		Properties: properties,
	}
	if userInfo.DistinctID != "" {
		sendTdData.DistinctId = userInfo.DistinctID
	}
	if err := thinkdata.SendData(sendTdData); err != nil {
		global.LOG.Error("TdPointer fail", zap.Error(err))
		return
	}
	// 增加第二个点
	properties["app_id"] = userInfo.AppID
	sendTdDataTwo := thinkdata.SendThinkData{
		IsDebug:    global.CONFIG.System.Env != "public",
		ServerURL:  "https://ta.aipersona.cloud",
		AppId:      "a49afb9dfc7f466abde4576961ee5554",
		EventName:  eventName,
		AccountId:  fmt.Sprintf("%d", userInfo.Id),
		DistinctId: "",
		Properties: properties,
	}
	if userInfo.DistinctID != "" {
		sendTdDataTwo.DistinctId = userInfo.DistinctID
	}
	if err := thinkdata.SendData(sendTdDataTwo); err != nil {
		global.LOG.Error("TdPointerTwo fail", zap.Error(err))
		return
	}

}

func TdPointerUserSet(userInfo *model.DigitalUser, properties map[string]interface{}) {
	defer func() {
		if aErr := recover(); aErr != nil {
			global.LOG.Error("TdPointerUserSet panic", zap.Any("err", aErr))
		}
	}()
	appInfo, appInfoExist := global.AppMap[userInfo.AppID]
	if !appInfoExist {
		global.LOG.Error("TdPointerUserSet appInfo not exist", zap.Any("userInfo", userInfo))
		return
	}
	sendTdData := thinkdata.UserSetData{
		IsDebug:    global.CONFIG.System.Env != "public",
		ServerURL:  "https://ta.aipersona.cloud",
		AppId:      appInfo.ThinkData.Data().AppId,
		AccountId:  fmt.Sprintf("%d", userInfo.Id),
		DistinctId: "",
		Properties: properties,
	}
	if userInfo.DistinctID != "" {
		sendTdData.DistinctId = userInfo.DistinctID
	}
	if err := thinkdata.UserSet(sendTdData); err != nil {
		global.LOG.Error("TdPointerUserSet fail", zap.Error(err))
		return
	}
	// 增加第二个点
	properties["app_id"] = userInfo.AppID
	sendTdDataTwo := thinkdata.UserSetData{
		IsDebug:    global.CONFIG.System.Env != "public",
		ServerURL:  "https://ta.aipersona.cloud",
		AppId:      "a49afb9dfc7f466abde4576961ee5554",
		AccountId:  fmt.Sprintf("%d", userInfo.Id),
		DistinctId: "",
		Properties: properties,
	}
	if userInfo.DistinctID != "" {
		sendTdDataTwo.DistinctId = userInfo.DistinctID
	}
	if err := thinkdata.UserSet(sendTdDataTwo); err != nil {
		global.LOG.Error("TdPointerUserSetTwo fail", zap.Error(err))
		return
	}
}
