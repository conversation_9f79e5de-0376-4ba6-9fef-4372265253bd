package backend

type ServiceGroup struct {
	MsgTimeRecordService
	AgoraService
	CallbackService
	FireBaseUtilSer
	ImgToImgTrainService
	AmazonS3Service
	SyncDataService
	MsgService
	AppService
	AiRoleService
	AiRoleImgInfoService
	AiRoleMediaService
	AdjustLogService
	DigitalUserService
	DigitalUserProfileService
	FeedbackRecordService
	FlowerConsumeRecordService
	OrdersService
	ProductService
	ReSubOrdersService
	UsersIntimateService
	MediaShowService
	ChatGuideRecordService
	UserAiRoleFuncService
	MsgJobService
	PayMethodsService
	AiSendMediaService
	AiCommonReplyService
	FcmService
	ProductInfoService
	TouchOptionService
	UndressRecordService
	ImgConfigService
	DailyReportService
	WithdrawHistoryService
	KocUserInfoService
	SysUserKocService
	AiScenesService
	UserUseScenesService
	PushMsgService
	GiftService
	GiftAiImgService
	DiyPhotoTaskService
	BadgeService
	UserBadgeService
	AiProfileInfoService
	AiContactInfoService
	AppPageBannerService
	DiyConfigService
	ImgTemplateCategoryService
	ImgTemplateRecordService
	ImgGenRecordService
	VoiceCallService
	VideoTxtService
	ImgToVideoService
	LiveGiftService
	LiveInfoService
	LivePeopleService
	LiveReceiveGiftService
	VoiceInfoService
	LiveReceiveFansService
	PrivateSpaceService
	PrivateSpaceUserService
	UserAiMsgCountService
	AiSortStatisticsService
	AiBodyImgService
	AiBodyImgCacheService
	VideoCallService
	GuidedGiftGivingService
	VideoSimplifyImgVideoService
	UserFeedbackService
	BtcService
	BtcConfigService
}
