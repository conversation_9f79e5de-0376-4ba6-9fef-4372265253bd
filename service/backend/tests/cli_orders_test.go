package tests

import (
	"fmt"
	"testing"

	"github.com/shopspring/decimal"
)

func TestSendRevenueOld(t *testing.T) {
	revenue := decimal.NewFromFloat(100)
	sendRevenue := revenue.Sub(revenue.Mul(decimal.NewFromFloat(0.15))).Mul(decimal.NewFromFloat(0.5))
	fmt.Println(sendRevenue)
}

func TestSendRevenueNew(t *testing.T) {
	revenue := decimal.NewFromFloat(100)
	sendRevenue := revenue.Sub(revenue.Mul(decimal.NewFromFloat(0.3)))
	fmt.Println(sendRevenue)
}
