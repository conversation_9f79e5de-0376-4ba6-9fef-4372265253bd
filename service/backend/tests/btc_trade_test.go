package tests

import (
	"aimsg-server/global"
	"aimsg-server/model/backend/request/btc"
	"aimsg-server/service/backend"
	"context"
	"encoding/json"
	"log"
	"testing"
	"time"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/glebarez/sqlite"
	"github.com/go-playground/assert/v2"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	mockNow      = time.Now()
	last1Minutes = time.Now().Add(-(time.Minute * 1))
	mockApps     = []*model.App{
		{
			MODEL: model.MODEL{
				Id: 1,
			},
			Name: "test-1",
		},
		{
			MODEL: model.MODEL{
				Id: 2,
			},
			Name: "test-2",
		},
	}
	mockDigitalUser = &model.DigitalUser{
		MODEL: model.MODEL{
			Id: 1,
		},
		AppID: 1,
		Email: "<EMAIL>",
	}
	mockBtcWithdrawalRecords = []*model.BtcWithdrawalRecord{
		{
			ID:               2,
			AppId:            2,
			UserID:           2,
			Amount:           "12",
			WithdrawalMethod: 2,
			WithdrawalTime:   mockNow,
			Req:              `{"method":{"id":2,"name":"Lightning Address"},"address":"1234567890"}`,
			WithdrawalStatus: 1,
		},
		{
			ID:               1,
			AppId:            1,
			UserID:           1,
			Amount:           "12",
			WithdrawalMethod: 1,
			WithdrawalTime:   last1Minutes,
			Req:              `{"method":{"id":1,"name":"Invoice"},"address":"1234567890"}`,
		},
	}
	mockBtcChannel = &model.BtcChannel{
		ChannelCode:   "speed",
		ChannelName:   "Speed",
		ChannelStatus: 1,
	}
	mockBtcTrade = &model.BtcTrade{
		WithdrawalID: 1,
		TradeStatus:  0,
	}
)

func setupGlobalDB() {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("failed to open database: %v", err)
	}
	db.AutoMigrate(
		&model.BtcWithdrawalRecord{},
		&model.BtcTrade{},
		&model.BtcChannel{},
		&model.DigitalUser{},
		&model.App{},
	)
	global.DB = db
}

func setupGlobalRedis() {
	global.REDIS = redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       0,
	})
}

func setupMockData() {
	// mock data
	global.DB.Create(mockBtcWithdrawalRecords)
	global.DB.Create(mockBtcChannel)
	// global.DB.Create(mockBtcTrade)
	global.DB.Create(mockDigitalUser)
	global.DB.Create(mockApps)
}

func setupMockDataWithNoApp() {
	global.DB.Create(mockBtcWithdrawalRecords)
	global.DB.Create(mockBtcChannel)
}

func TestGetBtcTradesWithNoApp(t *testing.T) {
	setupGlobalRedis()
	setupGlobalDB()
	setupMockDataWithNoApp()
	global.DB.Create(mockBtcTrade)
	service := backend.BtcService{}
	resp, err := service.GetBtcTrades(context.Background(), &btc.GetBtcTradesRequest{
		Page:     1,
		PageSize: 10,
	})
	if err != nil {
		t.Fatalf("failed to get btc trades: %v", err)
	}

	row := resp.List[0]
	assert.Equal(t, int64(len(resp.List)), resp.Total)
	assert.Equal(t, row.ID, mockBtcWithdrawalRecords[1].ID)
	assert.Equal(t, row.AppId, uint(1))
	assert.Equal(t, row.AppName, "")
	assert.Equal(t, row.UserID, mockBtcWithdrawalRecords[1].UserID)
	assert.Equal(t, row.Amount, mockBtcWithdrawalRecords[1].Amount)
	assert.Equal(t, row.WithdrawalMethod, mockBtcWithdrawalRecords[1].WithdrawalMethod)
	assert.Equal(t, row.WithdrawalStatus, mockBtcWithdrawalRecords[1].WithdrawalStatus)
	assert.Equal(t, row.TradeStatus, mockBtcTrade.TradeStatus)
	assert.Equal(t, row.Currency, mockBtcTrade.Currency)
	assert.Equal(t, row.Req, json.RawMessage(mockBtcWithdrawalRecords[1].Req))
	assert.Equal(t, row.MethodId, 1)
	assert.Equal(t, row.Method, "Invoice")
	assert.Equal(t, row.WithdrawalTime, mockBtcWithdrawalRecords[1].WithdrawalTime.Format(time.RFC3339))
	assert.Equal(t, row.Message, mockBtcTrade.Message)
	assert.Equal(t, row.CreatedAt, mockBtcWithdrawalRecords[1].CreatedAt.Format(time.RFC3339))
	assert.Equal(t, row.UpdatedAt, mockBtcWithdrawalRecords[1].UpdatedAt.Format(time.RFC3339))
	assert.Equal(t, row.TradePerformedAt, nil)
}

func TestGetBtcTradesWithNoTrade(t *testing.T) {
	setupGlobalRedis()
	setupGlobalDB()
	setupMockData()

	service := backend.BtcService{}
	resp, err := service.GetBtcTrades(context.Background(), &btc.GetBtcTradesRequest{
		Page:     1,
		PageSize: 10,
	})
	if err != nil {
		t.Fatalf("failed to get btc trades: %v", err)
	}

	row := resp.List[0]

	assert.Equal(t, int64(len(resp.List)), resp.Total)
	assert.Equal(t, row.ID, mockBtcWithdrawalRecords[1].ID)
	assert.Equal(t, row.AppId, uint(1))
	assert.Equal(t, row.AppName, mockApps[0].Name)
	assert.Equal(t, row.UserID, mockBtcWithdrawalRecords[1].UserID)
	assert.Equal(t, row.Amount, mockBtcWithdrawalRecords[1].Amount)
	assert.Equal(t, row.WithdrawalMethod, mockBtcWithdrawalRecords[1].WithdrawalMethod)
	assert.Equal(t, row.WithdrawalStatus, mockBtcWithdrawalRecords[1].WithdrawalStatus)
	assert.Equal(t, row.TradeStatus, mockBtcTrade.TradeStatus)
	assert.Equal(t, row.Currency, mockBtcTrade.Currency)
	assert.Equal(t, row.Req, json.RawMessage(mockBtcWithdrawalRecords[1].Req))
	assert.Equal(t, row.MethodId, 1)
	assert.Equal(t, row.Method, "Invoice")
	assert.Equal(t, row.WithdrawalTime, mockBtcWithdrawalRecords[1].WithdrawalTime.Format(time.RFC3339))
	assert.Equal(t, row.Message, mockBtcTrade.Message)
	assert.Equal(t, row.CreatedAt, mockBtcWithdrawalRecords[1].CreatedAt.Format(time.RFC3339))
	assert.Equal(t, row.UpdatedAt, mockBtcWithdrawalRecords[1].UpdatedAt.Format(time.RFC3339))
	assert.Equal(t, row.TradePerformedAt, nil)
}

func TestGetBtcTradesWithTrade(t *testing.T) {
	setupGlobalRedis()
	setupGlobalDB()
	setupMockData()
	global.DB.Create(mockBtcTrade)
	service := backend.BtcService{}
	resp, err := service.GetBtcTrades(context.Background(), &btc.GetBtcTradesRequest{
		Page:     1,
		PageSize: 10,
	})
	if err != nil {
		t.Fatalf("failed to get btc trades: %v", err)
	}

	row := resp.List[0]

	assert.Equal(t, int64(len(resp.List)), resp.Total)
	assert.Equal(t, row.ID, mockBtcWithdrawalRecords[1].ID)
	assert.Equal(t, row.AppId, uint(mockApps[0].Id))
	assert.Equal(t, row.AppName, mockApps[0].Name)
	assert.Equal(t, row.UserID, mockBtcWithdrawalRecords[1].UserID)
	assert.Equal(t, row.Amount, mockBtcWithdrawalRecords[1].Amount)
	assert.Equal(t, row.WithdrawalMethod, mockBtcWithdrawalRecords[1].WithdrawalMethod)
	assert.Equal(t, row.WithdrawalStatus, mockBtcWithdrawalRecords[1].WithdrawalStatus)
	assert.Equal(t, row.TradeStatus, mockBtcTrade.TradeStatus)
	assert.Equal(t, row.Currency, mockBtcTrade.Currency)
	assert.Equal(t, row.Req, json.RawMessage(mockBtcWithdrawalRecords[1].Req))
	assert.Equal(t, row.MethodId, 1)
	assert.Equal(t, row.Method, "Invoice")
	assert.Equal(t, row.WithdrawalTime, mockBtcWithdrawalRecords[1].WithdrawalTime.Format(time.RFC3339))
	assert.Equal(t, row.Message, mockBtcTrade.Message)
	assert.Equal(t, row.CreatedAt, mockBtcWithdrawalRecords[1].CreatedAt.Format(time.RFC3339))
	assert.Equal(t, row.UpdatedAt, mockBtcWithdrawalRecords[1].UpdatedAt.Format(time.RFC3339))
	assert.Equal(t, row.TradePerformedAt, nil)
}

func TestGetBtcTradesWithAppId1(t *testing.T) {
	setupGlobalRedis()
	setupGlobalDB()
	setupMockData()
	global.DB.Create(mockBtcTrade)
	service := backend.BtcService{}
	resp, err := service.GetBtcTrades(context.Background(), &btc.GetBtcTradesRequest{
		Page:     1,
		PageSize: 10,
		AppId:    []uint{1},
	})
	if err != nil {
		t.Fatalf("failed to get btc trades: %v", err)
	}

	assert.Equal(t, len(resp.List), 1)
	assert.Equal(t, resp.Total, int64(1))
	assert.Equal(t, resp.List[0].AppId, uint(1))
}

func TestGetBtcTradesWithAppId2(t *testing.T) {
	setupGlobalRedis()
	setupGlobalDB()
	setupMockData()
	global.DB.Create(mockBtcTrade)
	service := backend.BtcService{}
	resp, err := service.GetBtcTrades(context.Background(), &btc.GetBtcTradesRequest{
		Page:     1,
		PageSize: 10,
		AppId:    []uint{2},
	})
	if err != nil {
		t.Fatalf("failed to get btc trades: %v", err)
	}

	assert.Equal(t, len(resp.List), 1)
	assert.Equal(t, resp.Total, int64(1))
	assert.Equal(t, resp.List[0].AppId, uint(2))
}

func TestGetBtcTradesWithAppId12(t *testing.T) {
	setupGlobalRedis()
	setupGlobalDB()
	setupMockData()
	global.DB.Create(mockBtcTrade)
	service := backend.BtcService{}
	resp, err := service.GetBtcTrades(context.Background(), &btc.GetBtcTradesRequest{
		Page:     1,
		PageSize: 10,
		AppId:    []uint{1, 2},
	})
	if err != nil {
		t.Fatalf("failed to get btc trades: %v", err)
	}

	assert.Equal(t, len(resp.List), 2)
	assert.Equal(t, resp.Total, int64(2))
	assert.Equal(t, resp.List[0].AppId, uint(1))
	assert.Equal(t, resp.List[1].AppId, uint(2))
}

func TestGetBtcTradesWithWithdrawalStatus(t *testing.T) {
	setupGlobalRedis()
	setupGlobalDB()
	setupMockData()
	global.DB.Create(mockBtcTrade)
	service := backend.BtcService{}
	status := 0
	resp, err := service.GetBtcTrades(context.Background(), &btc.GetBtcTradesRequest{
		Page:             1,
		PageSize:         10,
		WithdrawalStatus: &status,
	})
	if err != nil {
		t.Fatalf("failed to get btc trades: %v", err)
	}

	assert.Equal(t, len(resp.List), 1)
	assert.Equal(t, resp.Total, int64(1))
	assert.Equal(t, resp.List[0].ID, mockBtcWithdrawalRecords[0].ID)
}
