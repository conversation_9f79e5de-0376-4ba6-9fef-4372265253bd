package tests

import (
	"aimsg-server/global"
	"aimsg-server/service/backend"
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	reqBtc "aimsg-server/model/backend/request/btc"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
)

func TestCashOut(t *testing.T) {
	w := httptest.NewRecorder()

	ctx, _ := gin.CreateTestContext(w)
	ctx.Set(global.GinContextUserId, mockDigitalUser.Id)
	ctx.Set(global.GinContextAppId, mockApps[0].Id)

	setupGlobalRedis()
	setupGlobalDB()
	global.DB.AutoMigrate(
		&model.BtcUserEarnings{},
		&model.BtcProductSubscription{},
	)
	global.DB.Create(&model.BtcUserEarnings{
		UserID:   mockDigitalUser.Id,
		Date:     time.Now(),
		Earnings: "0.0000003",
	})

	global.DB.Create(mockDigitalUser)
	global.DB.Create(mockApps)

	cacheKey := fmt.Sprintf(backend.CacheCashOut, mockDigitalUser.Id, time.Now().Format("2006-01-02"))
	global.REDIS.Del(ctx, cacheKey)

	req := &reqBtc.CashOutReq{
		Id: 1,
		Method: reqBtc.Method{
			Id:      1,
			Address: "test address",
		},
		Amount: "0.0000003",
	}

	service := backend.BtcService{}
	_, err := service.CashOut(ctx, req)
	if err != nil {
		t.Fatalf("err: %v", err.Error())
	}

	record := &model.BtcWithdrawalRecord{}
	if err := global.DB.First(&record).Error; err != nil {
		panic(err)
	}

	data, err := json.Marshal(record)
	if err != nil {
		t.Fatalf("err: %v", err.Error())
	}

	t.Logf("record: %s", string(data))
}

func TestUpdateWithdrawalStatus(t *testing.T) {
	setupGlobalRedis()
	setupGlobalDB()
	setupMockData()

	httpReq, _ := http.NewRequest("POST", "/api/v1/btc/withdrawal/update-status12345", bytes.NewBuffer([]byte(`{"withdrawal_status":4,"ids":[1]}`)))

	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	ctx.Request = httpReq
	ctx.Set(global.GinContextUserId, mockDigitalUser.Id)
	ctx.Set(global.GinContextAppId, mockApps[0].Id)

	service := backend.BtcService{}
	err := service.UpdateBtcWithdrawalStatus(ctx, &reqBtc.UpdateBtcWithdrawalStatus{
		WithdrawalStatus: 4,
		IDS:              []int{1},
	})

	if err != nil {
		t.Fatalf("err: %v", err.Error())
	}

	t.Logf("err: %v", err)
}
