package backend

import (
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/utils"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"strings"
)

// IntimatePercentage 计算百分比  孙宏伟需求 2024-12-24 新增app_id 4号包数据 亲密度功能
func (m *UsersIntimateService) IntimatePercentage(c *gin.Context, aiRoleId string) (percentage decimal.Decimal, err error) {
	var (
		usersIntimateInfo *model.UsersIntimate
		userInfo          *model.DigitalUser
		userSer           DigitalUserService
		userId            = utils.GetDigitalUserID(c)
		aiRoleInfo        *model.AiRole
		aiRoleSer         AiRoleService
	)
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		return
	}

	intimateLevelCfg := GetIntimateLevelCfg(c, userInfo.AppID)
	if aiRoleInfo, err = aiRoleSer.GetByRoleId(aiRoleId); err != nil {
		return
	}
	if usersIntimateInfo, err = m.GetByUidAndRoleid(userId, aiRoleInfo.Id); err != nil {
		return
	}
	for _, cfg := range intimateLevelCfg {
		if cfg.Level-usersIntimateInfo.Level == 1 {
			// 计算百分比：经验值 / 当前等级目标值 * 100
			percentage = decimal.NewFromInt(int64(usersIntimateInfo.Intimate)).
				Div(decimal.NewFromInt(int64(cfg.Right))).
				Mul(decimal.NewFromInt(100)).
				Round(0)
			break
		}
		percentage = decimal.NewFromInt(100)
	}
	return
}

func (m *UsersIntimateService) IntimateLevelList(c *gin.Context, reqData *request.AiRoleIdReq) (res response.IntimateDialogCfg, err error) {
	var (
		usersIntimateInfo *model.UsersIntimate
		userInfo          *model.DigitalUser
		aiRoleInfo        *model.AiRole
		aiRoleSer         AiRoleService
		userSer           DigitalUserService
		userId            = utils.GetDigitalUserID(c)
	)
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		return
	}
	isSVip := userInfo.IsSVip()
	isSub := userInfo.IsSub()

	isChatVip := userInfo.IsChatVip()
	isContentVip := userInfo.IsContentVip()
	if aiRoleInfo, err = aiRoleSer.GetByRoleId(reqData.AiRoleId); err != nil {
		return
	}
	if usersIntimateInfo, err = m.GetByUidAndRoleid(userId, aiRoleInfo.Id); err != nil {
		return
	}
	intimateLevelCfg := GetIntimateLevelCfg(c, userInfo.AppID)
	res = GetIntimateDialogCfg(c, userInfo.AppID)
	if isSVip {
		res.UpgradeTip = "Dear super member, you are enjoying a 300% increase in intimacy"
	} else if isSub {
		res.UpgradeTip = "Premier intimacy increased by 300%"
	} else {
		res.UpgradeTip = "Pro intimacy increased by 200%"
	}
	// 孙宏伟需求 2024-12-24 新增app_id 4号包数据 亲密度功能
	if isChatVip {
		res.UpgradeTip = "Unlimited Chat Membership Increases Intimacy by 300%"
	}
	if isContentVip {
		res.UpgradeTip = "Content Membership Increases Intimacy by 300%"
	}

	var newLevelList []response.IntimateLevelListItem
	for _, lItem := range res.LevelList {
		if lItem.Level <= usersIntimateInfo.Level {
			// 已解锁
			lItem.State = 1
			lItem.Percentage = 100
		} else if lItem.Level-usersIntimateInfo.Level == 1 {
			// 进行中
			lItem.State = 2
			if isSVip {
				lItem.Btn = ""
			} else if isSub {
				lItem.Btn = "Become Premier to speed up"
			} else {
				lItem.Btn = "Become Pro to speed up"
			}
			var levelCfg response.IntimateLevelCfg
			for _, lCfg := range intimateLevelCfg {
				if lCfg.Level == lItem.Level {
					levelCfg = lCfg
					break
				}
			}
			if levelCfg.Right != 0 && usersIntimateInfo.Intimate != 0 {
				// 计算百分比
				percentage := decimal.NewFromInt(int64(usersIntimateInfo.Intimate)).Div(decimal.NewFromInt(int64(levelCfg.Right))).Mul(decimal.NewFromInt(100)).Round(0).IntPart()
				lItem.Percentage = int(percentage)
			}
		} else {
			// 未解锁
			lItem.State = 3
			lItem.Percentage = 0
		}
		newLevelList = append(newLevelList, lItem)
	}
	res.LevelList = newLevelList

	return
}

func (m *UsersIntimateService) IntimateUpgradeDialog(c *gin.Context, reqData *request.AiRoleIdReq) (res gin.H, err error) {
	var (
		usersIntimateInfo *model.UsersIntimate
		aiRoleInfo        *model.AiRole
		userInfo          *model.DigitalUser
		userSer           DigitalUserService
		aiRoleSer         AiRoleService
		userId            = utils.GetDigitalUserID(c)
	)
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		return
	}
	if aiRoleInfo, err = aiRoleSer.GetByRoleId(reqData.AiRoleId); err != nil {
		return
	}
	if usersIntimateInfo, err = m.GetByUidAndRoleid(userId, aiRoleInfo.Id); err != nil {
		return
	}
	isAudit := userInfo.IsAudit()
	isSub := userInfo.IsSub()

	iCfg := GetIntimateUpgradeDialogCfg(c, userInfo.AppID)

	if isAudit {
		iCfg = GetIntimateUpgradeDialogAuditCfg(c, userInfo.AppID)
	}
	userLevelCfg, userLevelCfgExist := iCfg[usersIntimateInfo.Level]
	if !userLevelCfgExist {
		err = fmt.Errorf("level config %d not exist", usersIntimateInfo.Level)
		return
	}
	userLevelCfg.Title = strings.ReplaceAll(userLevelCfg.Title, "{{ainame}}", aiRoleInfo.Nickname)
	userLevelCfg.Content = strings.ReplaceAll(userLevelCfg.Content, "{{ainame}}", aiRoleInfo.Nickname)
	userLevelCfg.BottomContent = strings.ReplaceAll(userLevelCfg.BottomContent, "{{ainame}}", aiRoleInfo.Nickname)

	if userLevelCfg.RequireType == 2 && isSub {
		// 如果用户已经是VIP,那就不显示Sub按钮,显示OK按钮
		userLevelCfg.RequireType = 1
	}

	res = gin.H{
		"level":          usersIntimateInfo.Level,
		"title":          userLevelCfg.Title,
		"content":        userLevelCfg.Content,
		"bottom_content": userLevelCfg.BottomContent,
		"icon":           utils.S3Url(userLevelCfg.Icon, userInfo.AppID),
		"level_icon":     utils.S3Url(userLevelCfg.LevelIcon, userInfo.AppID),
		"require_type":   userLevelCfg.RequireType,
		"body_show":      usersIntimateInfo.Level == 1 && !isAudit,
	}
	return
}
