package backend

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"log"
	"runtime/debug"
	"time"
)

type DigitalUserService struct{}

func (m *DigitalUserService) DayCreateUsersRdsKey(dayTime ...time.Time) string {
	var targetTime time.Time
	if len(dayTime) > 0 {
		targetTime = dayTime[0]
	} else {
		targetTime = time.Now()
	}
	return fmt.Sprintf(global.DayCreateUsersRdsKey, targetTime.Format(time.DateOnly))
}

func (m *DigitalUserService) DayCreateUsersIncr(dayTime ...time.Time) {
	var (
		targetTime time.Time
		ctx        = context.Background()
	)
	if len(dayTime) > 0 {
		targetTime = dayTime[0]
	} else {
		targetTime = time.Now()
	}
	rdsKey := m.DayCreateUsersRdsKey(targetTime)
	global.REDIS.Incr(ctx, rdsKey)
	global.REDIS.Expire(ctx, rdsKey, time.Hour*24*30*3)
}

func (m *DigitalUserService) DayActiveUsersRdsKey(dayTime ...time.Time) string {
	var targetTime time.Time
	if len(dayTime) > 0 {
		targetTime = dayTime[0]
	} else {
		targetTime = time.Now()
	}
	return fmt.Sprintf(global.DayActiveUsersRdsKey, targetTime.Format(time.DateOnly))
}

func (m *DigitalUserService) DayActiveUsersIncr(dayTime ...time.Time) {
	var (
		targetTime time.Time
		ctx        = context.Background()
	)
	if len(dayTime) > 0 {
		targetTime = dayTime[0]
	} else {
		targetTime = time.Now()
	}
	rdsKey := m.DayActiveUsersRdsKey(targetTime)
	global.REDIS.Incr(ctx, rdsKey)
	global.REDIS.Expire(ctx, rdsKey, time.Hour*24*30*3)
}

func (m *DigitalUserService) Create(c *gin.Context, reqData *model.DigitalUser) (err error) {
	err = global.DB.Model(&model.DigitalUser{}).Create(reqData).Error
	if err != nil {
		return
	}
	m.DayCreateUsersIncr()
	return
}

func (m *DigitalUserService) DeleteById(c *gin.Context, userId uint) (err error) {
	var (
		userInfo *model.DigitalUser
	)
	if userInfo, err = m.GetByIdUnscoped(c, userId); err != nil {
		return
	}
	updateMap := map[string]interface{}{
		"deleted_at": time.Now(),
	}
	if userInfo.Username != "" {
		updateMap["username"] = fmt.Sprintf("%s_%d", userInfo.Username, time.Now().Unix())
	}
	err = global.DB.Model(&model.DigitalUser{}).Unscoped().Where("id = ?", userId).Updates(updateMap).Error
	if err == nil {
		m.DelCache(c, userId)
	}
	return
}

func (m *DigitalUserService) DeleteByIds(c *gin.Context, reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.DigitalUser{}).Where("id IN (?)", reqData.Ids).Delete(&model.DigitalUser{}).Error
	return
}

func (m *DigitalUserService) Update(c *gin.Context, reqData *model.DigitalUser) (err error) {
	// var (
	// 	reqDataBytes []byte
	// 	updateMap    map[string]interface{}
	// )
	// if reqDataBytes, err = json.Marshal(reqData); err != nil {
	// 	return
	// }
	// if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
	// 	return err
	// }
	nowTime := time.Now()
	if reqData.SubExpire.Before(nowTime) && !reqData.SubExpire.IsZero() {
		reqData.SubStatus = model.SubStatusExpire
	} else if reqData.SubExpire.IsZero() {
		reqData.SubStatus = model.SubStatusNo
	} else {
		reqData.SubStatus = model.SubStatusIng
	}
	if reqData.SVipExpire.Before(nowTime) && !reqData.SVipExpire.IsZero() {
		reqData.SVipStatus = model.SubStatusExpire
	} else if reqData.SVipExpire.IsZero() {
		reqData.SVipStatus = model.SubStatusNo
	} else {
		reqData.SVipStatus = model.SubStatusIng
	}
	if reqData.ChatVipExpire.Before(nowTime) && !reqData.ChatVipExpire.IsZero() {
		reqData.ChatVipStatus = model.SubStatusExpire
	} else if reqData.ChatVipExpire.IsZero() {
		reqData.ChatVipStatus = model.SubStatusNo
	} else {
		reqData.ChatVipStatus = model.SubStatusIng

	}
	if reqData.ContentVipExpire.Before(nowTime) && !reqData.ContentVipExpire.IsZero() {
		reqData.ContentVipStatus = model.SubStatusExpire
	} else if reqData.ContentVipExpire.IsZero() {
		reqData.ContentVipStatus = model.SubStatusNo
	} else {
		reqData.ContentVipStatus = model.SubStatusIng

	}
	err = global.DB.Model(&model.DigitalUser{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	if err == nil {
		m.DelCache(c, reqData.Id)
	}
	return
}

func (m *DigitalUserService) UpdateByMap(c context.Context, userId uint, updateMap map[string]interface{}) (err error) {
	err = global.DB.Model(&model.DigitalUser{}).Where("id = ?", userId).Updates(updateMap).Error
	if err == nil {
		m.DelCache(c, userId)
	}
	return
}

func (m *DigitalUserService) ResetSubStatus() {
	var (
		err error
	)
	if err = global.DB.Model(&model.DigitalUser{}).Where("sub_expire < NOW()").Updates(map[string]interface{}{
		"sub_status": model.SubStatusExpire,
	}).Error; err != nil {
		global.LOG.Error("ResetSubStatus", zap.Error(err))
	}
	if err = global.DB.Model(&model.DigitalUser{}).Where("svip_expire < NOW()").Updates(map[string]interface{}{
		"svip_status": model.SubStatusExpire,
	}).Error; err != nil {
		global.LOG.Error("ResetSubStatus", zap.Error(err))
	}
	if err = global.DB.Model(&model.DigitalUser{}).Where("chat_vip_expire < NOW()").Updates(map[string]interface{}{
		"chat_vip_status": model.SubStatusExpire,
	}).Error; err != nil {
		global.LOG.Error("ResetSubStatus", zap.Error(err))
	}
	if err = global.DB.Model(&model.DigitalUser{}).Where("content_vip_expire < NOW()").Updates(map[string]interface{}{
		"content_vip_status": model.SubStatusExpire,
	}).Error; err != nil {
		global.LOG.Error("ResetSubStatus", zap.Error(err))
	}

	return
}

func (m *DigitalUserService) UpdateByDeviceIdMap(c *gin.Context, deviceId string, updateMap map[string]interface{}) (err error) {
	err = global.DB.Model(&model.DigitalUser{}).Where("device_id = ?", deviceId).Updates(updateMap).Error
	return
}

func (m *DigitalUserService) CacheKey(userId uint) string {
	return fmt.Sprintf("digital_user_info1:%d", userId)
}

func (m *DigitalUserService) SetCache(ctx context.Context, record *model.DigitalUser) {
	var (
		err           error
		recordBytes   []byte
		cacheRedisKey = m.CacheKey(record.Id)
	)
	recordBytes, err = json.Marshal(record)
	if err != nil {
		global.LOG.Error("SetCache", zap.Error(err))
	}
	recordStr := string(recordBytes)
	if err = global.REDIS.SetEx(ctx, cacheRedisKey, recordStr, time.Hour*10).Err(); err != nil {
		global.LOG.Error("SetCache", zap.Error(err))
	}
	return
}

func (m *DigitalUserService) DelCache(ctx context.Context, userId uint) {
	var (
		cacheRedisKey = m.CacheKey(userId)
	)
	global.REDIS.Del(ctx, cacheRedisKey)
	return
}

func (m *DigitalUserService) GetCache(ctx context.Context, userId uint) (record *model.DigitalUser, err error) {
	var (
		cacheRedisStr string
		cacheRedisKey = m.CacheKey(userId)
	)
	defer func() {
		if r := recover(); r != nil {
			log.Printf("date0401 Recovered from panic: %v\nStack Trace:\n%s", r, debug.Stack())
		}
	}()
	if m == nil {
		return nil, errors.New("DigitalUserService is nil")
	}
	if ctx == nil {
		log.Println("Warning: ctx is nil, using context.Background()")
		ctx = context.Background()
	}
	if global.REDIS == nil {
		return nil, errors.New("redis is nil")
	}
	if cacheRedisStr, err = global.REDIS.Get(ctx, cacheRedisKey).Result(); err != nil {
		return
	}
	if err = json.Unmarshal([]byte(cacheRedisStr), &record); err != nil {
		return
	}
	return
}

func (m *DigitalUserService) GetById(ctx context.Context, userId uint) (res *model.DigitalUser, err error) {
	res, err = m.GetCache(ctx, userId)
	if res != nil {
		t2 := time.Date(2025, 4, 9, 0, 0, 0, 0, time.UTC)
		userStatus := res.UserStatus
		if res.AppID == 4 && res.CreatedAt.After(t2) && userStatus == model.UserStatusAudit {
			userStatus = model.UserStatusNormal
		}
		res.UserStatus = userStatus
	}

	if err != nil {
		err = global.DB.Model(&model.DigitalUser{}).Where("id = ?", userId).First(&res).Error
		// 如果没有找到记录，返回错误
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = errs.UserDelErr
		}
		// 没有错误，设置缓存
		if err == nil {
			m.SetCache(ctx, res)
		}
	}
	return
}

func (m *DigitalUserService) GetByIdUnscoped(ctx context.Context, userId uint) (res *model.DigitalUser, err error) {
	err = global.DB.Model(&model.DigitalUser{}).Where("id = ?", userId).Unscoped().First(&res).Error
	// 如果没有找到记录，返回错误
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = errs.UserDelErr
	}
	return
}

func (m *DigitalUserService) FindOne(data *model.DigitalUser) (res *model.DigitalUser, err error) {
	db := global.DB.Model(&model.DigitalUser{})
	if data.Id != 0 {
		db = db.Where("id = ?", data.Id)
	}
	if data.AppID > 0 {
		db = db.Where("app_id = ?", data.AppID)
	}
	if data.Username != "" {
		db = db.Where("username = ?", data.Username)
	}
	if data.DeviceID != "" {
		db = db.Where("device_id = ?", data.DeviceID)
	}
	if data.Email != "" {
		db = db.Where("email = ?", data.Email)
	}
	if data.ThirdUserId != "" {
		db = db.Where("third_user_id = ?", data.ThirdUserId)
	}
	if data.LoginType != 0 {
		db = db.Where("login_type = ?", data.LoginType)
	}
	err = db.First(&res).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = errs.UserNotFoundErr
		}
	}
	return
}

func (m *DigitalUserService) UsernameCount(username string) (total int64, err error) {
	db := global.DB.Model(&model.DigitalUser{})
	db.Where("username = ?", username).Unscoped()
	if err = db.Count(&total).Error; err != nil {
		return
	}
	return
}

func (m *DigitalUserService) GetAll() (resList []*model.DigitalUser, err error) {
	err = global.DB.Model(&model.DigitalUser{}).Find(&resList).Error
	return
}

func (m *DigitalUserService) GetList(info req.DigitalUserSearch) (resList []*model.DigitalUser, err error) {
	db := global.DB.Model(&model.DigitalUser{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.UserGroup != 0 {
		db = db.Where("user_group = ?", info.UserGroup)
	}
	if info.Gender != 0 {
		db = db.Where("gender = ?", info.Gender)
	}
	if info.AgeRange != 0 {
		db = db.Where("age_range = ?", info.AgeRange)
	}
	if info.Email != "" {
		db = db.Where("email LIKE ?", "%"+info.Email+"%")
	}
	if info.ThirdUserId != "" {
		db = db.Where("third_user_id = ?", info.ThirdUserId)
	}
	if info.LoginType != 0 {
		db = db.Where("login_type = ?", info.LoginType)
	}
	if info.LoginIP != "" {
		db = db.Where("login_ip = ?", info.LoginIP)
	}
	if info.Version != "" {
		db = db.Where("version = ?", info.Version)
	}
	if info.NetworkName != "" {
		db = db.Where("network_name = ?", info.NetworkName)
	}
	if info.SVipStatus != 0 {
		db = db.Where("svip_status = ?", info.SVipStatus)
	}
	if info.DeviceID != "" {
		db = db.Where("device_id LIKE ?", "%"+info.DeviceID+"%")
	}
	if info.UserStatus != 0 {
		db = db.Where("user_status = ?", info.UserStatus)
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.SubStatus != 0 {
		db = db.Where("sub_status = ?", info.SubStatus)
	}
	if info.SubType != 0 {
		db = db.Where("sub_type = ?", info.SubType)
	}
	if info.Nickname != "" {
		db = db.Where("nickname LIKE ?", "%"+info.Nickname+"%")
	}
	if info.CountryCode != "" {
		db = db.Where("country_code = ?", info.CountryCode)
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}

func (m *DigitalUserService) SetContinuousActiveDay(c *gin.Context, reqData req.SetContinuousActiveDayReq) (err error) {
	continuousLoginKey := m.userContinuousActiveKey(reqData.UserId)
	err = global.REDIS.Set(c, continuousLoginKey, reqData.Day, 0).Err()
	return
}

func (m *DigitalUserService) GetTotal(info req.DigitalUserSearch) (total int64, err error) {
	db := global.DB.Model(&model.DigitalUser{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.DeviceID != "" {
		db = db.Where("device_id LIKE ?", "%"+info.DeviceID+"%")
	}
	if info.UserStatus != 0 {
		db = db.Where("user_status = ?", info.UserStatus)
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.SubStatus != 0 {
		db = db.Where("sub_status = ?", info.SubStatus)
	}
	if info.SubType != 0 {
		db = db.Where("sub_type = ?", info.SubType)
	}
	if info.Nickname != "" {
		db = db.Where("nickname LIKE ?", "%"+info.Nickname+"%")
	}
	if info.CountryCode != "" {
		db = db.Where("country_code = ?", info.CountryCode)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	return
}

func (m *DigitalUserService) BatchById(uids []uint) (resp map[uint]model.DigitalUser, err error) {
	var list []model.DigitalUser
	err = global.DB.Model(&model.DigitalUser{}).Where("id IN (?)", uids).Find(&list).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	resp = map[uint]model.DigitalUser{}
	for _, v := range list {
		resp[v.Id] = v
	}
	return
}
