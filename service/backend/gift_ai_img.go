package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm/clause"
	"time"
)

type GiftAiImgService struct{}

func (m *GiftAiImgService) Create(reqData *model.GiftAiImg) (err error) {
	err = global.DB.Model(&model.GiftAiImg{}).Create(reqData).Error
	m.SetCache(reqData)
	return
}

func (m *GiftAiImgService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.GiftAiImg{}).Where("id = ?", id).Delete(&model.GiftAiImg{}).Error
	return
}

func (m *GiftAiImgService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.GiftAiImg{}).Where("id IN (?)", reqData.Ids).Delete(&model.GiftAiImg{}).Error
	return
}

func (m *GiftAiImgService) Update(reqData *model.GiftAiImg) (err error) {
	err = global.DB.Model(&model.GiftAiImg{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	m.SetCache(reqData)
	return
}

func (m *GiftAiImgService) GetById(id uint) (res *model.GiftAiImg, err error) {
	err = global.DB.Model(&model.GiftAiImg{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *GiftAiImgService) GetAll() (resList []*model.GiftAiImg, err error) {
	err = global.DB.Model(&model.GiftAiImg{}).Find(&resList).Error
	return
}

func (m *GiftAiImgService) getCacheKey(reqData *model.GiftAiImg) (cKey string) {
	return fmt.Sprintf("GiftAiImg:%d_%d", reqData.GiftId, reqData.AiRoleId)
}

func (m *GiftAiImgService) SetCache(item *model.GiftAiImg) {
	cacheKey := m.getCacheKey(item)
	// 写入缓存
	global.REDIS.Set(context.Background(), cacheKey, item, time.Hour)
	return
}

func (m *GiftAiImgService) SearchOnDB(reqData *model.GiftAiImg) (resItem *model.GiftAiImg, err error) {
	err = global.DB.Model(&model.GiftAiImg{}).Where("ai_role_id = ? AND gift_id = ?", reqData.AiRoleId, reqData.GiftId).First(&resItem).Error
	if err == nil {
		m.SetCache(resItem)
	}
	return
}

func (m *GiftAiImgService) SearchOnCache(ctx context.Context, reqData *model.GiftAiImg) (resItem *model.GiftAiImg, err error) {
	var (
		cacheKey = m.getCacheKey(reqData)
	)
	cacheRes := global.REDIS.Get(ctx, cacheKey).Val()
	if cacheRes == "" {
		// 缓存为空
		return m.SearchOnDB(reqData)
	}
	// 缓存不为空
	if err = json.Unmarshal([]byte(cacheRes), &resItem); err != nil {
		return m.SearchOnDB(reqData)
	}
	return
}

func (m *GiftAiImgService) GetList(info req.GiftAiImgSearch) (resList []*model.GiftAiImg, total int64, err error) {
	db := global.DB.Model(&model.GiftAiImg{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.AiRoleId != 0 {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.GiftId != 0 {
		db = db.Where("gift_id = ?", info.GiftId)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
