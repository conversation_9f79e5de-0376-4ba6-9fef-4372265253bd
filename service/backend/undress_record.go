package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm/clause"
)

type UndressRecordService struct{}

func (m *UndressRecordService) Create(reqData *model.UndressRecord) (err error) {
	err = global.DB.Model(&model.UndressRecord{}).Create(reqData).Error
	return
}

func (m *UndressRecordService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.UndressRecord{}).Where("id = ?", id).Delete(&model.UndressRecord{}).Error
	return
}

func (m *UndressRecordService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.UndressRecord{}).Where("id IN (?)", reqData.Ids).Delete(&model.UndressRecord{}).Error
	return
}

func (m *UndressRecordService) Update(reqData *model.UndressRecord) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.UndressRecord{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	return
}

func (m *UndressRecordService) GetById(id uint) (res *model.UndressRecord, err error) {
	err = global.DB.Model(&model.UndressRecord{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *UndressRecordService) GetAll() (resList []*model.UndressRecord, err error) {
	err = global.DB.Model(&model.UndressRecord{}).Find(&resList).Error
	return
}

func (m *UndressRecordService) SearchCount(reqData *model.UndressRecord) (total int64, err error) {
	err = global.DB.Model(&model.UndressRecord{}).Where(reqData).Count(&total).Error
	return
}

func (m *UndressRecordService) GetList(info req.UndressRecordSearch) (resList []*model.UndressRecord, total int64, err error) {
	db := global.DB.Model(&model.UndressRecord{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if info.AiRoleId != 0 {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.MediaURL != "" {
		db = db.Where("media_url = ?", info.MediaURL)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
