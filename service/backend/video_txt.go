package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
)

type VideoTxtService struct{}

func (m *VideoTxtService) Create(ctx context.Context, reqData *model.VideoTxt) (err error) {
	err = global.DB.Model(&model.VideoTxt{}).Create(reqData).Error
	m.SyncAll()
	return
}

func (m *VideoTxtService) DeleteById(ctx context.Context, id uint) (err error) {
	err = global.DB.Model(&model.VideoTxt{}).Where("id = ?", id).Delete(&model.VideoTxt{}).Error
	m.SyncAll()
	return
}

func (m *VideoTxtService) DeleteByIds(ctx context.Context, reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.VideoTxt{}).Where("id IN (?)", reqData.Ids).Delete(&model.VideoTxt{}).Error
	m.SyncAll()
	return
}

func (m *VideoTxtService) Update(ctx context.Context, reqData *model.VideoTxt) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return
	}
	err = global.DB.Model(&model.VideoTxt{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	m.SyncAll()
	return
}

func (m *VideoTxtService) GetById(ctx context.Context, id uint) (res *model.VideoTxt, err error) {
	err = global.DB.Model(&model.VideoTxt{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *VideoTxtService) GetAll() (resList []*model.VideoTxt, err error) {
	err = global.DB.Model(&model.VideoTxt{}).Find(&resList).Error
	return
}

func (m *VideoTxtService) SyncAll() {
	var (
		err     error
		resList []*model.VideoTxt
		allMap  = map[uint]*model.VideoTxt{}
	)
	if resList, err = m.GetAll(); err != nil {
		global.LOG.Error("SyncAll 失败", zap.Error(err))
		return
	}
	for _, item := range resList {
		allMap[item.Id] = item
	}
	global.AllVideoTxtMap = allMap
	global.AllVideoTxtList = resList
}

func (m *VideoTxtService) GetList(ctx context.Context, info req.VideoTxtSearch) (resList []*model.VideoTxt, total int64, err error) {
	db := global.DB.Model(&model.VideoTxt{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.Txt != "" {
		db = db.Where("txt LIKE ?", "%"+info.Txt+"%")
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
