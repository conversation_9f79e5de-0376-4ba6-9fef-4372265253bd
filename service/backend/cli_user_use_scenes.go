package backend

import (
	"aimsg-server/errs"
	"aimsg-server/model/cli/request"
	"aimsg-server/utils"
	"errors"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"time"
)

func (m *UserUseScenesService) CliCheck(c *gin.Context, reqData *request.CliUserUseScenesCheckReq) (err error) {
	var (
		userInfo     *model.DigitalUser
		aiScenesInfo *model.AiScenes
		aiRoleInfo   *model.AiRole
		searchRes    *model.UserUseScenes
		aiRoleSer    AiRoleService
		aiSceneSer   AiScenesService
		userService  DigitalUserService
		userId       = utils.GetDigitalUserID(c)
		appId        = utils.GetAppID(c)
		now          = time.Now()
	)
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	if aiRoleInfo, err = aiRoleSer.GetByRoleId(reqData.RoleID); err != nil {
		return
	}
	if aiScenesInfo, err = aiSceneSer.GetById(c, reqData.SceneID); err != nil {
		return
	}
	if aiScenesInfo.SceneType == model.SceneTypePro {
		if !userInfo.IsSub() {
			err = errs.RequireSubErr
			return
		}
	}
	if aiScenesInfo.AiRoleId != aiRoleInfo.Id {
		err = fmt.Errorf("ai role id and scene id do not match")
		return
	}
	// 查询是否有记录,有的话就更新SceneId, 没有就创建
	searchReq := &model.UserUseScenes{
		DigitalUserId: userId,
		AiRoleId:      aiRoleInfo.Id,
	}
	createReq := &model.UserUseScenes{
		DigitalUserId: userId,
		AiRoleId:      aiRoleInfo.Id,
		SceneId:       aiScenesInfo.Id,
	}
	if searchRes, err = m.SearchOne(c, searchReq); err != nil {
		err = m.Create(c, createReq)
		if err != nil {
			return
		}
	} else {
		searchRes.SceneId = reqData.SceneID
		err = m.Update(c, searchRes)
	}
	aiSendMediaSer := AiSendMediaService{}
	aiSendMediaRecord := model.AiSendMedia{
		DigitalUserId: userInfo.Id,
		AiRoleId:      aiRoleInfo.Id,
		MediaType:     model.MediaTypeImg,
		MediaURL:      utils.S3Url(aiScenesInfo.ChatBG, appId),
		ThumbnailURL:  utils.S3Url(aiScenesInfo.ChatBG, appId),
		AsmrName:      "",
		SendTime:      now,
		UnlockState:   model.UnlockStateYes,
		UnlockTime:    &now,
	}
	aiSendMediaSer.SaveByMsg(&aiSendMediaRecord)
	return
}

func (m *UserUseScenesService) GetChecked(c *gin.Context, reqData *request.AiRoleIdReq) (res gin.H, err error) {
	var (
		userInfo     *model.DigitalUser
		aiScenesInfo *model.AiScenes
		aiRoleInfo   *model.AiRole
		searchRes    *model.UserUseScenes
		aiRoleSer    AiRoleService
		aiSceneSer   AiScenesService
		userService  DigitalUserService
		userId       = utils.GetDigitalUserID(c)
	)
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	if aiRoleInfo, err = aiRoleSer.GetByRoleId(reqData.AiRoleId); err != nil {
		return
	}

	// 查询是否有记录,有的话就更新SceneId, 没有就创建
	searchReq := &model.UserUseScenes{
		DigitalUserId: userInfo.Id,
		AiRoleId:      aiRoleInfo.Id,
	}
	if searchRes, err = m.SearchOne(c, searchReq); err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		// 未找到记录,返回空
		err = nil
		return
	}
	if aiScenesInfo, err = aiSceneSer.GetById(c, searchRes.SceneId); err != nil {
		return
	}
	res = gin.H{
		"id":             aiScenesInfo.Id,
		"ai_role_id":     aiScenesInfo.AiRoleId,
		"scene_name":     aiScenesInfo.SceneName,
		"scene_type":     aiScenesInfo.SceneType,
		"scene_describe": aiScenesInfo.SceneDescribe,
		"chat_bg":        utils.S3Url(aiScenesInfo.ChatBG, userInfo.AppID),
	}
	return
}
