package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/backend/response"
	"aimsg-server/model/common/request"
	"context"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

type OrdersService struct{}

func (m *OrdersService) Create(reqData *model.Order) (err error) {
	err = global.DB.Model(&model.Order{}).Create(reqData).Error
	return
}

func (m *OrdersService) AdjustTest(c *gin.Context, reqData *req.AdjustTestReq) (err error) {
	var (
		firebaseUtilSer FireBaseUtilSer
		profileInfo     *model.DigitalUserProfile
		userInfo        *model.DigitalUser
		now             = time.Now()
	)
	if reqData.UserId != 0 {
		if err = global.DB.Model(&model.DigitalUser{}).Where("id = ?", reqData.UserId).First(&userInfo).Error; err != nil {
			return
		}
		if err = global.DB.Model(&model.DigitalUserProfile{}).Where("digital_user_id = ?", reqData.UserId).First(&profileInfo).Error; err != nil {
			return
		}
		go m.SendAdjust(userInfo, profileInfo, reqData.Price, "USD", now.Unix(), false, "", false)
	}
	// firebase
	if reqData.OrderNo != "" {
		go firebaseUtilSer.SetCacheForApp(c, reqData.OrderNo)
	}
	return
}

func (m *OrdersService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.Order{}).Where("id = ?", id).Delete(&model.Order{}).Error
	return
}

func (m *OrdersService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.Order{}).Where("id IN (?)", reqData.Ids).Delete(&model.Order{}).Error
	return
}

func (m *OrdersService) Update(reqData *model.Order) (err error) {
	err = global.DB.Model(&model.Order{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	return
}

func (m *OrdersService) UpdateByMap(id uint, updateMap map[string]interface{}) (err error) {
	err = global.DB.Model(&model.Order{}).Where("id = ?", id).Updates(updateMap).Error
	return
}

func (m *OrdersService) GetById(id uint) (res *model.Order, err error) {
	err = global.DB.Model(&model.Order{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *OrdersService) UserIsFirstBuy(userId uint) (isFirst bool) {
	var (
		buyCount int64
		err      error
	)
	err = global.DB.Model(&model.Order{}).Where("digital_user_id = ? AND order_status = ?", userId, model.OSPayGoodSuccess).Count(&buyCount).Error
	if err != nil {
		return
	}
	isFirst = buyCount == 0
	return
}

func (m *OrdersService) GetByOrderNo(orderNo string) (res *model.Order, err error) {
	err = global.DB.Model(&model.Order{}).Where("order_no = ?", orderNo).First(&res).Error
	return
}

func (m *OrdersService) UserIsBuyCoin(userId uint) (isBuy bool, err error) {
	var (
		buyCoinCount int64
		rdsKey       = m.UserIsBuyCoinRdsKey(userId)
		ctx          = context.Background()
	)
	isBuy, err = global.REDIS.Get(ctx, rdsKey).Bool()
	if err != nil {
		err = global.DB.Raw("SELECT COUNT(*) AS total FROM orders AS o LEFT JOIN product AS p ON o.product_id = p.id WHERE o.order_status = 201 AND o.digital_user_id = ? AND p.product_type = 104", userId).Scan(&buyCoinCount).Error
		if err != nil {
			return
		}
		isBuy = buyCoinCount > 0
	}
	err = global.REDIS.Set(ctx, rdsKey, isBuy, time.Hour*24*7).Err()
	return
}

func (m *OrdersService) SetUserIsBuyCoin(userId uint) {
	rdsKey := m.UserIsBuyCoinRdsKey(userId)
	ctx := context.Background()
	global.REDIS.Set(ctx, rdsKey, true, time.Hour*24*7)
	return
}

func (m *OrdersService) UserIsBuyCoinRdsKey(userId uint) (rdsKey string) {
	rdsKey = fmt.Sprintf("user_have_been_buy_coin:%d", userId)
	return
}

func (m *OrdersService) GetCount(req *model.Order) (total int64, err error) {
	db := global.DB.Model(&model.Order{})
	if req.AppID != 0 {
		db = db.Where("app_id = ?", req.AppID)
	}
	if req.DigitalUserID != 0 {
		db = db.Where("digital_user_id = ?", req.DigitalUserID)
	}
	if req.PayMethodID != 0 {
		db = db.Where("pay_method_id = ?", req.PayMethodID)
	}
	if req.ProductID != 0 {
		db = db.Where("product_id = ?", req.ProductID)
	}
	if req.AiRoleId != 0 {
		db = db.Where("ai_role_id = ?", req.AiRoleId)
	}
	if req.OrderStatus != 0 {
		db = db.Where("order_status = ?", req.OrderStatus)
	}
	if req.OrderNo != "" {
		db = db.Where("order_no = ?", req.OrderNo)
	}
	if req.TradeNo != "" {
		db = db.Where("trade_no = ?", req.TradeNo)
	}

	err = db.Count(&total).Error
	return
}

func (m *OrdersService) GetByTradeNo(tradeNo string) (res *model.Order, err error) {
	err = global.DB.Model(&model.Order{}).Where("trade_no = ?", tradeNo).First(&res).Error
	return
}

func (m *OrdersService) GetAll() (resList []*model.Order, err error) {
	err = global.DB.Model(&model.Order{}).Find(&resList).Error
	return
}

func (m *OrdersService) SyncPriceCount() {
	var (
		goodResList  []response.SyncPriceCountRes
		subResList   []response.SyncPriceCountRes
		reSubResList []response.SyncPriceCountRes
		err          error
	)
	err = global.DB.Raw("SELECT o.digital_user_id, SUM(o.usd_price) AS total_price, COUNT(1) AS total_count FROM orders AS o LEFT JOIN product AS p ON o.product_id = p.id WHERE o.order_status = 201 AND o.order_test IS FALSE AND p.good_type = 1 GROUP BY o.digital_user_id;").Find(&goodResList).Error
	if err != nil {
		global.LOG.Error("SyncPriceCount error", zap.Error(err))
		return
	}

	for _, countRes := range goodResList {
		updateMap := map[string]interface{}{
			"good_price": gorm.Expr("good_price + ?", countRes.TotalPrice),
			"good_count": gorm.Expr("good_count + ?", countRes.TotalCount),
		}
		err = global.DB.Model(&model.DigitalUserProfile{}).Where("digital_user_id = ?", countRes.DigitalUserId).Updates(updateMap).Error
		if err != nil {
			global.LOG.Error("SyncPriceCount error", zap.Error(err))
			continue
		}
	}

	err = global.DB.Raw("SELECT o.digital_user_id, SUM(o.usd_price) AS total_price, COUNT(1) AS total_count FROM orders AS o LEFT JOIN product AS p ON o.product_id = p.id WHERE o.order_status = 201 AND o.order_test IS FALSE AND p.good_type = 2 GROUP BY o.digital_user_id;").Find(&subResList).Error
	if err != nil {
		global.LOG.Error("SyncPriceCount error", zap.Error(err))
		return
	}

	for _, countRes := range subResList {
		updateMap := map[string]interface{}{
			"sub_price": gorm.Expr("sub_price + ?", countRes.TotalPrice),
			"sub_count": gorm.Expr("sub_count + ?", countRes.TotalCount),
		}
		err = global.DB.Model(&model.DigitalUserProfile{}).Where("digital_user_id = ?", countRes.DigitalUserId).Updates(updateMap).Error
		if err != nil {
			global.LOG.Error("SyncPriceCount error", zap.Error(err))
			continue
		}
	}
	// 续订
	err = global.DB.Raw("SELECT digital_user_id, SUM(usd_price) AS total_price, COUNT(1) AS total_count FROM re_sub_orders WHERE deleted_at IS NULL GROUP BY digital_user_id").Find(&reSubResList).Error
	if err != nil {
		global.LOG.Error("SyncPriceCount error", zap.Error(err))
		return
	}
	for _, countRes := range reSubResList {
		updateMap := map[string]interface{}{
			"sub_price": gorm.Expr("sub_price + ?", countRes.TotalPrice),
			"sub_count": gorm.Expr("sub_count + ?", countRes.TotalCount),
		}
		err = global.DB.Model(&model.DigitalUserProfile{}).Where("digital_user_id = ?", countRes.DigitalUserId).Updates(updateMap).Error
		if err != nil {
			global.LOG.Error("SyncPriceCount error", zap.Error(err))
			continue
		}
	}

	global.LOG.Info("SyncPriceCount success")
}

func (m *OrdersService) GetList(info req.OrdersSearch) (resList []*model.Order, total int64, err error) {
	db := global.DB.Model(&model.Order{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.OrderStatus != 0 {
		db = db.Where("order_status = ?", info.OrderStatus)
	}
	if info.OrderNo != "" {
		db = db.Where("order_no LIKE ?", "%"+info.OrderNo+"%")
	}
	if info.TradeNo != "" {
		db = db.Where("trade_no LIKE ?", "%"+info.TradeNo+"%")
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.DigitalUserID != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserID)
	}
	if info.PayMethodID != 0 {
		db = db.Where("pay_method_id = ?", info.PayMethodID)
	}
	if info.ProductID != 0 {
		db = db.Where("product_id = ?", info.ProductID)
	}

	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
