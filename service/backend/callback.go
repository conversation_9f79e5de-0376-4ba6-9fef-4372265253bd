package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"time"
)

type CallbackService struct{}

func (m *CallbackService) PayCenter(c *gin.Context, reqData *request.PayCenterCallbackReq) (err error) {
	var (
		orderInfo *model.Order
		orderSer  OrdersService
	)
	global.LOG.Info("CallBackFromPayCenter callback start", zap.Any("callback reqData", reqData))
	defer orderSer.SubOrderUnLock(reqData.OrderNo)
	// 加锁
	if !orderSer.SubOrderLock(reqData.OrderNo) {
		global.LOG.Error("CallBackFromPayCenter OrderLock fail")
		err = nil
		return
	}
	if orderInfo, err = orderSer.GetByOrderNo(reqData.OrderNo); err != nil {
		global.LOG.Error("CallBackFromPayCenter GetByOrderNo fail", zap.Error(err))
		return
	}
	if orderInfo.OrderStatus == model.OSPayGoodSuccess {
		// 已经支付成功
		return
	}
	orderInfo.TradeNo = reqData.TradeNo
	switch reqData.State {
	case 200:
		// 支付成功
		if err = orderSer.CallbackSuccess(c, orderInfo); err != nil {
			return
		}
	default:
		// 支付失败
		if err = orderSer.CallbackFail(c, orderInfo); err != nil {
			return
		}
	}

	return
}

func (m *CallbackService) PayCenterSub(c *gin.Context, reqData *request.PayCenterSubCallbackReq) (err error) {
	var (
		orderInfo *model.Order
		orderSer  OrdersService
	)
	gLog := global.LOG.With(zap.String("func", "PayCenterSub"), zap.Any("reqData", reqData), zap.String("business", "pay_center"))
	defer orderSer.SubOrderUnLock(reqData.OrderNo)
	// 加锁
	if !orderSer.SubOrderLock(reqData.OrderNo) {
		gLog.Error("PayCenterSubOrderLock fail")
		err = nil
		return
	}
	// if setRes := global.REDIS.SetNX(c, fmt.Sprintf("pay_center_sub:%s", reqData.OrderNo), 1, time.Second*60).Val(); !setRes {
	// 	return
	// }
	if orderInfo, err = orderSer.GetByOrderNo(reqData.OrderNo); err != nil {
		return
	}
	orderInfo.TradeNo = reqData.EventID
	switch reqData.Event {
	case "activated":
		switch reqData.SubType {
		case "1":
			if orderInfo.OrderStatus == model.OSPayGoodSuccess {
				// 已经支付成功
				return
			}
			// 支付成功
			if err = orderSer.CallbackSuccess(c, orderInfo); err != nil {
				return
			}
		case "2":
			expiresDate, _ := time.Parse(time.RFC3339, reqData.NextBillTime)
			if err = orderSer.ReSubSuccess(c, orderInfo, expiresDate, reqData.TradeNo, false); err != nil {
				return
			}
		}

	default:
	}

	return
}
