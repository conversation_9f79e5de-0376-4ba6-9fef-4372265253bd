package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm/clause"
	"time"
)

type AiSendMediaService struct{}

func (m *AiSendMediaService) Create(reqData *model.AiSendMedia) (err error) {
	err = global.DB.Model(&model.AiSendMedia{}).Create(reqData).Error
	return
}

func (m *AiSendMediaService) SaveByMsg(reqData *model.AiSendMedia) {
	var (
		err        error
		existCount int64
	)
	// 先查询是否存在,不存在才创建
	err = global.DB.Model(&model.AiSendMedia{}).Where("digital_user_id = ? AND ai_role_id = ? AND media_type = ? AND media_url = ?", reqData.DigitalUserId, reqData.AiRoleId, reqData.MediaType, reqData.MediaURL).Count(&existCount).Error
	if err != nil {
		return
	}
	if existCount == 0 {
		err = global.DB.Model(&model.AiSendMedia{}).Create(reqData).Error
		return
	}
}

func (m *AiSendMediaService) UpdateUnlock(reqData *model.AiSendMedia) (err error) {
	err = global.DB.Model(&model.AiSendMedia{}).Where("digital_user_id = ? AND ai_role_id = ? AND media_type = ? AND media_url = ?", reqData.DigitalUserId, reqData.AiRoleId, reqData.MediaType, reqData.MediaURL).Updates(map[string]interface{}{
		"unlock_state": model.UnlockStateYes,
		"unlock_time":  time.Now(),
	}).Error
	return
}

func (m *AiSendMediaService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.AiSendMedia{}).Where("id = ?", id).Delete(&model.AiSendMedia{}).Error
	return
}

func (m *AiSendMediaService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.AiSendMedia{}).Where("id IN (?)", reqData.Ids).Delete(&model.AiSendMedia{}).Error
	return
}

func (m *AiSendMediaService) Update(reqData *model.AiSendMedia) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.AiSendMedia{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	return
}

func (m *AiSendMediaService) GetById(id uint) (res *model.AiSendMedia, err error) {
	err = global.DB.Model(&model.AiSendMedia{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *AiSendMediaService) SearchOne(info *model.AiSendMedia) (resItem *model.AiSendMedia, err error) {
	db := global.DB.Model(&model.AiSendMedia{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if info.AiRoleId != 0 {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.MediaType != 0 {
		db = db.Where("media_type = ?", info.MediaType)
	}
	if info.UnlockState != 0 {
		db = db.Where("unlock_state = ?", info.UnlockState)
	}
	if info.MediaURL != "" {
		db = db.Where("media_url = ?", info.MediaURL)
	}
	err = db.First(&resItem).Error
	return
}

func (m *AiSendMediaService) GetAll() (resList []*model.AiSendMedia, err error) {
	err = global.DB.Model(&model.AiSendMedia{}).Find(&resList).Error
	return
}

func (m *AiSendMediaService) GetAiSendDataAll(userId, aiId uint) (resList []*model.AiSendMedia, err error) {
	db := global.DB.Model(&model.AiSendMedia{})
	db = db.Where("digital_user_id = ?", userId)
	db = db.Where("ai_role_id = ?", aiId)
	db = db.Where("media_url != '' and media_url is not null")
	db = db.Where("thumbnail_url != '' and thumbnail_url is not null")
	err = db.Find(&resList).Error
	return
}

func (m *AiSendMediaService) GetList(info req.AiSendMediaSearch) (resList []*model.AiSendMedia, total int64, err error) {
	db := global.DB.Model(&model.AiSendMedia{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if info.AiRoleId != 0 {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.MediaType != 0 {
		db = db.Where("media_type = ?", info.MediaType)
	}
	if info.UnlockState != 0 {
		db = db.Where("unlock_state = ?", info.UnlockState)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
