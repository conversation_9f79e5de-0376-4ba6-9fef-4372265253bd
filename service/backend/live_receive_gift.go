package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type LiveReceiveGiftService struct{}

func (m *LiveReceiveGiftService) Create(ctx context.Context, reqData *model.LiveReceiveGift) (err error) {
	err = global.DB.Model(&model.LiveReceiveGift{}).Create(reqData).Error
	return
}

func (m *LiveReceiveGiftService) CreateOrFindUpdate(ctx context.Context, reqData *model.LiveReceiveGift) (err error) {
	receiveGiftInfo, findErr := m.FindOne(ctx, reqData)
	if findErr != nil {
		if findErr == gorm.ErrRecordNotFound {
			// 不存在则创建
			err = m.Create(ctx, reqData)
			return
		}
		return findErr
	}
	// 存在则更新
	reqData.Id = receiveGiftInfo.Id
	if err = m.Update(ctx, reqData); err != nil {
		return
	}
	return
}

func (m *LiveReceiveGiftService) DeleteById(ctx context.Context, id uint) (err error) {
	err = global.DB.Model(&model.LiveReceiveGift{}).Where("id = ?", id).Delete(&model.LiveReceiveGift{}).Error
	return
}

func (m *LiveReceiveGiftService) DeleteByIds(ctx context.Context, reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.LiveReceiveGift{}).Where("id IN (?)", reqData.Ids).Delete(&model.LiveReceiveGift{}).Error
	return
}

func (m *LiveReceiveGiftService) Update(ctx context.Context, reqData *model.LiveReceiveGift) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return
	}
	err = global.DB.Model(&model.LiveReceiveGift{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	return
}

func (m *LiveReceiveGiftService) GetById(ctx context.Context, id uint) (res *model.LiveReceiveGift, err error) {
	err = global.DB.Model(&model.LiveReceiveGift{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *LiveReceiveGiftService) FindOne(ctx context.Context, reqData *model.LiveReceiveGift) (res *model.LiveReceiveGift, err error) {
	db := global.DB.Model(&model.LiveReceiveGift{})
	if reqData.Id != 0 {
		db = db.Where("id = ?", reqData.Id)
	}
	if reqData.AppID != 0 {
		db = db.Where("app_id = ?", reqData.AppID)
	}
	if reqData.LiveInfoID != 0 {
		db = db.Where("live_info_id = ?", reqData.LiveInfoID)
	}
	if reqData.LiveUserID != 0 {
		db = db.Where("live_user_id = ?", reqData.LiveUserID)
	}
	if reqData.GiftID != 0 {
		db = db.Where("gift_id = ?", reqData.GiftID)
	}
	err = db.First(&res).Error
	return
}

func (m *LiveReceiveGiftService) GetAll(ctx context.Context) (resList []*model.LiveReceiveGift, err error) {
	err = global.DB.Model(&model.LiveReceiveGift{}).Find(&resList).Error
	return
}

func (m *LiveReceiveGiftService) GetList(ctx context.Context, info req.LiveReceiveGiftSearch) (resList []*model.LiveReceiveGift, total int64, err error) {
	db := global.DB.Model(&model.LiveReceiveGift{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.LiveInfoID != 0 {
		db = db.Where("live_info_id = ?", info.LiveInfoID)
	}
	if info.LiveUserID != 0 {
		db = db.Where("live_user_id = ?", info.LiveUserID)
	}
	if info.GiftID != 0 {
		db = db.Where("gift_id = ?", info.GiftID)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
