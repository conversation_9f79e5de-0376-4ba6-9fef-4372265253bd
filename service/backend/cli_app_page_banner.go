package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/response"
	"aimsg-server/utils"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
)

func (m AppPageBannerService) GetAppPageBanner(c *gin.Context) (bannerResList []*response.AppPageBannerItem, err error) {
	var (
		userInfo    *model.DigitalUser
		userService DigitalUserService
		userId      = utils.GetDigitalUserID(c)
	)
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	isAudit := userInfo.IsAudit()
	appBannerAll := global.AllBannerMap[userInfo.AppID]
	if appBannerAll == nil {
		return
	}
	for _, item := range appBannerAll {
		resItem := &response.AppPageBannerItem{
			ID:       item.Id,
			TurnPage: item.TurnPage,
			AiRoleId: item.AiRoleId,
			FuncType: item.FuncType,
			ImgId:    item.ImgTemplateRecordId,
			SceneId:  item.SceneId,
			Banner:   utils.S3Url(item.Banner, userInfo.AppID),
		}
		// 审核用户只展示审核状态显示的banner
		if isAudit && item.AuditShow == model.SwitchStatusDisabled {
			continue
		}
		bannerResList = append(bannerResList, resItem)
	}

	return
}
