package backend

import (
	"aimsg-server/global"
	"context"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/redis/go-redis/v9"
	"strconv"
	"time"
)

const (
	CacheGetTotalBtcByUser = "ai:btc:GetTotalBtcByUser:userid:%d"
	GetHistoryBtc          = "ai:btc:GetHistoryBtc10:userid:%d:day:%s"
)

// 获取当前用户的历史数据
func (m *BtcService) GetTotalBtcByUser(ctx context.Context, userID uint) (resp float64, err error) {
	cache := fmt.Sprintf(CacheGetTotalBtcByUser, userID)
	data, err := global.REDIS.Get(ctx, cache).Result()
	if err != nil && err != redis.Nil {
		return
	}
	if err != redis.Nil {
		resp, _ = strconv.ParseFloat(data, 64)
		return
	}
	err = global.DB.Model(&model.BtcUserEarnings{}).
		Where("user_id = ?", userID).Select("COALESCE(SUM(earnings), 0)").
		Scan(&resp).Error
	if err != nil {
		return
	}
	global.REDIS.Set(ctx, cache, resp, time.Second*5)
	return
}

// 获取当前用户的历史数据历史
func (m *BtcService) GetHistoryBtc(ctx context.Context, userID uint) (resp []*model.BtcUserEarnings, err error) {
	cache := fmt.Sprintf(GetHistoryBtc, userID, time.Now().Format("2006-01-02"))
	data, err := global.REDIS.Get(ctx, cache).Result()
	if err != nil && err != redis.Nil {
		return
	}
	if err != redis.Nil {
		_ = json.Unmarshal([]byte(data), &resp)
		return
	}
	err = global.DB.Model(&model.BtcUserEarnings{}).
		Where("user_id = ?", userID).
		Order("date DESC").
		Scan(&resp).Error
	if err != nil {
		return
	}
	jsonB, _ := json.Marshal(resp)
	global.REDIS.Set(ctx, cache, jsonB, time.Hour*24)
	return
}
