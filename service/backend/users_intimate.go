package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

type UsersIntimateService struct{}

const (
	usersIntimateCachePrefix = "ai:table:users_intimate_v5:"
)

// Create 创建数据，并清除相关缓存
func (m *UsersIntimateService) Create(reqData *model.UsersIntimate) (err error) {
	ctx := context.Background()
	err = global.DB.WithContext(ctx).Model(&model.UsersIntimate{}).Create(reqData).Error
	return
}

// DeleteById 删除数据，并清除相关缓存
func (m *UsersIntimateService) DeleteById(id uint) (err error) {
	ctx := context.Background()
	data, err := m.GetById(id)
	if err != nil {
		return
	}
	err = global.DB.WithContext(ctx).Model(&model.UsersIntimate{}).Where("id = ?", id).Delete(&model.UsersIntimate{}).Error
	if err == nil {
		m.clearCache(ctx, data)
	}
	return
}

// DeleteByIds 批量删除数据，并清除相关缓存
func (m *UsersIntimateService) DeleteByIds(reqData request.IdsReq) (err error) {
	dataMap := map[int]*model.UsersIntimate{}
	for _, id := range reqData.Ids {
		var data *model.UsersIntimate
		data, err = m.GetById(uint(id))
		if err != nil {
			return
		}
		dataMap[id] = data
	}
	err = global.DB.Model(&model.UsersIntimate{}).Where("id IN (?)", reqData.Ids).Delete(&model.UsersIntimate{}).Error
	if err == nil {
		ctx := context.Background()
		for _, id := range reqData.Ids {
			m.clearCache(ctx, dataMap[id])
		}
	}
	return
}

// Update 更新数据，并清除相关缓存
func (m *UsersIntimateService) Update(reqData *model.UsersIntimate) (err error) {
	ctx := context.Background()
	var data *model.UsersIntimate
	data, err = m.GetById(reqData.Id)
	if err != nil {
		return
	}
	err = global.DB.WithContext(ctx).Model(&model.UsersIntimate{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	if err == nil {
		m.clearCache(ctx, data)
	}
	return
}

// GetById 从 Redis 缓存获取数据，缓存未命中则查询数据库
func (m *UsersIntimateService) GetById(id uint) (res *model.UsersIntimate, err error) {
	ctx := context.Background()
	cacheKey := fmt.Sprintf("%s%d", usersIntimateCachePrefix, id)
	res = &model.UsersIntimate{}

	// 尝试从 Redis 获取
	data, err := global.REDIS.Get(ctx, cacheKey).Result()
	if err == nil {
		if json.Unmarshal([]byte(data), res) == nil {
			return res, nil
		}
	} else if err != redis.Nil {
		return nil, err // 只有在 err 不是 redis.Nil 时才返回错误
	}

	// 缓存未命中，查询数据库
	err = global.DB.WithContext(ctx).Model(&model.UsersIntimate{}).Where("id = ?", id).First(res).Error
	if err == nil {
		cacheData, _ := json.Marshal(res)
		global.REDIS.Set(ctx, cacheKey, cacheData, 10*time.Minute)
	}
	return
}

func (m *UsersIntimateService) GetAll() (resList []*model.UsersIntimate, err error) {
	err = global.DB.Model(&model.UsersIntimate{}).Find(&resList).Error
	return
}

func (m *UsersIntimateService) GetList(info req.UsersIntimateSearch) (resList []*model.UsersIntimate, total int64, err error) {
	db := global.DB.Model(&model.UsersIntimate{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.Level != 0 {
		db = db.Where("level = ?", info.Level)
	}
	if info.AiRoleId != 0 {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}

// GetByUidAndRoleid 查询数据，缓存未命中则创建
func (m *UsersIntimateService) GetByUidAndRoleid(userId, aiRoleId uint) (res *model.UsersIntimate, err error) {
	ctx := context.Background()
	cacheKey := fmt.Sprintf("%suid_%d_role_%d", usersIntimateCachePrefix, userId, aiRoleId)
	res = &model.UsersIntimate{}

	// 先尝试从 Redis 获取
	data, err := global.REDIS.Get(ctx, cacheKey).Result()
	if err == nil {
		if json.Unmarshal([]byte(data), res) == nil {
			return res, nil
		}
	} else if err != redis.Nil {
		return nil, err
	}

	// 缓存未命中，查询数据库
	if err = global.DB.WithContext(ctx).Model(&model.UsersIntimate{}).
		Where("digital_user_id = ? and ai_role_id = ?", userId, aiRoleId).
		Limit(1).
		First(&res).Error; err != nil {

		if errors.Is(err, gorm.ErrRecordNotFound) {

			lockKey := fmt.Sprintf("%s_create_lock_uid_%d_role_%d", usersIntimateCachePrefix, userId, aiRoleId)
			isLock, err2 := global.REDIS.SetNX(ctx, lockKey, 1, time.Second*30).Result()
			if err2 != nil {
				return nil, err2
			}
			if !isLock {
				time.Sleep(time.Millisecond)
				return m.GetByUidAndRoleid(userId, aiRoleId)
			}
			// 创建
			res = &model.UsersIntimate{
				DigitalUserId: userId,
				AiRoleId:      aiRoleId,
			}
			if err = m.Create(res); err != nil {
				return
			}
		} else {
			return
		}
	}

	// 存入 Redis 缓存
	cacheData, _ := json.Marshal(res)
	global.REDIS.Set(ctx, cacheKey, cacheData, 10*time.Minute)
	return
}

// clearCache 清除 Redis 缓存
func (m *UsersIntimateService) clearCache(ctx context.Context, data *model.UsersIntimate) {
	if data == nil {
		return
	}
	cacheKey1 := fmt.Sprintf("%s%d", usersIntimateCachePrefix, data.Id)
	cacheKey2 := fmt.Sprintf("%suid_%d_role_%d", usersIntimateCachePrefix, data.DigitalUserId, data.AiRoleId)
	global.REDIS.Del(ctx, cacheKey1, cacheKey2)
}
