package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"errors"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type LiveReceiveFansService struct{}

func (m *LiveReceiveFansService) SaveBatch(c context.Context, listData []*model.LiveReceiveFans) (err error) {
	// 先查询是否存在,如果存在就不插入
	for _, item := range listData {
		var (
			searchItem *model.LiveReceiveFans
		)
		searchReq := &model.LiveReceiveFans{
			AppID:      item.AppID,
			LiveUserID: item.LiveUserID,
			AiId:       item.AiId,
		}
		if searchItem, err = m.SearchOne(c, searchReq); err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				if err = m.Create(c, item); err != nil {
					return
				}
			} else {
				return
			}
		} else {
			searchItem.LiveInfoID = item.LiveInfoID
			searchItem.Rank = item.Rank
			if err = m.Update(c, searchItem); err != nil {
				return
			}
		}
	}
	return
}
func (m *LiveReceiveFansService) Create(ctx context.Context, reqData *model.LiveReceiveFans) (err error) {
	err = global.DB.Model(&model.LiveReceiveFans{}).Create(reqData).Error
	return
}

func (m *LiveReceiveFansService) DeleteById(ctx context.Context, id uint) (err error) {
	err = global.DB.Model(&model.LiveReceiveFans{}).Where("id = ?", id).Delete(&model.LiveReceiveFans{}).Error
	return
}

func (m *LiveReceiveFansService) DeleteByIds(ctx context.Context, reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.LiveReceiveFans{}).Where("id IN (?)", reqData.Ids).Delete(&model.LiveReceiveFans{}).Error
	return
}

func (m *LiveReceiveFansService) Update(ctx context.Context, reqData *model.LiveReceiveFans) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return
	}
	err = global.DB.Model(&model.LiveReceiveFans{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	return
}

func (m *LiveReceiveFansService) GetById(ctx context.Context, id uint) (res *model.LiveReceiveFans, err error) {
	err = global.DB.Model(&model.LiveReceiveFans{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *LiveReceiveFansService) GetAll(ctx context.Context) (resList []*model.LiveReceiveFans, err error) {
	err = global.DB.Model(&model.LiveReceiveFans{}).Find(&resList).Error
	return
}

func (m *LiveReceiveFansService) SearchOne(ctx context.Context, info *model.LiveReceiveFans) (res *model.LiveReceiveFans, err error) {
	db := global.DB.Model(&model.LiveReceiveFans{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.LiveInfoID != 0 {
		db = db.Where("live_info_id = ?", info.LiveInfoID)
	}
	if info.LiveUserID != 0 {
		db = db.Where("live_user_id = ?", info.LiveUserID)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if err = db.First(&res).Error; err != nil {
		return
	}
	return
}
func (m *LiveReceiveFansService) SearchCount(ctx context.Context, info *model.LiveReceiveFans) (count int64, err error) {
	db := global.DB.Model(&model.LiveReceiveFans{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.LiveInfoID != 0 {
		db = db.Where("live_info_id = ?", info.LiveInfoID)
	}
	if info.LiveUserID != 0 {
		db = db.Where("live_user_id = ?", info.LiveUserID)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if err = db.Count(&count).Error; err != nil {
		return
	}
	return
}

func (m *LiveReceiveFansService) GetList(ctx context.Context, info req.LiveReceiveFansSearch) (resList []*model.LiveReceiveFans, total int64, err error) {
	db := global.DB.Model(&model.LiveReceiveFans{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.LiveInfoID != 0 {
		db = db.Where("live_info_id = ?", info.LiveInfoID)
	}
	if info.LiveUserID != 0 {
		db = db.Where("live_user_id = ?", info.LiveUserID)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
