package backend

import (
	"aimsg-server/global"
	"aimsg-server/utils"
	"context"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
)

func (m *MsgService) RewardUnlockImg(userId, aiId uint, count int64) (err error) {
	var (
		ctx    = context.Background()
		rdsKey = fmt.Sprintf(global.RewardUnlockImgKey, userId, aiId)
	)
	err = global.REDIS.IncrBy(ctx, rdsKey, count).Err()
	return
}

func (m *MsgService) GetRewardUnlockImgCount(userId, aiId uint) (count int, err error) {
	var (
		ctx    = context.Background()
		rdsKey = fmt.Sprintf(global.RewardUnlockImgKey, userId, aiId)
	)
	count, err = global.REDIS.Get(ctx, rdsKey).Int()
	return
}

func (m *MsgService) RewardUnlockVideo(userId, aiId uint, count int64) (err error) {
	var (
		ctx    = context.Background()
		rdsKey = fmt.Sprintf(global.RewardUnlockVideoKey, userId, aiId)
	)
	err = global.REDIS.IncrBy(ctx, rdsKey, count).Err()
	return
}

func (m *MsgService) GetRewardUnlockVideoCount(userId, aiId uint) (count int, err error) {
	var (
		ctx    = context.Background()
		rdsKey = fmt.Sprintf(global.RewardUnlockVideoKey, userId, aiId)
	)
	count, err = global.REDIS.Get(ctx, rdsKey).Int()
	return
}

func (m *MsgService) RewardVoiceCall(userId, aiId uint, count int64) (err error) {
	var (
		ctx    = context.Background()
		rdsKey = fmt.Sprintf(global.RewardVoiceCallKey, userId, aiId)
	)
	err = global.REDIS.IncrBy(ctx, rdsKey, count).Err()
	return
}

func (m *MsgService) GetRewardVoiceCallCount(userId, aiId uint) (count int, err error) {
	var (
		ctx    = context.Background()
		rdsKey = fmt.Sprintf(global.RewardVoiceCallKey, userId, aiId)
	)
	count, err = global.REDIS.Get(ctx, rdsKey).Int()
	return
}

func (m *MsgService) RewardAsmr(userId, aiId uint, count int64) (err error) {
	var (
		ctx    = context.Background()
		rdsKey = fmt.Sprintf(global.RewardAsmrKey, userId, aiId)
	)
	err = global.REDIS.IncrBy(ctx, rdsKey, count).Err()
	return
}

func (m *MsgService) GetRewardAsmrCount(userId, aiId uint) (count int, err error) {
	var (
		ctx    = context.Background()
		rdsKey = fmt.Sprintf(global.RewardAsmrKey, userId, aiId)
	)
	count, err = global.REDIS.Get(ctx, rdsKey).Int()
	return
}

func (m *MsgService) RewardGift(userInfo *model.DigitalUser, giftId uint, count int64) (err error) {
	var (
		ctx = context.Background()
	)
	rdsKey := fmt.Sprintf(global.RewardGiftKey, userInfo.Id, giftId)
	err = global.REDIS.IncrBy(ctx, rdsKey, count).Err()
	return
}

func (m *MsgService) GetRewardGiftCount(userId, giftId uint) (count int, err error) {
	var (
		ctx    = context.Background()
		rdsKey = fmt.Sprintf(global.RewardGiftKey, userId, giftId)
	)
	count, err = global.REDIS.Get(ctx, rdsKey).Int()
	return
}

func (m *MsgService) RewardUserFunc(usersIntimate *model.UsersIntimate, userInfo *model.DigitalUser) {
	verCompare340 := utils.CompareVersions(userInfo.Version, "3.4.0")
	if userInfo.AppID < 21 && userInfo.AppID != 2 && userInfo.AppID != 1 && userInfo.AppID != 11 && userInfo.AppID != 13 && userInfo.AppID != 4 && userInfo.AppID != 10 && userInfo.AppID != 6 && userInfo.AppID != 7 && userInfo.AppID != 17 {
		return
	}
	// 孙宏伟需求 2024-12-24 新增app_id 4号包数据 亲密度功能 增加
	verCompare364 := utils.CompareVersions(userInfo.Version, "3.6.4")
	if verCompare364 > 0 && userInfo.AppID == 4 {
		verCompare340 = 1
	}
	// 孙宏伟需求 2024-12-24 新增app_id 10号包数据 亲密度功能 增加
	verCompare363 := utils.CompareVersions(userInfo.Version, "3.6.3")
	if verCompare363 > 0 && userInfo.AppID == 10 {
		verCompare340 = 1
	}
	verCompare365 := utils.CompareVersions(userInfo.Version, "3.6.5")
	if verCompare365 > 0 && userInfo.AppID == 6 {
		verCompare340 = 1
	}

	if verCompare365 > 0 && userInfo.AppID == 7 {
		verCompare340 = 1
	}
	if userInfo.AppID == 17 {
		verCompare340 = 1
	}

	if userInfo.AppID >= 21 {
		verCompare340 = 1
	}

	if verCompare340 < 0 {
		return
	}
	var (
		err        error
		aiRoleInfo *model.AiRole
		aiRoleSer  AiRoleService
		ctx        = context.Background()
		// isSub      = userInfo.IsSub()
		isSVip = userInfo.IsSVip()
	)
	if aiRoleInfo, err = aiRoleSer.GetById(ctx, usersIntimate.AiRoleId); err != nil {
		return
	}
	// 奖励用户
	if userInfo.AppID == 13 {
		switch usersIntimate.Level {
		case 1:
			// 解锁图片1次/角色
			_ = m.RewardUnlockImg(userInfo.Id, usersIntimate.AiRoleId, 1)
			// 立即发送一个图片
			go m.SendImgMsg(ctx, userInfo, aiRoleInfo)
		case 2:
			// 立即发送一个视频
			// go m.SendVideoMsg(ctx, userInfo, aiRoleInfo)
		case 3:
			go m.SendVideoMsg(ctx, userInfo, aiRoleInfo)
		default:
		}
	} else {
		switch usersIntimate.Level {
		case 1:
			// 解锁图片1次/角色
			_ = m.RewardUnlockImg(userInfo.Id, usersIntimate.AiRoleId, 1)
			// 立即发送一个图片
			go m.SendImgMsg(ctx, userInfo, aiRoleInfo)
		case 2:
			// 立即发送一个视频
			go m.SendVideoMsg(ctx, userInfo, aiRoleInfo)
			// 解锁视频1次/角色
			// _ = m.RewardUnlockVideo(userInfo.Id, usersIntimate.AiRoleId, 1)
		case 3:
			// var giftIdArr []uint
			// for _, giftRes := range global.AllGiftList {
			// 	// 随机送礼物，不包含最贵的sex toy
			// 	if giftRes.Id == 3 {
			// 		continue
			// 	}
			// 	giftIdArr = append(giftIdArr, giftRes.Id)
			// }
			// randGiftId := utils.RandList(giftIdArr)
			// // 随机一次免费礼物赠送
			// _ = m.RewardGift(userInfo, randGiftId, 1)

			// 给用户发送一个礼物消息
			go m.AISendUserGiftMsg(ctx, userInfo.Id, aiRoleInfo.RoleID)
		case 4:
			// 语音通话,SVIP免费一分钟
			if isSVip {
				_ = m.RewardVoiceCall(userInfo.Id, usersIntimate.AiRoleId, 1)
			}
			// 孙宏伟需求 2024-12-24 新增app_id 4号包数据
			if userInfo.AppID != 4 && userInfo.AppID != 10 && userInfo.AppID != 6 && userInfo.AppID != 7 && userInfo.AppID != 17 {
				_ = m.RewardAsmr(userInfo.Id, usersIntimate.AiRoleId, 1)
			} else if userInfo.IsChatVip() {
				_ = m.RewardAsmr(userInfo.Id, usersIntimate.AiRoleId, 1)
			}
			// // VIP和SVIP赠送一次免费的ASMR
			// if isSub || isSVip {
			// 	_ = m.RewardAsmr(userInfo.Id, usersIntimate.AiRoleId, 1)
			// }
			// ASMR 发送一次
			if err = m.SendAsmrTypeMsg(userInfo, aiRoleInfo); err != nil {
				return
			}
		default:
		}
	}
}
