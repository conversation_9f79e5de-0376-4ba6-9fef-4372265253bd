package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
	"time"
)

type AiScenesService struct{}

func (m *AiScenesService) Create(c *gin.Context, reqData *model.AiScenes) (err error) {
	err = global.DB.Model(&model.AiScenes{}).Create(reqData).Error
	if err == nil {
		m.SetCache(c, reqData)
	}
	return
}

func (m *AiScenesService) DeleteById(c *gin.Context, id uint) (err error) {
	err = global.DB.Model(&model.AiScenes{}).Where("id = ?", id).Delete(&model.AiScenes{}).Error
	if err == nil {
		m.Del<PERSON>ache(c, id)
	}
	return
}

func (m *AiScenesService) DeleteByIds(c *gin.Context, reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.AiScenes{}).Where("id IN (?)", reqData.Ids).Delete(&model.AiScenes{}).Error
	return
}

func (m *AiScenesService) Update(c *gin.Context, reqData *model.AiScenes) (err error) {
	err = global.DB.Model(&model.AiScenes{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	if err == nil {
		m.DelCache(c, reqData.Id)
	}
	return
}

func (m *AiScenesService) GetById(ctx context.Context, scenesId uint) (res *model.AiScenes, err error) {
	res, err = m.GetCache(ctx, scenesId)
	if err != nil {
		err = global.DB.Model(&model.AiScenes{}).Where("id = ?", scenesId).First(&res).Error
		if err == nil {
			m.SetCache(ctx, res)
		}
	}
	return
}

func (m *AiScenesService) CacheKey(scenesId uint) string {
	return fmt.Sprintf("ai_scenes:%d", scenesId)
}

func (m *AiScenesService) SetCache(ctx context.Context, record *model.AiScenes) {
	var (
		err           error
		recordBytes   []byte
		cacheRedisKey = m.CacheKey(record.Id)
	)
	recordBytes, err = json.Marshal(record)
	if err != nil {
		global.LOG.Error("SetCache", zap.Error(err))
	}
	recordStr := string(recordBytes)
	if err = global.REDIS.SetEx(ctx, cacheRedisKey, recordStr, time.Hour).Err(); err != nil {
		global.LOG.Error("SetCache", zap.Error(err))
	}
	return
}

func (m *AiScenesService) DelCache(ctx context.Context, scenesId uint) {
	var (
		cacheRedisKey = m.CacheKey(scenesId)
	)
	global.REDIS.Del(ctx, cacheRedisKey)
	return
}

func (m *AiScenesService) GetCache(ctx context.Context, scenesId uint) (record *model.AiScenes, err error) {
	var (
		cacheRedisStr string
		cacheRedisKey = m.CacheKey(scenesId)
	)
	if cacheRedisStr, err = global.REDIS.Get(ctx, cacheRedisKey).Result(); err != nil {
		return
	}
	if err = json.Unmarshal([]byte(cacheRedisStr), &record); err != nil {
		return
	}
	return
}

func (m *AiScenesService) GetAIDefaultScene(ctx context.Context, aiRoleId uint) (res *model.AiScenes, err error) {
	var (
		resBytes []byte
		cacheStr string
		cacheKey = fmt.Sprintf("AiScenesDefault:%d", aiRoleId)
	)
	if cacheStr = global.REDIS.Get(ctx, cacheKey).Val(); cacheStr != "" {
		if err = json.Unmarshal([]byte(cacheStr), &res); err == nil {
			return
		}
	}
	err = global.DB.Model(&model.AiScenes{}).Where("ai_role_id = ? AND scene_type = ?", aiRoleId, model.SceneTypeDefault).First(&res).Error
	if err == nil {
		if resBytes, err = json.Marshal(res); err == nil {
			cacheStr = string(resBytes)
			global.REDIS.SetEx(ctx, cacheKey, cacheStr, time.Hour)
		}
	}
	return
}

func (m *AiScenesService) GetAll() (resList []*model.AiScenes, err error) {
	err = global.DB.Model(&model.AiScenes{}).Find(&resList).Error
	return
}

func (m *AiScenesService) GetList(c *gin.Context, info req.AiScenesSearch) (resList []*model.AiScenes, total int64, err error) {
	db := global.DB.Model(&model.AiScenes{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.AiRoleId != 0 {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.SceneType != 0 {
		db = db.Where("scene_type = ?", info.SceneType)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
