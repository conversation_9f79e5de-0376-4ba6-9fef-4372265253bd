package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/model/common/request"
	"aimsg-server/utils"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
)

type GiftService struct{}

func (m *GiftService) Create(reqData *model.Gift) (err error) {
	err = global.DB.Model(&model.Gift{}).Create(reqData).Error
	m.SyncGiftAll()
	return
}

func (m *GiftService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.Gift{}).Where("id = ?", id).Delete(&model.Gift{}).Error
	m.SyncGiftAll()
	return
}

func (m *GiftService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.Gift{}).Where("id IN (?)", reqData.Ids).Delete(&model.Gift{}).Error
	m.SyncGiftAll()
	return
}

func (m *GiftService) Update(reqData *model.Gift) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.Gift{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	m.SyncGiftAll()
	return
}

func (m *GiftService) GetById(id uint) (res *model.Gift, err error) {
	err = global.DB.Model(&model.Gift{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *GiftService) GetAll() (resList []*model.Gift, err error) {
	err = global.DB.Model(&model.Gift{}).Order("sort DESC").Find(&resList).Error
	return
}

func (m *GiftService) SyncGiftAll() {
	var (
		err          error
		dbResList    []*model.Gift
		allList      []*response.GiftRes
		maleList     []*response.GiftRes
		allGiftMap   = map[uint]*model.Gift{}
		allGiftIdArr = []uint{}
	)
	dbResList, err = m.GetAll()
	if err != nil {
		global.LOG.Error("SyncGiftAll 失败", zap.Error(err))
		return
	}
	for _, gift := range dbResList {
		allGiftMap[gift.Id] = gift
		allGiftIdArr = append(allGiftIdArr, gift.Id)
	}
	for _, gift := range dbResList {
		giftItem := &response.GiftRes{
			Id:      gift.Id,
			Name:    gift.Name,
			Icon:    utils.S3Url(gift.Icon, 0),
			Consume: gift.Consume,
			Msg:     gift.Msg,
		}
		allList = append(allList, giftItem)
		if gift.MaleShow == model.SwitchStatusDisabled {
			continue
		}
		maleList = append(maleList, giftItem)
	}
	global.AllGiftMap = allGiftMap
	global.AllGiftIdArr = allGiftIdArr
	global.AllGiftList = allList
	global.MaleGiftList = maleList
}

func (m *GiftService) GetList(info req.GiftSearch) (resList []*model.Gift, total int64, err error) {
	db := global.DB.Model(&model.Gift{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.Name != "" {
		db = db.Where("name LIKE ?", "%"+info.Name+"%")
	}
	if info.MaleShow != 0 {
		db = db.Where("male_show = ?", info.MaleShow)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
