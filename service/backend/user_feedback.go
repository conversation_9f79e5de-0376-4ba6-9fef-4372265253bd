package backend

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/utils"
	"errors"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"strings"
	"time"
)

type UserFeedbackService struct{}

func (v *UserFeedbackService) Test() {
	return
}

func (v *UserFeedbackService) GetAIRobotInfoByAppId(c *gin.Context) (res map[string]interface{}, err error) {
	// 根据不同的appid 获取不同的机器人客服
	userId := utils.GetDigitalUserID(c)

	// userId := uint(90001083)
	// 根据用户id 获取用户的appid
	var userInfo *model.DigitalUser
	var userService DigitalUserService
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return nil, errs.RequireLoginErr
	}

	s3Url := utils.S3Url("public/image/12b180c1-b4a8-49ce-8d91-dd6e95977ea3.png", userInfo.AppID)
	if userInfo.AppID == 17 {
		res = map[string]interface{}{
			"name": "Fantasy service assistant",
			"img":  s3Url,
			"id":   model.AI_ROBOT,
		}
	} else if userInfo.AppID == 1 {
		s3Url = utils.S3Url("public/image/87f5745c-1567-4ed7-af0e-61a3dc935d26.png", userInfo.AppID)
		res = map[string]interface{}{
			"name": "Persona service assistant",
			"img":  s3Url,
			"id":   model.AI_ROBOT,
		}
	} else if userInfo.AppID == 11 {
		s3Url = utils.S3Url("public/image/dac48d69-a0f0-4fd1-ac38-f6769aecbfa6.png", userInfo.AppID)
		res = map[string]interface{}{
			"name": "Persona service assistant",
			"img":  s3Url,
			"id":   model.AI_ROBOT,
		}
	} else if userInfo.AppID == 10 {
		s3Url = utils.S3Url("public/image/fcbc99d2-594d-4410-8ac2-03d23c2bc52a.png", userInfo.AppID)
		res = map[string]interface{}{
			"name": "Fantasy service assistant",
			"img":  s3Url,
			"id":   model.AI_ROBOT,
		}
	} else if userInfo.AppID == 4 {
		// 头像
		s3Url = utils.S3Url("public/image/6d59e0c6-3352-48f3-9657-2b9a86270536.png", userInfo.AppID)
		res = map[string]interface{}{
			"name": "Fantasy service assistant",
			"img":  s3Url,
			"id":   model.AI_ROBOT,
		}
	} else if userInfo.AppID == 6 {
		// 头像
		s3Url = utils.S3Url("public/image/1c600893-36b6-4dc8-ad48-2f34f68d1b35.png", userInfo.AppID)
		res = map[string]interface{}{
			"name": "Lover service assistant",
			"img":  s3Url,
			"id":   model.AI_ROBOT,
		}
	} else if userInfo.AppID == 7 {
		// 头像 xin
		s3Url = utils.S3Url("public/image/13f18a16-308c-41b9-9b70-2b1f6ad87686.png", userInfo.AppID)
		res = map[string]interface{}{
			"name": "Lover service assistant",
			"img":  s3Url,
			"id":   model.AI_ROBOT,
		}
	} else if userInfo.AppID == 13 {
		// 头像 新13号包
		s3Url = utils.S3Url("public/image/0296207c-b667-498f-ba48-c91757432ee9.png", userInfo.AppID)
		res = map[string]interface{}{
			"name": "Pick me service assistant",
			"img":  s3Url,
			"id":   model.AI_ROBOT,
		}
	} else if userInfo.AppID == 25 {
		res = map[string]interface{}{
			"name": "Service assistant",
			"img":  s3Url,
			"id":   model.AI_ROBOT,
		}
	} else {
		res = map[string]interface{}{
			"name": "Lover service assistant",
			"img":  s3Url,
			"id":   model.AI_ROBOT,
		}
	}

	return
}

func (v *UserFeedbackService) GetFeedbackList(c *gin.Context) (res map[string]interface{}, err error) {

	// 通过用户id 查询会话的记录 只保留最近多少天的数据 通过会话的记录查询反馈的记录
	// 如果是第一次就返回一个消息 文字加语音 如果不是第一次就不再返回
	userId := utils.GetDigitalUserID(c)
	// userId := uint(90001083)
	var userInfo *model.DigitalUser
	var userService DigitalUserService

	if userInfo, err = userService.GetById(c, userId); err != nil {
		return nil, err
	}
	s3Url := utils.S3Url("public/video/robot17_new.mp3", userInfo.AppID)
	var userFeedbackSession model.UserFeedbackSession
	err = global.DB.Model(&model.UserFeedbackSession{}).Where("digital_user_id = ? ", userId).First(&userFeedbackSession).Error
	if err != nil {
		res = map[string]interface{}{
			"voice_url": s3Url,
			"content":   "Hi! Welcome to contact our customer service team. No matter what problems you've encountered or what kind of assistance you need, please feel free to let us know. Once we receive your questions, we'll immediately go all out to solve them and provide you with feedback in a timely manner!",
		}
		return res, nil
	}
	return nil, nil
}

func (v *UserFeedbackService) SendMsg(c *gin.Context, reqData request.UserFeedbackSendMsg) (res interface{}, err error) {
	// 发送消息 存储并且返回对应的数据
	userId := utils.GetDigitalUserID(c)
	// userId := uint(90001085)

	// 查看是否有邮箱
	var userFeedbackEmail model.UserFeedbackEmail
	err = global.DB.Model(&model.UserFeedbackEmail{}).Where("digital_user_id = ?", userId).First(&userFeedbackEmail).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("email is null")
		}
		return
	}

	// 先查看用户是否有未结束的会话  如果有直接插入 如果没有就创建一个会话 并插入对应的数据
	var userFeedbackSession model.UserFeedbackSession
	err = global.DB.Model(&model.UserFeedbackSession{}).Where("digital_user_id = ? and status = ?", userId, 1).First(&userFeedbackSession).Error
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		// 创建一个会话
		userFeedbackSession = model.UserFeedbackSession{
			Email:         userFeedbackEmail.Email,
			DigitalUserID: int64(userId),
		}
		err = global.DB.Create(&userFeedbackSession).Error
		if err != nil {
			return nil, err
		}
	}
	// 创建一个反馈
	if userFeedbackSession.Id != 0 {
		userFeedbackSessionInfo := model.UserFeedbackSessionInfo{
			Category:      reqData.Category,
			SessionID:     uint64(userFeedbackSession.Id),
			DigitalUserID: int64(userId),
		}
		if reqData.Category == 1 {
			userFeedbackSessionInfo.ImgURL = reqData.ImgUrl
		} else {
			userFeedbackSessionInfo.Content = reqData.Content
		}
		err = global.DB.Create(&userFeedbackSessionInfo).Error
		if err != nil {
			return nil, err
		}
	} else {
		return nil, errors.New("session not present")
	}

	// 选择一个回复通知
	res = map[string]interface{}{
		"voice_url": "",
		"content":   "",
	}

	// // 定义多个预配置的 res
	// resOptions := []map[string]interface{}{
	// 	{
	// 		"voice_url": "",
	// 		"content":   "",
	// 	},
	// 	{
	// 		"voice_url": "",
	// 		"content":   "",
	// 	},
	// 	{
	// 		"voice_url": "",
	// 		"content":   "",
	// 	},
	// }
	//
	// // 初始化随机数种子
	// rand.Seed(time.Now().UnixNano())
	//
	// // 随机选择一个 res
	// randomIndex := rand.Intn(len(resOptions))
	//
	// // 返回随机选择的 res
	// res = resOptions[randomIndex]

	return
}

func (v *UserFeedbackService) Reply(c *gin.Context, reqData request.UserFeedbackReply) (err error) {

	urlStr := ""
	if reqData.ImgUrl != "" {
		urlStr = v.ExtractPath(reqData.ImgUrl)
		reqData.ImgUrl = urlStr
	}

	// 获取用户信息
	var userService DigitalUserService
	var userInfo *model.DigitalUser
	if userInfo, err = userService.GetById(c, uint(reqData.UserId)); err != nil {
		return
	}

	s3Url := utils.S3Url(urlStr, userInfo.AppID)

	// 回复消息
	err = global.DB.Model(&model.UserFeedbackSession{}).
		Where("id = ?", reqData.SessionId).
		Updates(map[string]interface{}{
			"reply":  reqData.Content + ": " + s3Url,
			"status": 2,
		}).Error
	if err != nil {
		return
	}

	// 回复调用声望 组装数据
	msgContent := model.MessageContent{
		Text:       reqData.Content,
		OriginText: reqData.Content,
		ImgURL:     s3Url,
	}
	userIdStr := fmt.Sprintf("%d", reqData.UserId)

	aiSendMsg := &model.MsgInfo{
		MessageID:      "",
		Channel:        nil,
		SendTime:       time.Now().UnixMilli(),
		DeletedAt:      nil,
		MessageType:    model.MsgTypeUserFeedbackNoticy,
		SenderID:       model.AI_ROBOT,
		SenderName:     "robot",
		ReceiverID:     userIdStr,
		ReceiverName:   "",
		MessageContent: msgContent,
	}

	appInfo := global.AppMap[userInfo.AppID]
	var msgService MsgService
	err = msgService.SendMsgToAgoraWithoutMongo(aiSendMsg, appInfo)
	return
}

func (v *UserFeedbackService) SetUserEmail(c *gin.Context, reqData request.UserFeedbackSetEmail) (res interface{}, err error) {
	// 配置不同的邮箱
	userId := utils.GetDigitalUserID(c)
	// userId := uint(90001083)

	var userFeedbackEmail model.UserFeedbackEmail
	err = global.DB.Model(&model.UserFeedbackEmail{}).Where("digital_user_id = ?", userId).First(&userFeedbackEmail).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 创建一个会话
			userFeedbackEmail = model.UserFeedbackEmail{
				Email:         reqData.Email,
				DigitalUserID: int64(userId),
			}
			err = global.DB.Create(&userFeedbackEmail).Error
			if err != nil {
				return nil, err
			}
			return
		}

		return
	}
	// 更新邮箱
	err = global.DB.Model(&model.UserFeedbackEmail{}).
		Where(" digital_user_id= ?", userId).
		Updates(map[string]interface{}{
			"email": reqData.Email,
		}).Error
	if err != nil {
		return
	}

	err = global.DB.Model(&model.UserFeedbackSession{}).
		Where(" digital_user_id= ?", userId).
		Updates(map[string]interface{}{
			"email": reqData.Email,
		}).Error

	return
}

func (v *UserFeedbackService) GetUserFeedbackList(c *gin.Context, reqData request.UserFeedbackListReq) (res []*model.UserFeedbackSession, total int64, err error) {

	// 设置分页偏移量
	offset := (reqData.Page - 1) * reqData.PageSize

	// 构建查询条件
	query := global.DB.Model(&model.UserFeedbackSession{})

	// 如果 user_id 不为空，加入条件
	if reqData.UserId != 0 {
		query = query.Where("digital_user_id = ?", reqData.UserId)
	}

	// 如果 status 不为空，加入条件
	if reqData.Status != 0 {
		query = query.Where("status = ?", reqData.Status)
	}

	// 查询总记录数
	err = query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	// 分页查询数据
	err = query.
		Order("id desc").
		Offset(offset).
		Limit(reqData.PageSize).
		Find(&res).Error
	if err != nil {
		return nil, 0, err
	}

	// 将查出来id 放入到list 里去查对应的聊天记录 按顺序放入到对应的会话里
	var sessionIds []uint
	for _, value := range res {
		sessionIds = append(sessionIds, value.Id)
	}

	var userFeedbackSessionInfo []*model.UserFeedbackSessionInfo
	err = global.DB.Model(&model.UserFeedbackSessionInfo{}).
		Where("session_id in (?)", sessionIds).
		Order("id asc").
		Find(&userFeedbackSessionInfo).Error
	if err != nil {
		return nil, 0, err
	}
	// 将session_id 一样的聊天记录 放到同一个list里
	// var tempMap = make(map[uint64][]model.UserFeedbackSessionTemp)

	var tempImgMap = make(map[uint64][]model.UserFeedbackSessionTemp)
	var tempContentMap = make(map[uint64][]model.UserFeedbackSessionTemp)

	for _, value := range userFeedbackSessionInfo {
		if _, ok := tempImgMap[value.SessionID]; ok {
			if value.Category == 1 {
				tempImgMap[value.SessionID] = append(tempImgMap[value.SessionID], model.UserFeedbackSessionTemp{
					Content:   value.Content,
					ImgUrl:    value.ImgURL,
					CreatedAt: value.CreatedAt,
				})
			}

		} else {
			if value.Category == 1 {
				tempImgMap[value.SessionID] = append(tempImgMap[value.SessionID], model.UserFeedbackSessionTemp{
					Content:   value.Content,
					ImgUrl:    value.ImgURL,
					CreatedAt: value.CreatedAt,
				})
			}
		}
	}

	for _, value := range userFeedbackSessionInfo {
		if _, ok := tempContentMap[value.SessionID]; ok {
			if value.Category == 2 {
				tempContentMap[value.SessionID] = append(tempContentMap[value.SessionID], model.UserFeedbackSessionTemp{
					Content:   value.Content,
					ImgUrl:    value.ImgURL,
					CreatedAt: value.CreatedAt,
				})
			}

		} else {
			if value.Category == 2 {
				tempContentMap[value.SessionID] = append(tempContentMap[value.SessionID], model.UserFeedbackSessionTemp{
					Content:   value.Content,
					ImgUrl:    value.ImgURL,
					CreatedAt: value.CreatedAt,
				})
			}
		}
	}

	for _, value := range res {
		// if temp, ok := tempMap[uint64(value.Id)]; ok {
		// 	value.UserFeedList = temp
		// }

		if tempImgs, ok := tempImgMap[uint64(value.Id)]; ok {
			value.UserFeedImgList = tempImgs
		}

		if tempContents, ok := tempContentMap[uint64(value.Id)]; ok {
			value.UserFeedContentList = tempContents
		}
	}

	return

}

func (v *UserFeedbackService) DeleteSession(c *gin.Context, reqData request.UserFeedbackDeleteSessionReq) (err error) {
	// 确保 sessionId 不为空
	if reqData.SessionId == 0 {
		return fmt.Errorf("session_id cannot be empty")
	}

	// 启动事务
	tx := global.DB.Begin()

	// 删除 UserFeedbackSession 表中的数据
	if err = tx.Model(&model.UserFeedbackSession{}).
		Where("id = ?", reqData.SessionId).
		Delete(&model.UserFeedbackSession{}).Error; err != nil {
		tx.Rollback() // 发生错误时回滚事务
		return fmt.Errorf("failed to delete from UserFeedbackSession: %v", err)
	}

	// 删除 UserFeedbackSessionInfo 表中的数据
	if err = tx.Model(&model.UserFeedbackSessionInfo{}).
		Where("session_id = ?", reqData.SessionId).
		Delete(&model.UserFeedbackSessionInfo{}).Error; err != nil {
		tx.Rollback() // 发生错误时回滚事务
		return fmt.Errorf("failed to delete from UserFeedbackSessionInfo: %v", err)
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	return nil
}

// ExtractPath 从URL中提取路径部分
func (v *UserFeedbackService) ExtractPath(url string) string {
	// 查找第一个"/public"的开始位置
	index := strings.Index(url, "public")
	if index != -1 {
		// 从"/public"开始截取剩余部分
		return url[index:]
	}
	// 如果没有找到"/public"部分，返回空字符串或其他自定义内容
	return ""
}
