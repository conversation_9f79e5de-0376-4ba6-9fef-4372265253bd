package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

type WithdrawHistoryService struct{}

func (m *WithdrawHistoryService) Create(reqData *model.WithdrawHistory) (err error) {
	var (
		kocUserInfo    *model.KocUserInfo
		kocUserInfoSer KocUserInfoService
	)
	// 获取AI角色信息
	if reqData.KocUserId == 0 {
		err = fmt.Errorf("koc_user_id不能为空")
		return
	}
	// 查询KOC用户信息, 更新总收入和待结算收入
	if kocUserInfo, err = kocUserInfoSer.GetById(reqData.KocUserId); err != nil {
		return
	}
	if kocUserInfo == nil {
		err = fmt.Errorf("KOC用户信息不存在")
		return
	}
	if kocUserInfo.PendingIncome.LessThan(reqData.Amount) {
		err = fmt.Errorf("待结算收入不足")
		return
	}
	kocUserInfo.PendingIncome = kocUserInfo.PendingIncome.Sub(reqData.Amount)
	// 更新KOC用户信息
	if err = kocUserInfoSer.Update(kocUserInfo); err != nil {
		return
	}
	err = global.DB.Transaction(func(tx *gorm.DB) (tErr error) {
		// 更新KOC用户信息
		if tErr = tx.Model(&model.KocUserInfo{}).Where("id = ?", kocUserInfo.Id).Updates(kocUserInfo).Error; tErr != nil {
			return
		}
		// 创建提现记录
		if tErr = tx.Model(&model.WithdrawHistory{}).Create(reqData).Error; tErr != nil {
			return
		}
		return
	})
	return
}

func (m *WithdrawHistoryService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.WithdrawHistory{}).Where("id = ?", id).Delete(&model.WithdrawHistory{}).Error
	return
}

func (m *WithdrawHistoryService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.WithdrawHistory{}).Where("id IN (?)", reqData.Ids).Delete(&model.WithdrawHistory{}).Error
	return
}

func (m *WithdrawHistoryService) Update(reqData *model.WithdrawHistory) (err error) {
	err = global.DB.Model(&model.WithdrawHistory{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	return
}

func (m *WithdrawHistoryService) GetById(id uint) (res *model.WithdrawHistory, err error) {
	err = global.DB.Model(&model.WithdrawHistory{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *WithdrawHistoryService) GetAll() (resList []*model.WithdrawHistory, err error) {
	err = global.DB.Model(&model.WithdrawHistory{}).Find(&resList).Error
	return
}

func (m *WithdrawHistoryService) GetList(c *gin.Context, info req.WithdrawHistorySearch) (resList []*model.WithdrawHistory, total int64, err error) {
	var (
		kocUserId     uint
		sysUserKocSer SysUserKocService
		db            = global.DB.Model(&model.WithdrawHistory{})
	)
	if kocUserId, err = sysUserKocSer.GetSysUserKocUserId(c); err != nil {
		return
	}
	if kocUserId != 0 {
		info.KocUserId = kocUserId
	}
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.KocUserId != 0 {
		db = db.Where("koc_user_id = ?", info.KocUserId)
	}
	if !time.Time(info.Day).IsZero() {
		db = db.Where("day = ?", info.Day)
	}
	if info.Platform != 0 {
		db = db.Where("platform = ?", info.Platform)
	}
	if info.Account != "" {
		db = db.Where("account LIKE ?", "%"+info.Account+"%")
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
