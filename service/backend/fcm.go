package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/backend/request"
	"aimsg-server/model/backend/response"
	"aimsg-server/utils"
	"context"
	"encoding/json"
	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/auth"
	"firebase.google.com/go/v4/messaging"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"google.golang.org/api/option"
	"time"
)

type FcmService struct{}

func (m *FcmService) InitAllClient() {
	var (
		err           error
		fcmJsonBytes  []byte
		fcmClientMap  = map[uint]*messaging.Client{}
		authClientMap = map[uint]*auth.Client{}
		ctx           = context.Background()
		fcmApp        *firebase.App
		fcmClient     *messaging.Client
	)
	for _, app := range global.AppMap {
		zLog := global.LOG.With(zap.Any("appID", app.Id))
		if app.FcmJson == nil {
			zLog.Error("app.FcmJson == nil")
			continue
		}
		fcmJsonBytes, err = json.Marshal(app.FcmJson)
		if err != nil {
			zLog.Error("json.Marshal(app.FcmJson) err", zap.Error(err))
			continue
		}
		opt := option.WithCredentialsJSON(fcmJsonBytes)
		fcmApp, err = firebase.NewApp(ctx, nil, opt)
		if err != nil {
			zLog.Error("firebase.NewApp err", zap.Error(err))
			continue
		}
		authClient, authClientErr := fcmApp.Auth(ctx)
		if authClientErr != nil {
			zLog.Error("fcmApp.Auth err", zap.Error(authClientErr), zap.Uint("AppID", app.Id))
		} else {
			authClientMap[app.Id] = authClient
		}
		fcmClient, err = fcmApp.Messaging(ctx)
		if err != nil {
			zLog.Error("fcmApp.Messaging(ctx) err", zap.Error(err))
			continue
		}
		fcmClientMap[app.Id] = fcmClient

	}
	global.FcmClientMap = fcmClientMap
	global.AuthClientMap = authClientMap
}

func (m *FcmService) PushReplyMsg(ctx context.Context, userInfo *model.DigitalUser, aiRoleInfo *model.AiRole, msgInfo *model.MsgInfo) {
	var (
		err                 error
		sendRes             string
		fcmPushMsgDataBytes []byte
		fcmPushMsgData      = model.FcmPushMsg{
			PushType: model.PushTypeReplyMsg,
			PushData: model.PushDataReplyMsg{
				AiId:     aiRoleInfo.Id,
				RoleId:   aiRoleInfo.RoleID,
				MsgId:    msgInfo.MessageID,
				MsgType:  msgInfo.MessageType,
				ReplyMsg: msgInfo.MessageContent.Text,
			},
		}
	)

	if userInfo.FcmToken == "" {
		return
	}
	if fcmPushMsgDataBytes, err = json.Marshal(fcmPushMsgData); err != nil {
		return
	}
	fcmPushMsgDataStr := string(fcmPushMsgDataBytes)
	pushBody := msgInfo.MessageContent.Text
	switch msgInfo.MessageType {
	case model.MsgTypeTxtImg, model.MsgTypeTxtImgVoice:
		pushBody = "【Photo】" + pushBody
	case model.MsgTypeTxtVideo, model.MsgTypeTxtVideoVoice:
		pushBody = "【Video】" + pushBody
	default:
	}

	message := &messaging.Message{
		Data: map[string]string{
			"data": fcmPushMsgDataStr,
		},
		Notification: &messaging.Notification{
			Title:    aiRoleInfo.Nickname,
			Body:     pushBody,
			ImageURL: utils.S3Url(aiRoleInfo.Avatar, userInfo.AppID),
		},
		Android: &messaging.AndroidConfig{
			Notification: &messaging.AndroidNotification{
				DefaultSound: true,
			},
		},
		APNS:  &messaging.APNSConfig{Payload: &messaging.APNSPayload{Aps: &messaging.Aps{Sound: "default"}}},
		Token: userInfo.FcmToken,
	}
	appFcmClient, appFcmClientExist := global.FcmClientMap[userInfo.AppID]
	if !appFcmClientExist {
		global.LOG.Error("fcmClient not exist", zap.Uint("AppID", userInfo.AppID))
		return
	}
	sendRes, err = appFcmClient.Send(ctx, message)
	if err != nil {
		global.LOG.Error("PushReplyMsg error", zap.Any("message", message), zap.Any("userInfo", userInfo), zap.Error(err), zap.String("sendRes", sendRes))
		return
	}
}

func (m *FcmService) PushCommonMsgSingle(c *gin.Context, userId uint, title, body string) (err error) {
	var (
		fcmTokenList []response.SignInNoticeFcmToken
		fcmAppTokens = map[uint][]string{}
	)
	var reqData request.PushCommonMsgSingleReq
	reqData.UserId = userId
	reqData.Title = title
	reqData.Body = body

	db := global.DB.Model(&model.DigitalUser{}).Select("id, app_id, fcm_token")
	// 只查询正常用户,用推送token的用户
	db = db.Where("user_status = ? AND fcm_token != ''", int(model.UserStatusNormal))
	if reqData.UserId > 0 {
		db = db.Where("id = ?", reqData.UserId)
	}
	if err = db.Find(&fcmTokenList).Error; err != nil {
		return
	}
	if len(fcmTokenList) == 0 {
		return
	}
	for _, f := range fcmTokenList {
		fcmAppTokens[f.AppId] = append(fcmAppTokens[f.AppId], f.FcmToken)
	}
	for appId, tokenArr := range fcmAppTokens {
		if len(tokenArr) > global.FcmMaxSize {
			tokenListArr := utils.SplitArray(tokenArr, global.FcmMaxSize)
			for _, tArr := range tokenListArr {
				if err = m.PushCommonMsgMul(c, appId, reqData.Title, reqData.Body, tArr); err != nil {
					global.LOG.Error("PushMulticastNotice error", zap.Error(err))
					continue
				}
			}
		} else {
			if err = m.PushCommonMsgMul(c, appId, reqData.Title, reqData.Body, tokenArr); err != nil {
				global.LOG.Error("PushMulticastNotice error", zap.Error(err))
				continue
			}
		}
	}
	return
}

func (m *FcmService) PushCommonMsgMul(ctx context.Context, appId uint, title, body string, fcmTokens []string) (err error) {
	var (
		sendRes             *messaging.BatchResponse
		fcmPushMsgDataBytes []byte
		fcmPushMsgData      = model.FcmPushMsg{
			PushType: model.PushTypeCommon,
		}
	)
	appFcmClient, appFcmClientExist := global.FcmClientMap[appId]
	if !appFcmClientExist {
		global.LOG.Error("fcmClient not exist", zap.Uint("AppID", appId))
		err = fmt.Errorf("fcmClient(%d) not exist", appId)
		return
	}
	if fcmPushMsgDataBytes, err = json.Marshal(fcmPushMsgData); err != nil {
		return
	}
	fcmPushMsgDataStr := string(fcmPushMsgDataBytes)
	message := &messaging.MulticastMessage{
		Tokens: fcmTokens,
		Data: map[string]string{
			"data": fcmPushMsgDataStr,
		},
		Notification: &messaging.Notification{
			Title: title,
			Body:  body,
		},
		Android: &messaging.AndroidConfig{
			Notification: &messaging.AndroidNotification{
				DefaultSound: true,
			},
		},
		APNS: &messaging.APNSConfig{Payload: &messaging.APNSPayload{Aps: &messaging.Aps{Sound: "default"}}},
	}
	if sendRes, err = appFcmClient.SendEachForMulticast(ctx, message); err != nil {
		global.LOG.Error("PushCommonMsgMul error", zap.Any("message", message), zap.Error(err), zap.Any("sendRes", sendRes), zap.Strings("fcmTokens", fcmTokens))
		return
	}
	return
}

func (m *FcmService) PushReceiveFlower(ctx context.Context, appId uint, tokenList []string) (err error) {
	var (
		sendRes             *messaging.BatchResponse
		fcmPushMsgDataBytes []byte
	)
	appFcmClient, appFcmClientExist := global.FcmClientMap[appId]
	if !appFcmClientExist {
		global.LOG.Error("fcmClient not exist", zap.Uint("AppID", appId))
		return
	}
	fcmPushMsgData := model.FcmPushMsg{
		PushType: model.PushTypeReceiveFlower,
		PushData: model.PushDataReceiveFlower{
			Title: "Membership expiration reminder",
			Msg:   "Membership expires in one day, your baby looks forward to chatting with you~",
		},
	}
	if fcmPushMsgDataBytes, err = json.Marshal(fcmPushMsgData); err != nil {
		return
	}
	fcmPushMsgDataStr := string(fcmPushMsgDataBytes)
	message := &messaging.MulticastMessage{
		Tokens: tokenList,
		Data: map[string]string{
			"data": fcmPushMsgDataStr,
		},
		Notification: &messaging.Notification{
			Title: "Membership expiration reminder",
			Body:  "Membership expires in one day, your baby looks forward to chatting with you~",
		},
		APNS: &messaging.APNSConfig{Payload: &messaging.APNSPayload{Aps: &messaging.Aps{Sound: "default"}}},
	}
	if sendRes, err = appFcmClient.SendEachForMulticast(ctx, message); err != nil {
		global.LOG.Info("==>fcm push PushReceiveFlowerIOS error", zap.Error(err))
		return
	}
	global.LOG.Info("==>fcm push PushReceiveFlowerIOS", zap.Any("sendRes", sendRes))
	return
}

func (m *FcmService) SendPushPushReceiveFlowerTest(c *gin.Context, reqData *request.SendPushPushReceiveFlowerTestReq) (err error) {
	var (
		userInfo    *model.DigitalUser
		userService DigitalUserService
	)
	if userInfo, err = userService.GetById(c, reqData.UserId); err != nil {
		return
	}
	if userInfo.FcmToken == "" {
		err = fmt.Errorf("用户没有fcm_token")
		return
	}
	err = m.PushReceiveFlower(c, userInfo.AppID, []string{userInfo.FcmToken})
	return
}

func (m *FcmService) SignInNotice() {
	var (
		err          error
		fcmTokenList []response.SignInNoticeFcmToken
		fcmAppTokens = map[uint][]string{}
		ctx          = context.Background()
		cfg          = GetSignInNoticeCfg(ctx)
		dayStart     = utils.StartOfDay()
	)
	regDays := cfg.RegDay
	noActiveDay := cfg.NoActiveDay
	// 注册时间在几天前
	regStart := dayStart.AddDate(0, 0, regDays)
	regEnd := regStart.AddDate(0, 0, 1).Add(-time.Nanosecond)
	// 未活跃时间
	noActiveEnd := dayStart.AddDate(0, 0, noActiveDay).AddDate(0, 0, 1).Add(-time.Nanosecond)
	db := global.DB.Model(&model.DigitalUser{}).Select("id, app_id, fcm_token")
	// 创建时间范围
	db = db.Where("created_at BETWEEN ? AND ?", regStart, regEnd)
	// 活跃时间
	db = db.Where("active_at < ?", noActiveEnd)
	// 只查询正常用户,用推送token的用户
	db = db.Where("user_status = ? AND fcm_token != ''", int(model.UserStatusNormal))
	if err = db.Find(&fcmTokenList).Error; err != nil {
		return
	}
	if len(fcmTokenList) == 0 {
		return
	}
	for _, f := range fcmTokenList {
		fcmAppTokens[f.AppId] = append(fcmAppTokens[f.AppId], f.FcmToken)
	}
	for appId, tokenArr := range fcmAppTokens {
		var tArrs [][]string
		if len(tokenArr) > global.FcmMaxSize {
			tArrs = utils.SplitArray(tokenArr, global.FcmMaxSize)
			for _, tArr := range tArrs {
				if err = m.PushReceiveFlower(ctx, appId, tArr); err != nil {
					global.LOG.Error("m.PushReceiveFlower(ctx, appId, tArr) error", zap.Error(err))
					continue
				}
			}
		} else {
			if err = m.PushReceiveFlower(ctx, appId, tokenArr); err != nil {
				global.LOG.Error("m.PushReceiveFlower(ctx, appId, tokenArr) error", zap.Error(err))
				continue
			}
		}
	}
}

func (m *FcmService) PushDiySuccessMsg(ctx context.Context, diyPhotoTaskInfo *model.DiyPhotoTask) (err error) {
	var (
		sendRes             string
		userInfo            *model.DigitalUser
		userSer             DigitalUserService
		aiRoleInfo          *model.AiRole
		aiSer               AiRoleService
		fcmPushMsgDataBytes []byte
		fcmPushMsgData      = model.FcmPushMsg{
			PushType: model.PushTypeDiyOrders,
		}
	)
	if diyPhotoTaskInfo.DiyType != model.DiyTypeAiRole {
		err = fmt.Errorf("diyType(%d) != DiyTypeAiRole,", diyPhotoTaskInfo.DiyType)
		return
	}
	// 查询用户信息
	if userInfo, err = userSer.GetById(ctx, diyPhotoTaskInfo.DigitalUserId); err != nil {
		err = fmt.Errorf("userInfo GetById error, err: %v", err)
		return
	}
	if userInfo.FcmToken == "" {
		err = fmt.Errorf("user fcm_token is empty")
		return
	}
	// 查询AI角色信息
	if aiRoleInfo, err = aiSer.GetById(ctx, diyPhotoTaskInfo.AiRoleId); err != nil {
		err = fmt.Errorf("aiRoleInfo GetAiRoleById error, err: %v", err)
		return
	}
	diyConfigItem, diyConfigItemExist := global.DiyConfigMap[diyPhotoTaskInfo.DiyConfigId]
	if !diyConfigItemExist {
		err = fmt.Errorf("diy_config: %d not exist", diyPhotoTaskInfo.DiyConfigId)
		return
	}
	title := fmt.Sprintf("Your Custom AI Album by %s is Ready!", aiRoleInfo.Nickname)
	body := fmt.Sprintf("Your %s album with %s is ready! Enjoy %d images in the app!", aiRoleInfo.Nickname, diyConfigItem.Name, diyPhotoTaskInfo.ImgCount)

	if fcmPushMsgDataBytes, err = json.Marshal(fcmPushMsgData); err != nil {
		err = fmt.Errorf("json.Marshal(fcmPushMsgData) error, err: %v", err)
		return
	}
	fcmPushMsgDataStr := string(fcmPushMsgDataBytes)

	message := &messaging.Message{
		Data: map[string]string{
			"data": fcmPushMsgDataStr,
		},
		Notification: &messaging.Notification{
			Title: title,
			Body:  body,
		},
		Android: &messaging.AndroidConfig{
			Notification: &messaging.AndroidNotification{
				DefaultSound: true,
			},
		},
		APNS:  &messaging.APNSConfig{Payload: &messaging.APNSPayload{Aps: &messaging.Aps{Sound: "default"}}},
		Token: userInfo.FcmToken,
	}
	appFcmClient, appFcmClientExist := global.FcmClientMap[userInfo.AppID]
	if !appFcmClientExist {
		err = fmt.Errorf("fcmClient(%d) not exist", userInfo.AppID)
		global.LOG.Error("fcmClient not exist", zap.Uint("AppID", userInfo.AppID))
		return
	}
	sendRes, err = appFcmClient.Send(ctx, message)
	if err != nil {
		err = fmt.Errorf("appFcmClient.Send error, err: %v", err)
		global.LOG.Error("PushReplyMsg error", zap.Any("message", message), zap.Any("userInfo", userInfo), zap.Error(err), zap.String("sendRes", sendRes))
		return
	}
	return
}
