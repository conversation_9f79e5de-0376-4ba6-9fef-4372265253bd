package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
	"time"
)

type VideoCallService struct{}

func (m *VideoCallService) CacheKey(recId uint) string {
	return fmt.Sprintf("video_call_cache_by_room_id:%d", recId)
}

func (m *VideoCallService) SetCache(ctx context.Context, record *model.VideoCall) {
	var (
		err           error
		recordBytes   []byte
		cacheRedisKey = m.<PERSON>(record.Id)
	)
	if recordBytes, err = json.Marshal(record); err != nil {
		global.LOG.Error("SetCache", zap.Error(err))
	}
	recordStr := string(recordBytes)
	if err = global.REDIS.SetEx(ctx, cacheRedisKey, recordStr, time.Hour).Err(); err != nil {
		global.LOG.Error("SetCache", zap.Error(err))
	}
	return
}

func (m *VideoCallService) DelCache(ctx context.Context, recId uint) {
	var (
		cacheRedisKey = m.CacheKey(recId)
	)
	global.REDIS.Del(ctx, cacheRedisKey)
	return
}

func (m *VideoCallService) GetCache(ctx context.Context, recId uint) (record *model.VideoCall, err error) {
	var (
		cacheRedisStr string
		cacheRedisKey = m.CacheKey(recId)
	)
	if cacheRedisStr, err = global.REDIS.Get(ctx, cacheRedisKey).Result(); err != nil {
		return
	}
	if err = json.Unmarshal([]byte(cacheRedisStr), &record); err != nil {
		return
	}
	return
}

func (m *VideoCallService) GetByIdWithCache(ctx context.Context, recId uint) (res *model.VideoCall, err error) {
	res, err = m.GetCache(ctx, recId)
	if err != nil {
		err = global.DB.Model(&model.VideoCall{}).Where("id = ?", recId).First(&res).Error
		if err == nil {
			m.SetCache(ctx, res)
		}
	}
	return
}

func (m *VideoCallService) Create(ctx context.Context, reqData *model.VideoCall) (err error) {
	err = global.DB.Model(&model.VideoCall{}).Create(reqData).Error
	return
}

func (m *VideoCallService) DeleteById(ctx context.Context, id uint) (err error) {
	if err = global.DB.Model(&model.VideoCall{}).Where("id = ?", id).Delete(&model.VideoCall{}).Error; err != nil {
		return
	}
	m.DelCache(ctx, id)
	return
}

func (m *VideoCallService) DeleteByIds(ctx context.Context, reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.VideoCall{}).Where("id IN (?)", reqData.Ids).Delete(&model.VideoCall{}).Error
	return
}

func (m *VideoCallService) Update(ctx context.Context, reqData *model.VideoCall) (err error) {
	if err = global.DB.Model(&model.VideoCall{}).Where("id = ?", reqData.Id).Updates(reqData).Error; err != nil {
		return
	}
	return
}

func (m *VideoCallService) GetById(ctx context.Context, id uint) (res *model.VideoCall, err error) {
	err = global.DB.Model(&model.VideoCall{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *VideoCallService) GetByRoomId(ctx context.Context, roomId string) (res *model.VideoCall, err error) {
	err = global.DB.Model(&model.VideoCall{}).Where("room_id = ?", roomId).First(&res).Error
	return
}

func (m *VideoCallService) GetAll(ctx context.Context) (resList []*model.VideoCall, err error) {
	err = global.DB.Model(&model.VideoCall{}).Find(&resList).Error
	return
}

func (m *VideoCallService) SearchOne(ctx context.Context, info *model.VideoCall) (res *model.VideoCall, err error) {
	db := global.DB.Model(&model.VideoCall{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.CallStatus != 0 {
		db = db.Where("call_status = ?", info.CallStatus)
	}
	if info.CloseId != 0 {
		db = db.Where("close_id = ?", info.CloseId)
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if info.RealAnchorId != 0 {
		db = db.Where("real_anchor_id = ?", info.RealAnchorId)
	}
	if info.RoomId != "" {
		db = db.Where("room_id = ?", info.RoomId)
	}
	if err = db.First(&res).Error; err != nil {
		return
	}
	return
}

func (m *VideoCallService) SearchCount(ctx context.Context, info *model.VideoCall) (total int64, err error) {
	db := global.DB.Model(&model.VideoCall{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.CallStatus != 0 {
		db = db.Where("call_status = ?", info.CallStatus)
	}
	if info.CloseId != 0 {
		db = db.Where("close_id = ?", info.CloseId)
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if info.RealAnchorId != 0 {
		db = db.Where("real_anchor_id = ?", info.RealAnchorId)
	}
	if info.RoomId != "" {
		db = db.Where("room_id = ?", info.RoomId)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	return
}

func (m *VideoCallService) GetList(ctx context.Context, info req.VideoCallSearch) (resList []*model.VideoCall, total int64, err error) {
	db := global.DB.Model(&model.VideoCall{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.CallStatus != 0 {
		db = db.Where("call_status = ?", info.CallStatus)
	}
	if info.CloseId != 0 {
		db = db.Where("close_id = ?", info.CloseId)
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if info.RealAnchorId != 0 {
		db = db.Where("real_anchor_id = ?", info.RealAnchorId)
	}
	if info.RoomId != "" {
		db = db.Where("room_id = ?", info.RoomId)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
