package backend

import (
	"aimsg-server/model/cli/response"
	"context"
	"math/rand"
	"time"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
)

type MediaShowService struct{}

func (m *MediaShowService) GetAiMediaConfData(aiRoleData *model.AiRole) (lv1ImgList, lv2ImgList []string, lv1ImgToLv2Map map[string]string, lv1VideoList []response.AiRoleMediaVideoItem, lv2VideoList []response.AiRoleMediaVideoItem) {
	var (
		err        error
		mediaList  []*model.AiRoleMedia
		aiMediaSer AiRoleMediaService
	)
	lv1ImgToLv2Map = make(map[string]string)
	// 拿到配置的视频、图片
	mediaSearchReq := &model.AiRoleMedia{
		AiRoleId: aiRoleData.Id,
	}
	mediaList, err = aiMediaSer.SearchAll(context.Background(), mediaSearchReq)
	if err != nil {
		return
	}
	for _, item := range mediaList {
		switch item.MediaType {
		case model.MediaTypeImg:
			if item.MediaURL != "" {
				lv1ImgList = append(lv1ImgList, item.MediaURL)
			}
			if item.UndressUrl != "" {
				lv2ImgList = append(lv2ImgList, item.UndressUrl)
			}
			if item.MediaURL != "" && item.UndressUrl != "" {
				lv1ImgToLv2Map[item.MediaURL] = item.UndressUrl
			}
		case model.MediaTypeVideo:
			videoItem := response.AiRoleMediaVideoItem{
				VideoUrl: item.MediaURL,
				VideoImg: item.ThumbnailURL,
			}
			if item.MediaLevel == 2 {
				lv2VideoList = append(lv2VideoList, videoItem)
			} else {
				lv1VideoList = append(lv1VideoList, videoItem)
			}
		default:

		}
	}
	if aiRoleData.RoleType == model.AiRoleTypePrivate {
		lv1ImgList = append(lv1ImgList, aiRoleData.ImgList...)
	}
	return
}

func (m *MediaShowService) randMedia(media []string) string {
	var mediaUrl string
	if len(media) > 0 {
		length := len(media)
		rander := rand.New(rand.NewSource(time.Now().UnixNano()))
		mediaUrl = media[rander.Intn(length)]
	}
	return mediaUrl
}
