package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
)

type ImgTemplateRecordService struct{}

func (m *ImgTemplateRecordService) Create(reqData *model.ImgTemplateRecord) (err error) {
	err = global.DB.Model(&model.ImgTemplateRecord{}).Create(reqData).Error
	m.SyncAll()
	return
}

func (m *ImgTemplateRecordService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.ImgTemplateRecord{}).Where("id = ?", id).Delete(&model.ImgTemplateRecord{}).Error
	m.SyncAll()
	return
}

func (m *ImgTemplateRecordService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.ImgTemplateRecord{}).Where("id IN (?)", reqData.Ids).Delete(&model.ImgTemplateRecord{}).Error
	m.SyncAll()
	return
}

func (m *ImgTemplateRecordService) Update(reqData *model.ImgTemplateRecord) (err error) {
	err = global.DB.Model(&model.ImgTemplateRecord{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	m.SyncAll()
	return
}

func (m *ImgTemplateRecordService) GetById(id uint) (res *model.ImgTemplateRecord, err error) {
	err = global.DB.Model(&model.ImgTemplateRecord{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *ImgTemplateRecordService) GetAll() (resList []*model.ImgTemplateRecord, err error) {
	err = global.DB.Model(&model.ImgTemplateRecord{}).Order("sort DESC").Find(&resList).Error
	return
}

func (m *ImgTemplateRecordService) SyncAll() {
	var (
		err     error
		resList []*model.ImgTemplateRecord
		resMap  = map[uint]*model.ImgTemplateRecord{}
	)
	resList, err = m.GetAll()
	if err != nil {
		global.LOG.Error("SyncAll 失败", zap.Error(err))
		return
	}
	for _, item := range resList {
		resMap[item.Id] = item
	}
	global.ImgTemplateRecordList = resList
	global.ImgTemplateRecordMap = resMap
}

func (m *ImgTemplateRecordService) GetList(info req.ImgTemplateRecordSearch) (resList []*model.ImgTemplateRecord, total int64, err error) {
	db := global.DB.Model(&model.ImgTemplateRecord{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.CategoryId != 0 {
		db = db.Where("category_id = ?", info.CategoryId)
	}
	if info.Status != 0 {
		db = db.Where("status = ?", info.Status)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
