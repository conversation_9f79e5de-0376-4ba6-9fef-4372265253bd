package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
)

type AiCommonReplyService struct{}

func (m *AiCommonReplyService) Create(reqData *model.AiCommonReply) (err error) {
	err = global.DB.Model(&model.AiCommonReply{}).Create(reqData).Error
	m.SyncReplyList()
	return
}

func (m *AiCommonReplyService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.AiCommonReply{}).Where("id = ?", id).Delete(&model.AiCommonReply{}).Error
	m.SyncReplyList()
	return
}

func (m *AiCommonReplyService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.AiCommonReply{}).Where("id IN (?)", reqData.Ids).Delete(&model.AiCommonReply{}).Error
	m.SyncReplyList()
	return
}

func (m *AiCommonReplyService) Update(reqData *model.AiCommonReply) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.AiCommonReply{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	m.SyncReplyList()
	return
}

func (m *AiCommonReplyService) GetById(id uint) (res *model.AiCommonReply, err error) {
	err = global.DB.Model(&model.AiCommonReply{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *AiCommonReplyService) GetAll() (resList []*model.AiCommonReply, err error) {
	err = global.DB.Model(&model.AiCommonReply{}).Find(&resList).Error
	return
}

func (m *AiCommonReplyService) SyncReplyList() {
	var (
		err             error
		resList         []*model.AiCommonReply
		contentList     []string
		idleContentList []string
		liveOneList     []string
		liveManyList    []string
	)
	resList, err = m.GetAll()
	if err != nil {
		global.LOG.Error("SyncReplyList 失败", zap.Error(err))
		return
	}
	for _, r := range resList {
		if r.ReplyType == model.ReplyTypeReplyMsg {
			contentList = append(contentList, r.Content)
		} else if r.ReplyType == model.ReplyTypeIdleReply {
			idleContentList = append(idleContentList, r.Content)
		} else if r.ReplyType == model.ReplyTypeLiveOne {
			liveOneList = append(liveOneList, r.Content)
		} else if r.ReplyType == model.ReplyTypeLiveMany {
			liveManyList = append(liveManyList, r.Content)
		}
	}
	global.ReplyList = contentList
	global.IdleReplyList = idleContentList
	global.LiveOneReplyList = liveOneList
	global.LiveManyReplyList = liveManyList
}

func (m *AiCommonReplyService) GetList(info req.AiCommonReplySearch) (resList []*model.AiCommonReply, total int64, err error) {
	db := global.DB.Model(&model.AiCommonReply{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.ReplyType != 0 {
		db = db.Where("reply_type = ?", info.ReplyType)
	}
	if info.Content != "" {
		db = db.Where("content LIKE ?", "%"+info.Content+"%")
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
