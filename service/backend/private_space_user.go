package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm/clause"
)

type PrivateSpaceUserService struct{}

func (m *PrivateSpaceUserService) Create(ctx context.Context, reqData *model.PrivateSpaceUser) (err error) {
	err = global.DB.Model(&model.PrivateSpaceUser{}).Create(reqData).Error
	return
}

func (m *PrivateSpaceUserService) DeleteById(ctx context.Context, id uint) (err error) {
	err = global.DB.Model(&model.PrivateSpaceUser{}).Where("id = ?", id).Delete(&model.PrivateSpaceUser{}).Error
	return
}

func (m *PrivateSpaceUserService) DeleteByIds(ctx context.Context, reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.PrivateSpaceUser{}).Where("id IN (?)", reqData.Ids).Delete(&model.PrivateSpaceUser{}).Error
	return
}

func (m *PrivateSpaceUserService) Update(ctx context.Context, reqData *model.PrivateSpaceUser) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return
	}
	err = global.DB.Model(&model.PrivateSpaceUser{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	return
}

func (m *PrivateSpaceUserService) GetById(ctx context.Context, id uint) (res *model.PrivateSpaceUser, err error) {
	err = global.DB.Model(&model.PrivateSpaceUser{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *PrivateSpaceUserService) GetAll(ctx context.Context) (resList []*model.PrivateSpaceUser, err error) {
	err = global.DB.Model(&model.PrivateSpaceUser{}).Find(&resList).Error
	return
}

func (m *PrivateSpaceUserService) SearchAll(ctx context.Context, info *model.PrivateSpaceUser) (resList []*model.PrivateSpaceUser, err error) {
	db := global.DB.Model(&model.PrivateSpaceUser{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.PrivateSpaceId != 0 {
		db = db.Where("private_space_id = ?", info.PrivateSpaceId)
	}
	err = db.Find(&resList).Error
	return
}

func (m *PrivateSpaceUserService) SearchOne(ctx context.Context, info *model.PrivateSpaceUser) (res *model.PrivateSpaceUser, err error) {
	db := global.DB.Model(&model.PrivateSpaceUser{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.PrivateSpaceId != 0 {
		db = db.Where("private_space_id = ?", info.PrivateSpaceId)
	}
	if err = db.First(&res).Error; err != nil {
		return
	}
	return
}

func (m *PrivateSpaceUserService) SearchCount(ctx context.Context, info *model.PrivateSpaceUser) (total int64, err error) {
	db := global.DB.Model(&model.PrivateSpaceUser{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.PrivateSpaceId != 0 {
		db = db.Where("private_space_id = ?", info.PrivateSpaceId)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	return
}

func (m *PrivateSpaceUserService) GetList(ctx context.Context, info req.PrivateSpaceUserSearch) (resList []*model.PrivateSpaceUser, total int64, err error) {
	db := global.DB.Model(&model.PrivateSpaceUser{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.PrivateSpaceId != 0 {
		db = db.Where("private_space_id = ?", info.PrivateSpaceId)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
