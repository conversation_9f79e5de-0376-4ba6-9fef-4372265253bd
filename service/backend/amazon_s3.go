package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/utils"
	"fmt"
	v4 "github.com/aws/aws-sdk-go-v2/aws/signer/v4"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/sts"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"mime/multipart"
	"path"
	"path/filepath"
	"strings"
	"time"
)

type AmazonS3Service struct{}

func (s *AmazonS3Service) CredentialsData() (credentialsRes *sts.Credentials, err error) {
	var (
		assumeRoleResp *sts.AssumeRoleOutput
		s3Client       = GetS3Client()
		svc            = sts.New(s3Client.Session)
	)
	// 构建AssumeRole输入参数
	params := &sts.AssumeRoleInput{
		RoleArn:         aws.String("arn:aws:iam::************:role/s3-admin"), // 替换为角色的ARN
		RoleSessionName: aws.String("myStsSession"),                            // 自定义会话名称
		DurationSeconds: aws.Int64(int64(time.Hour.Seconds())),                 // 临时凭证的有效期（以秒为单位）
	}

	// 调用AssumeRole操作获取临时凭证
	if assumeRoleResp, err = svc.AssumeRole(params); err != nil {
		return
	}
	credentialsRes = assumeRoleResp.Credentials
	return
}

func (s *AmazonS3Service) CliS3UploadFileUrl(c *gin.Context, data request.S3UploadFileUrlReq) (res gin.H, err error) {
	var (
		presignedReq *v4.PresignedHTTPRequest
		s3V2Client   = GetS3ClientV2(c)
	)
	if !utils.IsImageFile(data.FileName) {
		err = fmt.Errorf("file type is not allowed")
		return
	}
	imgPathList := []string{"public", "img"}
	switch data.FileType {
	case 1:
		imgPathList = append(imgPathList, "face")
	case 2:
		imgPathList = append(imgPathList, "normal")
	case 3:
		imgPathList = append(imgPathList, "ai_avatar")
	default:
		err = fmt.Errorf("file type is not allowed")
		return
	}
	fileExt := filepath.Ext(data.FileName)
	fileName := fmt.Sprintf("%s%s", data.FileHash, fileExt)
	imgPathList = append(imgPathList, fileName)
	fileKey := strings.Join(imgPathList, "/")
	presignedReq, err = s3V2Client.PresignPutObject(fileKey, data.ContentType, int64(15*time.Minute.Seconds()))
	if err != nil {
		return
	}
	res = gin.H{
		"file_key": fileKey,
		"url":      presignedReq.URL,
		"method":   presignedReq.Method,
		"header":   presignedReq.SignedHeader,
	}
	return
}

func (s *AmazonS3Service) S3UploadFileUrlOrigin(c *gin.Context, data request.S3UploadFileUrlReq) (res gin.H, err error) {
	var (
		presignedReq *s3.PresignedPostRequest
		s3V2Client   = GetS3ClientV2(c)
	)
	if !utils.IsImageFile(data.FileName) {
		err = fmt.Errorf("file type is not allowed")
		return
	}
	imgPathList := []string{"public", "img"}
	switch data.FileType {
	case 1:
		imgPathList = append(imgPathList, "face")
	case 2:
		imgPathList = append(imgPathList, "normal")
	case 3:
		imgPathList = append(imgPathList, "ai_avatar")
	default:
		err = fmt.Errorf("file type is not allowed")
		return
	}
	fileExt := filepath.Ext(data.FileName)
	if data.FileHash == "" {
		data.FileHash = uuid.NewString()
	}
	fileName := fmt.Sprintf("%s%s", data.FileHash, fileExt)
	imgPathList = append(imgPathList, fileName)
	fileKey := strings.Join(imgPathList, "/")
	presignedReq, err = s3V2Client.PresignPostObject(fileKey, data.ContentType, int64(10*time.Minute.Seconds()))
	if err != nil {
		return
	}
	res = gin.H{
		"url":    presignedReq.URL,
		"fields": presignedReq.Values,
	}
	return
}

func (s *AmazonS3Service) UploadFile(c *gin.Context, fileHeader *multipart.FileHeader, business string) (url, key string, err error) {
	var (
		fileName string
		file     multipart.File
		s3Client = GetS3Client()
	)
	if fileHeader == nil {
		err = fmt.Errorf("file is required")
		return
	}
	if file, err = fileHeader.Open(); err != nil {
		return
	}
	defer file.Close()
	fileName = uuid.NewString()
	// if fileName, err = utils.HashFileMD5(file); err != nil {
	// 	return
	// }
	contentType := fileHeader.Header.Get("Content-Type")
	key = fmt.Sprintf("public/%s/%s%s", business, fileName, path.Ext(fileHeader.Filename)) // 文件名格式 自己可以改 建议保证唯一性
	if err = s3Client.UploadMultipartFile(file, key, contentType); err != nil {
		return
	}
	url = global.CONFIG.AwsS3.BaseURL + key
	return
}
