package backend

import (
	"aimsg-server/global"
	reqBtc "aimsg-server/model/backend/request/btc"
	"aimsg-server/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"go.uber.org/zap"
)

type BtcService struct{}

const (
	whattomineAPI = "https://whattomine.com/api/v1/calculate"
	token         = "e64800b8f4a045fbac5dbbadcdbff77bad253192b1d53205cb0351052a11594f"
	RedisBtc      = "ai:btc:data:rate:%d:day:%s"
)

type WhattomineReq struct {
	Cost     float64                  `json:"cost"`
	Settings []map[string]interface{} `json:"settings"`
}

type CoinData struct {
	Tag                string `json:"tag"`
	Name               string `json:"name"`
	EstimatedRewards   string `json:"estimated_rewards"`
	EstimatedRewards24 string `json:"estimated_rewards24"`
	BtcRevenue         string `json:"btc_revenue"`
	BtcRevenue24       string `json:"btc_revenue24"`
	Revenue            string `json:"revenue"`
	Revenue24          string `json:"revenue24"`
	Profit             string `json:"profit"`
	Profit24           string `json:"profit24"`
}

var rateMap map[string]*model.BtcBlockchainHashRate

func init() {
	rateMap = make(map[string]*model.BtcBlockchainHashRate)
}
func (m *BtcService) GetTotalByUser(c *gin.Context) (resp reqBtc.GetTotalUserResp, err error) {
	userId := utils.GetDigitalUserID(c)
	totalBtc, err := m.CashOutBtcNum(c, userId)
	if err != nil {
		return resp, err
	}
	return reqBtc.GetTotalUserResp{
		fmt.Sprintf("%.16f", totalBtc),
	}, nil
	return
}

// 获取用户的当前的btc总额
func (m *BtcService) GetTotalByUserAll(c *gin.Context) (resp reqBtc.GetTotalUserResp, err error) {
	var totalBtc float64 = 0
	userId := utils.GetDigitalUserID(c)
	btcNumOld, err := m.GetTotalBtcByUser(c, userId)
	fmt.Printf("历史总额%.16f\r\n", btcNumOld)
	totalBtc += btcNumOld
	if err != nil {
		return resp, err
	}

	//实时计算今天的
	productList, err := m.GetProductToday(c, userId)
	if err != nil {
		return resp, err
	}
	//获得系数
	powerList, err := m.getFreePower(userId)
	if err != nil {
		return resp, err
	}
	now := time.Now()
	midnight := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	//nextDay := now.Add(+time.Hour * 24)
	//nextDayMidnight := time.Date(nextDay.Year(), nextDay.Month(), nextDay.Day(), 0, 0, 0, 0, nextDay.Location())
	for _, item := range productList {
		startTime := item.StartTime.Unix()
		if startTime < midnight.Unix() {
			startTime = midnight.Unix()
		}
		endTime := now.Unix()

		hashrate, err := m.getProductHashrate(item.ProductID, item.ProductType, userId, item.StartTime)
		if err != nil {
			return resp, err
		}
		// 免费产品才有系数  系数的开始时间和结束时间 两端有系数和无系数
		if item.ProductType != ProductTypeShop {
			if len(powerList.Items) == 0 { // 没有系数
				seconds := endTime - startTime
				num, err := m.getBtcBlock(hashrate, seconds)
				if err != nil {
					return resp, err
				}
				totalBtc += num
				fmt.Println("商品消费id", item.ID, time.Unix(startTime, 0), time.Unix(endTime, 0), fmt.Sprintf("%.16f", num))
			} else { // 不买系数前 和 买系数后
				for _, v := range powerList.Items {
					seconds1 := v.Time.Unix() - startTime
					num1, err := m.getBtcBlock(hashrate, seconds1)
					if err != nil {
						return resp, nil
					}
					totalBtc += num1

					seconds2 := endTime - v.Time.Unix()
					tempHashrate := hashrate * (1 + v.Ratio/100)
					num2, err := m.getBtcBlock(tempHashrate, seconds2)
					if err != nil {
						return resp, err
					}
					totalBtc += num2

					fmt.Println("商品消费id", item.ID, time.Unix(startTime, 0), time.Unix(v.Time.Unix(), 0), fmt.Sprintf("%.16f", num1))
					fmt.Println("商品消费id", item.ID, time.Unix(v.Time.Unix(), 0), time.Unix(endTime, 0), fmt.Sprintf("%.16f", num2))
				}
			}
		} else {
			seconds := endTime - startTime
			num, err := m.getBtcBlock(hashrate, seconds)
			if err != nil {
				return resp, err
			}
			totalBtc += num
			fmt.Println("商品消费id", item.ID, time.Unix(startTime, 0), time.Unix(endTime, 0), fmt.Sprintf("%.16f", num))
		}
	}

	return reqBtc.GetTotalUserResp{
		fmt.Sprintf("%.16f", totalBtc),
	}, nil
	return
}

// 获取用户的当前的算力
func (m *BtcService) GetRateByUser(c *gin.Context) (resp reqBtc.GetRateByUserResp, err error) {
	//获取当前用户今天有效期内购买的产品列表
	userId := utils.GetDigitalUserID(c)

	productList, err := m.GetProductToday(c, userId)
	if err != nil {
		return resp, err
	}
	var totalHashrate float64 = 0

	//获得系数
	powerList, err := m.getFreePower(userId)
	if err != nil {
		return resp, err
	}
	for _, item := range productList {
		hashrate, err := m.getProductHashrate(item.ProductID, item.ProductType, userId, item.StartTime)
		if err != nil {
			return resp, err
		}
		if item.ProductType != ProductTypeShop {
			totalHashrate += hashrate * (1 + powerList.Total/100)
		} else {
			totalHashrate += hashrate
		}
	}
	miningSpeed, miningSpeedUnit := m.powerHash(uint64(totalHashrate), "H/s")
	return reqBtc.GetRateByUserResp{
		miningSpeed,
		miningSpeedUnit,
	}, nil
}

// 根据用户的消费产品历史记录 计算出算力 然后再计算出所得的btc
// 1、获取用户的购买产品记录
// 2、根据一个产品的开始时间和结束时间 划分为每天的分组
//
//	循环每天的分组 判断当前天在规定范围内是的利率 （然后再根据利率分组）
func (m *BtcService) CrontabCalYestoday() (err error) {
	now := time.Now()
	midnight := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	lockKey := fmt.Sprintf("ai:craontb:btc:1CrontabCalYestoday:v1:%s", midnight.Format("2006-01-02"))
	isLock := global.REDIS.SetNX(context.Background(), lockKey, 1, time.Hour*24).Val()
	if !isLock {
		return nil
	}
	//查看昨天哪些人有权益
	var list []*model.BtcProductSubscription
	yestoday := now.Add(-time.Hour * 24)
	yestodayMidnight := time.Date(yestoday.Year(), yestoday.Month(), yestoday.Day(), 0, 0, 0, 0, yestoday.Location())
	err = global.DB.
		Where("(start_time <= ? and end_time >= ? and product_type!=0) or (start_time <= ? and product_type=0)", midnight.Format("2006-01-02 15:04:05"), yestodayMidnight.Format("2006-01-02 15:04:05"), midnight.Format("2006-01-02 15:04:05")).
		Find(&list).Error
	if err != nil {
		global.REDIS.Del(context.Background(), lockKey)
		return
	}
	if len(list) == 0 {
		return
	}
	userBtcMap := map[uint]float64{}
	for _, item := range list {
		// 获取用户信息
		var (
			userService DigitalUserService
		)
		if _, err = userService.GetById(context.Background(), item.UserID); err != nil {
			continue
		}

		if _, ok := userBtcMap[item.UserID]; !ok {
			userBtcMap[item.UserID] = 0
		}
		startTime := item.StartTime.Unix()
		endTime := item.EndTime.Unix()
		if item.ProductType == ProductTypeShop {
			endTime = midnight.Unix()
		} else {
			if endTime > midnight.Unix() {
				endTime = midnight.Unix()
			}
		}
		hashrate, err := m.getProductHashrate(item.ProductID, item.ProductType, item.UserID, item.StartTime)
		if err != nil {
			global.REDIS.Del(context.Background(), lockKey)
			global.LOG.Error("Btc crontab error", zap.Any("err", err))
			return err
		}
		// 免费产品才有系数  系数的开始时间和结束时间 两端有系数和无系数
		if item.ProductType != ProductTypeShop {
			//获得系数
			powerList, err := m.getFreePower(item.UserID)
			if err != nil {
				global.REDIS.Del(context.Background(), lockKey)
				return err
			}
			if len(powerList.Items) == 0 { // 没有系数
				fmt.Println("商品消费id", item.ID, time.Unix(startTime, 0), time.Unix(endTime, 0))
				seconds := endTime - startTime
				num, err := m.getBtcBlock(hashrate, seconds)
				if err != nil {
					global.REDIS.Del(context.Background(), lockKey)
					return err
				}
				userBtcMap[item.UserID] += num
			} else { // 不买系数前 和 买系数后
				for _, v := range powerList.Items {
					seconds1 := v.Time.Unix() - startTime
					num1, err := m.getBtcBlock(hashrate, seconds1)
					if err != nil {
						global.REDIS.Del(context.Background(), lockKey)
						return err
					}
					userBtcMap[item.UserID] += num1

					seconds2 := endTime - v.Time.Unix()
					tempHashrate := hashrate * (1 + v.Ratio/100)
					num2, err := m.getBtcBlock(tempHashrate, seconds2)
					if err != nil {
						global.REDIS.Del(context.Background(), lockKey)
						return err
					}
					userBtcMap[item.UserID] += num2
					fmt.Println("商品消费id", item.ID, time.Unix(startTime, 0), time.Unix(v.Time.Unix(), 0))
					fmt.Println("商品消费id", item.ID, time.Unix(v.Time.Unix(), 0), time.Unix(endTime, 0))

				}
			}
		} else {
			seconds := endTime - startTime
			num, err := m.getBtcBlock(hashrate, seconds)
			if err != nil {
				global.REDIS.Del(context.Background(), lockKey)
				return err
			}
			fmt.Println("商品消费id", item.ID, time.Unix(startTime, 0), time.Unix(endTime, 0))
			userBtcMap[item.UserID] += num
		}
	}

	for userId, v := range userBtcMap {
		//入库
		earning := model.BtcUserEarnings{
			UserID:   userId,
			Date:     yestodayMidnight,
			Earnings: fmt.Sprintf("%.16f", v),
		}

		if err := global.DB.Create(&earning).Error; err != nil {
			global.LOG.Error("Btc crontab error 插入失败，跳过:", zap.Any("err", err))
		} else {
			global.REDIS.Del(context.Background(), fmt.Sprintf(CacheGetTotalBtcByUser, userId))
		}

	}
	return nil
}

func (m *BtcService) IncomeDetails(c *gin.Context) (resp *reqBtc.IncomeDetailsResp, err error) {
	userId := utils.GetDigitalUserID(c)
	incomes, err := m.GetHistoryBtc(c, userId)
	list := make([]*reqBtc.Benefits, 0, len(incomes))
	yestodayTotal := fmt.Sprintf("%.16f", 0.0)
	now := time.Now()
	yesterdayMidnight := time.Date(now.Year(), now.Month(), now.Day()-1, 0, 0, 0, 0, now.Location())
	for k, v := range incomes {
		if k == 0 {
			if v.Date.Format("2006-01-02") == yesterdayMidnight.Format("2006-01-02") {
				yestodayTotal = v.Earnings
				continue
			}
		}
		list = append(list, &reqBtc.Benefits{
			Id:      v.ID,
			Content: v.Earnings,
			Date:    v.Date.Format("Jan 02 2006"),
		})
	}
	resp = &reqBtc.IncomeDetailsResp{
		TotalEarnings: yestodayTotal,
		MoreBenefit:   list,
	}
	return
}

// 根据用户的消费产品历史 计算出昨日所得的btc
func (m *BtcService) getBtcToYestoday(c *gin.Context) (resp string, err error) {
	userId := utils.GetDigitalUserID(c)
	data, err := m.GetHistoryBtc(c, userId)
	var info float64
	now := time.Now()
	yesterdayMidnight := time.Date(now.Year(), now.Month(), now.Day()-1, 0, 0, 0, 0, now.Location())
	for _, v := range data {
		if v.Date.Format("2006-01-02") == yesterdayMidnight.Format("2006-01-02") {
			info, _ = strconv.ParseFloat(v.Earnings, 64)
		}
	}
	return fmt.Sprintf("%.16f", info), nil
}

func (m *BtcService) getHashRate(rate uint64, curT time.Time) (resp model.BtcBlockchainHashRate, err error) {
	day := curT.Format("2006-01-02")
	if data, ok := rateMap[day]; ok {
		resp = *data
		return resp, nil
	}
	redisKey := fmt.Sprintf(RedisBtc, rate, day)
	jsonStr := global.REDIS.Get(context.Background(), redisKey).Val()
	if jsonStr != "" {
		rateMap[day] = &resp
		_ = json.Unmarshal([]byte(jsonStr), &resp)
		return
	}

	//不是今天的时间如果也没有历史数据就给今天的数据， 不做历史缓存
	now := time.Now()
	todayMidnight := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	tomorrow := todayMidnight.Add(24 * time.Hour)
	if curT.Unix() < tomorrow.Unix() {
		day := now.Format("2006-01-02")
		redisKey := fmt.Sprintf(RedisBtc, rate, day)
		jsonStr := global.REDIS.Get(context.Background(), redisKey).Val()
		if jsonStr != "" {
			rateMap[day] = &resp
			_ = json.Unmarshal([]byte(jsonStr), &resp)
			return
		}
	}

	// db中没有就查询 api
	resp = m.blockchainHashRate(rate, curT)
	b, _ := json.Marshal(resp)
	rateMap[day] = &resp
	err = global.REDIS.SetNX(context.Background(), redisKey, b, time.Hour*24*365*10).Err()
	if err != nil {
		return
	}
	return
}

func (m *BtcService) blockchainHashRate(rate uint64, curT time.Time) (record model.BtcBlockchainHashRate) {
	//请求api
	data, apiData, err := m.getBlockchainHashRate(rate)
	if err != nil {
		global.LOG.Error("Btc矿机计算api请求错误 error", zap.Any("err", err))
		return
	}
	_ = copier.Copy(&record, &data)
	record.JsonData = apiData
	record.Hashrate = rate
	record.Day = curT
	//落库
	err = global.DB.Create(&record).Error
	if err != nil {
		global.LOG.Error("Btc矿机计算api请求错误 error", zap.Any("err", err))
		return
	}
	return
}

func (BtcService) getBlockchainHashRate(rate uint64) (resp CoinData, apiData string, err error) {
	req := map[string]interface{}{
		"cost": 0.1,
		"settings": []interface{}{
			map[string]interface{}{
				"algorithm": "sha256",
				"power":     3500,
				"hashrate":  rate,
			},
		},
	}
	var data interface{}
	apiLog, curlStr, err := utils.HttpPostJson(fmt.Sprintf("%s?api_token=%s", whattomineAPI, token), req, &data, 3)
	if err != nil {
		err = errors.New(fmt.Sprintf("%s, %s, err:%s", apiLog, curlStr, err))
		return
	}
	josnB, _ := json.Marshal(data)
	var list []CoinData
	apiData = string(josnB)
	err = json.Unmarshal(josnB, &list)
	if err != nil {
		global.LOG.Error("Btc 矿机计算解析 error", zap.Any("err", err))
	}
	isSet := false
	for _, v := range list {
		if v.Tag == "BTC" {
			resp = v
			isSet = true
			break
		}
	}
	if !isSet {
		err = errors.New("btc 不存在")
		global.LOG.Error("Btc 矿机计算解析结果中不存在btc error")
		return
	}
	return
}
