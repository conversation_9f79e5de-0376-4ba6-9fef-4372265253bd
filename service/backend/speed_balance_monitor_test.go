package backend

import (
	"aimsg-server/config"
	"aimsg-server/global"
	"testing"
)

func TestSpeedBalanceMonitorService_GetBalanceStatus(t *testing.T) {
	// 设置测试配置
	global.CONFIG.SpeedAPI = config.SpeedAPI{
		Enabled:       true,
		BaseURL:       "https://api.tryspeed.com",
		Authorization: "Basic c2tfdGVzdF9tYmthamk2bVhGTjV1Q29IbWJrYWptbnZtZ25rNWF2YW1ia2FqbW53S2Ztc3pDRlY6",
		Threshold:     100000,
		FeiShuBotID:   "test-bot-id",
	}

	service := &SpeedBalanceMonitorService{}

	status, err := service.GetBalanceStatus()
	if err != nil {
		t.Logf("获取余额状态失败（这可能是正常的，如果 API 密钥无效）: %v", err)
		return
	}

	t.Logf("余额状态: %+v", status)

	// 验证返回的状态结构
	if enabled, ok := status["enabled"].(bool); !ok || !enabled {
		t.Error("期望 enabled 为 true")
	}

	if _, ok := status["balance"]; !ok {
		t.Error("期望返回 balance 字段")
	}

	if threshold, ok := status["threshold"].(int64); !ok || threshold != 100000 {
		t.Error("期望 threshold 为 100000")
	}
}

func TestSpeedBalanceMonitorService_GetBalance(t *testing.T) {
	service := &SpeedBalanceMonitorService{}

	err := service.CheckBalance()
	if err != nil {
		t.Logf("获取余额状态失败（这可能是正常的，如果 API 密钥无效）: %v", err)
		return
	}
}
