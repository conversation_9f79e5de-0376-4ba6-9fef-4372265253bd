package backend

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/td"
	"aimsg-server/utils"
	"context"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
	"time"
)

func (m *VoiceCallService) CreateVoiceCall(c *gin.Context, reqData *request.CreateVoiceCallReq) (res gin.H, err error) {
	var (
		aiRoleInfo      *model.AiRole
		userInfo        *model.DigitalUser
		userProfileInfo *model.DigitalUserProfile
		userService     DigitalUserService
		profileService  DigitalUserProfileService
		aiRoleSer       AiRoleService
		msgCountSer     UserAiMsgCountService
		msgService      MsgService
		userId          = utils.GetDigitalUserID(c)
	)
	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	isSub := userInfo.IsSub()
	verCompare350 := utils.CompareVersions(userInfo.Version, "3.5.0")
	appInfo := global.AppMap[userInfo.AppID]
	isSvip := userInfo.IsSVip()
	isChatVip := userInfo.IsChatVip()
	// isContentVip := userInfo.IsContentVip()
	if IsVipSVipUser(userInfo) {
		if verCompare350 >= 0 && !isSub {
			err = errs.RequireSubErr
			return
		}
	} else {
		if !isChatVip {
			err = errs.ChatVipErr
			return
		}
	}
	if userProfileInfo, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	// 获取AI角色信息
	if aiRoleInfo, err = aiRoleSer.GetByRoleId(reqData.AiRoleId); err != nil {
		return
	}
	rewardVoiceCallCount, _ := msgService.GetRewardVoiceCallCount(userId, aiRoleInfo.Id)
	// 语音通话每分钟价格
	minPrice := appInfo.DetailConfig.Data().MinPrice
	if IsVipSVipUser(userInfo) {
		if isSvip {
			minPrice = appInfo.DetailConfig.Data().MinPriceSVip
		} else if isSub {
			minPrice = appInfo.DetailConfig.Data().MinPriceSub
		}
	} else {
		minPrice = appInfo.DetailConfig.Data().ChatVipMinPrice
	}
	if rewardVoiceCallCount > 0 {
		minPrice = 0
	}
	if userProfileInfo.FlowerAmount < minPrice {
		err = errs.VoiceNoFloErr
		return
	}
	callMinute := 1
	totalPrice := minPrice * callMinute
	// 创建语音通话
	voiceCallRecord := &model.VoiceCall{
		AppID:         userInfo.AppID,
		AiId:          aiRoleInfo.Id,
		AiRoleId:      aiRoleInfo.RoleID,
		DigitalUserId: userInfo.Id,
		MinPrice:      minPrice,
		CallMinute:    callMinute,
		TotalPrice:    totalPrice,
	}
	// 生成第一句话的语音
	appVoiceCallCfg := GetAppVoiceCallCfg(c, userInfo.AppID)
	randFirstMsg := utils.RandList(appVoiceCallCfg.FirstMsgs)
	randIdleMsg := utils.RandList(appVoiceCallCfg.IdleMsgs)
	firstMsgKey, firstMsgKeyExist := m.VoiceCache(c, randFirstMsg, aiRoleInfo)
	if !firstMsgKeyExist {
		voiceUuidStr := uuid.NewString()
		firstMsgKey, _ = msgService.ElevenLabsTts(voiceUuidStr, randFirstMsg, aiRoleInfo)
	}
	idleMsgKey, idleMsgKeyExist := m.VoiceCache(c, randIdleMsg, aiRoleInfo)
	if !idleMsgKeyExist {
		voiceUuidStr := uuid.NewString()
		idleMsgKey, _ = msgService.ElevenLabsTts(voiceUuidStr, randIdleMsg, aiRoleInfo)
	}
	intimateSubType := model.SubTypeVoiceCall

	err = global.DB.Transaction(func(tx *gorm.DB) (tErr error) {
		// 创建语音通话记录
		if tErr = tx.Model(&model.VoiceCall{}).Create(voiceCallRecord).Error; tErr != nil {
			return
		}
		// 创建FlowerConsumeRecord记录
		var fRecord = &model.FlowerConsumeRecord{
			DigitalUserId: userId,
			AiId:          aiRoleInfo.Id,
			AiRoleId:      aiRoleInfo.RoleID,
			ConsumeType:   model.ConsumeTypeVoiceCall,
			RelationID:    fmt.Sprintf("%d", voiceCallRecord.Id),
			OriginAmount:  userProfileInfo.FlowerAmount,
			ConsumeAmount: totalPrice,
		}
		if tErr = tx.Model(&model.FlowerConsumeRecord{}).Create(fRecord).Error; tErr != nil {
			return
		}
		go TdPointer(userInfo, userProfileInfo, td.ServerEvent, map[string]interface{}{
			"event_tag":         "coins_consume",
			"coins_consume_num": -totalPrice,
			"ai_role_id":        aiRoleInfo.RoleID,
			"ai_role_type":      aiRoleInfo.RoleType,
			"time_long":         1,
			"source":            "voice_call_consume",
		})
		remainAmount := userProfileInfo.FlowerAmount - totalPrice
		updateMap := map[string]interface{}{
			"flower_amount": remainAmount,
		}
		_ = msgService.SaveIntimateRecord(c, userInfo.Id, aiRoleInfo, model.IntimateTypeAdd, intimateSubType)
		txTran := tx.Model(&model.DigitalUserProfile{}).Where("id = ? AND flower_amount = ?", userProfileInfo.Id, userProfileInfo.FlowerAmount).Updates(updateMap)
		if tErr = txTran.Error; tErr != nil {
			return
		}
		if txTran.RowsAffected == 0 {
			tErr = errs.VoiceNoFloErr
			return
		}
		return
	})
	if err != nil {
		return
	}
	// 更新UserAiMsgCount
	if err = msgCountSer.FindUpdate(c, userId, aiRoleInfo, map[string]interface{}{
		"call_minute": gorm.Expr("call_minute + 1"),
	}); err != nil {
		return
	}
	res = gin.H{
		"id":            voiceCallRecord.Id,
		"first_msg_url": utils.S3Url(firstMsgKey, userInfo.AppID),
		"idle_msg_url":  utils.S3Url(idleMsgKey, userInfo.AppID),
	}
	if rewardVoiceCallCount > 0 {
		_ = msgService.RewardVoiceCall(userId, aiRoleInfo.Id, -1)
	}
	return
}

func (m *VoiceCallService) VoiceCallDeduction(c *gin.Context, reqData *request.VoiceCallDeductionReq) (err error) {
	var (
		voiceCallRecord *model.VoiceCall
		aiRoleInfo      *model.AiRole
		aiRoleSer       AiRoleService
		userInfo        *model.DigitalUser
		userService     DigitalUserService
		msgService      MsgService
		userProfileInfo *model.DigitalUserProfile
		profileService  DigitalUserProfileService
		msgCountSer     UserAiMsgCountService
		userId          = utils.GetDigitalUserID(c)
		appId           = utils.GetAppID(c)
	)
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	isSub := userInfo.IsSub()
	isSvip := userInfo.IsSVip()
	// 查询通话记录
	if voiceCallRecord, err = m.GetById(c, reqData.Id); err != nil {
		return
	}
	// 判断用户是否匹配
	if voiceCallRecord.DigitalUserId != userId {
		err = fmt.Errorf("user id not match")
		return
	}
	// 获取AI角色信息
	if aiRoleInfo, err = aiRoleSer.GetByRoleId(voiceCallRecord.AiRoleId); err != nil {
		return
	}
	rewardVoiceCallCount, _ := msgService.GetRewardVoiceCallCount(userId, aiRoleInfo.Id)
	appInfo := global.AppMap[appId]
	// 语音通话每分钟价格
	minPrice := appInfo.DetailConfig.Data().MinPrice
	if IsVipSVipUser(userInfo) {
		if isSvip {
			minPrice = appInfo.DetailConfig.Data().MinPriceSVip
		} else if isSub {
			minPrice = appInfo.DetailConfig.Data().MinPriceSub
		}
	} else {
		minPrice = appInfo.DetailConfig.Data().ChatVipMinPrice
	}
	if rewardVoiceCallCount > 0 {
		minPrice = 0
	}
	if userProfileInfo, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	if userProfileInfo.FlowerAmount < minPrice {
		err = errs.VoiceNoFloErr
		return
	}
	err = global.DB.Transaction(func(tx *gorm.DB) (tErr error) {
		// 创建FlowerConsumeRecord记录
		var fRecord = &model.FlowerConsumeRecord{
			DigitalUserId: userId,
			AiId:          aiRoleInfo.Id,
			AiRoleId:      aiRoleInfo.RoleID,
			ConsumeType:   model.ConsumeTypeVoiceCall,
			RelationID:    fmt.Sprintf("%d", reqData.Id),
			OriginAmount:  userProfileInfo.FlowerAmount,
			ConsumeAmount: minPrice,
		}
		if tErr = tx.Model(&model.FlowerConsumeRecord{}).Create(fRecord).Error; tErr != nil {
			return
		}
		go TdPointer(userInfo, userProfileInfo, td.ServerEvent, map[string]interface{}{
			"event_tag":         "coins_consume",
			"coins_consume_num": -minPrice,
			"ai_role_id":        aiRoleInfo.RoleID,
			"ai_role_type":      aiRoleInfo.RoleType,
			"time_long":         voiceCallRecord.CallMinute + 1,
			"source":            "voice_call_consume",
		})
		remainAmount := userProfileInfo.FlowerAmount - minPrice
		updateMap := map[string]interface{}{
			"flower_amount": remainAmount,
		}
		txTran := tx.Model(&model.DigitalUserProfile{}).Where("id = ? AND flower_amount = ?", userProfileInfo.Id, userProfileInfo.FlowerAmount).Updates(updateMap)
		if tErr = txTran.Error; tErr != nil {
			return
		}
		if txTran.RowsAffected == 0 {
			tErr = errs.VoiceNoFloErr
			return
		}
		// 更新通话记录
		voiceCallRecordEditMap := map[string]interface{}{
			"call_minute": gorm.Expr("call_minute + ?", 1),
			"total_price": gorm.Expr("total_price + ?", minPrice),
		}
		if tErr = tx.Model(&model.VoiceCall{}).Where("id = ?", reqData.Id).Updates(voiceCallRecordEditMap).Error; tErr != nil {
			return
		}
		return
	})
	if err != nil {
		return
	}
	// 更新UserAiMsgCount
	if err = msgCountSer.FindUpdate(c, userId, aiRoleInfo, map[string]interface{}{
		"call_minute": gorm.Expr("call_minute + 1"),
	}); err != nil {
		return
	}
	if rewardVoiceCallCount > 0 {
		_ = msgService.RewardVoiceCall(userId, aiRoleInfo.Id, -1)
	}
	return
}

func (m *VoiceCallService) VoiceCallMsg(c *gin.Context, reqData *request.VoiceCallMsgReq) (err error) {
	var (
		aiRoleInfo   *model.AiRole
		userInfo     *model.DigitalUser
		profileInfo  *model.DigitalUserProfile
		userService  DigitalUserService
		profileSer   DigitalUserProfileService
		aiRoleSer    AiRoleService
		msgService   MsgService
		userId       = utils.GetDigitalUserID(c)
		now          = time.Now()
		nowTimestamp = now.UnixMilli()
		userIdStr    = fmt.Sprintf("%d", userId)
	)
	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	if profileInfo, err = profileSer.GetByUserId(userId); err != nil {
		return
	}
	// 获取AI角色信息
	if aiRoleInfo, err = aiRoleSer.GetByRoleId(reqData.AiRoleId); err != nil {
		return
	}
	// 保存用户发送的消息到MongoDB
	userSendMessageId := uuid.NewString()
	userSendMsg := &model.MsgInfo{
		MessageID:      userSendMessageId,
		Channel:        nil,
		SendTime:       nowTimestamp,
		DeletedAt:      nil,
		MessageType:    model.MsgTypeVoiceCall,
		SenderID:       userIdStr,
		SenderName:     userInfo.Nickname,
		ReceiverID:     reqData.AiRoleId,
		ReceiverName:   aiRoleInfo.Nickname,
		MessageContent: model.MessageContent{Text: reqData.Msg},
	}
	if err = msgService.CreateMsgRecord(c, userSendMsg); err != nil {
		return
	}
	// 从空闲队列中删除
	global.REDIS.ZRem(c, global.IDLE_MSG_QUEUE_KEY, fmt.Sprintf("%d_%s", userId, reqData.AiRoleId))
	redisMsgReq := &request.RedisMsgReq{
		UrlType:     msgService.GetMsgUrlType(userInfo, profileInfo),
		SendType:    request.SendTypeVoiceCall,
		UserId:      userId,
		AiRoleId:    reqData.AiRoleId,
		VoiceCallId: reqData.Id,
		UserMsg:     reqData.Msg,
	}
	go msgService.SendAiMsg(c, redisMsgReq)
	return
}

func (m *VoiceCallService) voiceCacheRedisKey(msgContent string, aiRoleInfo *model.AiRole) string {
	cacheFullKey := fmt.Sprintf("%s:%s", msgContent, aiRoleInfo.RoleID)
	cacheFullKeyMd5 := utils.MD5(cacheFullKey)
	return fmt.Sprintf("voice:cache:%s:%s", aiRoleInfo.RoleID, cacheFullKeyMd5)
}

func (m *VoiceCallService) VoiceCache(ctx context.Context, msgContent string, aiRoleInfo *model.AiRole) (s3ObjectKey string, exist bool) {
	cacheFullKeyMd5 := m.voiceCacheRedisKey(msgContent, aiRoleInfo)
	exist = global.REDIS.Exists(ctx, cacheFullKeyMd5).Val() > 0
	if exist {
		s3ObjectKey = global.REDIS.Get(ctx, cacheFullKeyMd5).Val()
	}
	return
}
