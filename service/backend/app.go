package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"aimsg-server/utils"
	"context"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
)

type AppService struct{}

func (m *AppService) CopyAppData(reqData *req.CopyAppDataReq) (err error) {
	var (
		sourceAppInfo    *model.App
		targetAppInfo    *model.App
		appPageBannerSer AppPageBannerService
		badgeSer         BadgeService
		diyConfigSer     DiyConfigService
		imgConfigSer     ImgConfigService
		liveGiftSer      LiveGiftService
		livePeopleSer    LivePeopleService
		productSer       ProductService
		productInfoSer   ProductInfoService
		voiceInfoSer     VoiceInfoService
	)
	if sourceAppInfo, err = m.GetById(reqData.SourceAppId); err != nil {
		return
	}
	if targetAppInfo, err = m.GetById(reqData.TargetAppId); err != nil {
		return
	}
	if err = appPageBannerSer.CopyInfo(sourceAppInfo.Id, targetAppInfo.Id); err != nil {
		err = fmt.Errorf("表:app_page_banner,复制App页面Banner失败: %v", err)
		return
	}
	if err = badgeSer.CopyInfo(sourceAppInfo.Id, targetAppInfo.Id); err != nil {
		err = fmt.Errorf("表:app_page_banner,复制徽章失败: %v", err)
		return
	}
	if err = diyConfigSer.CopyInfo(sourceAppInfo.Id, targetAppInfo.Id); err != nil {
		err = fmt.Errorf("表:app_page_banner,复制DIY配置失败: %v", err)
		return
	}
	if err = imgConfigSer.CopyInfo(sourceAppInfo.Id, targetAppInfo.Id); err != nil {
		err = fmt.Errorf("表:app_page_banner,复制图片配置失败: %v", err)
		return
	}
	if err = liveGiftSer.CopyInfo(sourceAppInfo.Id, targetAppInfo.Id); err != nil {
		err = fmt.Errorf("表:app_page_banner,复制直播礼物失败: %v", err)
		return
	}
	if err = livePeopleSer.CopyInfo(sourceAppInfo.Id, targetAppInfo.Id); err != nil {
		err = fmt.Errorf("表:app_page_banner,复制直播人物失败: %v", err)
		return
	}
	if err = productSer.CopyInfo(sourceAppInfo.Id, targetAppInfo.Id); err != nil {
		err = fmt.Errorf("表:app_page_banner,复制商品失败: %v", err)
		return
	}
	if err = productInfoSer.CopyInfo(sourceAppInfo.Id, targetAppInfo.Id); err != nil {
		err = fmt.Errorf("表:app_page_banner,复制商品信息失败: %v", err)
		return
	}
	if err = voiceInfoSer.CopyInfo(sourceAppInfo.Id, targetAppInfo.Id); err != nil {
		err = fmt.Errorf("表:app_page_banner,复制语音示例失败: %v", err)
		return
	}

	return
}

func (m *AppService) Create(reqData *model.App) (err error) {
	err = global.DB.Model(&model.App{}).Create(reqData).Error
	return
}

func (m *AppService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.App{}).Where("id = ?", id).Delete(&model.App{}).Error
	return
}

func (m *AppService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.App{}).Where("id = ?", reqData.Ids).Delete(&model.App{}).Error
	return
}

func (m *AppService) Update(reqData *model.App) (err error) {
	err = global.DB.Model(&model.App{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	return
}

func (m *AppService) GetById(id uint) (res *model.App, err error) {
	err = global.DB.Model(&model.App{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *AppService) GetAll() (resList []model.App, err error) {
	err = global.DB.Model(&model.App{}).Find(&resList).Error
	return
}

func (m *AppService) SyncAppOtherConfig() {
	ctx := context.Background()
	global.APPOtherCfg = GetAppOtherConfig(ctx)
}

func (m *AppService) SyncAllAiFuncCfg() {
	ctx := context.Background()
	global.AllAiFuncCfgMap = GetAllAiFuncSwitchCfg(ctx)
}

func (m *AppService) SyncAppMap() {
	var (
		err                   error
		resList               []model.App
		appMap                = map[uint]model.App{}
		androidPackageNameMap = map[string]model.App{}
	)
	resList, err = m.GetAll()
	if err != nil {
		global.LOG.Error("SyncAppMap 失败", zap.Error(err))
		return
	}
	for _, app := range resList {
		appMap[app.Id] = app
		if app.AppType == model.AppTypeAndroid {
			androidPackageNameMap[app.PackageName] = app
		}
	}
	global.AppMap = appMap
	global.AndroidPackageNameMap = androidPackageNameMap
}

func (m *AppService) GetList(info req.AppSearch) (resList []*model.App, total int64, err error) {
	db := global.DB.Model(&model.App{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.Name != "" {
		db = db.Where("name LIKE ?", "%"+info.Name+"%")
	}
	if info.PackageName != "" {
		db = db.Where("package_name LIKE ?", "%"+info.PackageName+"%")
	}
	if info.Status != 0 {
		db = db.Where("status = ?", info.Status)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}

func (m *AppService) AppConfig(c *gin.Context) (resObj gin.H) {
	appId := c.GetUint(global.GinContextAppId)
	appInfo, _ := global.AppMap[appId]
	appAuditPlan := m.GetAppAuditPlan(c, appId)
	appSplashAdCfg := GetSplashAdCfg(c, appId)
	appRequireImgSubMsgCfg := GetRequireImgSubMsgCfg(c, appId)
	resObj = gin.H{
		"flo_close_count": appInfo.DetailConfig.Data().FloCloseCount,
		"flo_close_step":  5,
		"file_url_prefix": appInfo.S3ImgPrefix,
		"img_upload_count": gin.H{
			"min": 8,
			"max": 24,
		},
		"agora_id":              appInfo.AgoraData.Data().AppId,
		"user_send_count_max":   appInfo.DetailConfig.Data().FreeMsgCount,
		"splash_ads":            appInfo.DetailConfig.Data().SplashAds,
		"app_audit_plan":        appAuditPlan,
		"start_show_sub_dialog": global.APPOtherCfg.StartShowSubDialog,
		"fb_report_second":      5,
		"require_img_step":      appRequireImgSubMsgCfg.Step,
	}
	resObj["low_dialog_count"] = appSplashAdCfg.LowDialogCount
	return
}

func (m *AppService) BannerList(c *gin.Context) (res gin.H) {
	appId := utils.GetAppID(c)
	return gin.H{
		"list": []string{
			utils.S3Url("public/banner/a6a6af53-9632-4bc0-8933-f2ebbe0d6e71.png", appId),
			utils.S3Url("public/banner/f6a52a03-7355-4d27-b8bd-a0dfc9fa0763.png", appId),
			utils.S3Url("public/banner/fbf5b135-a7be-475c-bbe9-e2286f0738a4.png", appId),
		},
	}
}

func (m *AppService) ImgDemoList(c *gin.Context) (res gin.H) {
	appId := utils.GetAppID(c)
	return gin.H{
		"list": []gin.H{
			{"img_url": utils.S3Url("public/other/338409c5-85f0-402f-9b8f-55ba0f20f297.png", appId), "status": 1, "tips": "Clear features"},
			{"img_url": utils.S3Url("public/other/7fa36395-c68b-4ddf-93aa-ccf7624f4637.png", appId), "status": 2, "tips": "Not positive"},
			{"img_url": utils.S3Url("public/other/2a4928c5-65aa-49b2-a3e6-1929bc040beb.png", appId), "status": 3, "tips": "Unobstructible"},
		},
	}
}

func (m *AppService) IconConfig(c *gin.Context) (resObj gin.H) {
	appId := utils.GetAppID(c)
	resObj = gin.H{
		"vip_icon": gin.H{
			"role_list": utils.S3Url("public/icon/vip_pro.png", appId),
			"chat_list": utils.S3Url("public/icon/vip_pro.png", appId),
		},
	}
	return
}

func (m *AppService) SetAppAuditPlan(c *gin.Context, appId uint, appAuditPlan int) (err error) {
	rKey := fmt.Sprintf(global.APP_AUDIT_PLAN_KEY, appId)
	err = global.REDIS.Set(c, rKey, appAuditPlan, 0).Err()
	return
}

func (m *AppService) GetAppAuditPlan(c *gin.Context, appId uint) (plan int) {
	rKey := fmt.Sprintf(global.APP_AUDIT_PLAN_KEY, appId)
	appAuditPlan, err := global.REDIS.Get(c, rKey).Int()
	if err != nil {
		return 2
	}
	return appAuditPlan
}

func (m *AppService) BatchById(uids []uint) (resp map[uint]model.App, err error) {
	if len(uids) == 0 {
		return nil, nil
	}
	var list []model.App
	err = global.DB.Model(&model.App{}).Where("id IN (?)", uids).Find(&list).Error
	if err != nil {
		return
	}
	resp = map[uint]model.App{}
	for _, v := range list {
		resp[v.Id] = v
	}
	return
}
