package backend

import (
	"aimsg-server/global"
	"context"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
	"time"
)

const (
	CacheGetProductToday = "ai:btc:GetProductToday3:user:%d"
)

// 获取当前用户今天有效期内购买的产品列表
func (m *BtcService) GetProductToday(ctx context.Context, userId uint) (resp []*model.BtcProductSubscription, err error) {
	now := time.Now()
	midnight := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	cacheKey := fmt.Sprintf(CacheGetProductToday, userId)
	jsonStr, err := global.REDIS.Get(ctx, cacheKey).Result()
	if err != nil && err != redis.Nil {
		return resp, err
	}
	if err != redis.Nil {
		list := []*model.BtcProductSubscription{}
		_ = json.Unmarshal([]byte(jsonStr), &list)
		for _, item := range list {
			if item.ProductType != ProductTypeShop && item.EndTime.Before(now) {
				continue
			}
			resp = append(resp, item)
		}
		return resp, nil
	}

	nextDay := now.Add(+time.Hour * 24)
	nextDayMidnight := time.Date(nextDay.Year(), nextDay.Month(), nextDay.Day(), 0, 0, 0, 0, nextDay.Location())
	err = global.DB.Where("user_id = ?", userId).
		Where("(start_time < ? and end_time > ? and product_type!=0) or (start_time < ? and product_type=0)", nextDayMidnight.Format("2006-01-02 15:04:05"), midnight.Format("2006-01-02 15:04:05"), nextDayMidnight.Format("2006-01-02 15:04:05")).
		Find(&resp).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return resp, err
	}
	jsonB, _ := json.Marshal(resp)
	global.REDIS.Set(ctx, cacheKey, jsonB, time.Hour*24)
	return resp, nil
}
