package backend

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/utils"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
)

func (m *DigitalUserProfileService) SetNSFW(c *gin.Context, reqData *request.SetNSFWReq) (err error) {
	var (
		userInfo *model.DigitalUser
		userSer  DigitalUserService
		userId   = utils.GetDigitalUserID(c)
		// appVersion = c.GetHeader("App-Version")
	)
	// 获取用户信息 才桥 0220 需求
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		return
	}
	// 需求地址 https://shimo.im/docs/B1Awd4W7m8FKN3m8/ 《persona.AI双订阅权益优化2.0》，可复制链接后用石墨文档 App 打开
	// 免费用户阶段，nsfw开关可打开
	// verCompare331 := utils.CompareVersions(appVersion, "3.3.1")
	isSub := userInfo.IsSub()
	isChatVip := userInfo.IsChatVip()
	isContentVip := userInfo.IsContentVip()
	if IsVipSVipUser(userInfo) {
		if !isSub && reqData.Nsfw {
			return errs.RequireSubErr
		}
	} else {
		if !isContentVip && !isChatVip && reqData.Nsfw {
			err = errs.ChatVipErr
			return
		}
	}
	editMap := map[string]interface{}{
		"nsfw": reqData.Nsfw,
	}
	if err = m.UpdateByMap(c, userId, editMap); err != nil {
		return err
	}
	return
}

func (m *DigitalUserProfileService) AddWatchAdMsgCount(c *gin.Context, data *request.AiRoleIdReq) (err error) {
	var (
		userId     = utils.GetDigitalUserID(c)
		msgService MsgService
	)
	addCount := global.APPOtherCfg.WatchAdMsgCount
	err = msgService.IncrWatchAdMsgCount(c, userId, addCount)
	return
}
