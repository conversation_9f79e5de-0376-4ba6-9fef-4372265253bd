package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
)

type MsgJobService struct{}

func (m *MsgJobService) Create(reqData *model.MsgJob) (err error) {
	err = global.DB.Model(&model.MsgJob{}).Create(reqData).Error
	m.SyncMsgJobMap()
	return
}

func (m *MsgJobService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.MsgJob{}).Where("id = ?", id).Delete(&model.MsgJob{}).Error
	m.SyncMsgJobMap()
	return
}

func (m *MsgJobService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.MsgJob{}).Where("id IN (?)", reqData.Ids).Delete(&model.MsgJob{}).Error
	m.SyncMsgJobMap()
	return
}

func (m *MsgJobService) Update(reqData *model.MsgJob) (err error) {
	err = global.DB.Model(&model.MsgJob{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	m.SyncMsgJobMap()
	return
}

func (m *MsgJobService) GetById(id uint) (res *model.MsgJob, err error) {
	err = global.DB.Model(&model.MsgJob{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *MsgJobService) GetAll() (resList []model.MsgJob, err error) {
	err = global.DB.Model(&model.MsgJob{}).Find(&resList).Error
	return
}

func (m *MsgJobService) SyncMsgJobMap() {
	var (
		err     error
		resList []model.MsgJob
		resMap  = map[uint]model.MsgJob{}
	)
	if resList, err = m.GetAll(); err != nil {
		global.LOG.Error("SyncMsgJobMap 失败", zap.Error(err))
		return
	}
	for _, app := range resList {
		resMap[app.Id] = app
	}
	global.MsgJobMap = resMap
}

func (m *MsgJobService) GetList(info req.MsgJobSearch) (resList []*model.MsgJob, total int64, err error) {
	db := global.DB.Model(&model.MsgJob{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.Status != 0 {
		db = db.Where("status = ?", info.Status)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
