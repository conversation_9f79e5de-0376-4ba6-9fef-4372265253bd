package backend

//
// import (
// 	"aimsg-server/model/cli/response"
// 	"context"
// 	"git.costnovel.com/cashbox-ai/ai-par-model/model"
// )
//
// func AiFuncShow(ctx context.Context, userId uint, aiRoleId string) (funcShow response.AiFuncShowRes, err error) {
// 	var (
// 		intimateInfo    *model.UsersIntimate
// 		userProfileInfo *model.DigitalUserProfile
// 		userInfo        *model.DigitalUser
// 		aiInfo          *model.AiRole
// 		aiSer           AiRoleService
// 		userSer         DigitalUserService
// 		msgSer          MsgService
// 		userIntimateSer UsersIntimateService
// 		profileSer      DigitalUserProfileService
// 	)
// 	if userInfo, err = userSer.GetById(ctx, userId); err != nil {
// 		return
// 	}
// 	appId := userInfo.AppID
// 	isSub := userInfo.IsSub()
// 	isSVip := userInfo.IsSVip()
// 	isAudit := userInfo.IsAudit()
// 	if userProfileInfo, err = profileSer.GetByUserId(userId); err != nil {
// 		return
// 	}
// 	if aiInfo, err = aiSer.GetByRoleId(aiRoleId); err != nil {
// 		return
// 	}
// 	if intimateInfo, err = userIntimateSer.GetByUidAndRoleid(userId, aiInfo.Id); err != nil {
// 		return
// 	}
// 	sendTotal := msgSer.GetUserSendMsgTotal(ctx, userInfo.Id, aiInfo.RoleID)
//
// 	return
// }
