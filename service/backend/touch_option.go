package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"aimsg-server/utils"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
)

type TouchOptionService struct{}

func (m *TouchOptionService) SyncTouchOptionList() {
	var (
		err              error
		resList          []*model.TouchOption
		newResList       []*model.TouchOption
		ordersOptionList []*model.TouchOption
	)
	resList, err = m.GetAll()
	if err != nil {
		global.LOG.Error("SyncTouchOptionList 失败", zap.Error(err))
		return
	}
	for _, option := range resList {
		if option.Icon != "" {
			option.Icon = utils.S3Url(option.Icon, 0)
		}
		if option.OptionType == model.OptionTypeAction {
			newResList = append(newResList, option)
		} else if option.OptionType == model.OptionTypeOrders {
			ordersOptionList = append(ordersOptionList, option)
		}
	}
	global.TouchOptionList = newResList
	global.OrdersOptionList = ordersOptionList
}

func (m *TouchOptionService) Create(reqData *model.TouchOption) (err error) {
	err = global.DB.Model(&model.TouchOption{}).Create(reqData).Error
	m.SyncTouchOptionList()
	return
}

func (m *TouchOptionService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.TouchOption{}).Where("id = ?", id).Delete(&model.TouchOption{}).Error
	m.SyncTouchOptionList()
	return
}

func (m *TouchOptionService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.TouchOption{}).Where("id IN (?)", reqData.Ids).Delete(&model.TouchOption{}).Error
	m.SyncTouchOptionList()
	return
}

func (m *TouchOptionService) Update(reqData *model.TouchOption) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.TouchOption{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	m.SyncTouchOptionList()
	return
}

func (m *TouchOptionService) GetById(id uint) (res *model.TouchOption, err error) {
	err = global.DB.Model(&model.TouchOption{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *TouchOptionService) GetAll() (resList []*model.TouchOption, err error) {
	err = global.DB.Model(&model.TouchOption{}).Order("sort DESC").Find(&resList).Error
	return
}

func (m *TouchOptionService) GetList(info req.TouchOptionSearch) (resList []*model.TouchOption, total int64, err error) {
	db := global.DB.Model(&model.TouchOption{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.Name != "" {
		db = db.Where("name LIKE ?", "%"+info.Name+"%")
	}
	if info.TouchType != 0 {
		db = db.Where("touch_type = ?", info.TouchType)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("sort DESC")
	}
	err = db.Find(&resList).Error
	return
}
