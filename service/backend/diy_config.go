package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/model/common/request"
	"aimsg-server/utils"
	"context"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
	"runtime"
	"time"
)

type DiyConfigService struct{}

func (m *DiyConfigService) CopyInfo(sourceAppId, targetAppId uint) (err error) {
	var (
		sourceInfos []*model.DiyConfig
		newInfos    []*model.DiyConfig
		nowTime     = time.Now()
	)
	sourceInfos, err = m.FindByAppId(sourceAppId)
	if err != nil {
		return
	}
	for _, sourceInfo := range sourceInfos {
		sourceInfo.Id = 0
		sourceInfo.CreatedAt = nowTime
		sourceInfo.UpdatedAt = nowTime
		sourceInfo.AppID = targetAppId
		newInfos = append(newInfos, sourceInfo)
	}
	if len(newInfos) == 0 {
		return
	}
	err = global.DB.Model(&model.DiyConfig{}).Create(&newInfos).Error
	return
}

func (m *DiyConfigService) FindByAppId(appId uint) (resList []*model.DiyConfig, err error) {
	err = global.DB.Model(&model.DiyConfig{}).Where("app_id = ?", appId).Find(&resList).Error
	return
}

func (m *DiyConfigService) Create(ctx context.Context, reqData *model.DiyConfig) (err error) {
	if reqData.MaskPrefix != "" {
		var s3V2Client = GetS3ClientV2(ctx)
		if reqData.MaskCount, err = s3V2Client.PathObjectsCount(reqData.MaskPrefix); err != nil {
			global.LOG.Error("PathObjectsCount 失败", zap.Error(err))
			return
		}
	}
	var (
		createItems []*model.DiyConfig
	)
	if reqData.AppID == 0 {
		for appId := range global.AppMap {
			createItems = append(createItems, &model.DiyConfig{
				AppID:         appId,
				Name:          reqData.Name,
				Price:         reqData.Price,
				Level:         reqData.Level,
				DiyConfigType: reqData.DiyConfigType,
				NoSwap:        reqData.NoSwap,
				Img:           reqData.Img,
				MaskPrefix:    reqData.MaskPrefix,
				MaskCount:     reqData.MaskCount,
				Sort:          reqData.Sort,
			})
		}
	} else {
		createItems = append(createItems, reqData)
	}
	err = global.DB.Model(&model.DiyConfig{}).Create(createItems).Error
	go m.SyncAll()
	return
}

func (m *DiyConfigService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.DiyConfig{}).Where("id = ?", id).Delete(&model.DiyConfig{}).Error
	go m.SyncAll()
	return
}

func (m *DiyConfigService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.DiyConfig{}).Where("id IN (?)", reqData.Ids).Delete(&model.DiyConfig{}).Error
	go m.SyncAll()
	return
}

func (m *DiyConfigService) Update(ctx context.Context, reqData *model.DiyConfig) (err error) {
	if reqData.MaskPrefix != "" {
		var s3V2Client = GetS3ClientV2(ctx)
		if reqData.MaskCount, err = s3V2Client.PathObjectsCount(reqData.MaskPrefix); err != nil {
			global.LOG.Error("PathObjectsCount 失败", zap.Error(err))
			return
		}
	}
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.DiyConfig{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	go m.SyncAll()
	return
}

func (m *DiyConfigService) UpdateByIdMap(recId uint, updateMap map[string]interface{}) (err error) {
	err = global.DB.Model(&model.DiyConfig{}).Where("id = ?", recId).Updates(updateMap).Error
	// m.SyncAll()
	return
}

func (m *DiyConfigService) UpdateByMaskPrefix(maskPrefix string, updateMap map[string]interface{}) (err error) {
	err = global.DB.Model(&model.DiyConfig{}).Where("mask_prefix = ?", maskPrefix).Updates(updateMap).Error
	return
}

func (m *DiyConfigService) GetById(id uint) (res *model.DiyConfig, err error) {
	err = global.DB.Model(&model.DiyConfig{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *DiyConfigService) GetAll() (resList []model.DiyConfig, err error) {
	err = global.DB.Model(&model.DiyConfig{}).Order("sort DESC, id DESC").Find(&resList).Error
	return
}

func (m *DiyConfigService) SyncAll() {
	var (
		err            error
		resList        []model.DiyConfig
		ctx            = context.Background()
		appResMap      = map[uint][]model.DiyConfig{}
		diyConfigMap   = map[uint]model.DiyConfig{}
		maskImgItemMap = map[string]response.MaskImgItem{}
		s3ClientV2     = GetS3ClientV2(ctx)
	)
	defer func() {
		if rErr := recover(); rErr != nil {
			global.LOG.Error("DiyConfigService SyncAll panic", zap.Any("rErr", err))
		}
	}()
	resList, err = m.GetAll()
	if err != nil {
		global.LOG.Error("SyncAll 失败", zap.Error(err))
		return
	}
	for _, r := range resList {
		r.Img = utils.S3Url(r.Img, r.AppID)
		appResMap[r.AppID] = append(appResMap[r.AppID], r)
		diyConfigMap[r.Id] = r
		if runtime.GOOS != "darwin" {
			maskImgItem, maskImgItemExist := maskImgItemMap[r.MaskPrefix]
			if !maskImgItemExist && r.MaskPrefix != "" {
				var maskFiles []string
				// 获取路径下的所有图片
				if maskFiles, err = s3ClientV2.ListFilesInS3Path(r.MaskPrefix); err != nil {
					global.LOG.Error("ListFilesInS3Path 失败", zap.String("MaskPrefix", r.MaskPrefix), zap.Error(err))
					continue
				}
				maskImgItem = response.MaskImgItem{
					MaskImgs:  maskFiles,
					MaskCount: len(maskFiles),
				}
				if updateErr := m.UpdateByMaskPrefix(r.MaskPrefix, map[string]interface{}{"mask_count": maskImgItem.MaskCount}); updateErr != nil {
					global.LOG.Error("更新MaskCount失败", zap.Error(updateErr))
				}
				maskImgItemMap[r.MaskPrefix] = maskImgItem
			}
		}
	}
	global.DiyConfigMaskMap = maskImgItemMap
	global.AppDiyConfigMap = appResMap
	global.DiyConfigMap = diyConfigMap
}

func (m *DiyConfigService) GetList(info req.DiyConfigSearch) (resList []*model.DiyConfig, total int64, err error) {
	db := global.DB.Model(&model.DiyConfig{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.DiyConfigType != 0 {
		db = db.Where("diy_config_type = ?", info.DiyConfigType)
	}
	if info.Level != 0 {
		db = db.Where("level = ?", info.Level)
	}
	if info.Name != "" {
		db = db.Where("name LIKE ?", "%"+info.Name+"%")
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
