package backend

import (
	"aimsg-server/global"
	"context"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/shopspring/decimal"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"time"
)

func (m *OrdersService) TmpStaOne() (err error) {
	type TmpOne struct {
		AiRoleId    uint             `json:"ai_role_id"`
		RoleId      string           `json:"role_id"`
		RoleType    model.AiRoleType `json:"role_type"`
		RoleTypeStr string           `json:"role_type_str"`
		PayAmount   decimal.Decimal  `json:"pay_amount"`
		ChatCount   int64            `json:"chat_count"`
	}
	type TmpTwo struct {
		ReceiverId         string `json:"receiver_id" bson:"receiver_id"`
		UniqueSendersCount int64  `json:"uniqueSendersCount" bson:"uniqueSendersCount"`
	}
	var (
		resList      []*TmpOne
		chatList     []TmpTwo
		countMap     = map[string]int64{}
		ctx          = context.Background()
		startTimeStr = "2024-07-16 00:00:00"
		endTimeStr   = "2024-08-16 00:00:00"
	)
	startTime, _ := time.Parse(time.DateTime, startTimeStr)
	endTime, _ := time.Parse(time.DateTime, endTimeStr)

	err = global.DB.Model(&model.Order{}).Raw("SELECT o.ai_role_id, a.role_id, a.role_type, CASE a.role_type WHEN 1 THEN 'Discover' WHEN 2 THEN 'Private' WHEN 3 THEN 'Influencers' WHEN 4 THEN 'Anime' WHEN 5 THEN 'Fans' ELSE 'Unknown' END AS role_type, SUM(o.pay_amount) AS pay_amount FROM orders o LEFT JOIN ai_role a ON o.ai_role_id = a.id WHERE o.deleted_at IS NULL AND o.created_at BETWEEN ? AND ? AND o.order_status = 201 AND o.ai_role_id != 0 GROUP BY o.ai_role_id, a.role_id, a.role_type ORDER BY pay_amount DESC;", startTime, endTime).Find(&resList).Error
	if err != nil {
		global.LOG.Error("Find error", zap.Error(err))
		return
	}
	pipeline := mongo.Pipeline{
		{{"$match", bson.D{
			{"send_time", bson.D{
				{"$gte", startTime.UnixMilli()},
				{"$lt", endTime.UnixMilli()},
			}},
		}}},
		{{"$group", bson.D{
			{"_id", "$receiver_id"},
			{"uniqueSenders", bson.D{{"$addToSet", "$sender_id"}}},
		}}},
		{{"$project", bson.D{
			{"_id", 0},
			{"receiver_id", "$_id"},
			{"uniqueSendersCount", bson.D{{"$size", "$uniqueSenders"}}},
		}}},
	}
	if err = global.MONGO.Aggregate(ctx, pipeline).All(&chatList); err != nil {
		global.LOG.Error("Aggregate error", zap.Error(err))
		return
	}
	for _, c := range chatList {
		countMap[c.ReceiverId] = c.UniqueSendersCount
	}
	for _, tOne := range resList {
		tOne.ChatCount = countMap[tOne.RoleId]
	}
	file := excelize.NewFile()
	sheetName := "Data"
	index, err := file.NewSheet(sheetName)
	if err != nil {
		global.LOG.Error("Unable to create a new sheet", zap.Error(err))
		return
	}
	headers := []string{"AiId", "RoleId", "PayAmount", "RoleType", "ChatCount"}
	for i, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(i+1, 1)
		file.SetCellValue(sheetName, cell, header)
	}
	for i, record := range resList {
		row := i + 2
		file.SetCellValue(sheetName, fmt.Sprintf("A%d", row), record.AiRoleId)
		file.SetCellValue(sheetName, fmt.Sprintf("B%d", row), record.RoleId)
		file.SetCellValue(sheetName, fmt.Sprintf("C%d", row), record.PayAmount.String())
		file.SetCellValue(sheetName, fmt.Sprintf("D%d", row), record.RoleTypeStr)
		file.SetCellValue(sheetName, fmt.Sprintf("E%d", row), record.ChatCount)
	}
	file.SetActiveSheet(index)
	fileName := fmt.Sprintf("output_%s_%s.xlsx", startTime.Format("20060102"), endTime.Format("20060102"))
	if err = file.SaveAs(fileName); err != nil {
		global.LOG.Error("Unable to save Excel file", zap.Error(err))
		return
	}
	global.LOG.Info("Excel file generated successfully", zap.String("fileName", fileName))
	return
}
