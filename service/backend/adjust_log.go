package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"aimsg-server/utils"
	"context"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
)

type AdjustLogService struct{}

func (m *AdjustLogService) Create(reqData *model.AdjustLog) (err error) {
	err = global.DB.Model(&model.AdjustLog{}).Create(reqData).Error
	return
}

func (m *AdjustLogService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.AdjustLog{}).Where("id = ?", id).Delete(&model.AdjustLog{}).Error
	return
}

func (m *AdjustLogService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.AdjustLog{}).Where("id IN (?)", reqData.Ids).Delete(&model.AdjustLog{}).Error
	return
}

func (m *AdjustLogService) Update(reqData *model.AdjustLog) (err error) {
	err = global.DB.Model(&model.AdjustLog{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	return
}

func (m *AdjustLogService) GetById(id uint) (res *model.AdjustLog, err error) {
	err = global.DB.Model(&model.AdjustLog{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *AdjustLogService) GetAll() (resList []*model.AdjustLog, err error) {
	err = global.DB.Model(&model.AdjustLog{}).Find(&resList).Error
	return
}

func (m *AdjustLogService) FixAdjust() (err error) {
	var (
		resList []*model.AdjustLog
		ctx     = context.Background()
	)
	db := global.DB.Model(&model.AdjustLog{})
	db = db.Where("created_at > '2024-07-09 04:57:17.348000' AND res_status != 200")
	if err = db.Find(&resList).Error; err != nil {
		global.LOG.Error("获取adjust日志失败", zap.Error(err))
		return
	}
	for _, aRec := range resList {
		var (
			apiLog   string
			curlStr  string
			userInfo *model.DigitalUser
			userSer  DigitalUserService
			reqData  = aRec.ReqData
		)
		// 获取用户信息
		if userInfo, err = userSer.GetById(ctx, aRec.DigitalUserID); err != nil {
			global.LOG.Error("获取用户信息失败", zap.Error(err))
			continue
		}
		if userInfo.AdjustIdData.Data().Idfa != "" {
			reqData["idfa"] = userInfo.AdjustIdData.Data().Idfa
		}
		if userInfo.AdjustIdData.Data().Idfv != "" {
			reqData["idfv"] = userInfo.AdjustIdData.Data().Idfv
		}
		if userInfo.AdjustIdData.Data().Adid != "" {
			reqData["adid"] = userInfo.AdjustIdData.Data().Adid
		}
		if userInfo.AdjustIdData.Data().GpsAdid != "" {
			reqData["gps_adid"] = userInfo.AdjustIdData.Data().GpsAdid
		}
		// 发送adjust日志
		if apiLog, curlStr, err = utils.HttpPostForm(global.AdjustUrl, reqData, nil, 10); err != nil {
			global.LOG.Error("发送adjust日志失败", zap.Error(err), zap.String("apiLog", apiLog), zap.String("curlStr", curlStr))
			aRec.ResStatus = 400
		} else {
			aRec.ResStatus = 200
		}
		aRec.ResContent = apiLog
		if err = m.Update(aRec); err != nil {
			global.LOG.Error("更新adjust日志失败", zap.Error(err))
			continue
		}
	}
	global.LOG.Info("修复adjust日志完成")
	return
}

func (m *AdjustLogService) GetList(info req.AdjustLogSearch) (resList []*model.AdjustLog, total int64, err error) {
	db := global.DB.Model(&model.AdjustLog{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.ResStatus != 0 {
		db = db.Where("res_status = ?", info.ResStatus)
	}
	if info.OrderNo != "" {
		db = db.Where("order_no = ?", info.OrderNo)
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.DigitalUserID != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserID)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
