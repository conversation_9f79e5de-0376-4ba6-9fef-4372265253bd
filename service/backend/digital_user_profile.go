package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"errors"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type DigitalUserProfileService struct{}

func (m *DigitalUserProfileService) Create(reqData *model.DigitalUserProfile) (err error) {
	err = global.DB.Model(&model.DigitalUserProfile{}).Create(reqData).Error
	return
}

func (m *DigitalUserProfileService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.DigitalUserProfile{}).Where("id = ?", id).Delete(&model.DigitalUserProfile{}).Error
	return
}

func (m *DigitalUserProfileService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.DigitalUserProfile{}).Where("id IN (?)", reqData.Ids).Delete(&model.DigitalUserProfile{}).Error
	return
}

func (m *DigitalUserProfileService) Update(reqData *model.DigitalUserProfile) (err error) {
	err = global.DB.Model(&model.DigitalUserProfile{}).Where("id = ?", reqData.Id).Updates(&reqData).Error
	return
}

func (m *DigitalUserProfileService) UpdateByMap(ctx context.Context, userId uint, updateMap map[string]interface{}) (err error) {
	err = global.DB.Model(&model.DigitalUserProfile{}).Where("digital_user_id = ?", userId).Updates(updateMap).Error
	return
}

func (m *DigitalUserProfileService) GetById(id uint) (res *model.DigitalUserProfile, err error) {
	err = global.DB.Model(&model.DigitalUserProfile{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *DigitalUserProfileService) GetByUserId(userId uint) (res *model.DigitalUserProfile, err error) {
	if err = global.DB.Model(&model.DigitalUserProfile{}).Where("digital_user_id = ?", userId).First(&res).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 创建
			res = &model.DigitalUserProfile{
				DigitalUserId: userId,
			}
			if err = m.Create(res); err != nil {
				return
			}
		} else {
			return
		}
	}
	return
}

func (m *DigitalUserProfileService) GetAll() (resList []*model.DigitalUserProfile, err error) {
	err = global.DB.Model(&model.DigitalUserProfile{}).Find(&resList).Error
	return
}

func (m *DigitalUserProfileService) GetList(info req.DigitalUserProfileSearch) (resList []*model.DigitalUserProfile, total int64, err error) {
	db := global.DB.Model(&model.DigitalUserProfile{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
