package backend

import (
	"aimsg-server/global"
	"aimsg-server/utils"
	"context"
	"encoding/json"
	"fmt"
)

func IpCacheKey(ip string) (rdsKey string) {
	return fmt.Sprintf("ai-server:ip:%s", ip)
}

func GetIpInfoWithCache(ip string) (ipInfo utils.IpGeoRes, err error) {
	var (
		ctx    = context.Background()
		rdsKey = IpCacheKey(ip)
	)
	// 从缓存中获取
	cacheStr, rErr := global.REDIS.Get(ctx, rdsKey).Result()
	if rErr == nil && cacheStr != "" {
		// 解析缓存, 如果解析成功, 直接返回
		if jsonErr := json.Unmarshal([]byte(cacheStr), &ipInfo); jsonErr == nil {
			return
		}
	}
	// 从接口获取
	if ipInfo, err = utils.GetIpInfoByIpGeoApi(ip); err != nil {
		// 钉钉通知
		ddNotifyMsg := fmt.Sprintf(`## 中台IP查询接口失败
- IP地址: %s
- [中台查询: %s](https://game-geo.51payment.live/geo/ip/query?ip=%s)
- [IP-API查询: %s](https://ip-api.com/#%s)
- [ipinfo.io查询: %s](https://ipinfo.io/widget/demo/%s)
- [ipinfo.info查询: %s](https://ipinfo.info/ip_api.php?ip=%s)
- [iplocation.net查询: %s](https://api.iplocation.net/?ip=%s)
`,
			ip,
			ip, ip,
			ip, ip,
			ip, ip,
			ip, ip,
			ip, ip,
		)
		SendDDNotice(ddNotifyMsg)
		return
	}
	return
}
