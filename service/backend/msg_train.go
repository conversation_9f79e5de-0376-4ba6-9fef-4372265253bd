package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/utils"
	"aimsg-server/utils/alarm"
	"context"
	"errors"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/google/uuid"
	"github.com/qiniu/qmgo"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
	"math/rand"
	"strings"
	"time"
)

func (m *MsgService) getAiReplyMsg(ctx context.Context, msgApi model.MsgJob, userSendMsg string, aiRoleInfo *model.AiRole, userInfo *model.DigitalUser, sendMsgReq *request.RedisMsgReq, userNsfw bool) (aiReplyMsg string, err error) {
	msgApiUrl := msgApi.ApiUrl
	newStart := m.MsgTrainNewStart()
	if sendMsgReq.SendType == request.SendTypeUserQuickReply {
		if newStart {
			return m.getUserQuickReplyMsgV3(ctx, msgApi, userSendMsg, aiRoleInfo, userInfo, sendMsgReq)
		}
		if strings.HasSuffix(msgApiUrl, "v1/chat/completions") {
			return m.getUserQuickReplyMsgV2(ctx, msgApi, userSendMsg, aiRoleInfo, userInfo, sendMsgReq)
		} else {
			return m.getUserQuickReplyMsgMy(ctx, msgApi, userSendMsg, aiRoleInfo, userInfo, sendMsgReq)
		}

	} else {
		// 如果策略开,并且用户是1组的,走minimax接口,否则走原来的逻辑 去掉ab 测试 whw 2025-02-07 军锋
		if newStart {
			return m.getReplyMsgMiniMax(ctx, msgApi, userSendMsg, aiRoleInfo, userInfo, sendMsgReq)
		} else {
			if strings.HasSuffix(msgApiUrl, "v1/chat/completions") {
				return m.getReplyMsgV2(ctx, msgApi, userSendMsg, aiRoleInfo, userInfo, sendMsgReq)
			} else {
				return m.getReplyMsgMy(ctx, msgApi, userSendMsg, aiRoleInfo, userInfo, sendMsgReq)
			}
		}
	}

}

func (m *MsgService) GetMsgHistoryV2(ctx context.Context, userIdStr, roleIdStr string) (msgHis []request.MsgItemV2, err error) {
	var (
		msgInfos   []model.MsgInfo
		msgIdsMaps []bson.M
		msgIds     []string
		limitNum   = 10
		userIDs    = []string{userIdStr, roleIdStr}
		pipeline   = qmgo.Pipeline{
			bson.D{
				{"$match", bson.D{
					{"sender_id", bson.D{{"$in", userIDs}}},
					{"receiver_id", bson.D{{"$in", userIDs}}},
					{"deleted_at", nil},
					{"message_content.text", bson.D{{"$ne", nil}}},
				}},
			},
			bson.D{
				{"$group", bson.D{
					{"_id", "$message_id"},
					{"count", bson.D{{"$sum", 1}}},
					{"latest_send_time", bson.D{{"$max", "$send_time"}}},
				}},
			},
			bson.D{{"$match", bson.D{{"count", 2}}}},
			bson.D{{"$sort", bson.D{{"latest_send_time", 1}}}},
			bson.D{{"$limit", limitNum}},
			bson.D{{"$project", bson.D{{"message_id", "$_id"}, {"_id", 0}}}},
		}
	)
	startTime := time.Now()
	if err = global.MONGO.Aggregate(ctx, pipeline).All(&msgIdsMaps); err != nil {
		return
	}
	global.LOG.Info(" api响应速度1", zap.Any("time", time.Since(startTime).Seconds()))

	// 未聊过天
	if len(msgIdsMaps) == 0 {
		return
	}
	for _, idsMap := range msgIdsMaps {
		msgIds = append(msgIds, idsMap["message_id"].(string))
	}
	startTime = time.Now()
	// 查询消息startTime
	if err = global.MONGO.Find(ctx, bson.M{"message_id": bson.M{"$in": msgIds}}).Sort("send_time").All(&msgInfos); err != nil {
		return
	}
	global.LOG.Info(" api响应速度2", zap.Any("time", time.Since(startTime).Seconds()))
	var msgPair []string
	for _, msgInfo := range msgInfos {
		msgInfoText := msgInfo.MessageContent.OriginText
		if msgInfoText == "" {
			msgInfoText = msgInfo.MessageContent.Text
		}
		// 用户发送的消息添加到第一个,主播发送的添加到第二个
		if msgInfo.SenderID == userIdStr {
			msgPair = append([]string{msgInfoText}, msgPair...)
		} else {
			msgPair = append(msgPair, msgInfoText)
		}
		if len(msgPair) == 2 {
			msgItemPair := []request.MsgItemV2{
				{Role: "user", Content: msgPair[0]},
				{Role: "assistant", Content: msgPair[1]},
			}
			msgHis = append(msgHis, msgItemPair...)
			msgPair = []string{}
		}
	}
	return
}

func (m *MsgService) getReplyMsgV2(ctx context.Context, msgApi model.MsgJob, userSendMsg string, aiRoleInfo *model.AiRole, userInfo *model.DigitalUser, sendMsgReq *request.RedisMsgReq) (aiReplyMsg string, err error) {
	var (
		apiLog    string
		curlStr   string
		apiRes    response.AiMsgResV2
		msgHis    []request.MsgItemV2
		firstMsg  = aiRoleInfo.FirstMsg
		userIdStr = fmt.Sprintf("%d", userInfo.Id)
	)

	msgTimeoutConfig := GetMsgSendTimeOutConfig(ctx)
	msgModelStrageCfg := GetMsgModelStrageCfg(ctx)
	if msgHis, err = m.GetMsgHistoryV2(ctx, userIdStr, aiRoleInfo.RoleID); err != nil {
		return
	}
	messages := []request.MsgItemV2{
		{Role: "user", Content: "<|BEGIN-VISIBLE-CHAT|>"},
		{Role: "assistant", Content: firstMsg},
	}
	if len(msgHis) > 0 {
		messages = append(messages, msgHis...)
	}
	// 添加用户说的话
	messages = append(messages, request.MsgItemV2{Role: "user", Content: userSendMsg})

	reqData := aiRoleInfo.ReqData
	if msgModelStrageCfg.Open && aiRoleInfo.RoleType != model.AiRoleTypePrivate && userInfo.UserGroup == 2 {
		reqData = aiRoleInfo.ReqDataTwo
	}
	reqData["user_name"] = userInfo.Nickname
	reqData["bot_name"] = aiRoleInfo.Nickname
	reqData["messages"] = messages
	reqData["greeting"] = firstMsg

	if sendMsgReq.SendType == request.SendTypeVoiceCall {
		msgReqCfg := GetMsgReqMapCfg(ctx)
		reqData["max_new_tokens"] = msgReqCfg.MaxNewTokens
		reqData["temperature"] = msgReqCfg.Temperature
		reqData["top_p"] = msgReqCfg.TopP
		reqData["top_k"] = msgReqCfg.TopK
		reqData["repetition_penalty"] = msgReqCfg.RepetitionPenalty
	}

	reqStart := time.Now()
	// 请求AI接口
	if apiLog, curlStr, err = utils.HttpPostJson(msgApi.ApiUrl, reqData, &apiRes, msgTimeoutConfig.Seconds); err != nil {
		global.LOG.Error("getReplyMsgV2 error", zap.Any("err", err), zap.Any("apiRes", apiRes), zap.Any("apiLog", apiLog), zap.Any("curlStr", curlStr))
		return
	}
	global.LOG.Info("getReplyMsgV2", zap.String("apiLog", apiLog), zap.Any("apiRes", apiRes))

	if len(apiRes.Choices) == 0 {
		err = errors.New("apiRes.Results is empty, apiLog: " + apiLog)
		return
	}
	aiReplyInfo := apiRes.Choices[0]
	aiReplyMsg = aiReplyInfo.Message.Content
	reqSeconds := int(time.Now().Sub(reqStart).Seconds())
	if msgTimeoutConfig.Status && reqSeconds > msgTimeoutConfig.Seconds && len(global.ReplyList) > 0 {
		// 超时,从通用回复获取一条
		aiReplyMsg = global.ReplyList[rand.Intn(len(global.ReplyList))]
	}
	if aiReplyMsg == "" && len(global.ReplyList) > 0 {
		aiReplyMsg = global.ReplyList[rand.Intn(len(global.ReplyList))]
	}
	// 如果结尾是\n,那么就不要结尾的\n
	aiReplyMsg = strings.TrimRight(aiReplyMsg, "\n")

	// 替换括号为*
	aiReplyMsg = utils.ReplaceBracketsWithStars(aiReplyMsg)

	// 1月7日 军锋需求修改星号匹配 双星替换为单星 ** -> *
	aiReplyMsg = strings.ReplaceAll(aiReplyMsg, "**", "*")
	return
}

func (m *MsgService) getUserQuickReplyMsgV3(ctx context.Context, msgApi model.MsgJob, lastestSendMsg string, aiRoleInfo *model.AiRole, userInfo *model.DigitalUser, sendMsgReq *request.RedisMsgReq) (aiReplyMsg string, err error) {
	var (
		apiLog  string
		curlStr string
		apiRes  response.AiMsgResMiniMax
	)
	msgApi.ApiUrl = "http://34.210.195.120:8901/v3/chat/completions"
	messages := []request.MsgItemV2{
		{Role: "assistant", Content: lastestSendMsg},
	}
	reqData := aiRoleInfo.ReqData
	reqData["user_name"] = aiRoleInfo.Nickname
	reqData["bot_name"] = userInfo.Nickname

	// reqData["user_name"] = userInfo.Nickname
	// reqData["bot_name"] = aiRoleInfo.Nickname
	reqData["messages"] = messages
	// 请求AI接口
	if apiLog, curlStr, err = utils.HttpPostJson(msgApi.ApiUrl, reqData, &apiRes, 30); err != nil {
		global.LOG.Error("getUserQuickReplyMsgV3 error", zap.Any("err", err), zap.Any("apiRes", apiRes), zap.Any("apiLog", apiLog), zap.Any("curlStr", curlStr))
		return
	}
	global.LOG.Info("getUserQuickReplyMsgV3", zap.String("apiLog", apiLog), zap.Any("apiRes", apiRes))
	if apiRes.Code != 0 {
		global.LOG.Error("getUserQuickReplyMsgV3 error", zap.Any("err", err), zap.Any("apiRes", apiRes), zap.Any("apiLog", apiLog))
	}

	aiReplyMsg = apiRes.Data.Answer
	if aiReplyMsg == "" && len(global.ReplyList) > 0 {
		aiReplyMsg = global.ReplyList[rand.Intn(len(global.ReplyList))]
	}
	// 如果结尾是\n,那么就不要结尾的\n
	aiReplyMsg = strings.TrimRight(aiReplyMsg, "\n")
	// 1月7日 军锋需求修改星号匹配 双星替换为单星 ** -> *
	aiReplyMsg = strings.ReplaceAll(aiReplyMsg, "**", "*")
	return
}

func (m *MsgService) getUserQuickReplyMsgV2(ctx context.Context, msgApi model.MsgJob, lastestSendMsg string, aiRoleInfo *model.AiRole, userInfo *model.DigitalUser, sendMsgReq *request.RedisMsgReq) (aiReplyMsg string, err error) {
	var (
		apiLog  string
		curlStr string
		apiRes  response.AiMsgResV2
	)
	// msgApi.ApiUrl = "http://35.163.192.237:8901/v3/chat/completions"
	messages := []request.MsgItemV2{
		{Role: "assistant", Content: lastestSendMsg},
	}
	reqData := aiRoleInfo.ReqData
	reqData["user_name"] = aiRoleInfo.Nickname
	reqData["bot_name"] = userInfo.Nickname

	// reqData["user_name"] = userInfo.Nickname
	// reqData["bot_name"] = aiRoleInfo.Nickname
	reqData["messages"] = messages
	// 请求AI接口
	if apiLog, curlStr, err = utils.HttpPostJson(msgApi.ApiUrl, reqData, &apiRes, 30); err != nil {
		global.LOG.Error("getUserQuickReplyMsgV2 error", zap.Any("err", err), zap.Any("apiRes", apiRes), zap.Any("apiLog", apiLog), zap.Any("curlStr", curlStr))
		return
	}
	global.LOG.Info("getUserQuickReplyMsgV2", zap.String("apiLog", apiLog), zap.Any("apiRes", apiRes))
	if len(apiRes.Choices) == 0 {
		err = errors.New("apiRes.Results is empty, apiLog: " + apiLog)
		return
	}
	aiReplyInfo := apiRes.Choices[0]
	aiReplyMsg = aiReplyInfo.Message.Content
	// 如果结尾是\n,那么就不要结尾的\n
	aiReplyMsg = strings.TrimRight(aiReplyMsg, "\n")
	// 替换括号为*
	aiReplyMsg = utils.ReplaceBracketsWithStars(aiReplyMsg)
	// 1月7日 军锋需求修改星号匹配 双星替换为单星 ** -> *
	aiReplyMsg = strings.ReplaceAll(aiReplyMsg, "**", "*")
	return
}

func (m *MsgService) getUserQuickReplyMsgMy(ctx context.Context, msgApi model.MsgJob, lastestSendMsg string, aiRoleInfo *model.AiRole, userInfo *model.DigitalUser, sendMsgReq *request.RedisMsgReq) (aiReplyMsg string, err error) {
	var (
		apiLog  string
		curlStr string
		apiRes  response.AiMsgRes
		history = map[string]interface{}{}
	)
	visible := [][]string{{"", lastestSendMsg}}
	history = map[string]interface{}{
		"visible": visible,
	}
	reqData := aiRoleInfo.ReqData
	reqData["name1"] = aiRoleInfo.Nickname
	reqData["name2"] = userInfo.Nickname
	reqData["user_input"] = lastestSendMsg
	reqData["history"] = history
	// 请求AI接口
	if apiLog, curlStr, err = utils.HttpPostJson(msgApi.ApiUrl, reqData, &apiRes, 10); err != nil {
		global.LOG.Error("getUserQuickReplyMsgMy error", zap.Any("err", err), zap.Any("apiRes", apiRes), zap.Any("apiLog", apiLog), zap.Any("curlStr", curlStr))
		return
	}
	global.LOG.Info("getUserQuickReplyMsgMy", zap.String("apiLog", apiLog), zap.Any("apiRes", apiRes))
	if len(apiRes.Results) == 0 {
		err = errors.New("apiRes.Results is empty, apiLog: " + apiLog)
		return
	}
	aiReplyInfo := apiRes.Results[0]
	aiReplyInfoVisible := aiReplyInfo.History.Visible[len(aiReplyInfo.History.Visible)-1]
	if len(aiReplyInfoVisible) != 2 {
		err = errors.New("len(aiReplyInfoVisible) != 2, apiLog: " + apiLog)
		return
	}
	aiReplyMsg = aiReplyInfoVisible[1]
	// 如果结尾是\n,那么就不要结尾的\n
	aiReplyMsg = strings.TrimRight(aiReplyMsg, "\n")
	// 1月7日 军锋需求修改星号匹配 双星替换为单星 ** -> *
	aiReplyMsg = strings.ReplaceAll(aiReplyMsg, "**", "*")
	return
}

func (m *MsgService) getReplyMsgMiniMax(ctx context.Context, msgApi model.MsgJob, userSendMsg string, aiRoleInfo *model.AiRole, userInfo *model.DigitalUser, sendMsgReq *request.RedisMsgReq) (aiReplyMsg string, err error) {
	var (
		apiLog            string
		curlStr           string
		apiRes            response.AiMsgResMiniMax
		msgHis            []request.MsgItemV2
		userIdStr         = fmt.Sprintf("%d", userInfo.Id)
		reqUrl            = msgApi.ApiUrl
		userIntimateSer   UsersIntimateService
		usersIntimateInfo *model.UsersIntimate
	)
	reqUrl = "http://34.210.195.120:8901/v3/chat/completions"
	msgTimeoutConfig := GetMsgSendTimeOutConfig(ctx)
	if msgHis, err = m.GetMsgHistoryV2(ctx, userIdStr, aiRoleInfo.RoleID); err != nil {
		return
	}
	messages := []request.MsgItemV2{}
	if len(msgHis) > 0 {
		messages = append(messages, msgHis...)
	}
	// 添加用户说的话
	messages = append(messages, request.MsgItemV2{Role: "user", Content: userSendMsg})
	reqData := aiRoleInfo.ReqData
	reqData["messages"] = messages
	reqData["user_name"] = userInfo.Nickname
	if usersIntimateInfo, err = userIntimateSer.GetByUidAndRoleid(userInfo.Id, aiRoleInfo.Id); err != nil {
		return
	}
	reqData["input"] = map[string]interface{}{
		"intimate_level": usersIntimateInfo.Level,
		"app_id":         userInfo.AppID,
	}
	reqStart := time.Now()
	// 请求AI接口
	start := time.Now()
	if apiLog, curlStr, err = utils.HttpPostJson(reqUrl, reqData, &apiRes, msgTimeoutConfig.Seconds); err != nil {
		global.LOG.Error("getReplyMsgMiniMax error", zap.Any("err", err), zap.Any("apiRes", apiRes), zap.Any("apiLog", apiLog), zap.String("curlStr", curlStr))
		return
	}
	if apiRes.Code != 0 {
		Alarm(ctx, "大模型报错", apiLog, curlStr, "chat_err")
		global.LOG.Error("getReplyMsgMiniMax error", zap.Any("err", err), zap.Any("apiRes", apiRes), zap.Any("apiLog", apiLog))
	}
	duration := time.Since(start)
	if duration > time.Second*20 {
		Alarm(ctx, "大模型超时20s", apiLog, curlStr, "chat")
	}
	global.LOG.Info("getReplyMsgMiniMax api响应速度", zap.Any("time", duration), zap.Any("reqData", reqData))
	sendMsgReq.Api3Time = duration
	start = time.Now()
	aiReplyMsg = apiRes.Data.Answer
	reqSeconds := int(time.Now().Sub(reqStart).Seconds())
	if msgTimeoutConfig.Status && reqSeconds > msgTimeoutConfig.Seconds && len(global.ReplyList) > 0 {
		// 超时,从通用回复获取一条
		aiReplyMsg = global.ReplyList[rand.Intn(len(global.ReplyList))]
	}
	if aiReplyMsg == "" && len(global.ReplyList) > 0 {
		aiReplyMsg = global.ReplyList[rand.Intn(len(global.ReplyList))]
	}
	// 如果结尾是\n,那么就不要结尾的\n
	aiReplyMsg = strings.TrimRight(aiReplyMsg, "\n")
	// 1月7日 军锋需求修改星号匹配 双星替换为单星 ** -> *
	aiReplyMsg = strings.ReplaceAll(aiReplyMsg, "**", "*")
	global.LOG.Info("getReplyMsgMiniMax  api响应后", zap.Any("time", time.Since(start)), zap.Any("reqData", reqData))
	return
}

// execVoiceCallTrain 语音通话逻辑
func (m *MsgService) execVoiceCallTrain(ctx context.Context, msgApi model.MsgJob, sendMsgReq *request.RedisMsgReq) (err error) {
	var (
		replyMsg         string
		lastVoiceMsgInfo *model.MsgInfo
		aiRoleInfo       *model.AiRole
		userInfo         *model.DigitalUser
		userProfileInfo  *model.DigitalUserProfile
		aiRoleSer        AiRoleService
		userService      DigitalUserService
		profileService   DigitalUserProfileService
		userIdStr        = fmt.Sprintf("%d", sendMsgReq.UserId)
		now              = time.Now()
	)
	gLog := global.LOG.With(zap.String("Func", "execVoiceCallTrain"), zap.String("Name", msgApi.Name), zap.String("ApiUrl", msgApi.ApiUrl), zap.Any("sendMsgReq", sendMsgReq))
	// 获取AI角色信息
	if aiRoleInfo, err = aiRoleSer.GetByRoleId(sendMsgReq.AiRoleId); err != nil {
		return
	}
	// 获取用户信息
	if userInfo, err = userService.GetById(ctx, sendMsgReq.UserId); err != nil {
		return
	}
	// 查询用户Profile信息
	if userProfileInfo, err = profileService.GetByUserId(sendMsgReq.UserId); err != nil {
		return
	}
	// 查询用户和该AI角色的最新一条聊天记录,按照send_time倒序
	if lastVoiceMsgInfo, err = m.LastVoiceCallMsg(ctx, userIdStr, sendMsgReq.AiRoleId); err != nil {
		gLog.Error("获取最后一条语言消息失败", zap.Any("err", err))
		return
	}
	gLog.Info("获取最后一条语言消息成功", zap.Any("lastVoiceMsgInfo", lastVoiceMsgInfo))
	userSendMsg := lastVoiceMsgInfo.MessageContent.Text
	// App信息
	appInfo := global.AppMap[userInfo.AppID]
	// 获取回复消息
	if replyMsg, err = m.getAiReplyMsg(ctx, msgApi, userSendMsg, aiRoleInfo, userInfo, sendMsgReq, userProfileInfo.Nsfw); err != nil {
		gLog.Error("获取大模型回复消息失败", zap.Any("err", err))
		return
	}
	// 解锁
	lockRedisKey := m.GetMsgLockRedisKey(sendMsgReq)
	_ = global.REDIS.Del(ctx, lockRedisKey).Val()
	gLog.Info("4.获取回复消息成功&&消息已解锁", zap.String("userMsg", userSendMsg), zap.String("replyMsg", replyMsg))
	voiceText := m.ExtractVoiceTextV3(replyMsg)
	if voiceText == "" {
		// 如果没有提取到语音文本,则用模型回复的文本
		voiceText = replyMsg
	}
	msgVoiceKey, msgVoiceKeyExist := m.VoiceCache(ctx, voiceText, aiRoleInfo.RoleID)
	if !msgVoiceKeyExist {
		msgVoiceKey, _ = m.ElevenLabsTts(lastVoiceMsgInfo.MessageID, voiceText, aiRoleInfo)
	}
	gLog.Info("msg voice gen success", zap.String("voiceText", voiceText), zap.String("msgVoiceKey", utils.S3Url(msgVoiceKey, userInfo.AppID)))
	if msgVoiceKey == "" {
		// TODO 生成语音失败怎么办?
	}
	msgContent := model.MessageContent{
		Avatar:     utils.S3Url(aiRoleInfo.Avatar, userInfo.AppID),
		Text:       replyMsg,
		OriginText: replyMsg,
		CanUndress: false,
		AudioURL:   utils.S3Url(msgVoiceKey, userInfo.AppID),
	}
	aiSendMsg := &model.MsgInfo{
		MessageID:      lastVoiceMsgInfo.MessageID,
		Channel:        nil,
		SendTime:       now.UnixMilli(),
		DeletedAt:      nil,
		MessageType:    model.MsgTypeVoiceCall,
		SenderID:       sendMsgReq.AiRoleId,
		SenderName:     aiRoleInfo.Nickname,
		ReceiverID:     userIdStr,
		ReceiverName:   userInfo.Nickname,
		MessageContent: msgContent,
	}
	// 发送消息到声网
	if err = m.SendMsgToAgora(aiSendMsg, appInfo); err != nil {
		gLog.Error("SendMsgToAgora error", zap.Any("err", err))
		return
	}
	gLog.Info("发送消息到声网成功")
	return
}

func (m *MsgService) execLivePushMsgTrain(ctx context.Context, msgReq *request.LivePushMsgReq) (err error) {
	var (
		replyMsg    string
		aiRoleInfo  *model.AiRole
		userInfo    *model.DigitalUser
		liveSer     LiveInfoService
		aiRoleSer   AiRoleService
		userService DigitalUserService
		userIdStr   = fmt.Sprintf("%d", msgReq.UserId)
		now         = time.Now()
	)
	gLog := global.LOG.With(zap.String("Func", "execLivePushMsgTrain"), zap.Uint("LiveInfoId", msgReq.LiveInfoId), zap.Uint("UserId", msgReq.UserId), zap.String("AiId", msgReq.AiRoleId))
	// 获取AI角色信息
	if aiRoleInfo, err = aiRoleSer.GetByRoleId(msgReq.AiRoleId); err != nil {
		return
	}
	// 获取用户信息
	if userInfo, err = userService.GetById(ctx, msgReq.UserId); err != nil {
		return
	}
	// App信息
	appInfo := global.AppMap[userInfo.AppID]
	// 查询是第几次直播
	searchCountReq := &model.LiveInfo{
		AppID:         userInfo.AppID,
		DigitalUserId: userInfo.Id,
	}
	liveCount, liveCountErr := liveSer.SearchCount(ctx, searchCountReq)
	if liveCountErr != nil {
		gLog.Error("SearchCount error", zap.Any("err", liveCountErr))
		liveCount = 1
	}
	if liveCount > 1 {
		if len(global.LiveManyReplyList) > 0 {
			replyMsg = utils.RandList(global.LiveManyReplyList)
		} else {
			replyMsg = utils.RandList(global.EnglishCommentsNoSecondTime)
		}
	} else {
		if len(global.LiveOneReplyList) > 0 {
			replyMsg = utils.RandList(global.LiveOneReplyList)
		} else {
			replyMsg = utils.RandList(global.EnglishFirstComments)
		}
	}
	msgContent := model.MessageContent{
		Avatar:     utils.S3Url(aiRoleInfo.Avatar, userInfo.AppID),
		Text:       replyMsg,
		OriginText: replyMsg,
		CanUndress: false,
	}
	aiSendMsg := &model.MsgInfo{
		MessageID:      uuid.NewString(),
		Channel:        nil,
		SendTime:       now.UnixMilli(),
		DeletedAt:      nil,
		MessageType:    model.MsgTypeTxt,
		SenderID:       msgReq.AiRoleId,
		SenderName:     aiRoleInfo.Nickname,
		ReceiverID:     userIdStr,
		ReceiverName:   userInfo.Nickname,
		MessageContent: msgContent,
	}
	// 发送消息到声网
	if err = m.SendMsgToAgora(aiSendMsg, appInfo); err != nil {
		gLog.Error("SendMsgToAgora error", zap.Any("err", err))
		return
	}
	gLog.Info("execLivePushMsgTrain success")
	return
}

func Alarm(ctx context.Context, title, apiLog, curlStr, redisKey string) {
	lockKey := fmt.Sprintf("PreventTriggerWithin5Min:trigger_lockv1:%s", redisKey)
	countKey := fmt.Sprintf("PreventTriggerWithin5Min:count:%s", redisKey)

	// 自增计数器
	count, err := global.REDIS.Incr(ctx, countKey).Result()
	if err != nil {
		return
	}

	// 设置计数器的过期时间（仅第一次设置）
	if count == 1 {
		global.REDIS.Expire(ctx, countKey, 5*time.Minute)
	}

	// 如果达到10次，触发报警
	if count <= 10 {
		return
	}
	global.REDIS.Del(ctx, countKey)

	// 尝试设置防重复的 key
	ok, err := global.REDIS.SetNX(ctx, lockKey, time.Now().Unix(), 5*time.Minute).Result()
	if err != nil || !ok {
		// 锁存在，已经触发过报警，直接 return
		return
	}

	// 第一次触发报警
	alarm.P0(title, "5分钟内触发次数大于10次触发", apiLog, curlStr)
}
