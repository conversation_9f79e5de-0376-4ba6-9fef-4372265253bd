package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"aimsg-server/utils"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
	"math/rand"
	"time"
)

type ChatGuideRecordService struct{}

func (m *ChatGuideRecordService) Create(reqData *model.ChatGuideRecord) (err error) {
	err = global.DB.Model(&model.ChatGuideRecord{}).Create(reqData).Error
	m.SyncMsgList()
	m.SyncFastMsgList()
	return
}

func (m *ChatGuideRecordService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.ChatGuideRecord{}).Where("id = ?", id).Delete(&model.ChatGuideRecord{}).Error
	m.SyncMsgList()
	m.SyncFastMsgList()
	return
}

func (m *ChatGuideRecordService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.ChatGuideRecord{}).Where("id IN (?)", reqData.Ids).Delete(&model.ChatGuideRecord{}).Error
	m.SyncMsgList()
	m.SyncFastMsgList()
	return
}

func (m *ChatGuideRecordService) Update(reqData *model.ChatGuideRecord) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.ChatGuideRecord{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	m.SyncMsgList()
	m.SyncFastMsgList()
	return
}

func (m *ChatGuideRecordService) GetById(id uint) (res *model.ChatGuideRecord, err error) {
	err = global.DB.Model(&model.ChatGuideRecord{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *ChatGuideRecordService) GetAll() (resList []*model.ChatGuideRecord, err error) {
	err = global.DB.Model(&model.ChatGuideRecord{}).Order("sort DESC").Find(&resList).Error
	return
}

func (m *ChatGuideRecordService) GetGuideTypeMsgAll() (resList []*model.ChatGuideRecord, err error) {
	err = global.DB.Model(&model.ChatGuideRecord{}).Where("guide_type = ?", int(model.GuideTypeMsg)).Order("sort DESC").Find(&resList).Error
	return
}

func (m *ChatGuideRecordService) GetGuideTypeFastMsgAll() (resList []*model.ChatGuideRecord, err error) {
	err = global.DB.Model(&model.ChatGuideRecord{}).Where("guide_type = ?", int(model.GuideTypeFastMsg)).Order("sort DESC").Find(&resList).Error
	return
}

func (m *ChatGuideRecordService) SyncMsgList() {
	var (
		err     error
		resList []*model.ChatGuideRecord
	)
	resList, err = m.GetGuideTypeMsgAll()
	if err != nil {
		global.LOG.Error("SyncFastMsgList 失败", zap.Error(err))
		return
	}
	global.GuideMsgList = resList
}

func (m *ChatGuideRecordService) SyncFastMsgList() {
	var (
		err         error
		resList     []*model.ChatGuideRecord
		contentList []string
	)
	resList, err = m.GetGuideTypeFastMsgAll()
	if err != nil {
		global.LOG.Error("SyncFastMsgList 失败", zap.Error(err))
		return
	}
	for _, r := range resList {
		contentList = append(contentList, r.Msg)
	}
	global.FastMsgList = contentList
}

func (m *ChatGuideRecordService) RandomGuideMsgList(num int) []*model.ChatGuideRecord {
	msgList := global.GuideMsgList
	msgListLen := len(msgList)
	if msgListLen <= num {
		return msgList
	}
	selectedIndices := make(map[int]bool)
	for len(selectedIndices) < num {
		randIndex := rand.Intn(msgListLen)
		selectedIndices[randIndex] = true
	}
	var res []*model.ChatGuideRecord
	for index := range selectedIndices {
		res = append(res, msgList[index])
	}
	return res
}

func (m *ChatGuideRecordService) ChatGuideListV2(c *gin.Context) (res gin.H, err error) {
	var (
		userService DigitalUserService
		userInfo    *model.DigitalUser
		userId      = utils.GetDigitalUserID(c)
	)
	appId := utils.GetAppID(c)
	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	t2 := time.Date(2025, 4, 9, 0, 0, 0, 0, time.UTC)
	if (appId == 4 && userInfo.CreatedAt.After(t2)) || appId == 25 {
		return gin.H{
			"list":     []interface{}{},
			"list_two": []interface{}{},
		}, nil
	}
	if userInfo.IsAudit() {
		// 审核用户不显示
		return
	}
	// 从FastMsgList随机取两个
	fastMsgList := utils.RandomStringList(global.FastMsgList, 2)

	res = gin.H{
		"list":     fastMsgList,
		"list_two": m.RandomGuideMsgList(5),
	}
	return
}

func (m *ChatGuideRecordService) GetList(info req.ChatGuideRecordSearch) (resList []*model.ChatGuideRecord, total int64, err error) {
	db := global.DB.Model(&model.ChatGuideRecord{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.GuideType != 0 {
		db = db.Where("guide_type = ?", info.GuideType)
	}
	if info.Label != "" {
		db = db.Where("label = ?", info.Label)
	}
	if info.Msg != "" {
		db = db.Where("msg LIKE ?", "%"+info.Msg+"%")
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
