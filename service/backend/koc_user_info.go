package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"gorm.io/gorm/clause"
)

type KocUserInfoService struct{}

func (m *KocUserInfoService) Create(reqData *model.KocUserInfo) (err error) {
	err = global.DB.Model(&model.KocUserInfo{}).Create(reqData).Error
	return
}

func (m *KocUserInfoService) RegCreate(reqData *req.KocUserInfoRegisterReq) (err error) {
	var (
		revenueUsd     decimal.Decimal
		allRevenueUsd  decimal.Decimal
		dailyReportSer DailyReportService
	)
	// 从daily_report表中,该AI角色的总收入
	allRevenueUsd, err = dailyReportSer.GetTotalIncomeByAiRoleId(reqData.AiRoleIdUint, nil)
	if err != nil {
		return
	}
	reqData.AllIncome = allRevenueUsd

	if len(reqData.PendingIncomeDayRange) == 2 {
		// 从daily_report表中获取这个日期范围内,该AI角色的总收入
		revenueUsd, err = dailyReportSer.GetTotalIncomeByAiRoleId(reqData.AiRoleIdUint, reqData.PendingIncomeDayRange)
		if err != nil {
			return
		}
		reqData.PendingIncome = revenueUsd
	}
	createData := &model.KocUserInfo{
		AiRoleId:      reqData.AiRoleId,
		Nickname:      reqData.Nickname,
		Platform:      reqData.Platform,
		Account:       reqData.Account,
		AllIncome:     reqData.AllIncome,
		PendingIncome: reqData.PendingIncome,
	}
	err = global.DB.Model(&model.KocUserInfo{}).Create(createData).Error
	return
}

func (m *KocUserInfoService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.KocUserInfo{}).Where("id = ?", id).Unscoped().Delete(&model.KocUserInfo{}).Error
	return
}

func (m *KocUserInfoService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.KocUserInfo{}).Where("id IN (?)", reqData.Ids).Delete(&model.KocUserInfo{}).Error
	return
}

func (m *KocUserInfoService) Update(reqData *model.KocUserInfo) (err error) {
	err = global.DB.Model(&model.KocUserInfo{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	return
}

func (m *KocUserInfoService) GetById(id uint) (res *model.KocUserInfo, err error) {
	err = global.DB.Model(&model.KocUserInfo{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *KocUserInfoService) GetCurrentInfo(c *gin.Context) (res *model.KocUserInfo, err error) {
	var (
		kocUserId     uint
		sysUserKocSer SysUserKocService
	)
	kocUserId, err = sysUserKocSer.GetSysUserKocUserId(c)
	if err != nil {
		return
	}
	if kocUserId == 0 {
		err = fmt.Errorf("当前用户未绑定KOC用户信息")
		return
	}
	err = global.DB.Model(&model.KocUserInfo{}).Where("id = ?", kocUserId).First(&res).Error
	return
}

func (m *KocUserInfoService) GetAll() (resList []*model.KocUserInfo, err error) {
	err = global.DB.Model(&model.KocUserInfo{}).Find(&resList).Error
	return
}

func (m *KocUserInfoService) SearchOne(reqData *model.KocUserInfo) (resInfo *model.KocUserInfo, err error) {
	db := global.DB.Model(&model.KocUserInfo{})
	if reqData.Id != 0 {
		db = db.Where("id = ?", reqData.Id)
	}
	if reqData.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", reqData.AiRoleId)
	}
	err = db.First(&resInfo).Error
	return
}

func (m *KocUserInfoService) GetList(info req.KocUserInfoSearch) (resList []*model.KocUserInfo, total int64, err error) {
	db := global.DB.Model(&model.KocUserInfo{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.Platform != 0 {
		db = db.Where("platform = ?", info.Platform)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
