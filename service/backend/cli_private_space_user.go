package backend

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	"aimsg-server/model/cli/response"
	"aimsg-server/model/cli/td"
	req "aimsg-server/model/common/request"
	"aimsg-server/utils"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

func (m *PrivateSpaceUserService) UserPrivateSpaceList(c *gin.Context) (res gin.H, err error) {
	var (
		privateSpaces     []*response.PrivateSpaceListItem
		privateSpacesList []*response.PrivateSpaceListItem
		userId            = utils.GetDigitalUserID(c)
		appId             = utils.GetAppID(c)
	)
	db := global.DB.Table("private_space AS p")
	db = db.Joins("LEFT JOIN private_space_user AS pu ON p.id = pu.private_space_id")
	db = db.Where("pu.digital_user_id = ?", userId)
	db.Select("p.id, DATE_FORMAT(pu.created_at,'%Y/%c/%e') AS buy_time, p.name, p.media_type, p.media_size, p.thumbnail_url, p.img_count, p.video_second, p.price")
	if err = db.Find(&privateSpacesList).Error; err != nil {
		return
	}
	// 组装返回数据
	for _, s := range privateSpacesList {
		s.ThumbnailURL = utils.S3Url(s.ThumbnailURL, appId)
		s.IsBuy = true
		privateSpaces = append(privateSpaces, s)
	}
	res = gin.H{
		"list": privateSpaces,
	}
	return
}

func (m *PrivateSpaceUserService) UnlockPrivateSpace(c *gin.Context, reqData *req.IdReq) (res gin.H, err error) {
	var (
		mediaList        []string
		recCount         int64
		userInfo         *model.DigitalUser
		userSer          DigitalUserService
		aiRoleInfo       *model.AiRole
		aiRoleSer        AiRoleService
		userProfile      *model.DigitalUserProfile
		profileSer       DigitalUserProfileService
		privateSpaceInfo *model.PrivateSpace
		privateSpaceSer  PrivateSpaceService
		msgService       MsgService
		msgCountSer      UserAiMsgCountService
		userId           = utils.GetDigitalUserID(c)
		appId            = utils.GetAppID(c)
	)
	// 查询用户信息
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		err = fmt.Errorf("user get err: %v", err)
		return
	}
	isSVip := userInfo.IsSVip()
	// isChatVip := userInfo.IsChatVip()
	isContentVip := userInfo.IsContentVip()
	verCompare320 := utils.CompareVersions(userInfo.Version, "3.2.0")
	if IsVipSVipUser(userInfo) {
		if !isSVip && verCompare320 >= 0 {
			err = errs.RequireSVipErr
			return
		}
	} else {
		if !isContentVip {
			err = errs.ContentVipErr
			return
		}
	}
	// 查询私人空间信息
	if privateSpaceInfo, err = privateSpaceSer.GetById(c, reqData.ID); err != nil {
		err = fmt.Errorf("privateSpace get err: %v", err)
		return
	}
	// 查询AI角色信息
	if aiRoleInfo, err = aiRoleSer.GetById(c, privateSpaceInfo.AiId); err != nil {
		err = fmt.Errorf("aiRole get err: %v", err)
		return
	}
	// 价格
	floCount := privateSpaceInfo.Price
	// 查询用户是否已购买
	if recCount, err = m.SearchCount(c, &model.PrivateSpaceUser{
		DigitalUserId:  userId,
		PrivateSpaceId: reqData.ID,
	}); err != nil {
		return
	}
	if recCount == 0 {
		// 未购买, 用户购买
		if userProfile, err = profileSer.GetByUserId(userId); err != nil {
			return
		}
		if userProfile.FlowerAmount < floCount {
			// 金币不足
			err = errs.FloShopErr
			return
		}
		if err = global.DB.Transaction(func(tx *gorm.DB) (tErr error) {
			// 创建FlowerConsumeRecord记录
			fRecord := &model.FlowerConsumeRecord{
				DigitalUserId: userId,
				AiId:          aiRoleInfo.Id,
				AiRoleId:      aiRoleInfo.RoleID,
				ConsumeType:   model.ConsumeTypeBuyPrivateSpace,
				RelationID:    fmt.Sprintf("%d", privateSpaceInfo.Id),
				OriginAmount:  userProfile.FlowerAmount,
				ConsumeAmount: floCount,
			}
			go TdPointer(userInfo, userProfile, td.ServerEvent, map[string]interface{}{
				"event_tag":         "coins_consume",
				"coins_consume_num": -floCount,
				"ai_role_id":        aiRoleInfo.RoleID,
				"ai_role_type":      aiRoleInfo.RoleType,
				"source":            "private_space_consume",
			})
			if tErr = tx.Model(&model.FlowerConsumeRecord{}).Create(fRecord).Error; tErr != nil {
				return
			}
			updateMap := map[string]interface{}{
				"flower_amount": gorm.Expr("flower_amount - ?", floCount),
			}
			if tErr = tx.Model(&model.DigitalUserProfile{}).Where("id = ?", userProfile.Id).Updates(updateMap).Error; tErr != nil {
				return
			}
			// 创建PrivateSpaceUser记录
			psUser := &model.PrivateSpaceUser{
				DigitalUserId:  userId,
				AiId:           privateSpaceInfo.AiId,
				PrivateSpaceId: reqData.ID,
			}
			if tErr = tx.Model(&model.PrivateSpaceUser{}).Create(psUser).Error; tErr != nil {
				return
			}
			return
		}); err != nil {
			return
		}
		_ = msgService.SaveIntimateRecord(c, userInfo.Id, aiRoleInfo, model.IntimateTypeAdd, model.SubTypePrivateSpace)
		// 更新UserAiMsgCount
		if err = msgCountSer.FindUpdate(c, userId, aiRoleInfo, map[string]interface{}{
			"private_space_count": gorm.Expr("private_space_count + 1"),
		}); err != nil {
			return
		}
	}
	// 返回资源列表
	if privateSpaceInfo.MediaType == model.MediaTypeImg {
		// 从Aws的S3里获取图片列表
		s3ClientV2 := GetS3ClientV2(c)
		var imgFiles []string
		if imgFiles, err = s3ClientV2.ListFilesInS3Path(privateSpaceInfo.MediaUrl); err != nil {
			global.LOG.Error("ListFilesInS3Path 失败", zap.String("Prefix", privateSpaceInfo.MediaUrl), zap.Error(err))
		}
		for _, imgFile := range imgFiles {
			mediaList = append(mediaList, utils.S3Url(imgFile, appId))
		}
	} else {
		mediaList = append(mediaList, utils.S3Url(privateSpaceInfo.MediaUrl, appId))
	}
	res = gin.H{
		"list": mediaList,
	}
	return
}
