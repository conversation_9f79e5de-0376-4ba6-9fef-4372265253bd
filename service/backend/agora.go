package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/utils"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AgoraService struct{}

func (m *AgoraService) RTCWebhook(c *gin.Context, reqData *request.RTCWebhookReq) (err error) {
	var (
		signature = c.<PERSON>eader("Agora-Signature-V2")
		appIdStr  = c.Param("app_id")
	)
	gLog := global.LOG.With(zap.String("func", "RTCWebhook"), zap.Any("reqData", reqData))
	raw := c.MustGet(global.GinContextRawData)
	if signature == "" {
		gLog.Error("signature is empty")
		err = fmt.Errorf("signature is empty")
		return
	}
	if appIdStr == "" {
		gLog.Error("appId is empty")
		err = fmt.Errorf("appId is empty")
		return
	}
	appIdUint := utils.StrToUint(appIdStr)
	appInfo, appInfoExist := global.AppMap[appIdUint]
	if !appInfoExist {
		gLog.Error("appInfo not exist")
		err = fmt.Errorf("appInfo not exist")
		return
	}

	if appIdUint == 1 {
		// 转发请求
		reqDataMap := utils.Struct2Map(reqData)
		headerMap := map[string]string{
			"X-IgnoreSignature": "297dc6634dd420872c10444946aadd55",
		}
		reqUrl := fmt.Sprintf("%s/callback/connected", global.CONFIG.Vita.Url)
		apiLogStr, curlStr, curlErr := utils.HttpPostJsonWithHeader(reqUrl, reqDataMap, headerMap, nil, 10)
		if curlErr != nil {
			global.LOG.Error("RTCWebhook Proxy fail", zap.Error(curlErr), zap.String("apiLog", apiLogStr), zap.String("curl", curlStr))
			return
		}
	}

	rawStr := string(raw.([]byte))
	calcSignature := utils.CalcSignatureV2(appInfo.AgoraData.Data().RtcCallbackSecret, rawStr)
	if calcSignature != signature {
		gLog.Error("signature not match")
		err = fmt.Errorf("signature not match")
		return
	}
	defer func() {
		if err != nil {
			gLog.Error("RTCWebhook failed", zap.Error(err))
		} else {
			gLog.Info("RTCWebhook success")
		}
	}()

	payloadByte, _ := json.Marshal(reqData.Payload)
	switch reqData.EventType {
	case request.EventTypeChannelCreate:
		// 创建频道
		var rtcReq request.RTCPayload101
		if err = json.Unmarshal(payloadByte, &rtcReq); err != nil {
			return
		}
		// do something
	case request.EventTypeChannelDestroy:
		// 表示最后一个用户离开频道且频道销毁
		var rtcReq request.RTCPayload102
		if err = json.Unmarshal(payloadByte, &rtcReq); err != nil {
			return
		}
		// do something
	case request.EventTypeBroadcasterJoinChannel:
		// 直播场景下，主播加入频道
		var rtcReq request.RTCPayload103
		if err = json.Unmarshal(payloadByte, &rtcReq); err != nil {
			return
		}
		// do something
	case request.EventTypeBroadcasterLeaveChannel:
		// 直播场景下，主播离开频道
		var rtcReq request.RTCPayload104
		if err = json.Unmarshal(payloadByte, &rtcReq); err != nil {
			return
		}
		// do something
	case request.EventTypeAudienceJoinChannel:
		// 直播场景下，观众加入频道
		var rtcReq request.RTCPayload105
		if err = json.Unmarshal(payloadByte, &rtcReq); err != nil {
			return
		}
		// do something
	case request.EventTypeAudienceLeaveChannel:
		// 直播场景下，观众离开频道
		var rtcReq request.RTCPayload106
		if err = json.Unmarshal(payloadByte, &rtcReq); err != nil {
			return
		}
		// do something
	case request.EventTypeCommunicationModeJoinChannel:
		// 通信场景下用户加入频道
		var rtcReq request.RTCPayload107
		if err = json.Unmarshal(payloadByte, &rtcReq); err != nil {
			return
		}
		// do something
	case request.EventTypeCommunicationModeLeaveChannel:
		// 通信场景下用户离开频道
		var rtcReq request.RTCPayload108
		if err = json.Unmarshal(payloadByte, &rtcReq); err != nil {
			return
		}
		// do something
	case request.EventTypeClientRoleChangeToBroadcaster:
		// 观众将角色切换为主播
		var rtcReq request.RTCPayload111
		if err = json.Unmarshal(payloadByte, &rtcReq); err != nil {
			return
		}
		// do something
	case request.EventTypeClientRoleChangeToAudience:
		// 主播将角色切换为观众
		var rtcReq request.RTCPayload112
		if err = json.Unmarshal(payloadByte, &rtcReq); err != nil {
			return
		}
		// do something
	default:
		// do nothing
	}
	// do something
	return
}
