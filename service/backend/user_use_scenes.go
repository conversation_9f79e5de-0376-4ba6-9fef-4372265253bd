package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
	"time"
)

type UserUseScenesService struct{}

func (m *UserUseScenesService) Create(c *gin.Context, reqData *model.UserUseScenes) (err error) {
	err = global.DB.Model(&model.UserUseScenes{}).Create(reqData).Error
	if err == nil {
		m.SetCache(c, reqData)
	}
	return
}

func (m *UserUseScenesService) DeleteById(c *gin.Context, id uint) (err error) {
	err = global.DB.Model(&model.UserUseScenes{}).Where("id = ?", id).Delete(&model.UserUseScenes{}).Error
	return
}

func (m *UserUseScenesService) DeleteByIds(c *gin.Context, reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.UserUseScenes{}).Where("id IN (?)", reqData.Ids).Delete(&model.UserUseScenes{}).Error
	return
}

func (m *UserUseScenesService) Update(c *gin.Context, reqData *model.UserUseScenes) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.UserUseScenes{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	if err == nil {
		m.DelCache(c, reqData.DigitalUserId, reqData.AiRoleId)
	}
	return
}

func (m *UserUseScenesService) GetById(c *gin.Context, id uint) (res *model.UserUseScenes, err error) {
	err = global.DB.Model(&model.UserUseScenes{}).Where("id = ?", id).First(&res).Error
	if err == nil {
		m.SetCache(c, res)
	}
	return
}

func (m *UserUseScenesService) SearchOne(ctx context.Context, reqData *model.UserUseScenes) (res *model.UserUseScenes, err error) {
	res, err = m.GetCache(ctx, reqData.DigitalUserId, reqData.AiRoleId)
	if err != nil {
		db := global.DB.Model(&model.UserUseScenes{})
		if reqData.DigitalUserId != 0 {
			db = db.Where("digital_user_id = ?", reqData.DigitalUserId)
		}
		if reqData.SceneId != 0 {
			db = db.Where("scene_id = ?", reqData.SceneId)
		}
		if reqData.AiRoleId != 0 {
			db = db.Where("ai_role_id = ?", reqData.AiRoleId)
		}
		err = db.First(&res).Error
		if err == nil {
			m.SetCache(ctx, res)
		}
	}
	return
}

func (m *UserUseScenesService) CacheKey(userId, aiId uint) string {
	return fmt.Sprintf("user_use_scenes:%d:%d", aiId, userId)
}

func (m *UserUseScenesService) SetCache(ctx context.Context, record *model.UserUseScenes) {
	var (
		err           error
		recordBytes   []byte
		cacheRedisKey = m.CacheKey(record.DigitalUserId, record.AiRoleId)
	)
	recordBytes, err = json.Marshal(record)
	if err != nil {
		global.LOG.Error("SetCache", zap.Error(err))
	}
	recordStr := string(recordBytes)
	if err = global.REDIS.SetEx(ctx, cacheRedisKey, recordStr, time.Hour).Err(); err != nil {
		global.LOG.Error("SetCache", zap.Error(err))
	}
	return
}

func (m *UserUseScenesService) DelCache(ctx context.Context, userId, aiId uint) {
	var (
		cacheRedisKey = m.CacheKey(userId, aiId)
	)
	global.REDIS.Del(ctx, cacheRedisKey, cacheRedisKey)
	return
}

func (m *UserUseScenesService) GetCache(ctx context.Context, userId, aiId uint) (record *model.UserUseScenes, err error) {
	var (
		cacheRedisStr string
		cacheRedisKey = m.CacheKey(userId, aiId)
	)
	if cacheRedisStr, err = global.REDIS.Get(ctx, cacheRedisKey).Result(); err != nil {
		return
	}
	if err = json.Unmarshal([]byte(cacheRedisStr), &record); err != nil {
		return
	}
	return
}

func (m *UserUseScenesService) GetAll() (resList []*model.UserUseScenes, err error) {
	err = global.DB.Model(&model.UserUseScenes{}).Find(&resList).Error
	return
}

func (m *UserUseScenesService) GetList(info req.UserUseScenesSearch) (resList []*model.UserUseScenes, total int64, err error) {
	db := global.DB.Model(&model.UserUseScenes{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if info.SceneId != 0 {
		db = db.Where("scene_id = ?", info.SceneId)
	}
	if info.AiRoleId != 0 {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
