package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/utils"
	"context"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"path/filepath"
	"strings"
	"time"
)

func (m *ImgToImgTrainService) SetRequireImgTask(reqData *request.RequireImgReq) {
	var (
		ctx      = context.Background()
		redisKey = global.REQUIRE_IMG_TRAIN_KEY
	)
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Panic("SetRequireImgTask panic", zap.Any("err", err))
		}
	}()
	reqDataBytes, jsonErr := json.Marshal(reqData)
	if jsonErr != nil {
		return
	}
	reqDataStr := string(reqDataBytes)
	global.REDIS.ZAdd(ctx, redisKey, redis.Z{
		Score:  float64(time.Now().Unix()),
		Member: reqDataStr,
	})

}
func (m *ImgToImgTrainService) RequireImgTrain() {
	var (
		ctx      = context.Background()
		redisKey = global.REQUIRE_IMG_TRAIN_KEY
	)
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Panic("RequireImgTrain panic", zap.Any("err", err))
		}
	}()
	for {
		time.Sleep(time.Millisecond * 500)
		redisMsgList, err := global.REDIS.ZRange(ctx, redisKey, 0, -1).Result()
		if err != nil {
			global.LOG.Error("ZRevRange error", zap.Any("err", err))
			continue
		}
		if len(redisMsgList) == 0 {
			continue
		}
		var (
			imgReq  *request.RequireImgReq
			taskStr = redisMsgList[0]
		)
		if _, err = global.REDIS.ZRem(ctx, redisKey, taskStr).Result(); err != nil {
			global.LOG.Error("删除消息key出错", zap.Any("err", err), zap.String("taskStr", taskStr))
		}
		if err = json.Unmarshal([]byte(taskStr), &imgReq); err != nil {
			continue
		}

		if err = m.requireImgTaskTrain(ctx, imgReq); err != nil {
			global.LOG.Error("requireImgTaskTrain error", zap.Any("err", err), zap.Any("imgReq", imgReq))
		}
	}
}

func (m *ImgToImgTrainService) GetBodyImgUndressImgPath(ctx context.Context, bodyImgKey string) (undressImgKey string) {
	var (
		s3ClientV2 = GetS3ClientV2(ctx)
	)
	imgExt := filepath.Ext(bodyImgKey)
	undressImgKey = bodyImgKey[:len(bodyImgKey)-len(imgExt)] + "_undress" + imgExt
	isExist := s3ClientV2.FileExists(undressImgKey)
	if !isExist {
		// 文件不存在,返回空
		return ""
	}
	return
}

func (m *ImgToImgTrainService) GetSetImgToUndressRdsKey(aiRoleInfo *model.AiRole) string {
	return fmt.Sprintf("img_to_undress:%d", aiRoleInfo.Id)
}

func (m *ImgToImgTrainService) SetImgToUndress(ctx context.Context, aiRoleInfo *model.AiRole, imgPubPathS3, undressPubPathS3 string) (err error) {
	rdsKey := m.GetSetImgToUndressRdsKey(aiRoleInfo)
	err = global.REDIS.HSet(ctx, rdsKey, imgPubPathS3, undressPubPathS3).Err()
	return
}

func (m *ImgToImgTrainService) GetImgToUndress(ctx context.Context, aiRoleInfo *model.AiRole, imgPubPathS3 string) (undressPubPathS3 string) {
	rdsKey := m.GetSetImgToUndressRdsKey(aiRoleInfo)
	undressPubPathS3 = global.REDIS.HGet(ctx, rdsKey, imgPubPathS3).Val()
	return
}

func (m *ImgToImgTrainService) requireImgTaskTrain(ctx context.Context, imgReq *request.RequireImgReq) (err error) {
	var (
		imgPubPathS3     string
		undressPubPathS3 string
		userInfo         *model.DigitalUser
		userSer          DigitalUserService
		aiRoleInfo       *model.AiRole
		aiRoleSer        AiRoleService
		msgSer           MsgService
		bodyImgCacheSer  AiBodyImgCacheService
		userIntimateSer  UsersIntimateService
		aiSendMediaSer   AiSendMediaService
		canUndress       bool
	)
	gLog := global.LOG.With(zap.String("func", "requireImgTaskTrain"), zap.String("business", "requireImgTaskTrain"), zap.Any("imgReq", imgReq))
	gLog.Info("requireImgTaskTrain start")

	undressImgKey := m.GetBodyImgUndressImgPath(ctx, imgReq.BodyImgKey)

	// 获取用户信息
	if userInfo, err = userSer.GetById(ctx, imgReq.UserId); err != nil {
		gLog.Error("userSer.GetById error", zap.Any("err", err))
		return
	}
	isAudit := userInfo.IsAudit()
	isSub := userInfo.IsSub()
	isSVip := userInfo.IsSVip()
	appInfo := global.AppMap[userInfo.AppID]
	// 获取AI角色信息
	if aiRoleInfo, err = aiRoleSer.GetByRoleId(imgReq.AiRoleId); err != nil {
		gLog.Error("aiRoleSer.GetByRoleId error", zap.Any("err", err))
		return
	}
	bodyImgItem, bodyImgItemExist := global.AllAiBodyImg[imgReq.BodyImgId]
	if !bodyImgItemExist {
		err = fmt.Errorf("bodyImgId(%d) not found", imgReq.BodyImgId)
		return
	}

	// 获取奖励解锁图片次数
	rewardUnlockImgCount, _ := msgSer.GetRewardUnlockImgCount(userInfo.Id, aiRoleInfo.Id)
	// 查询数据库是否生成过,
	cacheRes, cacheResErr := bodyImgCacheSer.SearchOne(ctx, &model.AiBodyImgCache{
		AiId:       aiRoleInfo.Id,
		AiRoleId:   aiRoleInfo.RoleID,
		BodyImgKey: imgReq.BodyImgKey,
	})
	if cacheResErr == nil {
		// 不需要再次调用换脸
		imgPubPathS3 = cacheRes.BodyImgFullUrl
		undressPubPathS3 = cacheRes.UndressImgFullUrl
		if cacheRes.UndressImgFullUrl == "" && undressImgKey != "" {
			if undressPubPathS3, err = m.Img2ImgReplaceFaceRequireImg(aiRoleInfo.Avatar, undressImgKey); err != nil {
				gLog.Error("Img2ImgReplaceFaceV2 undressImgKey error", zap.Any("err", err))
				sendDDMsg := fmt.Sprintf("索要照片换Undress图片任务失败,error: %v", err.Error())
				SendDDNotice(sendDDMsg)
				return
			}
			if err = m.SetImgToUndress(ctx, aiRoleInfo, imgPubPathS3, undressPubPathS3); err != nil {
				return
			}
		}
	} else {
		// 需要调用换脸
		if imgPubPathS3, err = m.Img2ImgReplaceFaceRequireImg(aiRoleInfo.Avatar, imgReq.BodyImgKey); err != nil {
			gLog.Error("Img2ImgReplaceFaceV2 error", zap.Any("err", err))
			sendDDMsg := fmt.Sprintf("1索要照片任务失败,error: %v", err.Error())
			SendDDNotice(sendDDMsg)
			return
		}
		if undressImgKey != "" {
			if undressPubPathS3, err = m.Img2ImgReplaceFaceRequireImg(aiRoleInfo.Avatar, undressImgKey); err != nil {
				gLog.Error("Img2ImgReplaceFaceV2 undressImgKey error", zap.Any("err", err))
				sendDDMsg := fmt.Sprintf("2索要照片任务失败,error: %v", err.Error())
				SendDDNotice(sendDDMsg)
				return
			}
			if err = m.SetImgToUndress(ctx, aiRoleInfo, imgPubPathS3, undressPubPathS3); err != nil {
				return
			}
		}
	}
	// 发送消息
	msgText := utils.RandList(global.BodyImgMsgList)
	msgText = strings.ReplaceAll(msgText, "__", bodyImgItem.BodyName)
	now := time.Now()
	aiSendMsgInfo := &model.MsgInfo{
		MessageID:    uuid.NewString(),
		Channel:      nil,
		SendTime:     now.UnixMilli(),
		DeletedAt:    nil,
		MessageType:  model.MsgTypeTxtImg,
		SenderID:     aiRoleInfo.RoleID,
		SenderName:   aiRoleInfo.Nickname,
		ReceiverID:   fmt.Sprintf("%d", userInfo.Id),
		BodyImgKey:   imgReq.BodyImgKey,
		ReceiverName: userInfo.Nickname,
		MessageContent: model.MessageContent{
			Avatar:            utils.S3Url(aiRoleInfo.Avatar, userInfo.AppID),
			Text:              msgText,
			OriginText:        msgText,
			CanUndress:        false,
			BodyImgKey:        imgReq.BodyImgKey,
			BodyUndressResKey: undressPubPathS3,
			ImgURL:            utils.S3Url(imgPubPathS3, userInfo.AppID),
			UnlockState:       model.UnlockStateNo,
		},
	}
	// 是否可以undress
	if !isAudit {
		canUndress = undressImgKey != ""
	}
	if canUndress {
		var (
			intimateInfo *model.UsersIntimate
		)
		// 获取亲密值记录
		if intimateInfo, err = userIntimateSer.GetByUidAndRoleid(userInfo.Id, aiRoleInfo.Id); err != nil {
			return
		}
		// 未订阅用户且亲密等级小于5不可以undress
		if intimateInfo.Level < 5 {
			canUndress = false
		} else {
			if !isSub && !isSVip {
				canUndress = false
			}
		}
	}
	aiSendMsgInfo.MessageContent.CanUndress = canUndress
	if rewardUnlockImgCount > 0 {
		aiSendMsgInfo.MessageContent.Free = true
		// 减少解锁次数
		_ = msgSer.RewardUnlockImg(userInfo.Id, aiRoleInfo.Id, -1)
	}
	_ = msgSer.SetUserBodyImgArr(userInfo.Id, aiRoleInfo.Id, imgReq.BodyImgKey)
	_ = bodyImgCacheSer.Save(ctx, &model.AiBodyImgCache{
		AiId:              aiRoleInfo.Id,
		AiRoleId:          aiRoleInfo.RoleID,
		BodyImgKey:        imgReq.BodyImgKey,
		UndressImgKey:     undressImgKey,
		BodyImgFullUrl:    imgPubPathS3,
		UndressImgFullUrl: undressPubPathS3,
	})

	if err = msgSer.SendMsgToAgora(aiSendMsgInfo, appInfo); err != nil {
		return
	}
	// 保存发送过的记录
	aiSendMediaRecord := model.AiSendMedia{
		DigitalUserId: userInfo.Id,
		AiRoleId:      aiRoleInfo.Id,
		MediaType:     model.MediaTypeImg,
		MediaURL:      aiSendMsgInfo.MessageContent.ImgURL,
		ThumbnailURL:  aiSendMsgInfo.MessageContent.ImgURL,
		CanUndress:    canUndress,
		AsmrName:      "",
		SendTime:      now,
		UnlockState:   model.UnlockStateNo,
		UnlockTime:    nil,
	}
	aiSendMediaSer.SaveByMsg(&aiSendMediaRecord)
	gLog.Info("requireImgTaskTrain success")
	return
}
