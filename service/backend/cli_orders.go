package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/model/cli/td"
	"aimsg-server/service/backend/call"
	"aimsg-server/utils"
	"aimsg-server/utils/notify"
	"context"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"git.costnovel.com/center/middleware-sdk/thinkdata"
	"github.com/awa/go-iap/appstore"
	"github.com/awa/go-iap/playstore"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"google.golang.org/api/androidpublisher/v3"
	"gorm.io/gorm"
)

func (m *OrdersService) SendTdIapServer(userInfo *model.DigitalUser, properties map[string]interface{}) {
	defer func() {
		if aErr := recover(); aErr != nil {
			global.LOG.Error("SendTd panic", zap.Any("err", aErr))
		}
	}()
	appInfo, appInfoExist := global.AppMap[userInfo.AppID]
	if !appInfoExist {
		global.LOG.Error("appInfo not exist", zap.Any("userInfo", userInfo))
		return
	}
	sendTdData := thinkdata.SendThinkData{
		IsDebug:    global.CONFIG.System.Env != "public",
		ServerURL:  "https://ta.aipersona.cloud",
		AppId:      appInfo.ThinkData.Data().AppId,
		EventName:  "jary_test",
		AccountId:  fmt.Sprintf("%d", userInfo.Id),
		DistinctId: fmt.Sprintf("%d", userInfo.Id),
		Properties: properties,
	}
	if err := thinkdata.SendData(sendTdData); err != nil {
		global.LOG.Error("SendThinkData fail", zap.Error(err))
		return
	}
}

func (m *OrdersService) AdjustSendErrDDNotify(adjustRec *model.AdjustLog, apiLog string) {
	var (
		err     error
		resBody string
		data    = notify.Message{Msgtype: ""}
	)
	appInfo := global.AppMap[adjustRec.AppID]
	msgText := fmt.Sprintf(`** <font color=#FF0000>【❗❗❗发送adjust失败❗❗❗】</font> **
- [AdjustLog ID: %d](https://aiadmin.aigirlchat.net/#/layout/backendOrders/adjustLog?id=%d)
- [应用名称: %s](https://aiadmin.aigirlchat.net/#/layout/backend_app/app)
- [应用ID: %d](https://aiadmin.aigirlchat.net/#/layout/backend_app/app)
- [订单编号: %s](https://aiadmin.aigirlchat.net/#/layout/backendOrders/orders?order_no=%s)
- 请求日志: %s
`,
		adjustRec.Id, adjustRec.Id,
		appInfo.Name,
		appInfo.Id,
		adjustRec.OrderNo, adjustRec.OrderNo,
		apiLog,
	)
	data.Markdown.Title = "发送adjust失败"
	data.Msgtype = "markdown"
	data.Markdown.Text = msgText
	data.At.AtMobiles = global.DiyDDAtMobiles
	resBody, err = notify.SendDingTalk(data, global.CONFIG.DDnotify.Token, global.CONFIG.DDnotify.Secret)
	if err != nil {
		global.LOG.Error("发送钉钉通知 error", zap.Any("err", err), zap.String("resBody", resBody))
		return
	}
}

func (m *OrdersService) GetPartnerParams(userInfo *model.DigitalUser, profileInfo *model.DigitalUserProfile) (params map[string]interface{}) {
	otherInfo := profileInfo.OtherInfo.Data()
	ipInfoRes, ipInfoErr := utils.GetIpInfoByIpGeoApi(userInfo.LoginIP)
	if ipInfoErr != nil {
		return
	}
	city := strings.Replace(ipInfoRes.Data.City, " ", "", -1)
	city = strings.ToLower(city)
	country := strings.ToLower(ipInfoRes.Data.Country)
	province := strings.ToLower(ipInfoRes.Data.Province)
	firstName := strings.ToLower(otherInfo.FirstName)
	lastName := strings.ToLower(otherInfo.LastName)
	email := strings.ToLower(otherInfo.Email)
	phone := strings.ToLower(otherInfo.Phone)
	params = map[string]interface{}{
		"city":       fmt.Sprintf("%x", sha256.Sum256([]byte(city))),
		"state":      fmt.Sprintf("%x", sha256.Sum256([]byte(province))),
		"country":    fmt.Sprintf("%x", sha256.Sum256([]byte(country))),
		"post_code":  fmt.Sprintf("%x", sha256.Sum256([]byte(ipInfoRes.Data.Zipcode))),
		"ip_address": userInfo.LoginIP,
	}
	if email != "" && utils.CheckIsEmail(email) {
		params["email_address"] = fmt.Sprintf("%x", sha256.Sum256([]byte(email)))
	}
	if phone != "" && len(phone) > 1 {
		params["phone"] = fmt.Sprintf("%x", sha256.Sum256([]byte(phone)))
	}
	if firstName != "" && len(firstName) > 1 {
		params["name"] = fmt.Sprintf("%x", sha256.Sum256([]byte(firstName)))
	}
	if lastName != "" && len(lastName) > 1 {
		params["last_name"] = fmt.Sprintf("%x", sha256.Sum256([]byte(lastName)))
	}
	if userInfo.Gender == model.GenderTypeFemale {
		params["gender"] = fmt.Sprintf("%x", sha256.Sum256([]byte("f"))) // 女性
	} else {
		params["gender"] = fmt.Sprintf("%x", sha256.Sum256([]byte("m"))) // 男性
	}
	params["date_of_birth"] = fmt.Sprintf("%x", sha256.Sum256([]byte(otherInfo.Birthday)))
	return
}

func (m *OrdersService) SendAdjust(userInfo *model.DigitalUser, profileInfo *model.DigitalUserProfile, revenue decimal.Decimal, currency string, atUnix int64, orderTest bool, orderNo string, isFreeSub bool) {
	var (
		err       error
		apiLog    string
		curlStr   string
		adjustSer AdjustLogService
	)
	sendRevenue := revenue
	defer func() {
		if aErr := recover(); aErr != nil {
			global.LOG.Error("SendAdjust panic", zap.Any("err", aErr))
		}
	}()
	// 发送adjust日志
	// 获取App信息
	appInfo, _ := global.AppMap[userInfo.AppID]

	eventToken := appInfo.AdjustData.Data().EventToken

	// 上报收入=（实际收入-15%手续费）*50%
	// sendRevenue = sendRevenue.Sub(sendRevenue.Mul(decimal.NewFromFloat(0.15))).Mul(decimal.NewFromFloat(0.5))

	// 上报收入=（实际收入-30%手续费) (2025-06-24)
	sendRevenue = sendRevenue.Sub(sendRevenue.Mul(decimal.NewFromFloat(0.3)))

	if userInfo.AppID == 1 || userInfo.AppID == 2 {
		sendRevenue = revenue
	}

	if isFreeSub {
		sendRevenue = decimal.Zero
	}
	reqData := map[string]interface{}{
		"created_at_unix": atUnix,
		"app_token":       appInfo.AdjustData.Data().AppToken,
		"event_token":     eventToken,
		"revenue":         sendRevenue.String(),
		"currency":        currency,
		"s2s":             1,
	}
	// 测试订单上报到测试环境
	if orderTest {
		reqData["environment"] = "sandbox"
	}
	if userInfo.AdjustIdData.Data().Idfa != "" {
		reqData["idfa"] = userInfo.AdjustIdData.Data().Idfa
	}
	if userInfo.AdjustIdData.Data().Idfv != "" {
		reqData["idfv"] = userInfo.AdjustIdData.Data().Idfv
	}
	if userInfo.AdjustIdData.Data().Adid != "" {
		reqData["adid"] = userInfo.AdjustIdData.Data().Adid
	}
	if userInfo.AdjustIdData.Data().WebUUID != "" {
		reqData["web_uuid"] = userInfo.AdjustIdData.Data().WebUUID
	}
	if userInfo.AdjustIdData.Data().GpsAdid != "" {
		reqData["gps_adid"] = userInfo.AdjustIdData.Data().GpsAdid
	}
	reqData["ip_address"] = userInfo.LoginIP
	params := m.GetPartnerParams(userInfo, profileInfo)
	if params != nil {
		paramsBytes, paramsJsonErr := json.Marshal(params)
		if paramsJsonErr == nil {
			reqData["partner_params"] = string(paramsBytes)
		}
	}
	// 保存日志
	adjustRecord := model.AdjustLog{
		AppID:         userInfo.AppID,
		OrderNo:       orderNo,
		DigitalUserID: userInfo.Id,
		ReqData:       reqData,
		Revenue:       revenue,
		Currency:      currency,
		ResStatus:     0,
		ResContent:    "",
		AdjustToken:   eventToken,
	}
	if err = adjustSer.Create(&adjustRecord); err != nil {
		return
	}
	// 发送adjust日志,最大重试3次,如果都不成功,就钉钉通知,并且记录失败日志
	for i := 0; i < 3; i++ {
		if apiLog, curlStr, err = utils.HttpPostForm(global.AdjustUrl, reqData, nil, 10); err != nil {
			global.LOG.Error("发送adjust日志失败", zap.Error(err), zap.String("apiLog", apiLog), zap.String("curlStr", curlStr))
			adjustRecord.ResStatus = 400
		} else {
			adjustRecord.ResStatus = 200
			break
		}
		i++
	}
	if adjustRecord.ResStatus == 400 {
		m.AdjustSendErrDDNotify(&adjustRecord, apiLog)
	}
	adjustRecord.ResContent = apiLog
	if err = adjustSer.Update(&adjustRecord); err != nil {
		return
	}
	return
}

func (m *OrdersService) SendAdjustOther(userInfo *model.DigitalUser, profileInfo *model.DigitalUserProfile, revenue decimal.Decimal, currency string, atUnix int64, orderTest bool, orderNo string, eventToken string, isFreeSub bool) {
	var (
		err       error
		apiLog    string
		curlStr   string
		adjustSer AdjustLogService
	)
	if eventToken == "" {
		return
	}
	sendRevenue := revenue
	defer func() {
		if aErr := recover(); aErr != nil {
			global.LOG.Error("SendAdjustOther panic", zap.Any("err", aErr))
		}
	}()
	// 发送adjust日志
	// 获取App信息
	appInfo, _ := global.AppMap[userInfo.AppID]

	// 上报收入=（实际收入-15%手续费）*50%
	// sendRevenue = sendRevenue.Sub(sendRevenue.Mul(decimal.NewFromFloat(0.15))).Mul(decimal.NewFromFloat(0.5))

	// 上报收入=（实际收入-30%手续费) (2025-06-24)
	sendRevenue = sendRevenue.Sub(sendRevenue.Mul(decimal.NewFromFloat(0.3)))

	if userInfo.AppID == 1 || userInfo.AppID == 2 {
		sendRevenue = revenue
	}

	if isFreeSub {
		sendRevenue = decimal.Zero
	}
	reqData := map[string]interface{}{
		"created_at_unix": atUnix,
		"app_token":       appInfo.AdjustData.Data().AppToken,
		"event_token":     eventToken,
		"revenue":         sendRevenue.String(),
		"currency":        currency,
		"s2s":             1,
	}
	// 测试订单上报到测试环境
	if orderTest {
		reqData["environment"] = "sandbox"
	}
	if userInfo.AdjustIdData.Data().Idfa != "" {
		reqData["idfa"] = userInfo.AdjustIdData.Data().Idfa
	}
	if userInfo.AdjustIdData.Data().Idfv != "" {
		reqData["idfv"] = userInfo.AdjustIdData.Data().Idfv
	}
	if userInfo.AdjustIdData.Data().Adid != "" {
		reqData["adid"] = userInfo.AdjustIdData.Data().Adid
	}
	if userInfo.AdjustIdData.Data().WebUUID != "" {
		reqData["web_uuid"] = userInfo.AdjustIdData.Data().WebUUID
	}
	if userInfo.AdjustIdData.Data().GpsAdid != "" {
		reqData["gps_adid"] = userInfo.AdjustIdData.Data().GpsAdid
	}
	reqData["ip_address"] = userInfo.LoginIP
	params := m.GetPartnerParams(userInfo, profileInfo)
	if params != nil {
		paramsBytes, paramsJsonErr := json.Marshal(params)
		if paramsJsonErr == nil {
			reqData["partner_params"] = string(paramsBytes)
		}
	}
	// 保存日志
	adjustRecord := model.AdjustLog{
		AppID:         userInfo.AppID,
		OrderNo:       orderNo,
		DigitalUserID: userInfo.Id,
		ReqData:       reqData,
		Revenue:       revenue,
		Currency:      currency,
		ResStatus:     0,
		ResContent:    "",
		AdjustToken:   eventToken,
	}
	if err = adjustSer.Create(&adjustRecord); err != nil {
		return
	}
	// 发送adjust日志,最大重试3次,如果都不成功,就钉钉通知,并且记录失败日志
	for i := 0; i < 3; i++ {
		if apiLog, curlStr, err = utils.HttpPostForm(global.AdjustUrl, reqData, nil, 10); err != nil {
			global.LOG.Error("发送adjust日志失败", zap.Error(err), zap.String("apiLog", apiLog), zap.String("curlStr", curlStr))
			adjustRecord.ResStatus = 400
		} else {
			adjustRecord.ResStatus = 200
			break
		}
		i++
	}
	if adjustRecord.ResStatus == 400 {
		m.AdjustSendErrDDNotify(&adjustRecord, fmt.Sprintf("单独商品上报adjust失败,请求日志是%s", apiLog))
	}
	adjustRecord.ResContent = apiLog
	if err = adjustSer.Update(&adjustRecord); err != nil {
		return
	}
	return
}

func (m *OrdersService) UserIsSubRecentHours(userId uint, hours int) (succOrders []*model.Order, err error) {
	// 查询用户在最近的小时内,是否有普通订阅的成功订单
	var (
		nowTime = time.Now()
	)
	hoursAgo := nowTime.Add(-time.Hour * time.Duration(hours))
	if err = global.DB.Raw("SELECT o.* FROM orders AS o LEFT JOIN product AS p ON o.product_id = p.id WHERE o.created_at >= ? AND o.digital_user_id = ? AND (p.product_type = 1 OR p.product_type = 2 OR p.product_type = 3) AND o.order_status = ? ORDER BY o.id DESC", hoursAgo, userId, model.OSPayGoodSuccess).Find(&succOrders).Error; err != nil {
		return
	}
	return

}

func (m *OrdersService) SendAdjustSvip(orderNo string, isFreeSub bool) {
	var (
		err         error
		apiLog      string
		curlStr     string
		succOrders  []*model.Order
		orderInfo   *model.Order
		productObj  *model.Product
		userInfo    *model.DigitalUser
		profileInfo *model.DigitalUserProfile
		adjustSer   AdjustLogService
		productSer  ProductService
		userSer     DigitalUserService
		profileSer  DigitalUserProfileService
		ctx         = context.Background()
	)
	defer func() {
		if aErr := recover(); aErr != nil {
			global.LOG.Error("SendAdjustSvip panic", zap.Any("err", aErr))
		}
	}()
	orderInfo, err = m.GetByOrderNo(orderNo)
	if err != nil {
		global.LOG.Error("SendAdjustSvip.GetByOrderNo fail", zap.Error(err))
		return
	}
	revenue := orderInfo.USDPrice
	atUnix := time.Now().Unix()
	if orderInfo.PayTime != nil {
		atUnix = orderInfo.PayTime.Unix()
	}
	currency := "USD"
	productObj, err = productSer.GetById(orderInfo.ProductID)
	if err != nil {
		global.LOG.Error("SendAdjustSvip productSer.GetById fail", zap.Error(err))
		return
	}
	eventToken := productObj.AdjustToken
	if eventToken == "" {
		return
	}
	userInfo, err = userSer.GetById(ctx, orderInfo.DigitalUserID)
	if err != nil {
		global.LOG.Error("SendAdjustSvip userSer.GetById fail", zap.Error(err))
		return
	}
	if profileInfo, err = profileSer.GetByUserId(orderInfo.DigitalUserID); err != nil {
		global.LOG.Error("SendAdjustSvip profileSer.GetByUserId fail", zap.Error(err))
		return
	}

	// 查询最近24小时内是否订阅了普通订阅
	if succOrders, err = m.UserIsSubRecentHours(userInfo.Id, 24); err != nil {
		global.LOG.Error("SendAdjustSvip UserIsSubRecentHours fail", zap.Error(err))
		return
	}
	if len(succOrders) == 0 {
		global.LOG.Info("SendAdjustSvip UserIsSubRecentHours not sub", zap.Uint("userId", userInfo.Id))
		return
	}
	// 最近一笔订阅
	latestSubOrder := succOrders[0]
	// 上报金额加上最近一笔的订阅金额
	revenue = revenue.Add(latestSubOrder.USDPrice)

	sendRevenue := revenue

	// 发送adjust日志
	// 获取App信息
	appInfo, _ := global.AppMap[userInfo.AppID]

	// 上报收入=（实际收入-15%手续费）*50%
	// sendRevenue = sendRevenue.Sub(sendRevenue.Mul(decimal.NewFromFloat(0.15))).Mul(decimal.NewFromFloat(0.5))

	// 上报收入=（实际收入-30%手续费) (2025-06-24)
	sendRevenue = sendRevenue.Sub(sendRevenue.Mul(decimal.NewFromFloat(0.3)))

	if userInfo.AppID == 1 || userInfo.AppID == 2 {
		sendRevenue = revenue
	}

	if isFreeSub {
		sendRevenue = decimal.Zero
	}

	reqData := map[string]interface{}{
		"created_at_unix": atUnix,
		"app_token":       appInfo.AdjustData.Data().AppToken,
		"event_token":     eventToken,
		"revenue":         sendRevenue.String(),
		"currency":        currency,
		"s2s":             1,
	}
	// 测试订单上报到测试环境
	if orderInfo.OrderTest {
		reqData["environment"] = "sandbox"
	}
	if userInfo.AdjustIdData.Data().Idfa != "" {
		reqData["idfa"] = userInfo.AdjustIdData.Data().Idfa
	}
	if userInfo.AdjustIdData.Data().Idfv != "" {
		reqData["idfv"] = userInfo.AdjustIdData.Data().Idfv
	}
	if userInfo.AdjustIdData.Data().Adid != "" {
		reqData["adid"] = userInfo.AdjustIdData.Data().Adid
	}
	if userInfo.AdjustIdData.Data().WebUUID != "" {
		reqData["web_uuid"] = userInfo.AdjustIdData.Data().WebUUID
	}
	if userInfo.AdjustIdData.Data().GpsAdid != "" {
		reqData["gps_adid"] = userInfo.AdjustIdData.Data().GpsAdid
	}
	reqData["ip_address"] = userInfo.LoginIP
	params := m.GetPartnerParams(userInfo, profileInfo)
	if params != nil {
		paramsBytes, paramsJsonErr := json.Marshal(params)
		if paramsJsonErr == nil {
			reqData["partner_params"] = string(paramsBytes)
		}
	}
	// 保存日志
	adjustRecord := model.AdjustLog{
		AppID:         userInfo.AppID,
		OrderNo:       orderNo,
		DigitalUserID: userInfo.Id,
		ReqData:       reqData,
		Revenue:       revenue,
		Currency:      currency,
		ResStatus:     0,
		ResContent:    "",
		AdjustToken:   eventToken,
	}
	if err = adjustSer.Create(&adjustRecord); err != nil {
		return
	}
	// 发送adjust日志,最大重试3次,如果都不成功,就钉钉通知,并且记录失败日志
	for i := 0; i < 3; i++ {
		if apiLog, curlStr, err = utils.HttpPostForm(global.AdjustUrl, reqData, nil, 10); err != nil {
			global.LOG.Error("发送adjust日志失败", zap.Error(err), zap.String("apiLog", apiLog), zap.String("curlStr", curlStr))
			adjustRecord.ResStatus = 400
		} else {
			adjustRecord.ResStatus = 200
			break
		}
		i++
	}
	if adjustRecord.ResStatus == 400 {
		m.AdjustSendErrDDNotify(&adjustRecord, fmt.Sprintf("单独商品上报adjust失败,请求日志是%s", apiLog))
	}
	adjustRecord.ResContent = apiLog
	if err = adjustSer.Update(&adjustRecord); err != nil {
		return
	}
	return
}

func (m *OrdersService) OrderPay(c *gin.Context, reqData *request.OrderPayReq) (res response.OrderPayRes, err error) {
	var (
		payUrl             string
		currency           string
		countryCode        string
		userInfo           *model.DigitalUser
		aiRoleInfo         *model.AiRole
		productInfo        *model.Product
		appThirdPayControl *model.AppThirdPayControl
		productService     ProductService
		userService        DigitalUserService
		aiRoleService      AiRoleService
		userId             = utils.GetDigitalUserID(c)
		now                = time.Now()
		nowTimestamp       = now.UnixMilli()
		orderNo            = fmt.Sprintf("%d%08d", nowTimestamp, userId)
	)
	clientIp := c.ClientIP()
	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		global.LOG.Info("OrderPay GetById ", zap.Any("userId", userId), zap.Error(err))
		return
	}
	appInfo := global.AppMap[userInfo.AppID]
	// 获取商品信息
	if productInfo, err = productService.GetById(reqData.ProductId); err != nil {
		global.LOG.Info("OrderPay  productService GetById ",
			zap.Any("userId", userId), zap.Any("productId", reqData.ProductId), zap.Error(err))
		return
	}
	// 创建订单
	orderInfo := model.Order{
		AppID:         userInfo.AppID,
		PayMethodID:   reqData.PayMethodId,
		DigitalUserID: userInfo.Id,
		ProductID:     productInfo.Id,
		OrderStatus:   model.OSWaitPay,
		OrderNo:       orderNo,
		PayAmount:     productInfo.USDPrice,
		USDPrice:      productInfo.USDPrice,
		Version:       userInfo.Version,
	}
	if reqData.AiRoleId != "" {
		if aiRoleInfo, err = aiRoleService.GetByRoleId(reqData.AiRoleId); err != nil {
			global.LOG.Info("OrderPay iRoleService.GetByRoleId error",
				zap.Any("userId", userId), zap.Any("AiRoleId", reqData.AiRoleId), zap.Error(err))
			return
		}
		orderInfo.AiRoleId = aiRoleInfo.Id
	}

	err = global.DB.Transaction(func(tx *gorm.DB) (tErr error) {
		if tErr = tx.Model(&model.Order{}).Create(&orderInfo).Error; tErr != nil {
			return
		}
		// 商品购买次数+1
		if tErr = tx.Model(&model.Product{}).Where("id = ?", productInfo.Id).Update("buy_count", gorm.Expr("buy_count + ?", 1)).Error; tErr != nil {
			return
		}
		return
	})
	if err != nil {
		global.LOG.Info("OrderPay db error", zap.Any("userId", userId), zap.Error(err))
		return
	}

	var defaultRes = response.OrderPayRes{
		Id:      orderInfo.Id,
		OrderNo: orderInfo.OrderNo,
		PayUrl:  "",
	}

	if reqData.PayMethodId == 10000 || reqData.PayMethodId == 1000 || reqData.PayMethodId == 2000 {
		global.LOG.Error("PayMethodId is not valid", zap.Any("PayMethodId", reqData.PayMethodId))
		return defaultRes, nil
	}

	getAppControlErr := global.DB.Model(&model.AppThirdPayControl{}).Where("app_id = ? and state = ?", orderInfo.AppID, model.ControlStateOpen).First(&appThirdPayControl).Error
	if err != nil {
		global.LOG.Error("getAppControlErr db error", zap.Any("appID", orderInfo.AppID), zap.Error(getAppControlErr))
	}

	global.LOG.Info("OrderPay debug", zap.Any("userId", userId), zap.Any("PayMethodId", reqData.PayMethodId))
	//如果配置不为空，那么则走支付中台
	if appThirdPayControl != nil && appThirdPayControl.Id > 0 {

		countryCode = reqData.CountryCode
		if countryCode == "" {
			ipInfo, ipGeoErr := GetIpInfoWithCache(clientIp)
			if ipGeoErr != nil {
				global.LOG.Error("GetIpInfoByIpGeoApi fail", zap.Error(ipGeoErr))
				return defaultRes, nil
			}
			countryCode = ipInfo.Data.Areacode
		}

		if countryCode == "" {
			global.LOG.Error("country_code is required")
			return defaultRes, nil
		}

		currency = utils.GetCurrencyByCountryCode(countryCode)

		if reqData.PayMethodId == 0 {
			fmt.Errorf(fmt.Sprintf("AppId(%d)、国家码(%s)从支付中台获取到的商户ID为0", orderInfo.AppID, countryCode))
			return defaultRes, nil
		}

		if productInfo.GoodType == model.GTGood { //一次性商品
			payReq := request.PayCenterReq{
				BundleId:          appInfo.PackageName,
				AppId:             appInfo.Id,
				ProjectId:         global.PayCenterProjectId,
				UserId:            fmt.Sprintf("%d", userInfo.Id),
				UsdAmount:         orderInfo.USDPrice,
				ExchangeRate:      decimal.NewFromInt(1),
				OrderPrice:        orderInfo.USDPrice,
				OrderNo:           orderInfo.OrderNo,
				Currency:          currency,
				MethodId:          reqData.PayMethodId,
				CountryCode:       countryCode,
				ProductName:       productInfo.Name,
				ProductId:         productInfo.Id,
				ClientIp:          clientIp,
				RedirectUrl:       fmt.Sprintf("%s/Orders/PayResult?order_no=%s", global.CONFIG.System.UrlPrefix, orderInfo.OrderNo),
				NotifyUrl:         fmt.Sprintf("%s/Callback/PayCenter", global.CONFIG.System.UrlPrefix),
				TargetOrg:         "",
				PaymentMethodType: "",
				AppName:           appInfo.Name,
				Body:              productInfo.Name,
			}
			centerPayUrl, _, centerErr := m.GetUrlFromPayCenter(payReq)
			if centerErr != nil {
				global.LOG.Error("OrderPay GetUrlFromPayCenter fail", zap.Error(centerErr), zap.Any("payReq", payReq))
			}
			payUrl = centerPayUrl
		} else { //订阅
			subPayReq := request.PayCenterSubscriptReq{
				BundleId:     appInfo.PackageName,
				AppId:        appInfo.Id,
				ProjectId:    global.PayCenterProjectId,
				UserId:       fmt.Sprintf("%d", userInfo.Id),
				UsdAmount:    orderInfo.USDPrice,
				ExchangeRate: decimal.NewFromInt(1),
				OrderPrice:   orderInfo.USDPrice,
				OrderNo:      orderInfo.OrderNo,
				Currency:     currency,
				MethodId:     reqData.PayMethodId,
				CountryCode:  countryCode,
				SubscriptID:  productInfo.PaypalID,
				ClientIp:     clientIp,
				RedirectUrl:  fmt.Sprintf("%s/Orders/PayResult?order_no=%s", global.CONFIG.System.UrlPrefix, orderInfo.OrderNo),
				NotifyUrl:    fmt.Sprintf("%s/Callback/PayCenterSub", global.CONFIG.System.UrlPrefix),
			}
			centerPayUrl, apiLog, centerErr := m.GetSubUrlFromPayCenter(subPayReq)
			if centerErr != nil {
				global.LOG.Error("OrderPay GetUrlFromPayCenter fail2", zap.Any("userId", userId), zap.Error(centerErr), zap.String("apiLog", apiLog))
			}
			payUrl = centerPayUrl
		}

		if payUrl == "" {
			res = defaultRes
		} else {
			res = response.OrderPayRes{
				Id:      orderInfo.Id,
				OrderNo: orderInfo.OrderNo,
				PayUrl:  payUrl,
			}
		}
		return
	}

	res = defaultRes

	return
}

func (m *OrdersService) SubOrderLock(orderNo string) (lockRes bool) {
	var (
		ctx = context.Background()
	)
	lockRdsKey := fmt.Sprintf("sub_order_lock:%s", orderNo)
	lockRes, _ = global.REDIS.SetNX(ctx, lockRdsKey, 1, time.Second*10).Result()
	return
}

func (m *OrdersService) SubOrderUnLock(orderNo string) {
	var (
		ctx = context.Background()
	)
	lockRdsKey := fmt.Sprintf("sub_order_lock:%s", orderNo)
	global.REDIS.Del(ctx, lockRdsKey)
	return
}

func (m *OrdersService) GoogleQuery(c *gin.Context, reqData *request.GoogleQueryReq) (res gin.H, err error) {
	var (
		gooPayBytes    []byte
		tradeNo        string
		userInfo       *model.DigitalUser
		orderInfo      *model.Order
		productInfo    *model.Product
		gpClient       *playstore.Client
		userService    DigitalUserService
		productService ProductService
		expiresDate    time.Time
		orderTest      = false
	)
	gLog := global.LOG.With(zap.String("func", "GoogleQuery"), zap.Any("reqData", reqData), zap.String("business", "google"))
	defer m.SubOrderUnLock(reqData.OrderNo)

	// 加锁
	if !m.SubOrderLock(reqData.OrderNo) {
		gLog.Error("SubOrderLock fail")
		err = nil
		return
	}
	// 获取订单信息
	if orderInfo, err = m.GetByOrderNo(reqData.OrderNo); err != nil {
		gLog.Error("GetByOrderNo fail", zap.Error(err))
		return
	}
	// 权益已下发
	if orderInfo.OrderStatus == model.OSPayGoodSuccess {
		gLog.Info("OrderStatus is OSPayGoodSuccess")
		return
	}
	// 获取用户信息
	if userInfo, err = userService.GetByIdUnscoped(c, orderInfo.DigitalUserID); err != nil {
		gLog.Error("userService.GetById fail", zap.Error(err))
		return
	}
	// 获取App信息
	appInfo, _ := global.AppMap[userInfo.AppID]
	if gooPayBytes, err = json.Marshal(appInfo.GooPayJson); err != nil {
		gLog.Error("json.Marshal(appInfo.GooPayJson) fail", zap.Error(err))
		return
	}
	// 获取商品信息
	if productInfo, err = productService.GetById(orderInfo.ProductID); err != nil {
		gLog.Error("productService.GetById fail", zap.Error(err))
		return
	}
	if gpClient, err = playstore.New(gooPayBytes); err != nil {
		gLog.Error("playstore.New fail", zap.Error(err))
		return
	}
	// 验证交易
	switch productInfo.GoodType {
	case model.GTSub:
		// 订阅类商品验证交易
		// 字段说明文档 https://developers.google.com/android-publisher/api-ref/rest/v3/purchases.subscriptionsv2?hl=zh-cn
		var v2 *androidpublisher.SubscriptionPurchaseV2
		// 订阅类商品验证交易
		if v2, err = gpClient.VerifySubscriptionV2(c, appInfo.PackageName, reqData.PurchaseToken); err != nil {
			gLog.Error("VerifySubscriptionV2 fail", zap.Error(err))
			return
		}
		gLog = gLog.With(zap.Any("v2", v2))
		if v2.AcknowledgementState != "ACKNOWLEDGEMENT_STATE_ACKNOWLEDGED" {
			// 未确认交易
			err = fmt.Errorf("google query fail, not acknowledged")
			return
		}
		// 是否是测试订单
		if len(v2.LineItems) == 0 {
			gLog.Error("LineItems is empty")
			return
		}
		lineItem := v2.LineItems[0]
		if expiresDate, err = time.Parse(time.RFC3339, lineItem.ExpiryTime); err != nil {
			gLog.Error("time.Parse fail", zap.Error(err))
			return
		}
		gLog = gLog.With(zap.Time("expiresDate", expiresDate))
		orderTest = v2.TestPurchase != nil
		gLog = gLog.With(zap.Bool("orderTest", orderTest))
		tradeNo = v2.LatestOrderId
	case model.GTGood:
		// 一次性购买类商品验证交易
		var verifyProduct *androidpublisher.ProductPurchase
		// 一次性购买类商品验证交易
		if verifyProduct, err = gpClient.VerifyProduct(c, appInfo.PackageName, productInfo.GooID, reqData.PurchaseToken); err != nil {
			gLog.Error("VerifyProduct fail", zap.Error(err))
			return
		}
		gLog = gLog.With(zap.Any("verifyProduct", verifyProduct))
		if verifyProduct.PurchaseState != 0 {
			// 未支付
			err = fmt.Errorf("google query fail, not pay")
			gLog.Error("google query fail, not pay")
			return
		}
		// if verifyProduct.ConsumptionState != 1 {
		//	// 未消耗
		//	err = fmt.Errorf("google query fail, not consumption")
		//	gLog.Error("google query fail, not consumption")
		//	return
		// }
		// if verifyProduct.AcknowledgementState != 1 {
		// 	// 未确认交易
		// 	err = fmt.Errorf("google query fail, not acknowledged")
		// 	gLog.Error("google query fail, not acknowledged")
		// 	return
		// }
		if verifyProduct.ObfuscatedExternalProfileId != orderInfo.OrderNo {
			// 订单号不匹配
			err = fmt.Errorf("google query fail, order no not match")
			gLog.Error("google query fail, order no not match")
			return
		}
		if verifyProduct.PurchaseType != nil {
			orderTest = *verifyProduct.PurchaseType == 0
			gLog = gLog.With(zap.Bool("orderTest", orderTest))
		}
		tradeNo = verifyProduct.OrderId
	default:
		// 未知类型商品
		err = fmt.Errorf("google query fail, unknown good type")
		gLog.Error("google query fail, unknown good type")
		return
	}
	// 查询是否有该流水号的订单,如果有,就返回错误
	transactionOrder, transactionErr := m.GetByTradeNo(tradeNo)
	if transactionErr == nil && transactionOrder.Id != 0 {
		err = fmt.Errorf("transaction_id exist")
		gLog.Error("transaction_id exist")
		return
	}
	orderInfo.TradeNo = tradeNo
	orderInfo.OrderTest = orderTest
	orderInfo.PurchaseToken = reqData.PurchaseToken
	now := time.Now()
	nowPointer := &now
	orderInfo.PayTime = nowPointer

	// 支付成功
	if err = m.OrderPaySuccess(c, orderInfo, expiresDate, orderTest, false); err != nil {
		gLog.Error("OrderPaySuccess fail", zap.Error(err))
		return
	}
	gLog.Info("OrderPaySuccess success")
	userService.DelCache(c, userInfo.Id)
	res = gin.H{
		"id":       orderInfo.Id,
		"order_no": orderInfo.OrderNo,
	}
	return
}

func (m *OrdersService) AppleQuery(c *gin.Context, reqData *request.AppleQueryReq) (res gin.H, err error) {
	var (
		transactionId  string
		userInfo       *model.DigitalUser
		orderInfo      *model.Order
		productInfo    *model.Product
		iosClient      *appstore.Client
		userService    DigitalUserService
		productService ProductService
		expiresDate    time.Time
		orderTest      = false
		isFreeSub      = false
		userId         = utils.GetDigitalUserID(c)
	)
	gLog := global.LOG.With(zap.String("func", "AppleQuery"), zap.Any("reqData", reqData), zap.String("business", "apple"))

	// 获取用户信息
	if userInfo, err = userService.GetByIdUnscoped(c, userId); err != nil {
		gLog.Error("userService.GetById fail", zap.Error(err))
		return
	}
	// 获取App信息
	appInfo, _ := global.AppMap[userInfo.AppID]

	// 获取订单信息
	if orderInfo, err = m.GetByOrderNo(reqData.OrderNo); err != nil {
		gLog.Error("GetByOrderNo fail", zap.Error(err))
		return
	}
	// 权益已下发
	if orderInfo.OrderStatus == model.OSPayGoodSuccess {
		gLog.Info("OrderStatus is OSPayGoodSuccess")
		return
	}
	// 获取商品信息
	if productInfo, err = productService.GetById(orderInfo.ProductID); err != nil {
		gLog.Error("productService.GetById fail", zap.Error(err))
		return
	}
	iosClient = appstore.New()
	// 验证交易

	// 订阅类商品验证交易
	iapRequest := appstore.IAPRequest{
		ReceiptData: reqData.PurchaseToken,
		Password:    appInfo.IOSPayPwd,
	}
	resp := &appstore.IAPResponse{}
	if err = iosClient.Verify(c, iapRequest, resp); err != nil {
		gLog.Error("Verify fail", zap.Error(err))
		return
	}
	gLog = gLog.With(zap.Any("VerifyResp", resp))
	if resp.Status != 0 {
		// 未支付
		err = fmt.Errorf("apple query fail, not pay, apple_status: %d", resp.Status)
		gLog.Error("apple query fail, not pay")
		return
	}
	if productInfo.GoodType == model.GTGood {
		// 一次性购买类商品验证交易
		if len(resp.Receipt.InApp) == 0 {
			// 未支付
			err = fmt.Errorf("apple query fail, not pay. in_app is empty")
			gLog.Error("apple query fail, not pay. in_app is empty")
			return
		}
		inAppZero := resp.Receipt.InApp[0]
		transactionId = inAppZero.TransactionID
		if inAppZero.ProductID != productInfo.IOSID {
			// 商品ID不匹配
			err = fmt.Errorf("apple query fail, product id not match")
			gLog.Error("apple query fail, product id not match")
			return
		}
	} else {
		// 订阅类商品验证交易
		if len(resp.LatestReceiptInfo) == 0 {
			// 未支付
			err = fmt.Errorf("apple query fail, not pay. latest_receipt_info is empty")
			gLog.Error("apple query fail, not pay. latest_receipt_info is empty")
			return
		}
		latestReceiptInfo := resp.LatestReceiptInfo[0]
		transactionId = latestReceiptInfo.TransactionID
		expiresDateMS, _ := strconv.ParseInt(latestReceiptInfo.ExpiresDateMS, 10, 64)
		expiresDate = time.UnixMilli(expiresDateMS)
		gLog = gLog.With(zap.String("ExpiresDateMS", latestReceiptInfo.ExpiresDateMS))
		isFreeSub = latestReceiptInfo.IsTrialPeriod == "true"
		if productInfo.ProductType == model.PTSubWeek && userInfo.AppID == 14 {
			// 查下之前有没有订阅过
			// 执行查询
			var count int64
			_ = global.DB.Table("orders as o").
				Joins("left join product as p on o.product_id = p.id").
				Where("o.deleted_at IS NULL").
				Where("o.digital_user_id = ?", userId).
				Where("p.product_type = ?", model.ProductInfoTypeSub).
				Where("o.order_status = ?", model.OSPayGoodSuccess).
				Count(&count).Error
			if count == 0 {
				// 上报
				var userProfileInfo *model.DigitalUserProfile
				var profileService DigitalUserProfileService
				if userProfileInfo, err = profileService.GetByUserId(userId); err != nil {
					return
				}
				go TdPointer(userInfo, userProfileInfo, td.ServerEvent, map[string]interface{}{
					"event_tag": "sub_free",
				})

			}

		}

	}
	// 查询是否有该流水号的订单,如果有,就返回错误
	transactionOrder, transactionErr := m.GetByTradeNo(transactionId)
	if transactionErr == nil && transactionOrder.Id != 0 {
		err = fmt.Errorf("transaction_id exist")
		gLog.Error("transaction_id exist")
		return
	}
	orderInfo.TradeNo = transactionId
	// 是否是测试订单
	orderTest = resp.Environment == appstore.Sandbox
	gLog = gLog.With(zap.Bool("orderTest", orderTest))
	orderInfo.OrderTest = orderTest
	orderInfo.PurchaseToken = reqData.PurchaseToken
	now := time.Now()
	nowPointer := &now
	orderInfo.PayTime = nowPointer

	// 支付成功
	if err = m.OrderPaySuccess(c, orderInfo, expiresDate, orderTest, isFreeSub); err != nil {
		gLog.Error("OrderPaySuccess fail", zap.Error(err))
		return
	}
	userService.DelCache(c, userId)
	res = gin.H{
		"id":       orderInfo.Id,
		"order_no": orderInfo.OrderNo,
	}
	gLog.Info("OrderPaySuccess success")
	return
}

func (m *OrdersService) OrderDDMsgLock() bool {
	ctx := context.Background()
	return global.REDIS.SetNX(ctx, global.OrderDDMsgLockKey, "1", time.Second*4).Val()
}

func (m *OrdersService) sendDDMsg(msgText string) {
	var (
		err     error
		resBody string
		data    = notify.Message{Msgtype: ""}
	)
	// if m.OrderDDMsgLock() {
	// 	return
	// }
	data.Markdown.Title = "订单通知消息"
	data.Msgtype = "markdown"
	data.Markdown.Text = msgText
	resBody, err = notify.SendDingTalk(data, global.OrderDDToken, global.OrderDDSecret)
	if err != nil {
		global.LOG.Error("发送钉钉通知 error", zap.Any("err", err), zap.String("resBody", resBody))
		return
	}
}

func (m *OrdersService) OrderRefund(c *gin.Context, userInfo *model.DigitalUser, orderInfo *model.Order, productInfo *model.Product) {
	var (
		err            error
		userProfile    *model.DigitalUserProfile
		userService    DigitalUserService
		profileService DigitalUserProfileService
	)
	if userProfile, err = profileService.GetByUserId(userInfo.Id); err != nil {
		return
	}
	orderInfo.OrderStatus = model.OSPayRefund
	if err = m.UpdateByMap(orderInfo.Id, map[string]interface{}{"order_status": model.OSPayRefund}); err != nil {
		return
	}
	switch productInfo.GoodType {
	case model.GTGood:
		var (
			productAmount = productInfo.Amount + productInfo.RewardAmount
			updateMap     = map[string]interface{}{}
		)
		// 一次性购买类商品退款
		switch productInfo.ProductType {
		case model.PTFlowers:
			// 减鲜花数量
			if userProfile.FlowerAmount <= productAmount {
				updateMap["flower_amount"] = 0
			} else if userProfile.FlowerAmount > productAmount {
				updateMap["flower_amount"] = userProfile.FlowerAmount - productAmount
			}
		case model.PTChat:
			// 减畅聊包数量
			if userProfile.ChatsAmount <= productAmount {
				updateMap["chats_amount"] = 0
			} else if userProfile.ChatsAmount > productAmount {
				updateMap["chats_amount"] = userProfile.FlowerAmount - productAmount
			}
		default:
			return
		}
		if err = profileService.UpdateByMap(c, userInfo.Id, updateMap); err != nil {
			return
		}
	case model.GTSub:
		// 订阅类商品退款
		if err = userService.UserSubRefund(c, userInfo, userProfile, productInfo); err != nil {
			return
		}
	default:
		return
	}
}

func (m *OrdersService) OrderSubDisable(orderInfo *model.Order) {
	var (
		err error
	)
	updateMap := map[string]interface{}{
		"order_sub_status": model.SwitchStatusDisabled,
	}
	if err = m.UpdateByMap(orderInfo.Id, updateMap); err != nil {
		return
	}
}

func (m *OrdersService) AppleNotify(c *gin.Context, reqData *appstore.SubscriptionNotificationV2SignedPayload) (err error) {
	var (
		userInfo           *model.DigitalUser
		orderInfo          *model.Order
		productInfo        *model.Product
		productService     ProductService
		userService        DigitalUserService
		iosClient          *appstore.Client
		decodedPayload     appstore.SubscriptionNotificationV2DecodedPayload
		transactionPayload appstore.JWSTransactionDecodedPayload
		userProfile        *model.DigitalUserProfile
		profileService     DigitalUserProfileService
	)

	gLog := global.LOG.With(zap.String("func", "AppleNotify"), zap.Any("reqData", reqData), zap.String("business", "apple"))
	iosClient = appstore.New()
	if err = iosClient.ParseNotificationV2WithClaim(reqData.SignedPayload, &decodedPayload); err != nil {
		gLog.Error("ParseNotificationV2WithClaim fail", zap.Error(err))
		return
	}
	gLog = gLog.With(zap.Any("decodedPayload", decodedPayload))
	if err = iosClient.ParseNotificationV2WithClaim(string(decodedPayload.Data.SignedTransactionInfo), &transactionPayload); err != nil {
		gLog.Error("ParseNotificationV2WithClaim fail", zap.Error(err))
		return
	}
	gLog = gLog.With(zap.Any("transactionPayload", transactionPayload))
	notificationType := decodedPayload.NotificationType
	originalTransactionId := transactionPayload.OriginalTransactionId
	if originalTransactionId == "" {
		err = fmt.Errorf("apple notify fail, originalTransactionId is empty")
		gLog.Error("originalTransactionId is empty")
		return
	}
	environment := transactionPayload.Environment
	tradeNo := transactionPayload.TransactionId
	lockKey := fmt.Sprintf(global.APPLE_NOTIFY_LOCK_KEY, tradeNo)
	if !global.REDIS.SetNX(c, lockKey, 1, time.Hour*24*7).Val() {
		gLog.Info("AppleNotify lock", zap.String("lockKey", lockKey))
		return
	}
	// 获取订单信息
	if orderInfo, err = m.GetByTradeNo(originalTransactionId); err != nil {
		gLog.Error("GetByTradeNo fail", zap.Error(err))
		return
	}
	gLog = gLog.With(zap.Any("orderId", orderInfo.Id))
	gLog = gLog.With(zap.Any("orderNo", orderInfo.OrderNo))
	// 获取用户信息
	if userInfo, err = userService.GetByIdUnscoped(c, orderInfo.DigitalUserID); err != nil {
		gLog.Error("GetById fail", zap.Error(err))
		return
	}
	gLog = gLog.With(zap.Any("userId", userInfo.Id))
	if userProfile, err = profileService.GetByUserId(userInfo.Id); err != nil {
		return
	}
	// 获取商品信息
	if productInfo, err = productService.GetById(orderInfo.ProductID); err != nil {
		gLog.Error("GetById fail", zap.Error(err))
		return
	}
	gLog = gLog.With(zap.Any("productId", productInfo.Id))
	orderTest := environment == "Sandbox"
	gLog = gLog.With(zap.Bool("orderTest", orderTest))

	appleNotifySubType := decodedPayload.Subtype

	switch notificationType {
	case appstore.NotificationTypeV2ConsumptionRequest:
		// 一种通知类型，表明客户发起了应用内消费品购买的退款请求，并且 App Store 要求您提供消费数据。有关详细信息，请参阅发送消费信息。
		gLog.Info("NotificationTypeV2ConsumptionRequest")
		// m.sendDDMsg(fmt.Sprintf("Apple, 环境:%s,用户(%d)发起了应用内消费品(%s)购买的退款请求,商品金额(%s), orderNo: %s", environment, userInfo.Id, productInfo.Name, productInfo.USDPrice.String(), orderInfo.OrderNo))
		return
	case appstore.NotificationTypeV2DidChangeRenewalPref:
		// 一种通知类型，与其 subtype 一起指示用户对其订阅计划进行了更改。如果 subtype 为 UPGRADE ，则用户升级了其订阅。升级立即生效，开始新的计费周期，用户将收到上一周期未使用部分的按比例退款。如果 subtype 为 DOWNGRADE ，则用户降级了订阅。降级将在下一个续订日期生效，并且不会影响当前有效的计划。
	case appstore.NotificationTypeV2DidChangeRenewalStatus:
		// 一种通知类型，与其 subtype 一起指示用户对订阅续订状态进行了更改。如果 subtype 为 AUTO_RENEW_ENABLED ，则用户重新启用订阅自动续订。如果 subtype 为 AUTO_RENEW_DISABLED ，则用户禁用了订阅自动续费，或者用户申请退款后 App Store 禁用了订阅自动续费。
		if appleNotifySubType == appstore.SubTypeV2AutoRenewEnabled {
			// 则用户重新启用订阅自动续订
		} else if appleNotifySubType == appstore.SubTypeV2AutoRenewDisabled {
			// 用户禁用了订阅自动续费
		}
	case appstore.NotificationTypeV2DidFailToRenew:
		// 一种通知类型，与其 subtype 一起指示订阅由于计费问题而未能续订。订阅进入计费重试期。如果 subtype 为 GRACE_PERIOD ，则在宽限期内继续提供服务。如果 subtype 为空，则订阅不在宽限期内，您可以停止提供订阅服务。
		gLog.Info("NotificationTypeV2DidFailToRenew")
		m.sendDDMsg(fmt.Sprintf("Apple, 环境:%s,用户(%d)订阅由于计费问题而未能续订,商品(%s),商品金额(%s), orderNo: %s", environment, userInfo.Id, productInfo.Name, productInfo.USDPrice.String(), orderInfo.OrderNo))
		return
	case appstore.NotificationTypeV2DidRenew:
		// 一种通知类型，与其 subtype 一起指示订阅已成功续订。如果 subtype 为 BILLING_RECOVERY ，则之前续订失败的过期订阅已成功续订。如果子状态为空，则活动订阅已成功自动续订新的交易周期。为客户提供对订阅内容或服务的访问权限。
		expiresDateTime := time.UnixMilli(transactionPayload.ExpiresDate)
		gLog = gLog.With(zap.String("expiresDateTime", expiresDateTime.Format(time.DateTime)))
		// 订阅续订
		if err = m.ReSubSuccess(c, orderInfo, expiresDateTime, tradeNo, orderTest); err != nil {
			gLog.Error("ReSubSuccess fail", zap.Error(err))
			return
		}
	case appstore.NotificationTypeV2Expired:
		// 一种通知类型，与其 subtype 一起指示订阅已过期。如果 subtype 为 VOLUNTARY ，则订阅在用户禁用订阅续订后过期。如果 subtype 为 BILLING_RETRY ，则订阅已过期，因为计费重试期已结束，但没有成功的计费交易。如果 subtype 为 PRICE_INCREASE ，则订阅已过期，因为用户不同意需要用户同意的价格上涨。如果 subtype 为 PRODUCT_NOT_FOR_SALE ，则订阅已过期，因为在订阅尝试续订时该产品无法购买。
		gLog.Info("NotificationTypeV2Expired")
		if productInfo.GoodType == model.GTSub {
			go TdPointer(userInfo, userProfile, td.ServerEvent, map[string]interface{}{
				"event_tag":  "cancel_sub",
				"sub_type":   productInfo.ProductType,
				"amount":     productInfo.USDPrice,
				"product_id": productInfo.Id,
			})
			m.OrderSubDisable(orderInfo)
		}
		// m.sendDDMsg(fmt.Sprintf("环境:%s,用户(%d)订阅已过期,商品(%s),商品金额(%s)", environment, userInfo.Id, productInfo.Name, productInfo.USDPrice.String()))
		return
	case appstore.NotificationTypeV2GracePeriodExpired:
		// 一种通知类型，指示计费宽限期已结束而无需续订订阅，因此您可以关闭对服务或内容的访问。通知用户他们的账单信息可能存在问题。 App Store 将在 60 天内继续重试计费，或者直到用户解决计费问题或取消订阅（以先到者为准）。有关更多信息，请参阅减少非自愿订户流失。
	case appstore.NotificationTypeV2OfferRedeemed:
		// 一种通知类型，与其 subtype 一起指示用户兑换了促销优惠或优惠代码。
	case appstore.NotificationTypeV2PriceIncrease:
		// 一种通知类型，与其 subtype 一起指示系统已通知用户自动续订订阅价格上涨。
	case appstore.NotificationTypeV2Refund:
		// 一种通知类型，指示 App Store 已成功对消费品应用内购买、非消费品应用内购买、自动续订订阅或非续订订阅的交易进行退款。
		gLog.Info("NotificationTypeV2Refund")
		if productInfo.GoodType == model.GTSub {
			go TdPointer(userInfo, userProfile, td.ServerEvent, map[string]interface{}{
				"event_tag":  "refund_sub",
				"sub_type":   productInfo.ProductType,
				"amount":     productInfo.USDPrice,
				"product_id": productInfo.Id,
			})
			m.OrderSubDisable(orderInfo)
		}
		m.sendDDMsg(fmt.Sprintf("Apple, 环境:%s,用户(%d)已退款,商品(%s),商品金额(%s), orderNo: %s", environment, userInfo.Id, productInfo.Name, productInfo.USDPrice.String(), orderInfo.OrderNo))
		// TODO 处理退款的逻辑
		m.OrderRefund(c, userInfo, orderInfo, productInfo)
		return
	case appstore.NotificationTypeV2RefundDeclined:
		// 一种通知类型，指示 App Store 拒绝了应用开发者使用以下任一方法发起的退款请求
	case appstore.NotificationTypeV2RefundReversed:
		// 一种通知类型，表明 App Store 由于客户提出的争议而撤销了之前授予的退款。如果您的应用因相关退款而撤销了内容或服务，则需要恢复它们。
		m.sendDDMsg(fmt.Sprintf("Apple, 环境:%s,App Store 由于客户提出的争议而撤销了之前授予的退款,商品(%s),商品金额(%s)", environment, productInfo.Name, productInfo.USDPrice.String()))
		return
	case appstore.NotificationTypeV2RenewalExtended:
		// 一种通知类型，指示 App Store 延长了特定订阅的订阅续订日期。您可以通过调用 App Store Server API 中的“延长订阅续订日期”或“为所有活跃订阅者延长订阅续订日期”来请求订阅续订日期延期。
	case appstore.NotificationTypeV2RenewalExtension:
		// 一种通知类型，与其 subtype 一起指示 App Store 正在尝试通过调用“为所有活跃订阅者延长订阅续订日期”来延长您请求的订阅续订日期。
	case appstore.NotificationTypeV2Revoke:
		// 指示用户有权通过“家人共享”进行应用内购买的通知类型不再可通过共享进行。当购买者对其购买禁用“家庭共享”、购买者（或家庭成员）离开家庭群组或购买者收到退款时，App Store 会发送此通知。您的应用程序还会收到 paymentQueue(_:didRevokeEntitlementsForProductIdentifiers:) 调用。家庭共享适用于非消耗性应用内购买和自动续订订阅。有关家庭共享的更多信息，请参阅在应用程序中支持家庭共享。
	case appstore.NotificationTypeV2Subscribed:
		// 一种通知类型，与其 subtype 一起指示用户订阅了产品。如果 subtype 为 INITIAL_BUY ，则用户首次通过家庭共享购买或接收了对订阅的访问权限。如果 subtype 为 RESUBSCRIBE ，则用户通过家庭共享重新订阅或接收对同一订阅或同一订阅组内的另一个订阅的访问权限。
		gLog.Info("NotificationTypeV2Subscribed")
		// m.sendDDMsg(fmt.Sprintf("Apple, 环境:%s,用户(%d)订阅了产品,商品(%s),商品金额(%s), orderNo: %s", environment, productInfo.Name, productInfo.USDPrice.String(), orderInfo.OrderNo))
		return
	case appstore.NotificationTypeV2Test:
		// 当您通过调用请求测试通知端点请求时，App Store 服务器发送的通知类型。调用该端点来测试您的服务器是否正在接收通知。仅当您提出请求时，您才会收到此通知。有关故障排除信息，请参阅获取测试通知状态端点。
	default:
		gLog.Info("notificationType is not match", zap.Any("notificationType", notificationType))
		return
	}
	userService.DelCache(c, orderInfo.DigitalUserID)
	gLog.Info("ReSubSuccess success")
	return
}

func (m *OrdersService) GoogleNotifyNew(c *gin.Context, reqData *request.GoogleNotifyReq) (err error) {
	var (
		expiresDateTime time.Time
		orderInfo       *model.Order
		userInfo        *model.DigitalUser
		userProfile     *model.DigitalUserProfile
		productInfo     *model.Product
		userService     DigitalUserService
		profileService  DigitalUserProfileService
		productService  ProductService
		gooPayJsonBytes []byte
		b64DecodeByte   []byte
		gpClient        *playstore.Client
		notifyData      playstore.DeveloperNotificationV2
	)
	gLog := global.LOG.With(zap.String("func", "GoogleNotify"), zap.Any("reqData", reqData), zap.String("business", "google"))

	if b64DecodeByte, err = base64.StdEncoding.DecodeString(reqData.Message.Data); err != nil {
		gLog.Error("base64 decode fail", zap.Error(err))
		return
	}
	if err = json.Unmarshal(b64DecodeByte, &notifyData); err != nil {
		gLog.Error("json unmarshal fail", zap.Error(err))
		return
	}
	gLog = gLog.With(zap.Any("notifyData", notifyData))
	// 上线修改
	if global.CONFIG.System.Env == "public" {
		messageId := reqData.Message.MessageId
		lockKey := fmt.Sprintf(global.GOOGLE_NOTIFY_LOCK_KEY, messageId)
		if !global.REDIS.SetNX(context.Background(), lockKey, 1, time.Hour*24*7).Val() {
			gLog.Info("GoogleNotify lock", zap.String("lockKey", lockKey))
			return
		}
	}
	appInfo, appInfoExist := global.AndroidPackageNameMap[notifyData.PackageName]
	if !appInfoExist {
		gLog.Error("app is not found", zap.String("PackageName", notifyData.PackageName))
		err = fmt.Errorf("app(%s) is not found", notifyData.PackageName)
		return
	}
	gLog = gLog.With(zap.Uint("AppId", appInfo.Id))
	if gooPayJsonBytes, err = json.Marshal(appInfo.GooPayJson); err != nil {
		gLog.Error("json.Marshal error", zap.Error(err))
		err = fmt.Errorf("json.Marshal error %v", err.Error())
		return
	}
	if gpClient, err = playstore.New(gooPayJsonBytes); err != nil {
		gLog.Error("playstore.New fail", zap.Error(err))
		return
	}
	if notifyData.SubscriptionNotification != nil {
		var subscriptionPurchaseV2 *androidpublisher.SubscriptionPurchaseV2
		// 文档地址 https://developer.android.com/google/play/billing/rtdn-reference?hl=zh-cn#sub
		purchaseToken := notifyData.SubscriptionNotification.PurchaseToken
		if subscriptionPurchaseV2, err = gpClient.VerifySubscriptionV2(c, notifyData.PackageName, purchaseToken); err != nil {
			gLog.Error("VerifySubscriptionV2 fail", zap.Error(err))
			return
		}
		gLog = gLog.With(zap.Any("subscriptionPurchaseV2", subscriptionPurchaseV2))
		if subscriptionPurchaseV2 == nil || subscriptionPurchaseV2.ExternalAccountIdentifiers == nil {
			gLog.Error("subscriptionPurchaseV2 or ExternalAccountIdentifiers is nil")
			return
		}
		orderNo := subscriptionPurchaseV2.ExternalAccountIdentifiers.ObfuscatedExternalProfileId
		// 获取订单信息
		if orderInfo, err = m.GetByOrderNo(orderNo); err != nil {
			gLog.Error("GetByOrderNo fail", zap.Error(err))
			return
		}
		// 获取用户信息
		if userInfo, err = userService.GetByIdUnscoped(c, orderInfo.DigitalUserID); err != nil {
			gLog.Error("GetById fail", zap.Error(err))
			return
		}
		if userProfile, err = profileService.GetByUserId(userInfo.Id); err != nil {
			return
		}
		// 获取商品信息
		if productInfo, err = productService.GetById(orderInfo.ProductID); err != nil {
			gLog.Error("GetById fail", zap.Error(err))
			return
		}
		gLog = gLog.With(zap.Uint("productId", productInfo.Id), zap.String("productName", productInfo.Name))
		orderTest := subscriptionPurchaseV2.TestPurchase != nil
		gLog = gLog.With(zap.Bool("orderTest", orderTest))
		switch notifyData.SubscriptionNotification.NotificationType {
		case playstore.SubscriptionNotificationTypeRenewed, playstore.SubscriptionNotificationTypeRecovered:
			// playstore.SubscriptionNotificationTypeRecovered 从账号保留状态恢复了订阅
			// playstore.SubscriptionNotificationTypeRenewed 续订了处于活动状态的订阅
			if len(subscriptionPurchaseV2.LineItems) == 0 {
				gLog.Error("LineItems is empty")
				return
			}
			lineItem := subscriptionPurchaseV2.LineItems[0]
			if expiresDateTime, err = time.Parse(time.RFC3339, lineItem.ExpiryTime); err != nil {
				gLog.Error("time.Parse fail", zap.Error(err))
				return
			}
			tradeNo := subscriptionPurchaseV2.LatestOrderId
			// 订阅续订
			if err = m.ReSubSuccess(c, orderInfo, expiresDateTime, tradeNo, orderTest); err != nil {
				gLog.Error("ReSubSuccess fail", zap.Error(err))
				return
			}
			gLog.Info("ReSubSuccess success")
		case playstore.SubscriptionNotificationTypeCanceled:
			// 自愿或非自愿地取消了订阅。如果是自愿取消，在用户取消时发送
			go TdPointer(userInfo, userProfile, td.ServerEvent, map[string]interface{}{
				"event_tag": "cancel_sub",
			})
		case playstore.SubscriptionNotificationTypePurchased:
			// 购买了新的订阅
			if orderInfo.OrderStatus == model.OSPayGoodSuccess {
				gLog.Info("OrderStatus is OSPayGoodSuccess")
				return
			}
			if subscriptionPurchaseV2.AcknowledgementState != "ACKNOWLEDGEMENT_STATE_ACKNOWLEDGED" {
				// 未确认交易
				err = fmt.Errorf("google query fail, not acknowledged")
				return
			}
			// 是否是测试订单
			if len(subscriptionPurchaseV2.LineItems) == 0 {
				gLog.Error("LineItems is empty")
				return
			}
			lineItem := subscriptionPurchaseV2.LineItems[0]
			expiresDate, parseErr := time.Parse(time.RFC3339, lineItem.ExpiryTime)
			if parseErr != nil {
				gLog.Error("time.Parse fail", zap.Error(parseErr))
				err = parseErr
				return
			}
			gLog = gLog.With(zap.Time("expiresDate", expiresDate))
			orderInfo.TradeNo = subscriptionPurchaseV2.LatestOrderId
			orderInfo.OrderTest = orderTest
			orderInfo.PurchaseToken = purchaseToken
			now := time.Now()
			nowPointer := &now
			orderInfo.PayTime = nowPointer
			// 支付成功
			if err = m.OrderPaySuccess(c, orderInfo, expiresDate, orderTest, false); err != nil {
				gLog.Error("OrderPaySuccess fail", zap.Error(err))
				return
			}
			gLog.Info("OrderPaySuccess success")
		case playstore.SubscriptionNotificationTypeAccountHold:
			// 订阅已进入账号保留状态（如果已启用）
		case playstore.SubscriptionNotificationTypeGracePeriod:
			// 订阅已进入宽限期（如果已启用）
		case playstore.SubscriptionNotificationTypeRestarted:
			// 用户已通过 Play > 账号 > 订阅恢复了订阅。订阅已取消，但在用户恢复时尚未到期。如需了解详情，请参阅恢复
		case playstore.SubscriptionNotificationTypePriceChangeConfirmed:
			// 用户已成功确认订阅价格变动
		case playstore.SubscriptionNotificationTypeDeferred:
			// 订阅的续订时间点已延期
		case playstore.SubscriptionNotificationTypePaused:
			// 订阅已暂停
		case playstore.SubscriptionNotificationTypePauseScheduleChanged:
			// 订阅暂停计划已更改
		case playstore.SubscriptionNotificationTypeRevoked:
			// 用户在到期时间之前已撤消订阅
			// 用户已退款,这个和下面的退款通知重复,退款通知在前,这个在后
		case playstore.SubscriptionNotificationTypeExpired:
			// 订阅已到期
		case playstore.SubscriptionNotificationTypePendingPurchaseCancelled:
			// 待处理的交易 项订阅已取消

		default:
			gLog.Error("Type not supported")
			return
		}
	} else if notifyData.OneTimeProductNotification != nil {
		gLog.Info("OneTimeProductNotification")
		var (
			orderTest     bool
			expiresDate   time.Time
			verifyProduct *androidpublisher.ProductPurchase
			purchaseToken = notifyData.OneTimeProductNotification.PurchaseToken
		)
		// 一次性购买类商品验证交易
		if verifyProduct, err = gpClient.VerifyProduct(c, appInfo.PackageName, notifyData.OneTimeProductNotification.SKU, purchaseToken); err != nil {
			gLog.Error("VerifyProduct fail", zap.Error(err))
			return
		}
		gLog = gLog.With(zap.Any("verifyProduct", verifyProduct))
		orderNo := verifyProduct.ObfuscatedExternalProfileId
		// 获取订单信息
		if orderInfo, err = m.GetByOrderNo(orderNo); err != nil {
			gLog.Error("GetByOrderNo fail", zap.Error(err))
			return
		}
		if orderInfo.OrderStatus == model.OSPayGoodSuccess {
			gLog.Info("OrderStatus is OSPayGoodSuccess")
			return
		}
		// 获取用户信息
		if userInfo, err = userService.GetByIdUnscoped(c, orderInfo.DigitalUserID); err != nil {
			gLog.Error("GetById fail", zap.Error(err))
			return
		}
		if userProfile, err = profileService.GetByUserId(userInfo.Id); err != nil {
			return
		}
		// 获取商品信息
		if productInfo, err = productService.GetById(orderInfo.ProductID); err != nil {
			gLog.Error("GetById fail", zap.Error(err))
			return
		}
		// 订单的购买状态。可能的值包括：0。已购买 1.已取消 2.待处理
		if verifyProduct.PurchaseState != 0 {
			// 未支付
			err = fmt.Errorf("google query fail, not pay")
			gLog.Error("google query fail, not pay")
			return
		}
		// 应用内商品的使用状态。可能的值包括：0。还将被消耗 1.已使用
		if verifyProduct.ConsumptionState != 1 {
			if err = gpClient.ConsumeProduct(c, appInfo.PackageName, productInfo.GooID, purchaseToken); err != nil {
				// 消耗商品失败
				err = fmt.Errorf("google ConsumeProduct fail, error: %v", err.Error())
				gLog.Error("google ConsumeProduct fail", zap.Error(err))
				return
			}
		}
		// 应用内商品的确认状态。可能的值包括：0。尚未确认 1.已确认
		// if verifyProduct.AcknowledgementState != 1 {
		// 	// 确认商品购买
		// 	if err = gpClient.AcknowledgeProduct(c, appInfo.PackageName, productInfo.GooID, purchaseToken, ""); err != nil {
		// 		// 确认购买应用内商品失败
		// 		err = fmt.Errorf("google AcknowledgeProduct fail, error: %v", err.Error())
		// 		gLog.Error("google AcknowledgeProduct fail", zap.Error(err))
		// 		return
		// 	}
		// }
		// 应用内商品的购买类型。仅当此次购买并非通过标准应用内购买结算流程进行时，才会设置此字段。可能的值包括：0。测试（即通过许可测试账号购买）1.促销（即使用促销代码购买）。不包括 Play Points 购买交易。2. 激励广告（即通过观看视频广告而不是付费观看）
		if verifyProduct.PurchaseType != nil {
			orderTest = *verifyProduct.PurchaseType == 0
			gLog = gLog.With(zap.Bool("orderTest", orderTest))
		}
		orderInfo.TradeNo = verifyProduct.OrderId
		orderInfo.OrderTest = orderTest
		orderInfo.PurchaseToken = purchaseToken
		now := time.Now()
		nowPointer := &now
		orderInfo.PayTime = nowPointer
		// 支付成功
		if err = m.OrderPaySuccess(c, orderInfo, expiresDate, orderTest, false); err != nil {
			gLog.Error("OrderPaySuccess fail", zap.Error(err))
			return
		}
		gLog.Info("OneTimeProductNotification OrderPaySuccess success")
	} else if notifyData.VoidedPurchaseNotification != nil {
		gLog.Info("VoidedPurchaseNotification")
		tradeNo := notifyData.VoidedPurchaseNotification.OrderID
		// 获取订单信息
		if orderInfo, err = m.GetByTradeNo(tradeNo); err != nil {
			gLog.Error("GetByOrderNo fail", zap.Error(err))
			return
		}
		// 获取用户信息
		if userInfo, err = userService.GetByIdUnscoped(c, orderInfo.DigitalUserID); err != nil {
			gLog.Error("GetById fail", zap.Error(err))
			return
		}
		// 获取商品信息
		if productInfo, err = productService.GetById(orderInfo.ProductID); err != nil {
			gLog.Error("GetById fail", zap.Error(err))
			return
		}
		m.OrderRefund(c, userInfo, orderInfo, productInfo)
		userService.DelCache(c, orderInfo.DigitalUserID)
	} else if notifyData.TestNotification != nil {
		gLog.Info("TestNotification")
	} else {
		gLog.Error("notifyData is not match")
		return
	}
	return
}

func (m *OrdersService) GoogleNotifyDemo(c *gin.Context, reqData *request.GoogleNotifyReq) (err error) {
	var (
		expiresDateTime time.Time
		orderInfo       *model.Order
		userInfo        *model.DigitalUser
		userProfile     *model.DigitalUserProfile
		productInfo     *model.Product
		userService     DigitalUserService
		profileService  DigitalUserProfileService
		productService  ProductService
		gooPayJsonBytes []byte
		b64DecodeByte   []byte
		gpClient        *playstore.Client
		notifyData      playstore.DeveloperNotificationV2
		v2              *androidpublisher.SubscriptionPurchaseV2
	)
	gLog := global.LOG.With(zap.String("func", "GoogleNotifyDemo"), zap.Any("reqData", reqData), zap.String("business", "google_demo"))

	if b64DecodeByte, err = base64.StdEncoding.DecodeString(reqData.Message.Data); err != nil {
		gLog.Error("base64 decode fail", zap.Error(err))
		return
	}
	if err = json.Unmarshal(b64DecodeByte, &notifyData); err != nil {
		gLog.Error("json unmarshal fail", zap.Error(err))
		return
	}
	gLog = gLog.With(zap.Any("notifyData", notifyData))
	// 如果是线上环境,一个通知只处理一次,这个业务费自己决定是否启用
	if global.CONFIG.System.Env == "public" {
		messageId := reqData.Message.MessageId
		lockKey := fmt.Sprintf(global.GOOGLE_NOTIFY_LOCK_KEY, messageId)
		if !global.REDIS.SetNX(context.Background(), lockKey, 1, time.Hour*24*7).Val() {
			gLog.Info("GoogleNotify lock", zap.String("lockKey", lockKey))
			return
		}
	}
	// 根据通知的包名获取对应的app信息
	appInfo, appInfoExist := global.AndroidPackageNameMap[notifyData.PackageName]
	if !appInfoExist {
		gLog.Error("app is not found", zap.String("PackageName", notifyData.PackageName))
		err = fmt.Errorf("app(%s) is not found", notifyData.PackageName)
		return
	}
	gLog = gLog.With(zap.Uint("AppId", appInfo.Id))

	// 获取google支付的json
	if gooPayJsonBytes, err = json.Marshal(appInfo.GooPayJson); err != nil {
		gLog.Error("json.Marshal error", zap.Error(err))
		err = fmt.Errorf("json.Marshal error %v", err.Error())
		return
	}
	// 初始化google支付客户端
	if gpClient, err = playstore.New(gooPayJsonBytes); err != nil {
		gLog.Error("playstore.New fail", zap.Error(err))
		return
	}
	// 判断通知类型,进行具体的业务配置
	if notifyData.SubscriptionNotification != nil {
		// 文档地址 https://developer.android.com/google/play/billing/rtdn-reference?hl=zh-cn#sub
		purchaseToken := notifyData.SubscriptionNotification.PurchaseToken
		if v2, err = gpClient.VerifySubscriptionV2(c, notifyData.PackageName, purchaseToken); err != nil {
			gLog.Error("VerifySubscriptionV2 fail", zap.Error(err))
			return
		}
		gLog = gLog.With(zap.Any("v2", v2))
		// 判断v2是否为空,以及是否有外部账户标识
		if v2 == nil || v2.ExternalAccountIdentifiers == nil {
			gLog.Error("v2 or ExternalAccountIdentifiers is nil")
			return
		}
		// 自己系统的订单号
		orderNo := v2.ExternalAccountIdentifiers.ObfuscatedExternalProfileId
		// 获取订单信息
		if orderInfo, err = m.GetByOrderNo(orderNo); err != nil {
			gLog.Error("GetByOrderNo fail", zap.Error(err))
			return
		}
		// 获取用户信息
		if userInfo, err = userService.GetByIdUnscoped(c, orderInfo.DigitalUserID); err != nil {
			gLog.Error("GetById fail", zap.Error(err))
			return
		}
		if userProfile, err = profileService.GetByUserId(userInfo.Id); err != nil {
			return
		}
		// 获取商品信息
		if productInfo, err = productService.GetById(orderInfo.ProductID); err != nil {
			gLog.Error("GetById fail", zap.Error(err))
			return
		}
		gLog = gLog.With(zap.Uint("productId", productInfo.Id), zap.String("productName", productInfo.Name))
		orderTest := v2.TestPurchase != nil
		gLog = gLog.With(zap.Bool("orderTest", orderTest))
		switch notifyData.SubscriptionNotification.NotificationType {
		case playstore.SubscriptionNotificationTypeRenewed, playstore.SubscriptionNotificationTypeRecovered:
			// playstore.SubscriptionNotificationTypeRecovered 从账号保留状态恢复了订阅
			// playstore.SubscriptionNotificationTypeRenewed 续订了处于活动状态的订阅
			if len(v2.LineItems) == 0 {
				gLog.Error("LineItems is empty")
				return
			}
			lineItem := v2.LineItems[0]
			if expiresDateTime, err = time.Parse(time.RFC3339, lineItem.ExpiryTime); err != nil {
				gLog.Error("time.Parse fail", zap.Error(err))
				return
			}
			tradeNo := v2.LatestOrderId
			// 订阅续订
			if err = m.ReSubSuccess(c, orderInfo, expiresDateTime, tradeNo, orderTest); err != nil {
				gLog.Error("ReSubSuccess fail", zap.Error(err))
				return
			}
			gLog.Info("ReSubSuccess success")
		case playstore.SubscriptionNotificationTypeCanceled:
			// 自愿或非自愿地取消了订阅。如果是自愿取消，在用户取消时发送
			go TdPointer(userInfo, userProfile, td.ServerEvent, map[string]interface{}{
				"event_tag": "cancel_sub",
			})
		case playstore.SubscriptionNotificationTypePurchased:
			// 购买了新的订阅
			if orderInfo.OrderStatus == model.OSPayGoodSuccess {
				gLog.Info("OrderStatus is OSPayGoodSuccess")
				return
			}
			if v2.AcknowledgementState != "ACKNOWLEDGEMENT_STATE_ACKNOWLEDGED" {
				// 未确认交易
				err = fmt.Errorf("google query fail, not acknowledged")
				return
			}
			// 是否是测试订单
			if len(v2.LineItems) == 0 {
				gLog.Error("LineItems is empty")
				return
			}
			lineItem := v2.LineItems[0]
			expiresDate, parseErr := time.Parse(time.RFC3339, lineItem.ExpiryTime)
			if parseErr != nil {
				gLog.Error("time.Parse fail", zap.Error(parseErr))
				err = parseErr
				return
			}
			gLog = gLog.With(zap.Time("expiresDate", expiresDate))
			orderInfo.TradeNo = v2.LatestOrderId
			orderInfo.OrderTest = orderTest
			orderInfo.PurchaseToken = purchaseToken
			now := time.Now()
			nowPointer := &now
			orderInfo.PayTime = nowPointer
			// 支付成功
			if err = m.OrderPaySuccess(c, orderInfo, expiresDate, orderTest, false); err != nil {
				gLog.Error("OrderPaySuccess fail", zap.Error(err))
				return
			}
			gLog.Info("OrderPaySuccess success")
		case playstore.SubscriptionNotificationTypeAccountHold:
			// 订阅已进入账号保留状态（如果已启用）
		case playstore.SubscriptionNotificationTypeGracePeriod:
			// 订阅已进入宽限期（如果已启用）
		case playstore.SubscriptionNotificationTypeRestarted:
			// 用户已通过 Play > 账号 > 订阅恢复了订阅。订阅已取消，但在用户恢复时尚未到期。如需了解详情，请参阅恢复
		case playstore.SubscriptionNotificationTypePriceChangeConfirmed:
			// 用户已成功确认订阅价格变动
		case playstore.SubscriptionNotificationTypeDeferred:
			// 订阅的续订时间点已延期
		case playstore.SubscriptionNotificationTypePaused:
			// 订阅已暂停
		case playstore.SubscriptionNotificationTypePauseScheduleChanged:
			// 订阅暂停计划已更改
		case playstore.SubscriptionNotificationTypeRevoked:
			// 用户在到期时间之前已撤消订阅
			// 用户已退款,这个和下面的退款通知重复,退款通知在前,这个在后
		case playstore.SubscriptionNotificationTypeExpired:
			// 订阅已到期
		case playstore.SubscriptionNotificationTypePendingPurchaseCancelled:
			// 待处理的交易 项订阅已取消

		default:
			gLog.Error("Type not supported")
			return
		}
	} else if notifyData.OneTimeProductNotification != nil {
		// 用户购买了一次性商品的回调通知
		gLog.Info("OneTimeProductNotification")
	} else if notifyData.VoidedPurchaseNotification != nil {
		// 退款通知
		gLog.Info("VoidedPurchaseNotification")
		// 退款的时候只能获取到谷歌的流水号,获取不到自己系统的订单号
		tradeNo := notifyData.VoidedPurchaseNotification.OrderID
		// 获取订单信息
		if orderInfo, err = m.GetByTradeNo(tradeNo); err != nil {
			gLog.Error("GetByOrderNo fail", zap.Error(err))
			return
		}
		// 获取用户信息
		if userInfo, err = userService.GetByIdUnscoped(c, orderInfo.DigitalUserID); err != nil {
			gLog.Error("GetById fail", zap.Error(err))
			return
		}
		// 获取商品信息
		if productInfo, err = productService.GetById(orderInfo.ProductID); err != nil {
			gLog.Error("GetById fail", zap.Error(err))
			return
		}
		m.OrderRefund(c, userInfo, orderInfo, productInfo)
		userService.DelCache(c, orderInfo.DigitalUserID)
	} else if notifyData.TestNotification != nil {
		gLog.Info("TestNotification")
	} else {
		gLog.Error("notifyData is not match")
		return
	}
	return
}

func (m *OrdersService) CallbackSuccess(c *gin.Context, orderInfo *model.Order) (err error) {
	var (
		now = time.Now()
	)
	nowPointer := &now
	orderInfo.PayTime = nowPointer
	err = m.OrderPaySuccess(c, orderInfo, now, false, false)
	return
}

func (m *OrdersService) CallbackFail(c *gin.Context, orderInfo *model.Order) (err error) {
	orderInfo.OrderStatus = model.OSPayFail
	err = m.UpdateByMap(orderInfo.Id, map[string]interface{}{"order_status": model.OSPayFail})
	return
}

func (m *OrdersService) OrderPaySuccess(c *gin.Context, orderInfo *model.Order, expiresDate time.Time, orderTest, isFreeSub bool) (err error) {
	var (
		userInfo        *model.DigitalUser
		productInfo     *model.Product
		profileInfo     *model.DigitalUserProfile
		profileSer      DigitalUserProfileService
		productSer      ProductService
		firebaseUtilSer FireBaseUtilSer
		userSer         DigitalUserService
		msgCountSer     UserAiMsgCountService
		aiRoleInfo      *model.AiRole
		aiRoleSer       AiRoleService
		now             = time.Now()
		rewardFlo       = 0
	)
	global.LOG.Info("CallBackFromPayCenter CallbackSuccess start", zap.Any("orderInfo", orderInfo))
	// 查询用户
	if userInfo, err = userSer.GetByIdUnscoped(c, orderInfo.DigitalUserID); err != nil {
		global.LOG.Error("CallBackFromPayCenter GetByIdUnscoped fail", zap.Error(err))
		return
	}
	// 查询商品
	if productInfo, err = productSer.GetById(orderInfo.ProductID); err != nil {
		global.LOG.Error("CallBackFromPayCenter GetProductByIdUnscoped fail", zap.Error(err))
		return
	}
	// 查询是否是首次购买
	isFirstBuy := m.UserIsFirstBuy(userInfo.Id)
	appInfo := global.AppMap[userInfo.AppID]
	if profileInfo, err = profileSer.GetByUserId(userInfo.Id); err != nil {
		global.LOG.Error("CallBackFromPayCenter GetByUserProfileId fail", zap.Error(err))
		return
	}
	if orderInfo.AiRoleId != 0 {
		// 查询AI角色信息
		if aiRoleInfo, err = aiRoleSer.GetById(c, orderInfo.AiRoleId); err != nil {
			global.LOG.Error("CallBackFromPayCenter GetByAiRoleId fail", zap.Error(err))
			return
		}
	}
	err = global.DB.Transaction(func(tx *gorm.DB) (tErr error) {
		var (
			tdProperties = map[string]interface{}{
				"renew":       0,
				"productType": productInfo.ProductType,
				"amount":      productInfo.USDPrice,
			}
		)
		orderInfo.OrderStatus = model.OSPayGoodSuccess
		// 商品支付次数+1
		if tErr = tx.Model(&model.Product{}).Where("id = ?", productInfo.Id).Update("pay_count", gorm.Expr("pay_count + ?", 1)).Error; tErr != nil {
			global.LOG.Error("Update pay_count fail", zap.Error(tErr))
			return
		}
		switch productInfo.GoodType {
		case model.GTGood:
			addAmount := productInfo.Amount + productInfo.RewardAmount
			profileEditMap := map[string]interface{}{
				"good_count": gorm.Expr("good_count + 1"),
				"good_price": gorm.Expr("good_price + ?", productInfo.USDPrice),
			}
			// 一次性购买类商品
			switch productInfo.ProductType {
			case model.PTFlowers:
				if appInfo.Id == 19 || appInfo.Id == 23 || appInfo.Id == 126 || appInfo.Id == 127 ||
					appInfo.Id == 128 || appInfo.Id == 129 || appInfo.Id == 130 || appInfo.Id == 131 {
					btc := BtcService{}
					global.LOG.Info("CallBackFromPayCenter orderAfter btc start", zap.Any("orderNo", orderInfo.OrderNo))
					err = btc.orderAfter(c, int(orderInfo.DigitalUserID), int(orderInfo.ProductID), ProductTypeShop, time.Now())
					if err != nil {
						return err
					}
				} else {
					// 增加鲜花数量
					profileEditMap["flower_amount"] = gorm.Expr("flower_amount + ?", addAmount)
					if tErr = tx.Model(&model.DigitalUserProfile{}).Where("digital_user_id = ?", userInfo.Id).Updates(profileEditMap).Error; tErr != nil {
						return
					}
					// 设置用户已购买金币的标记
					m.SetUserIsBuyCoin(userInfo.Id)
				}
			case model.PTChat:
				// 增加畅聊包数量
				profileEditMap["chats_amount"] = gorm.Expr("chats_amount + ?", addAmount)
				if tErr = tx.Model(&model.DigitalUserProfile{}).Where("digital_user_id = ?", userInfo.Id).Updates(profileEditMap).Error; tErr != nil {
					return
				}
			case model.PTSVip:
				// 获取增加的鲜花数量
				addAmount = appInfo.DetailConfig.Data().SVipYearSendFlo
				profileEditMap["flower_amount"] = gorm.Expr("flower_amount + ?", addAmount)
				if tErr = tx.Model(&model.DigitalUserProfile{}).Where("digital_user_id = ?", userInfo.Id).Updates(profileEditMap).Error; tErr != nil {
					return
				}
				userEditMap := map[string]interface{}{
					"svip_expire": time.Now().AddDate(1, 0, 0),
					"svip_status": model.SubStatusIng,
					"svip_type":   model.SubTypeYear,
				}
				verCompare := utils.CompareVersions(userInfo.Version, "3.2.0")
				if verCompare < 0 {
					userEditMap["old_svip"] = true
				} else {
					userEditMap["old_svip"] = false
				}
				if tErr = tx.Model(&model.DigitalUser{}).Where("id = ?", userInfo.Id).Updates(userEditMap).Error; tErr != nil {
					return
				}
			case model.PTSVipWeek:
				// 获取增加的鲜花数量
				addAmount = appInfo.DetailConfig.Data().SVipWeekSendFlo
				profileEditMap["flower_amount"] = gorm.Expr("flower_amount + ?", addAmount)
				if tErr = tx.Model(&model.DigitalUserProfile{}).Where("digital_user_id = ?", userInfo.Id).Updates(profileEditMap).Error; tErr != nil {
					return
				}
				userEditMap := map[string]interface{}{
					"svip_expire": time.Now().AddDate(0, 0, 7),
					"svip_status": model.SubStatusIng,
					"svip_type":   model.SubTypeWeek,
				}
				verCompare := utils.CompareVersions(userInfo.Version, "3.2.0")
				if verCompare < 0 {
					userEditMap["old_svip"] = true
				} else {
					userEditMap["old_svip"] = false
				}
				if tErr = tx.Model(&model.DigitalUser{}).Where("id = ?", userInfo.Id).Updates(userEditMap).Error; tErr != nil {
					return
				}
			case model.PTSVipMonth:
				// 获取增加的鲜花数量
				addAmount = appInfo.DetailConfig.Data().SVipMonthSendFlo
				profileEditMap["flower_amount"] = gorm.Expr("flower_amount + ?", addAmount)
				if tErr = tx.Model(&model.DigitalUserProfile{}).Where("digital_user_id = ?", userInfo.Id).Updates(profileEditMap).Error; tErr != nil {
					return
				}
				userEditMap := map[string]interface{}{
					"svip_expire": time.Now().AddDate(0, 1, 0),
					"svip_status": model.SubStatusIng,
					"svip_type":   model.SubTypeMonth,
				}
				verCompare := utils.CompareVersions(userInfo.Version, "3.2.0")
				if verCompare < 0 {
					userEditMap["old_svip"] = true
				} else {
					userEditMap["old_svip"] = false
				}
				if tErr = tx.Model(&model.DigitalUser{}).Where("id = ?", userInfo.Id).Updates(userEditMap).Error; tErr != nil {
					return
				}
			}
			if addAmount > 0 {
				tdPointerMap := map[string]interface{}{
					"event_tag":         "coins_consume",
					"coins_consume_num": addAmount,
					"source":            "purchase_get",
				}
				if isFirstBuy {
					tdPointerMap["first_pay"] = 1
				} else {
					tdPointerMap["first_pay"] = 0
				}
				if appInfo.AppType == model.AppTypeAndroid {
					tdPointerMap["product_id"] = productInfo.GooID
				} else if appInfo.AppType == model.AppTypeIOS {
					tdPointerMap["product_id"] = productInfo.IOSID
				} else {
					tdPointerMap["product_id"] = productInfo.PaypalID
				}
				if productInfo.ProductType == model.PTSVip || productInfo.ProductType == model.PTSVipWeek || productInfo.ProductType == model.PTSVipMonth {
					tdPointerMap["source"] = "svip_get"
				}
				if aiRoleInfo != nil {
					tdPointerMap["ai_role_id"] = aiRoleInfo.RoleID
					tdPointerMap["ai_role_type"] = aiRoleInfo.RoleType
					if !orderTest {
						go TdPointer(userInfo, profileInfo, td.ServerEvent, tdPointerMap)
					}
				} else {
					if !orderTest {
						go TdPointer(userInfo, profileInfo, td.ServerEvent, tdPointerMap)
					}
				}
			}
		case model.GTSub:
			var (
				userSubType  model.SubType
				userSVipType model.SubType
				chatVipType  model.SubType
				contVipType  model.SubType
			)
			// 订阅类商品
			if userInfo.SubStatus != model.SubStatusNo {
				// 用户不是第一次订阅
				tdProperties["renew"] = 1
			} else {
				go m.sendSubNoBuyFloQueue(c, userInfo)
			}
			switch productInfo.ProductType {
			case model.PTChatVipWeek:
				chatVipType = model.SubTypeWeek
				if userInfo.ChatVipExpire.Before(now) {
					expiresDate = now.AddDate(0, 0, 7)
				} else {
					expiresDate = userInfo.ChatVipExpire.AddDate(0, 0, 7)
				}
				rewardFlo = appInfo.DetailConfig.Data().ChatVipWeekSendFlo
			case model.PTChatVipMonth:
				chatVipType = model.SubTypeMonth
				if userInfo.ChatVipExpire.Before(now) {
					expiresDate = now.AddDate(0, 1, 0)
				} else {
					expiresDate = userInfo.ChatVipExpire.AddDate(0, 1, 0)
				}
				rewardFlo = appInfo.DetailConfig.Data().ChatVipMonthSendFlo
			case model.PTChatVipYear:
				chatVipType = model.SubTypeYear
				if userInfo.ChatVipExpire.Before(now) {
					expiresDate = now.AddDate(1, 0, 0)
				} else {
					expiresDate = userInfo.ChatVipExpire.AddDate(1, 0, 0)
				}
				rewardFlo = appInfo.DetailConfig.Data().ChatVipYearSendFlo
			case model.PTContentVipWeek:
				contVipType = model.SubTypeWeek
				if userInfo.ContentVipExpire.Before(now) {
					expiresDate = now.AddDate(0, 0, 7)
				} else {
					expiresDate = userInfo.ContentVipExpire.AddDate(0, 0, 7)
				}
				rewardFlo = appInfo.DetailConfig.Data().ContentVipWeekSendFlo
			case model.PTContentVipMonth:
				contVipType = model.SubTypeMonth
				if userInfo.ContentVipExpire.Before(now) {
					expiresDate = now.AddDate(0, 1, 0)
				} else {
					expiresDate = userInfo.ContentVipExpire.AddDate(0, 1, 0)
				}
				rewardFlo = appInfo.DetailConfig.Data().ContentVipMonthSendFlo
			case model.PTContentVipYear:
				contVipType = model.SubTypeYear
				if userInfo.ContentVipExpire.Before(now) {
					expiresDate = now.AddDate(1, 0, 0)
				} else {
					expiresDate = userInfo.ContentVipExpire.AddDate(1, 0, 0)
				}
				rewardFlo = appInfo.DetailConfig.Data().ContentVipYearSendFlo
			case model.PTSubWeek:
				// 周订阅
				userSubType = model.SubTypeWeek
				if userInfo.SubExpire.Before(now) {
					expiresDate = now.AddDate(0, 0, 7)
				} else {
					expiresDate = userInfo.SubExpire.AddDate(0, 0, 7)
				}
				rewardFlo = appInfo.DetailConfig.Data().SubSendFlo
			case model.PTSubMonth:
				// 月订阅
				userSubType = model.SubTypeMonth
				if userInfo.SubExpire.Before(now) {
					expiresDate = now.AddDate(0, 1, 0)
				} else {
					expiresDate = userInfo.SubExpire.AddDate(0, 1, 0)
				}
				rewardFlo = appInfo.DetailConfig.Data().SubMonthSendFlo
			case model.PTSubYear:
				// 年订阅
				userSubType = model.SubTypeYear
				if userInfo.SubExpire.Before(now) {
					expiresDate = now.AddDate(1, 0, 0)
				} else {
					expiresDate = userInfo.SubExpire.AddDate(1, 0, 0)
				}
				rewardFlo = appInfo.DetailConfig.Data().SubYearSendFlo
			case model.PTSVip:
				userSVipType = model.SubTypeYear
				if userInfo.SVipExpire.Before(now) {
					expiresDate = now.AddDate(1, 0, 0)
				} else {
					expiresDate = userInfo.SVipExpire.AddDate(1, 0, 0)
				}
				rewardFlo = appInfo.DetailConfig.Data().SVipYearSendFlo
			case model.PTSVipWeek:
				userSVipType = model.SubTypeWeek
				if userInfo.SVipExpire.Before(now) {
					expiresDate = now.AddDate(0, 0, 7)
				} else {
					expiresDate = userInfo.SVipExpire.AddDate(0, 0, 7)
				}
				rewardFlo = appInfo.DetailConfig.Data().SVipWeekSendFlo
			case model.PTSVipMonth:
				userSVipType = model.SubTypeMonth
				if userInfo.SVipExpire.Before(now) {
					expiresDate = now.AddDate(0, 1, 0)
				} else {
					expiresDate = userInfo.SVipExpire.AddDate(0, 1, 0)
				}
				rewardFlo = appInfo.DetailConfig.Data().SVipMonthSendFlo
			default:
				tErr = fmt.Errorf("order pay success fail, unknown product type")
				return
			}
			// 订单订阅状态
			orderInfo.OrderSubStatus = model.SwitchStatusNormal
			userUpdateMap := map[string]interface{}{
				"sub_status": int(model.SubStatusIng),
				"sub_expire": expiresDate,
				"sub_type":   int(userSubType),
			}
			if productInfo.ProductType == model.PTSubWeek || productInfo.ProductType == model.PTSubMonth || productInfo.ProductType == model.PTSubYear {
				userUpdateMap = map[string]interface{}{
					"sub_status": model.SubStatusIng,
					"sub_expire": expiresDate,
					"sub_type":   userSubType,
				}
				if orderInfo.AppID == 6 || orderInfo.AppID == 13 {
					userUpdateMap = map[string]interface{}{
						"sub_status":  model.SubStatusIng,
						"sub_expire":  expiresDate,
						"sub_type":    userSubType,
						"svip_status": model.SubStatusIng,
						"svip_expire": expiresDate,
						"svip_type":   userSubType,
					}
				}
			} else if productInfo.ProductType == model.PTSVip || productInfo.ProductType == model.PTSVipWeek || productInfo.ProductType == model.PTSVipMonth {
				userUpdateMap = map[string]interface{}{
					"svip_status": model.SubStatusIng,
					"svip_expire": expiresDate,
					"svip_type":   userSVipType,
				}
				verCompare := utils.CompareVersions(userInfo.Version, "3.2.0")
				if verCompare < 0 {
					userUpdateMap["old_svip"] = true
				} else {
					userUpdateMap["old_svip"] = false
				}
			} else if productInfo.ProductType == model.PTChatVipWeek || productInfo.ProductType == model.PTChatVipMonth || productInfo.ProductType == model.PTChatVipYear {
				userUpdateMap = map[string]interface{}{
					"chat_vip_status": model.SubStatusIng,
					"chat_vip_expire": expiresDate,
					"chat_vip_type":   chatVipType,
				}
			} else if productInfo.ProductType == model.PTContentVipWeek || productInfo.ProductType == model.PTContentVipMonth || productInfo.ProductType == model.PTContentVipYear {
				userUpdateMap = map[string]interface{}{
					"content_vip_status": model.SubStatusIng,
					"content_vip_expire": expiresDate,
					"content_vip_type":   contVipType,
				}
			}
			// 更新用户订阅状态
			if tErr = tx.Model(&model.DigitalUser{}).Where("id = ?", userInfo.Id).Updates(userUpdateMap).Error; tErr != nil {
				return
			}
			userSer.DelCache(c, userInfo.Id)
			if rewardFlo > 0 {
				tdPointerMap := map[string]interface{}{
					"event_tag":         "coins_consume",
					"coins_consume_num": rewardFlo,
					"sub_type":          productInfo.ProductType,
					"amount":            productInfo.USDPrice,
					"source":            "sub_get",
				}
				if isFirstBuy {
					tdPointerMap["first_pay"] = 1
				} else {
					tdPointerMap["first_pay"] = 0
				}
				// 自己的商品ID
				if appInfo.AppType == model.AppTypeAndroid {
					tdPointerMap["product_id"] = productInfo.GooID
				} else if appInfo.AppType == model.AppTypeIOS {
					tdPointerMap["product_id"] = productInfo.IOSID
				} else {
					tdPointerMap["product_id"] = productInfo.PaypalID
				}
				if productInfo.ProductType == model.PTSVip || productInfo.ProductType == model.PTSVipWeek || productInfo.ProductType == model.PTSVipMonth {
					tdPointerMap["source"] = "svip_get"
				}
				if productInfo.ProductType == model.PTChatVipWeek || productInfo.ProductType == model.PTChatVipMonth || productInfo.ProductType == model.PTChatVipYear {
					tdPointerMap["source"] = "chat_vip_get"
				}
				if productInfo.ProductType == model.PTContentVipWeek || productInfo.ProductType == model.PTContentVipMonth || productInfo.ProductType == model.PTContentVipYear {
					tdPointerMap["source"] = "content_vip_get"
				}
				if aiRoleInfo != nil {
					tdPointerMap["ai_role_id"] = aiRoleInfo.RoleID
					tdPointerMap["ai_role_type"] = aiRoleInfo.RoleType
					if !orderTest {
						go TdPointer(userInfo, profileInfo, td.ServerEvent, tdPointerMap)
					}
				} else {
					if !orderTest {
						go TdPointer(userInfo, profileInfo, td.ServerEvent, tdPointerMap)
					}
				}
			}
			profileUpdateMap := map[string]interface{}{
				"nsfw":          true,
				"flower_amount": gorm.Expr("flower_amount + ?", rewardFlo),
				"sub_count":     gorm.Expr("sub_count + 1"),
				"sub_price":     gorm.Expr("sub_price + ?", productInfo.USDPrice),
			}
			// 更新用户profile的nsfw状态
			if tErr = tx.Model(&model.DigitalUserProfile{}).Where("digital_user_id = ?", userInfo.Id).Updates(profileUpdateMap).Error; tErr != nil {
				return
			}
			_ = call.AddFreeTime(c, userInfo.Id, 60)
		default:
			tErr = fmt.Errorf("order pay success fail, unknown good type")
			return
		}
		adjustLockKey := fmt.Sprintf(global.ADJUST_ORDER_LOCK_KEY, orderInfo.TradeNo)
		lockRes := global.REDIS.SetNX(context.Background(), adjustLockKey, 1, time.Hour*24*7).Val()

		// 99.99美金的订单，直接 order_test 设置成测试订单类型 ,不上报adjust，不让bi拉取
		/*
			if productInfo.Id == 37 || productInfo.Id == 38 {
					// 鲜花和畅聊包不上报数数和adjust
					orderTest = true
					orderInfo.OrderTest = orderTest
				}
		*/
		// 订单修改
		if isFreeSub {
			orderInfo.USDPrice = decimal.Zero
		}
		if tErr = tx.Model(&model.Order{}).Where("id = ?", orderInfo.Id).Updates(orderInfo).Error; tErr != nil {
			return
		}
		go firebaseUtilSer.SetCacheForApp(c, orderInfo.OrderNo)
		if lockRes {
			// 数数打点
			// go m.SendTdIapServer(userInfo, tdProperties)
			// adjust上报
			go m.SendAdjust(userInfo, profileInfo, orderInfo.USDPrice, "USD", orderInfo.PayTime.Unix(), orderTest, orderInfo.OrderNo, isFreeSub)
			if productInfo.ProductType == model.PTSVip || productInfo.ProductType == model.PTSVipWeek || productInfo.ProductType == model.PTSVipMonth {
				// svip
				go m.SendAdjustSvip(orderInfo.OrderNo, isFreeSub)
			} else {
				go m.SendAdjustOther(userInfo, profileInfo, orderInfo.USDPrice, "USD", orderInfo.PayTime.Unix(), orderTest, orderInfo.OrderNo, productInfo.AdjustToken, isFreeSub)
			}
		}

		if userInfo.AppID == 16 {
			cost := 0
			types := model.VideoSimplifyConsumeTypeVipFlowers
			if productInfo.GoodType == model.GTGood {
				cost = productInfo.Amount
				types = model.VideoSimplifyConsumeTypeBuyFlowers
			}
			if productInfo.GoodType == model.GTSub {
				cost = rewardFlo
				types = model.VideoSimplifyConsumeTypeVipFlowers
			}
			var msgService MsgService
			_ = msgService.SaveVideoSimplifyConsumeRecord(userInfo.Id, cost, types, 0, 0)
		}

		return
	})
	if err != nil {
		return
	}
	// Firebase上报保存
	var fbSer FireBaseUtilSer
	productID := productInfo.GooID
	if appInfo.AppType == model.AppTypeIOS {
		productID = productInfo.IOSID
	} else if appInfo.AppType == model.AppTypeWeb {
		productID = productInfo.PaypalID
	}
	_ = fbSer.SetFirebaseReportEvent(c, userInfo.Id, response.FirebaseReportItem{
		EventID:   uuid.NewString(),
		EventName: "purchase",
		PropertyMap: map[string]interface{}{
			"currency":       "USD",
			"value":          orderInfo.USDPrice.InexactFloat64(),
			"transaction_id": orderInfo.OrderNo,
			"items": []map[string]interface{}{
				{
					"item_id":   productID,
					"item_name": productInfo.Name,
				},
			},
		},
	})

	if orderInfo.AiRoleId != 0 && aiRoleInfo != nil {
		switch productInfo.GoodType {
		case model.GTGood:
			// 更新UserAiMsgCount
			if err = msgCountSer.FindUpdate(c, orderInfo.DigitalUserID, aiRoleInfo, map[string]interface{}{
				"good_count": gorm.Expr("good_count + 1"),
				"good_price": gorm.Expr("good_price + ?", productInfo.USDPrice),
			}); err != nil {
				return
			}
		case model.GTSub:
			// 更新UserAiMsgCount
			if err = msgCountSer.FindUpdate(c, orderInfo.DigitalUserID, aiRoleInfo, map[string]interface{}{
				"sub_count": gorm.Expr("sub_count + 1"),
				"sub_price": gorm.Expr("sub_price + ?", productInfo.USDPrice),
			}); err != nil {
				return
			}
		default:
		}

	}
	// 删除用户缓存
	userSer.DelCache(c, userInfo.Id)
	return
}

func (m *OrdersService) ReSubSuccess(c *gin.Context, orderInfo *model.Order, expiresDate time.Time, tradeNo string, orderTest bool) (err error) {
	var (
		userInfo       *model.DigitalUser
		productInfo    *model.Product
		productSer     ProductService
		userSer        DigitalUserService
		profileInfo    *model.DigitalUserProfile
		profileService DigitalUserProfileService
		now            = time.Now()
	)
	// 查询用户
	if userInfo, err = userSer.GetByIdUnscoped(c, orderInfo.DigitalUserID); err != nil {
		return
	}
	// 查询商品
	if productInfo, err = productSer.GetById(orderInfo.ProductID); err != nil {
		return
	}

	appInfo := global.AppMap[userInfo.AppID]
	reSubRewardCoinCfg := GetReSubRewardCoinCfg(c, userInfo.AppID)
	if profileInfo, err = profileService.GetByUserId(userInfo.Id); err != nil {
		return
	}
	err = global.DB.Transaction(func(tx *gorm.DB) (tErr error) {
		var rewardCoin int
		switch productInfo.GoodType {
		case model.GTSub:
			switch productInfo.ProductType {
			case model.PTChatVipWeek:
				if userInfo.ChatVipExpire.Before(now) {
					expiresDate = now.AddDate(0, 0, 7)
				} else {
					expiresDate = userInfo.ChatVipExpire.AddDate(0, 0, 7)
				}
			case model.PTChatVipMonth:
				if userInfo.ChatVipExpire.Before(now) {
					expiresDate = now.AddDate(0, 1, 0)
				} else {
					expiresDate = userInfo.ChatVipExpire.AddDate(0, 1, 0)
				}
			case model.PTChatVipYear:
				if userInfo.ChatVipExpire.Before(now) {
					expiresDate = now.AddDate(1, 0, 0)
				} else {
					expiresDate = userInfo.ChatVipExpire.AddDate(1, 0, 0)
				}
			case model.PTContentVipWeek:
				if userInfo.ContentVipExpire.Before(now) {
					expiresDate = now.AddDate(0, 0, 7)
				} else {
					expiresDate = userInfo.ContentVipExpire.AddDate(0, 0, 7)
				}
			case model.PTContentVipMonth:
				if userInfo.ContentVipExpire.Before(now) {
					expiresDate = now.AddDate(0, 1, 0)
				} else {
					expiresDate = userInfo.ContentVipExpire.AddDate(0, 1, 0)
				}
			case model.PTContentVipYear:
				if userInfo.ContentVipExpire.Before(now) {
					expiresDate = now.AddDate(1, 0, 0)
				} else {
					expiresDate = userInfo.ContentVipExpire.AddDate(1, 0, 0)
				}
			case model.PTSubWeek:
				rewardCoin = reSubRewardCoinCfg.WeekVip
				// 周订阅
				if userInfo.SubExpire.Before(now) {
					expiresDate = now.AddDate(0, 0, 7)
				} else {
					expiresDate = userInfo.SubExpire.AddDate(0, 0, 7)
				}
			case model.PTSubMonth:
				rewardCoin = reSubRewardCoinCfg.MonthVip
				// 月订阅
				if userInfo.SubExpire.Before(now) {
					expiresDate = now.AddDate(0, 1, 0)
				} else {
					expiresDate = userInfo.SubExpire.AddDate(0, 1, 0)
				}
			case model.PTSubYear:
				rewardCoin = reSubRewardCoinCfg.YearVip
				// 年订阅
				if userInfo.SubExpire.Before(now) {
					expiresDate = now.AddDate(1, 0, 0)
				} else {
					expiresDate = userInfo.SubExpire.AddDate(1, 0, 0)
				}
			case model.PTSVip:
				if userInfo.SVipExpire.Before(now) {
					expiresDate = now.AddDate(1, 0, 0)
				} else {
					expiresDate = userInfo.SVipExpire.AddDate(1, 0, 0)
				}
			case model.PTSVipWeek:
				if userInfo.SVipExpire.Before(now) {
					expiresDate = now.AddDate(0, 0, 7)
				} else {
					expiresDate = userInfo.SVipExpire.AddDate(0, 0, 7)
				}
			case model.PTSVipMonth:
				if userInfo.SVipExpire.Before(now) {
					expiresDate = now.AddDate(0, 1, 0)
				} else {
					expiresDate = userInfo.SVipExpire.AddDate(0, 1, 0)
				}
			default:
				tErr = fmt.Errorf("order pay success fail, unknown product type")
				return
			}
			userUpdateMap := map[string]interface{}{
				"sub_status": int(model.SubStatusIng),
				"sub_expire": expiresDate,
			}
			if productInfo.ProductType == model.PTSubWeek || productInfo.ProductType == model.PTSubMonth || productInfo.ProductType == model.PTSubYear {
				userUpdateMap = map[string]interface{}{
					"sub_status": model.SubStatusIng,
					"sub_expire": expiresDate,
				}
				if orderInfo.AppID == 6 || orderInfo.AppID == 13 {
					userUpdateMap = map[string]interface{}{
						"sub_status":  model.SubStatusIng,
						"sub_expire":  expiresDate,
						"svip_status": model.SubStatusIng,
						"svip_expire": expiresDate,
					}
				}
			} else if productInfo.ProductType == model.PTSVip || productInfo.ProductType == model.PTSVipWeek || productInfo.ProductType == model.PTSVipMonth {
				userUpdateMap = map[string]interface{}{
					"svip_status": model.SubStatusIng,
					"svip_expire": expiresDate,
				}
			}
			if productInfo.ProductType == model.PTChatVipWeek || productInfo.ProductType == model.PTChatVipMonth || productInfo.ProductType == model.PTChatVipYear {
				userUpdateMap = map[string]interface{}{
					"chat_vip_status": model.SubStatusIng,
					"chat_vip_expire": expiresDate,
				}
			}
			if productInfo.ProductType == model.PTContentVipWeek || productInfo.ProductType == model.PTContentVipMonth || productInfo.ProductType == model.PTContentVipYear {
				userUpdateMap = map[string]interface{}{
					"content_vip_status": model.SubStatusIng,
					"content_vip_expire": expiresDate,
				}
			}
			// 更新用户订阅状态
			if tErr = tx.Model(&model.DigitalUser{}).Where("id = ?", userInfo.Id).Updates(userUpdateMap).Error; tErr != nil {
				return
			}
			profileUpdateMap := map[string]interface{}{
				"nsfw":      true,
				"sub_count": gorm.Expr("sub_count + 1"),
				"sub_price": gorm.Expr("sub_price + ?", productInfo.USDPrice),
			}
			if rewardCoin > 0 {
				profileUpdateMap["flower_amount"] = gorm.Expr("flower_amount + ?", rewardCoin)
			}
			if tErr = tx.Model(&model.DigitalUserProfile{}).Where("digital_user_id = ?", userInfo.Id).Updates(profileUpdateMap).Error; tErr != nil {
				return
			}
		default:
			tErr = fmt.Errorf("order resub not sub type")
			return
		}
		// 真实订单才上报数数和adjust
		adjustLockKey := fmt.Sprintf(global.ADJUST_ORDER_LOCK_KEY, tradeNo)
		lockRes := global.REDIS.SetNX(context.Background(), adjustLockKey, 1, time.Hour*24*7).Val()

		if lockRes {
			tdPointerMap := map[string]interface{}{
				"event_tag": "re_sub",
				"sub_type":  productInfo.ProductType,
				"amount":    productInfo.USDPrice,
			}
			// 自己的商品ID
			if appInfo.AppType == model.AppTypeAndroid {
				tdPointerMap["product_id"] = productInfo.GooID
			} else if appInfo.AppType == model.AppTypeIOS {
				tdPointerMap["product_id"] = productInfo.IOSID
			} else {
				tdPointerMap["product_id"] = productInfo.PaypalID
			}
			go TdPointer(userInfo, profileInfo, td.ServerEvent, tdPointerMap)
			// 数数打点
			// go m.SendTdIapServer(userInfo, tdProperties)
			// adjust上报
			go m.SendAdjust(userInfo, profileInfo, productInfo.USDPrice, "USD", now.Unix(), orderTest, orderInfo.OrderNo, false)
			// 续订订单增加记录
			reSubRecord := model.ReSubOrder{
				AppID:         userInfo.AppID,
				DigitalUserID: userInfo.Id,
				ProductID:     orderInfo.ProductID,
				OrderNo:       orderInfo.OrderNo,
				TradeNo:       tradeNo,
				PurchaseToken: orderInfo.PurchaseToken,
				PayAmount:     productInfo.USDPrice,
				PayTime:       &now,
				USDPrice:      productInfo.USDPrice,
				OrderTest:     orderTest,
			}
			if tErr = tx.Model(&model.ReSubOrder{}).Create(&reSubRecord).Error; tErr != nil {
				return
			}
			_ = call.AddFreeTime(c, userInfo.Id, 60)
		}
		return
	})
	// 删除用户缓存
	userSer.DelCache(c, userInfo.Id)
	return
}

func (m *OrdersService) sendSubNoBuyFloQueue(ctx context.Context, userInfo *model.DigitalUser) {
	subNoBuyCfg := GetSubNoBuyFloCfg(ctx)
	if subNoBuyCfg.Flo <= 0 {
		return
	}
	score := time.Now().Add(time.Minute * time.Duration(subNoBuyCfg.Minute)).Unix()
	queueItem := req.SubNoBuyFloQueueItem{
		UserId: userInfo.Id,
	}
	queueItemBytes, err := json.Marshal(queueItem)
	if err != nil {
		return
	}
	queueItemStr := string(queueItemBytes)
	err = global.REDIS.ZAdd(ctx, global.SUB_NO_BUY_FLO_QUEUE_KEY, redis.Z{Score: float64(score), Member: queueItemStr}).Err()
	if err != nil {
		global.LOG.Error("sendSubNoBuyFloQueue error", zap.Error(err))
	}
}

func (m *OrdersService) SubNoBuyFloQueue() {
	var (
		ctx      = context.Background()
		redisKey = global.SUB_NO_BUY_FLO_QUEUE_KEY
	)
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Error("SubNoBuyFloQueue panic", zap.Any("err", err))
		}
	}()
	for {
		time.Sleep(time.Second)
		now := time.Now()
		nowUnix := now.Unix()
		filter := &redis.ZRangeBy{
			Min:    "0",
			Max:    fmt.Sprintf("%d", nowUnix),
			Offset: 0,
			Count:  0,
		}
		redisMsgList, err := global.REDIS.ZRangeByScore(ctx, redisKey, filter).Result()
		if err != nil {
			global.LOG.Error("ZRevRange error", zap.Any("err", err))
			continue
		}
		if len(redisMsgList) == 0 {
			continue
		}
		memberStr := redisMsgList[0]
		if err = m.ExecSubNoBuyFlo(ctx, memberStr); err != nil {
			global.LOG.Error("execSubNoBuyFlo error", zap.Any("err", err))
		}
		if _, err = global.REDIS.ZRem(ctx, redisKey, memberStr).Result(); err != nil {
			global.LOG.Error("ZRem error", zap.Any("err", err))
		}
	}

}
func (m *OrdersService) ExecSubNoBuyFlo(ctx context.Context, memberStr string) (err error) {
	var (
		buyCount       int64
		reqData        req.SubNoBuyFloQueueItem
		profileService DigitalUserProfileService
		subNoBuyCfg    = GetSubNoBuyFloCfg(ctx)
	)
	if subNoBuyCfg.Flo <= 0 {
		return
	}
	if err = json.Unmarshal([]byte(memberStr), &reqData); err != nil {
		return
	}
	// 查询用户是否购买了鲜花商品
	err = global.DB.Raw("SELECT COUNT(1) AS buyCount FROM orders AS o LEFT JOIN product AS p ON o.product_id = p.id WHERE o.digital_user_id = ? AND o.order_status = ? AND p.product_type = ?", reqData.UserId, model.OSPayGoodSuccess, model.PTFlowers).Scan(&buyCount).Error
	if err != nil {
		return
	}
	if buyCount > 0 {
		return
	}

	if err = profileService.UpdateByMap(ctx, reqData.UserId, map[string]interface{}{
		"flower_amount": gorm.Expr("flower_amount + ?", subNoBuyCfg.Flo),
	}); err != nil {
		return
	}
	return
}

func (m *OrdersService) PayMethodList(c *gin.Context, reqData *request.PayMethodListReq) (res response.PayMethodListRes, err error) {
	var (
		userInfo            *model.DigitalUser
		userService         DigitalUserService
		payCenterMethodList []response.PayCenterMethodListRes
		userId              = utils.GetDigitalUserID(c)
		clientIP            = c.ClientIP()
		countryCode         string
	)
	gLog := global.LOG.With(zap.String("func", "PayMethodList"), zap.Any("reqData", reqData))
	global.LOG.Info("PayMethodList start", zap.Any("userId", userId))
	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		gLog.Error("user GetById fail", zap.Error(err))
		global.LOG.Error("PayMethodList userService.GetById error", zap.Any("userId", userId), zap.Error(err))
		return
	}
	appInfo := global.AppMap[userInfo.AppID]
	gLog = gLog.With(zap.Any("userInfo", userInfo))
	// 根据IP获取国家码
	ipInfo, ipGeoErr := GetIpInfoWithCache(clientIP)
	if ipGeoErr != nil && ipInfo.Data.Areacode == "" {
		global.LOG.Error("GetIpInfoWithCache fail", zap.Error(ipGeoErr))
		countryCode = userInfo.CountryCode
	} else {
		countryCode = ipInfo.Data.Areacode
	}

	// 调用支付中心接口
	pMReq := &request.PayCenterMethodListReq{
		AppId:       userInfo.AppID,
		BundleId:    appInfo.PackageName,
		ProjectId:   global.PayCenterProjectId,
		CountryCode: countryCode,
	}

	if payCenterMethodList, err = m.MethodListFromPayCenter(userInfo.AppID, pMReq); err != nil {
		gLog.Error("MethodListFromPayCenter fail", zap.Error(err))
		global.LOG.Error("PayMethodList SubMethodListFromPayCenter-->2 error", zap.Any("userId", userId),
			zap.Any("pMReq", pMReq), zap.Any("appId", userInfo.AppID), zap.Error(err))
		return
	}

	gLog = gLog.With(zap.Any("payCenterMethodList", payCenterMethodList))
	if len(payCenterMethodList) == 0 {
		err = fmt.Errorf("payCenterMethodList is empty")
		gLog.Error("payCenterMethodList is empty")
		global.LOG.Error("PayMethodList payCenterMethodList is empty", zap.Any("userId", userId))
		return
	}
	for _, methodItemRes := range payCenterMethodList {
		res.List = append(res.List, response.PayMethodItemRes{
			Id:   methodItemRes.Id,
			Name: methodItemRes.Name,
			Icon: methodItemRes.Icon,
		})
	}

	if res.List == nil {
		err = fmt.Errorf("payMethodList is empty")
		gLog.Error("payMethodList is empty")
		global.LOG.Error("PayMethodList payMethodList is empty", zap.Any("userId", userId), zap.Error(err))
		return
	}

	return
}

func (m *OrdersService) WebMethodList(c *gin.Context, reqData *request.PayMethodListReq) (res response.PayMethodListRes, err error) {
	var (
		userInfo               *model.DigitalUser
		productInfo            *model.Product
		productService         ProductService
		userService            DigitalUserService
		payCenterMethodList    []response.PayCenterMethodListRes
		payCenterSubMethodList []response.SubPayCenterMethodListRes
		userId                 = utils.GetDigitalUserID(c)
	)
	gLog := global.LOG.With(zap.String("func", "WebMethodList"), zap.Any("reqData", reqData))
	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		gLog.Error("user GetById fail", zap.Error(err))
		return
	}
	appInfo := global.AppMap[userInfo.AppID]
	gLog = gLog.With(zap.Any("userInfo", userInfo))
	// 获取商品信息
	if productInfo, err = productService.GetById(reqData.ProductId); err != nil {
		gLog.Error("product GetById fail", zap.Error(err))
		return
	}
	gLog = gLog.With(zap.Any("productInfo", productInfo))

	switch productInfo.GoodType {
	case model.GTGood:
		// 调用支付中心接口
		pMReq := &request.PayCenterMethodListReq{
			AppId:       userInfo.AppID,
			BundleId:    appInfo.PackageName,
			ProjectId:   global.PayCenterProjectId,
			CountryCode: "US",
		}
		if payCenterMethodList, err = m.MethodListFromPayCenter(userInfo.AppID, pMReq); err != nil {
			gLog.Error("MethodListFromPayCenter fail", zap.Error(err))
			return
		}
		gLog = gLog.With(zap.Any("payCenterMethodList", payCenterMethodList))
		if len(payCenterMethodList) == 0 {
			err = fmt.Errorf("payCenterMethodList is empty")
			gLog.Error("payCenterMethodList is empty")
			return
		}
		for _, methodItemRes := range payCenterMethodList {
			if methodItemRes.Id == 10000 {
				continue
			}
			res.List = append(res.List, response.PayMethodItemRes{
				Id:   methodItemRes.Id,
				Name: methodItemRes.Name,
				Icon: methodItemRes.Icon,
			})
		}
	case model.GTSub:
		// 调用支付中心接口
		pMReq := &request.PayCenterSubMethodListReq{
			AppId:       userInfo.AppID,
			BundleId:    appInfo.PackageName,
			ProjectId:   global.PayCenterProjectId,
			CountryCode: "US",
		}
		if payCenterSubMethodList, err = m.SubMethodListFromPayCenter(userInfo.AppID, pMReq); err != nil {
			gLog.Error("MethodListFromPayCenter fail", zap.Error(err))
			return
		}
		if len(payCenterSubMethodList) == 0 {
			err = fmt.Errorf("payCenterMethodList is empty")
			gLog.Error("payCenterMethodList is empty")
			return
		}
		for _, methodItemRes := range payCenterSubMethodList {
			if methodItemRes.Id == 10000 {
				continue
			}
			res.List = append(res.List, response.PayMethodItemRes{
				Id:   methodItemRes.Id,
				Name: methodItemRes.Name,
				Icon: methodItemRes.Icon,
			})
		}
	default:
		err = fmt.Errorf("product good_type error")
		return
	}
	return
}

func (m *OrdersService) SubPayMethodList(c *gin.Context, reqData *request.SubPayMethodListReq) (res response.PayMethodListRes, err error) {
	var (
		userInfo            *model.DigitalUser
		productInfo         *model.Product
		productService      ProductService
		userService         DigitalUserService
		payCenterMethodList []response.SubPayCenterMethodListRes
		userId              = utils.GetDigitalUserID(c)
		toPayCenter         = false
	)
	gLog := global.LOG.With(zap.String("func", "PayMethodList"), zap.Any("reqData", reqData))
	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		gLog.Error("user GetById fail", zap.Error(err))
		return
	}
	appInfo := global.AppMap[userInfo.AppID]
	gLog = gLog.With(zap.Any("userInfo", userInfo))
	// 获取商品信息
	if productInfo, err = productService.GetById(reqData.ProductId); err != nil {
		gLog.Error("product GetById fail", zap.Error(err))
		return
	}
	gLog = gLog.With(zap.Any("productInfo", productInfo))
	isAudit := userInfo.IsAudit()
	goodCountrysCfg := GetToPayCenterCountrySub(c, appInfo.Id)
	toPayCenter = !isAudit
	if len(goodCountrysCfg.Countrys) != 0 && toPayCenter {
		countryMap := map[string]bool{}
		for _, country := range goodCountrysCfg.Countrys {
			countryMap[country] = true
		}
		toPayCenter = countryMap[userInfo.CountryCode]
	}
	if userInfo.AppID == 5 {
		toPayCenter = true
	}
	if userInfo.AppID == 10 {
		toPayCenter = false
	}
	if userInfo.AppID == 2 {
		toPayCenter = true
	}

	gLog = gLog.With(zap.Bool("toPayCenter", toPayCenter))
	if toPayCenter {
		// 调用支付中心接口
		pMReq := &request.PayCenterSubMethodListReq{
			AppId:       userInfo.AppID,
			BundleId:    appInfo.PackageName,
			ProjectId:   global.PayCenterProjectId,
			CountryCode: userInfo.CountryCode,
		}
		if userInfo.AppID == 5 {
			pMReq.CountryCode = "US"
		}
		if userInfo.AppID == 2 {
			pMReq.CountryCode = "US"
		}

		if payCenterMethodList, err = m.SubMethodListFromPayCenter(userInfo.AppID, pMReq); err != nil {
			gLog.Error("MethodListFromPayCenter fail", zap.Error(err))
			return
		}
		gLog = gLog.With(zap.Any("payCenterMethodList", payCenterMethodList))
		if len(payCenterMethodList) == 0 {
			err = fmt.Errorf("payCenterMethodList is empty")
			gLog.Error("payCenterMethodList is empty")
			return
		}
		for _, methodItemRes := range payCenterMethodList {
			if methodItemRes.Id == 10000 {
				continue
			}
			res.List = append(res.List, response.PayMethodItemRes{
				Id:   methodItemRes.Id,
				Name: methodItemRes.Name,
				Icon: methodItemRes.Icon,
			})
		}
	} else {
		// 获取支付方式列表
		err = fmt.Errorf("please pay, toPayCenter: %v, userAudit: %v, CountryCode: %v", toPayCenter, isAudit, userInfo.CountryCode)
	}

	return
}

func (m *OrdersService) MethodListFromPayCenter(appId uint, payCenterReq *request.PayCenterMethodListReq) (res []response.PayCenterMethodListRes, err error) {
	var (
		resp      response.PayCenterMethodListResponse
		reqData   = make(map[string]interface{})
		apiLogStr string
		curlStr   string
	)
	reqData = utils.Struct2Map(payCenterReq)
	appInfo := global.AppMap[appId]
	headerMap := map[string]string{
		"App-Id":    fmt.Sprintf("%v", payCenterReq.AppId),
		"Bundle-Id": appInfo.PackageName,
	}
	apiLogStr, curlStr, err = utils.HttpPostJsonWithHeader(global.PayCenterMethodListUrl, reqData, headerMap, &resp, 10)
	if err != nil {
		global.LOG.Error("MethodListFromPayCenter fail", zap.Error(err), zap.String("apiLog", apiLogStr), zap.String("curl", curlStr))
		err = fmt.Errorf("RequestFail, apiLog: %s", apiLogStr)
		return
	}
	if resp.Code != 200 {
		err = fmt.Errorf("RequestFail, apiLog: %s", apiLogStr)
		return
	}

	if payCenterReq.ProjectId == 16 {
		global.LOG.Info("MethodListFromPayCenter end", zap.Any("reqData", reqData), zap.Any("respData", resp))
	}

	res = resp.Data.List
	return
}

func (m *OrdersService) SubMethodListFromPayCenter(appId uint, payCenterReq *request.PayCenterSubMethodListReq) (res []response.SubPayCenterMethodListRes, err error) {
	var (
		resp      response.SubPayCenterMethodListResponse
		reqData   = make(map[string]interface{})
		apiLogStr string
		curlStr   string
	)
	reqData = utils.Struct2Map(payCenterReq)
	appInfo := global.AppMap[appId]
	headerMap := map[string]string{
		"App-Id":    fmt.Sprintf("%v", payCenterReq.AppId),
		"Bundle-Id": appInfo.PackageName,
	}
	apiLogStr, curlStr, err = utils.HttpPostJsonWithHeader(global.PayCenterSubMethodListUrl, reqData, headerMap, &resp, 10)
	if err != nil {
		global.LOG.Error("MethodListFromPayCenter fail", zap.Error(err), zap.String("apiLog", apiLogStr), zap.String("curl", curlStr))
		err = fmt.Errorf("RequestFail, apiLog: %s", apiLogStr)
		return
	}
	if resp.Code != 200 {
		err = fmt.Errorf("Code != 200, apiLog: %s", apiLogStr)
		return
	}
	res = resp.Data.List
	return
}

func (m *OrdersService) GetUrlFromPayCenter(payCenterReq request.PayCenterReq) (payUrl, apiLogStr string, err error) {
	var (
		curlStr string
		resp    response.PayCenterPayResponse
		// notifyUrl    = global.ReturnPrefix + "/Callback/PayCenter"
		// exchangeRate = decimal.NewFromFloat(1)
	)
	reqMap := utils.Struct2Map(payCenterReq)
	headerMap := map[string]string{
		"App-Id":    fmt.Sprintf("%v", payCenterReq.AppId),
		"Bundle-Id": payCenterReq.BundleId,
	}
	apiLogStr, curlStr, err = utils.HttpPostJsonWithHeader(global.PayCenterPayUrl, reqMap, headerMap, &resp, 10)
	if err != nil {
		global.LOG.Error("GetUrlFromPayCenter fail", zap.Error(err), zap.String("apiLog", apiLogStr), zap.String("curl", curlStr))
		return
	}
	if resp.Code != 200 {
		err = fmt.Errorf("RequestFail: %s", resp.Msg)
		return
	}
	payUrl = resp.Data.PayURL
	return
}

func (m *OrdersService) GetSubUrlFromPayCenter(payCenterReq request.PayCenterSubscriptReq) (payUrl, apiLogStr string, err error) {
	var (
		curlStr string
		resp    response.PayCenterSubPayResponse
	)
	reqMap := utils.Struct2Map(payCenterReq)
	headerMap := map[string]string{
		"App-Id":    fmt.Sprintf("%v", payCenterReq.AppId),
		"Bundle-Id": payCenterReq.BundleId,
	}
	apiLogStr, curlStr, err = utils.HttpPostJsonWithHeader(global.PayCenterSubUrl, reqMap, headerMap, &resp, 10)
	if err != nil {
		global.LOG.Error("GetSubUrlFromPayCenter fail", zap.Error(err), zap.String("apiLog", apiLogStr), zap.String("curl", curlStr))
		err = fmt.Errorf("RequestFail: %s, apiLog: %s", err.Error(), apiLogStr)
		return
	}
	if resp.Code != 200 {
		err = fmt.Errorf("RequestFail: %s, apiLog: %s", resp.Msg, apiLogStr)
		return
	}
	payUrl = resp.Data.PayUrl
	return
}

func (m *OrdersService) FixReSubOrderSVIP() {
	var (
		err         error
		reSubOrders []model.ReSubOrder
		userSer     DigitalUserService
		ctx         = context.Background()
	)
	err = global.DB.Raw("SELECT rs.* FROM re_sub_orders AS rs LEFT JOIN product AS p ON rs.product_id = p.id WHERE rs.created_at > '2024-09-01' AND (p.product_type = 106 OR p.product_type = 107 OR p.product_type = 108) ORDER BY rs.id ASC").Scan(&reSubOrders).Error
	if err != nil {
		global.LOG.Error("FixReSubOrderSVIP error", zap.Error(err))
		return
	}
	for _, reSubOrder := range reSubOrders {
		// 查询商品
		var (
			expiresDate time.Time
		)

		productInfo, productInfoExist := global.AllProductMap[reSubOrder.ProductID]
		if !productInfoExist {
			global.LOG.Error("FixReSubOrderSVIP productInfo not exist", zap.Any("reSubOrder", reSubOrder))
			continue
		}
		switch productInfo.ProductType {
		case model.PTSVip:
			// 年订阅
			expiresDate = reSubOrder.PayTime.AddDate(1, 0, 0)
		case model.PTSVipMonth:
			// 月订阅
			expiresDate = reSubOrder.PayTime.AddDate(0, 1, 0)
		case model.PTSVipWeek:
			// 周订阅
			expiresDate = reSubOrder.PayTime.AddDate(0, 0, 7)
		default:
			global.LOG.Error("FixReSubOrderSVIP productInfo.ProductType error", zap.Any("reSubOrder", reSubOrder))
			continue
		}

		editMap := map[string]interface{}{
			"svip_status": model.SubStatusIng,
			"svip_expire": expiresDate,
		}
		err = userSer.UpdateByMap(ctx, reSubOrder.DigitalUserID, editMap)
		if err != nil {
			global.LOG.Error("FixReSubOrderSVIP UpdateByMap error", zap.Error(err))
			continue
		}
	}
	global.LOG.Info("FixReSubOrderSVIP success")
}

func (m *OrdersService) FixReSubOrderVIP() {
	var (
		err         error
		reSubOrders []model.ReSubOrder
		userSer     DigitalUserService
		ctx         = context.Background()
	)
	err = global.DB.Raw("SELECT rs.* FROM re_sub_orders AS rs LEFT JOIN product AS p ON rs.product_id = p.id WHERE rs.created_at > '2024-09-01' AND (p.product_type = 1 OR p.product_type = 2 OR p.product_type = 3) ORDER BY rs.id ASC").Scan(&reSubOrders).Error
	if err != nil {
		global.LOG.Error("FixReSubOrderVIP error", zap.Error(err))
		return
	}
	sLen := len(reSubOrders)
	for index, reSubOrder := range reSubOrders {
		// 查询商品
		var (
			expiresDate time.Time
		)

		productInfo, productInfoExist := global.AllProductMap[reSubOrder.ProductID]
		if !productInfoExist {
			global.LOG.Error("FixReSubOrderVIP productInfo not exist", zap.Any("reSubOrder", reSubOrder))
			continue
		}
		switch productInfo.ProductType {
		case model.PTSubYear:
			// 年订阅
			expiresDate = reSubOrder.PayTime.AddDate(1, 0, 0)
		case model.PTSubMonth:
			// 月订阅
			expiresDate = reSubOrder.PayTime.AddDate(0, 1, 0)
		case model.PTSubWeek:
			// 周订阅
			expiresDate = reSubOrder.PayTime.AddDate(0, 0, 7)
		default:
			global.LOG.Error("FixReSubOrderVIP productInfo.ProductType error", zap.Any("reSubOrder", reSubOrder))
			continue
		}

		editMap := map[string]interface{}{
			"sub_status": model.SubStatusIng,
			"sub_expire": expiresDate,
		}
		err = userSer.UpdateByMap(ctx, reSubOrder.DigitalUserID, editMap)
		if err != nil {
			global.LOG.Error("FixReSubOrderVIP UpdateByMap error", zap.Error(err))
			continue
		}
		fmt.Println("index", index, sLen)
	}
	global.LOG.Info("FixReSubOrderVIP success")
}

func (m *OrdersService) FixOrderVIP() {
	var (
		err     error
		oArrs   []model.Order
		userSer DigitalUserService
		ctx     = context.Background()
	)
	err = global.DB.Raw("SELECT rs.* FROM orders AS rs LEFT JOIN product AS p ON rs.product_id = p.id WHERE rs.created_at > '2024-09-01' AND rs.order_status = 201 AND (p.product_type = 1 OR p.product_type = 2 OR p.product_type = 3) ORDER BY rs.id ASC").Scan(&oArrs).Error
	if err != nil {
		global.LOG.Error("FixOrderVIP error", zap.Error(err))
		return
	}
	sLen := len(oArrs)
	for index, reSubOrder := range oArrs {
		// 查询商品
		var (
			expiresDate time.Time
		)

		productInfo, productInfoExist := global.AllProductMap[reSubOrder.ProductID]
		if !productInfoExist {
			global.LOG.Error("FixOrderVIP productInfo not exist", zap.Any("reSubOrder", reSubOrder))
			continue
		}
		switch productInfo.ProductType {
		case model.PTSubYear:
			// 年订阅
			expiresDate = reSubOrder.PayTime.AddDate(1, 0, 0)
		case model.PTSubMonth:
			// 月订阅
			expiresDate = reSubOrder.PayTime.AddDate(0, 1, 0)
		case model.PTSubWeek:
			// 周订阅
			expiresDate = reSubOrder.PayTime.AddDate(0, 0, 7)
		default:
			global.LOG.Error("FixOrderVIP productInfo.ProductType error", zap.Any("reSubOrder", reSubOrder))
			continue
		}

		editMap := map[string]interface{}{
			"sub_status": model.SubStatusIng,
			"sub_expire": expiresDate,
		}
		err = userSer.UpdateByMap(ctx, reSubOrder.DigitalUserID, editMap)
		if err != nil {
			global.LOG.Error("FixOrderVIP UpdateByMap error", zap.Error(err))
			continue
		}
		fmt.Println("index", index, sLen)
	}
	global.LOG.Info("FixOrderVIP success")
}

func (m *OrdersService) FixOrderSVIP() {
	var (
		err     error
		oArrs   []model.Order
		userSer DigitalUserService
		ctx     = context.Background()
	)
	err = global.DB.Raw("SELECT rs.* FROM orders AS rs LEFT JOIN product AS p ON rs.product_id = p.id WHERE rs.created_at > '2024-09-01' AND rs.order_status = 201 AND (p.product_type = 106 OR p.product_type = 107 OR p.product_type = 108) ORDER BY rs.id ASC").Scan(&oArrs).Error
	if err != nil {
		global.LOG.Error("FixOrderSVIP error", zap.Error(err))
		return
	}
	sLen := len(oArrs)
	for index, reSubOrder := range oArrs {
		// 查询商品
		var (
			expiresDate time.Time
		)

		productInfo, productInfoExist := global.AllProductMap[reSubOrder.ProductID]
		if !productInfoExist {
			global.LOG.Error("FixOrderSVIP productInfo not exist", zap.Any("reSubOrder", reSubOrder))
			continue
		}
		switch productInfo.ProductType {
		case model.PTSubYear:
			// 年订阅
			expiresDate = reSubOrder.PayTime.AddDate(1, 0, 0)
		case model.PTSubMonth:
			// 月订阅
			expiresDate = reSubOrder.PayTime.AddDate(0, 1, 0)
		case model.PTSubWeek:
			// 周订阅
			expiresDate = reSubOrder.PayTime.AddDate(0, 0, 7)
		default:
			global.LOG.Error("FixOrderSVIP productInfo.ProductType error", zap.Any("reSubOrder", reSubOrder))
			continue
		}

		editMap := map[string]interface{}{
			"sub_status": model.SubStatusIng,
			"sub_expire": expiresDate,
		}
		err = userSer.UpdateByMap(ctx, reSubOrder.DigitalUserID, editMap)
		if err != nil {
			global.LOG.Error("FixOrderSVIP UpdateByMap error", zap.Error(err))
			continue
		}
		fmt.Println("index", index, sLen)
	}
	global.LOG.Info("FixOrderSVIP success")
}
