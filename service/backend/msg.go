package backend

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/model/cli/td"
	"aimsg-server/utils"
	"aimsg-server/utils/agora"
	"aimsg-server/utils/elevenlabs"
	"aimsg-server/utils/notify"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"math/rand"
	"regexp"
	"strconv"
	"strings"
	"time"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/qiniu/qmgo"
	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"gorm.io/datatypes"
)

type MsgService struct{}

func (m *MsgService) getUserSendMsgTotalRedisKey(userId uint, roleId string) string {
	return fmt.Sprintf(global.USER_AIROLE_SEND_MSG_COUNT_KEY, roleId, userId)
}

// GetUserSendMsgTotal 用户和该AI角色,用户发送消息总数
func (m *MsgService) GetUserSendMsgTotal(ctx context.Context, userId uint, roleId string) (total int) {
	rKey := m.getUserSendMsgTotalRedisKey(userId, roleId)
	total, _ = global.REDIS.Get(ctx, rKey).Int()
	return
}

func (m *MsgService) UserSendMsgTotalIncr(ctx context.Context, userId uint, roleId string) (err error) {
	rKey := m.getUserSendMsgTotalRedisKey(userId, roleId)
	err = global.REDIS.Incr(ctx, rKey).Err()
	return
}

func (m *MsgService) getUserTotalAiRoleRedisKey(userId uint) string {
	return fmt.Sprintf(global.USER_TOTAL_AIROLE_KEY, userId)
}

// GetUserSendMsgTotal 用户聊过的AI角色总数
func (m *MsgService) GetUserTotalAiRole(ctx context.Context, userId uint) (total int) {
	rKey := m.getUserTotalAiRoleRedisKey(userId)
	rKeyCount := global.REDIS.SCard(ctx, rKey).Val()
	total = int(rKeyCount)
	return
}

func (m *MsgService) UserTotalAiRoleIncr(ctx context.Context, userId uint, roleId string) (err error) {
	rKey := m.getUserTotalAiRoleRedisKey(userId)
	err = global.REDIS.SAdd(ctx, rKey, roleId).Err()
	return
}

func (m *MsgService) CreateMsgRecord(ctx context.Context, msgRecord *model.MsgInfo) (err error) {
	_, err = global.MONGO.InsertOne(ctx, msgRecord)
	return
}

func (m *MsgService) LastMsg(ctx context.Context, userIdStr, roleIdStr string) (msgInfo *model.MsgInfo, err error) {
	var (
		filterMap = bson.M{
			"sender_id":   userIdStr,
			"receiver_id": roleIdStr,
			"deleted_at":  bson.M{"$eq": nil},
		}
	)
	err = global.MONGO.Find(ctx, filterMap).Sort("-send_time").Limit(1).One(&msgInfo)
	return
}

func (m *MsgService) LastVoiceCallMsg(ctx context.Context, userIdStr, roleIdStr string) (msgInfo *model.MsgInfo, err error) {
	var (
		filterMap = bson.M{
			"sender_id":    userIdStr,
			"receiver_id":  roleIdStr,
			"message_type": int(model.MsgTypeVoiceCall),
			"deleted_at":   bson.M{"$eq": nil},
		}
	)
	err = global.MONGO.Find(ctx, filterMap).Sort("-send_time").Limit(1).One(&msgInfo)
	return
}

func (m *MsgService) ResetChat(ctx context.Context, userIdStr, roleIdStr string) (err error) {
	var (
		ids       = []string{userIdStr, roleIdStr}
		filterMap = bson.M{
			"sender_id":   bson.M{"$in": ids},
			"receiver_id": bson.M{"$in": ids},
		}
	)
	_, err = global.MONGO.UpdateAll(ctx, filterMap, bson.M{"$set": bson.M{"deleted_at": time.Now()}})
	return
}

// GetMsgHistory 获取历史消息记录
func (m *MsgService) GetMsgHistory(ctx context.Context, userIdStr, roleIdStr string) (msgHis [][]string, err error) {
	var (
		msgInfos   []model.MsgInfo
		msgIdsMaps []bson.M
		msgIds     []string
		limitNum   = 20
		userIDs    = []string{userIdStr, roleIdStr}
		pipeline   = qmgo.Pipeline{
			bson.D{
				{"$match", bson.D{
					{"sender_id", bson.D{{"$in", userIDs}}},
					{"receiver_id", bson.D{{"$in", userIDs}}},
					{"deleted_at", nil},
					{"message_content.text", bson.D{{"$ne", nil}}},
				}},
			},
			bson.D{
				{"$group", bson.D{
					{"_id", "$message_id"},
					{"count", bson.D{{"$sum", 1}}},
					{"latest_send_time", bson.D{{"$max", "$send_time"}}},
				}},
			},
			bson.D{{"$match", bson.D{{"count", 2}}}},
			bson.D{{"$sort", bson.D{{"latest_send_time", 1}}}},
			bson.D{{"$limit", limitNum}},
			bson.D{{"$project", bson.D{{"message_id", "$_id"}, {"_id", 0}}}},
		}
	)
	if err = global.MONGO.Aggregate(ctx, pipeline).All(&msgIdsMaps); err != nil {
		return
	}
	// 未聊过天
	if len(msgIdsMaps) == 0 {
		return
	}
	for _, idsMap := range msgIdsMaps {
		msgIds = append(msgIds, idsMap["message_id"].(string))
	}
	// 查询消息
	if err = global.MONGO.Find(ctx, bson.M{"message_id": bson.M{"$in": msgIds}}).Sort("send_time").All(&msgInfos); err != nil {
		return
	}
	var msgPair []string
	for _, msgInfo := range msgInfos {
		msgInfoText := msgInfo.MessageContent.OriginText
		if msgInfoText == "" {
			msgInfoText = msgInfo.MessageContent.Text
		}
		// 用户发送的消息添加到第一个,主播发送的添加到第二个
		if msgInfo.SenderID == userIdStr {
			msgPair = append([]string{msgInfoText}, msgPair...)
		} else {
			msgPair = append(msgPair, msgInfoText)
		}
		if len(msgPair) == 2 {
			msgHis = append(msgHis, msgPair)
			msgPair = []string{}
		}
	}
	return
}

func (m *MsgService) getReplyMsgMy(ctx context.Context, msgApi model.MsgJob, userSendMsg string, aiRoleInfo *model.AiRole, userInfo *model.DigitalUser, sendMsgReq *request.RedisMsgReq) (aiReplyMsg string, err error) {
	var (
		apiLog    string
		curlStr   string
		apiRes    response.AiMsgRes
		msgHis    [][]string
		history   = map[string]interface{}{}
		userIdStr = fmt.Sprintf("%d", userInfo.Id)
	)
	msgTimeoutConfig := GetMsgSendTimeOutConfig(ctx)
	msgModelStrageCfg := GetMsgModelStrageCfg(ctx)
	// if global.CONFIG.System.Env {
	// 	// 开发环境
	// 	aiReplyMsg = "开发环境不调用AI接口"
	// 	return
	// }

	// 获取历史消息记录
	if msgHis, err = m.GetMsgHistory(ctx, userIdStr, aiRoleInfo.RoleID); err != nil {
		return
	}
	internal := [][]string{{"<|BEGIN-VISIBLE-CHAT|>", aiRoleInfo.FirstMsg}}
	visible := [][]string{{"", aiRoleInfo.FirstMsg}}
	if len(msgHis) > 0 {
		internal = append(internal, msgHis...)
		visible = append(visible, msgHis...)
	}
	history = map[string]interface{}{
		"internal": internal,
		"visible":  visible,
	}
	reqData := aiRoleInfo.ReqData
	if msgModelStrageCfg.Open && aiRoleInfo.RoleType != model.AiRoleTypePrivate && userInfo.UserGroup == 2 {
		reqData = aiRoleInfo.ReqDataTwo
	}
	reqData["name1"] = userInfo.Nickname
	reqData["name2"] = aiRoleInfo.Nickname
	reqData["user_input"] = userSendMsg
	reqData["history"] = history
	reqData["greeting"] = aiRoleInfo.FirstMsg
	if sendMsgReq.SendType == request.SendTypeVoiceCall {
		msgReqCfg := GetMsgReqMapCfg(ctx)
		reqData["max_new_tokens"] = msgReqCfg.MaxNewTokens
		reqData["temperature"] = msgReqCfg.Temperature
		reqData["top_p"] = msgReqCfg.TopP
		reqData["top_k"] = msgReqCfg.TopK
		reqData["repetition_penalty"] = msgReqCfg.RepetitionPenalty
	}
	reqStart := time.Now()
	// 请求AI接口
	if apiLog, curlStr, err = utils.HttpPostJson(msgApi.ApiUrl, reqData, &apiRes, msgTimeoutConfig.Seconds); err != nil {
		global.LOG.Error("HttpPostJson error", zap.String("apiLog", apiLog), zap.String("curlStr", curlStr), zap.Any("err", err))
		return
	}
	if len(apiRes.Results) == 0 {
		err = errors.New("apiRes.Results is empty, apiLog: " + apiLog)
		return
	}
	aiReplyInfo := apiRes.Results[0]
	if len(aiReplyInfo.History.Visible) == 0 {
		err = errors.New("aiReplyInfo.History.Visible is empty, apiLog: " + apiLog)
		return
	}
	aiReplyInfoVisible := aiReplyInfo.History.Visible[len(aiReplyInfo.History.Visible)-1]
	if len(aiReplyInfoVisible) != 2 {
		err = errors.New("len(aiReplyInfoVisible) != 2, apiLog: " + apiLog)
		return
	}
	aiReplyMsg = aiReplyInfoVisible[1]
	reqSeconds := int(time.Now().Sub(reqStart).Seconds())
	if msgTimeoutConfig.Status && reqSeconds > msgTimeoutConfig.Seconds && len(global.ReplyList) > 0 {
		// 超时,从通用回复获取一条
		aiReplyMsg = global.ReplyList[rand.Intn(len(global.ReplyList))]
	}
	if aiReplyMsg == "" && len(global.ReplyList) > 0 {
		aiReplyMsg = global.ReplyList[rand.Intn(len(global.ReplyList))]
	}
	// 如果结尾是\n,那么就不要结尾的\n
	aiReplyMsg = strings.TrimRight(aiReplyMsg, "\n")
	// 1月7日 军锋需求修改星号匹配 双星替换为单星 ** -> *
	aiReplyMsg = strings.ReplaceAll(aiReplyMsg, "**", "*")
	return
}

func (m *MsgService) voiceCacheRedisKey(msgContent string, aiRoleID string) string {
	cacheFullKey := fmt.Sprintf("%s:%s", msgContent, aiRoleID)
	cacheFullKeyMd5 := utils.MD5(cacheFullKey)
	return fmt.Sprintf("msg:voice:cache:%s:%s", aiRoleID, cacheFullKeyMd5)
}

func (m *MsgService) VoiceCache(ctx context.Context, msgContent string, aiRoleID string) (s3ObjectKey string, exist bool) {
	cacheFullKeyMd5 := m.voiceCacheRedisKey(msgContent, aiRoleID)
	exist = global.REDIS.Exists(ctx, cacheFullKeyMd5).Val() > 0
	if exist {
		s3ObjectKey = global.REDIS.Get(ctx, cacheFullKeyMd5).Val()
	}
	return
}

func (m *MsgService) ElevenLabsTts(messageId, text string, aiRoleInfo *model.AiRole) (voiceKey string, err error) {
	var (
		textToSpeechBytes []byte
		fileExists        bool
		s3Client          = GetS3Client()
	)
	global.LOG.Info("TextToSpeech1", zap.String("messageId", messageId), zap.String("RoleID", aiRoleInfo.RoleID), zap.String("text", text))
	voiceKey = fmt.Sprintf("public/voice/%s/%s.mp3", aiRoleInfo.RoleID, messageId)
	if fileExists, err = s3Client.FileExists(voiceKey); err != nil {
		return
	}
	if fileExists {
		return
	}
	eleVoiceReq := aiRoleInfo.EleVoiceReq
	voiceId := eleVoiceReq.Data().VoiceId
	// 1_03军锋需求修改文字转语音
	global.LOG.Info("TextToSpeech2", zap.String("messageId", messageId), zap.String("RoleID", aiRoleInfo.RoleID), zap.String("text", text))
	if voiceId == "" {
		if aiRoleInfo.Gender == model.GenderTypeMale {
			voiceId = "male-qn-qingse,male-qn-qingse"
		} else {
			voiceId = "Charming_Lady,danya_xuejie"
		}
	}
	vol := eleVoiceReq.Data().Vol
	if vol == 0 {
		vol = 1
	}

	pitch := eleVoiceReq.Data().Pitch
	if pitch == 0 {
		pitch = 0
	}
	speed := eleVoiceReq.Data().Speed
	if speed == 0 {
		speed = 1
	}
	// 1_03军锋需求修改文字转语音
	global.LOG.Info("TextToSpeech3", zap.String("messageId", messageId), zap.String("RoleID", aiRoleInfo.RoleID), zap.String("text", text))
	if textToSpeechBytes, err = elevenlabs.TextToSpeechWithMinMax(text, voiceId, vol, pitch, speed); err != nil {
		err = fmt.Errorf("TextToSpeech error, %v", err.Error())
		global.LOG.Error("TextToSpeech error", zap.String("messageId", messageId), zap.String("RoleID", aiRoleInfo.RoleID), zap.Any("err", err), zap.String("textToSpeechRes", string(textToSpeechBytes)))
		return
	}
	// 保存到本地
	// if err = os.WriteFile(fmt.Sprintf("%s.mp3", messageId), textToSpeechBytes, os.ModePerm); err != nil {
	// 	return
	// }
	global.LOG.Info("TextToSpeech4", zap.String("messageId", messageId), zap.String("RoleID", aiRoleInfo.RoleID), zap.String("text", text))
	if err = s3Client.UploadBytesToS3(textToSpeechBytes, voiceKey, "audio/mpeg"); err != nil {
		return
	}
	global.LOG.Info("TextToSpeech5", zap.String("messageId", messageId), zap.String("RoleID", aiRoleInfo.RoleID), zap.String("text", text))

	return
}

// 判断字符是否为标点符号
func (m *MsgService) isPunctuation(r rune) bool {
	punctuations := []rune{',', '.', '!', '?'}
	for _, p := range punctuations {
		if r == p {
			return true
		}
	}
	return false
}

// ExtractVoiceTextV3 1月7日 军锋需求修改星号匹配
func (m *MsgService) ExtractVoiceTextV3(text string) string {
	// 1 替换双星号 ** 为单星号 *
	step1 := strings.ReplaceAll(text, "**", "*")

	// 2 去掉 *包裹的内容*
	asteriskRegex := regexp.MustCompile(`\*[^*]*\*`)
	step2 := asteriskRegex.ReplaceAllString(step1, "")

	// 3 去掉反斜杠 \
	step3 := strings.ReplaceAll(step2, "\\", "")
	// 4 去掉双引号 "
	step4 := strings.ReplaceAll(step3, `"`, "")
	// 5 替换 . 为空格
	step5 := strings.ReplaceAll(step4, ".", " ")
	// 6 替换多个空格为单个空格
	spaceRegex := regexp.MustCompile(`\s+`)
	step6 := spaceRegex.ReplaceAllString(step5, " ")
	// 7 去掉首尾空格
	cleanedText := strings.TrimSpace(step6)
	return cleanedText
}

func (m *MsgService) ExtractVoiceTextV2(text string) string {
	// 提取英文双引号""包起来的内容
	reDoubleQuotes := regexp.MustCompile(`"(.*?)"`)
	matchDoubleQuotes := reDoubleQuotes.FindAllStringSubmatch(text, -1)

	// 提取中文双引号“”包起来的内容
	reChineseQuotes := regexp.MustCompile(`“(.*?)”`)
	matchChineseQuotes := reChineseQuotes.FindAllStringSubmatch(text, -1)

	// 合并双引号内的所有内容
	var extractedText string
	for _, match := range matchDoubleQuotes {
		content := match[1]
		if len(extractedText) > 0 && !m.isPunctuation(rune(extractedText[len(extractedText)-1])) {
			extractedText += ", "
		}
		extractedText += content
	}
	for _, match := range matchChineseQuotes {
		content := match[1]
		if len(extractedText) > 0 && !m.isPunctuation(rune(extractedText[len(extractedText)-1])) {
			extractedText += ", "
		}
		extractedText += content
	}

	// 判断是否有[],**包裹的内容,如果有,就去掉包裹的内容
	reSquareBrackets := regexp.MustCompile(`\[.*?\]`)
	extractedText = reSquareBrackets.ReplaceAllString(extractedText, "")

	reAsterisks := regexp.MustCompile(`\*\*.*?\*\*`)
	extractedText = reAsterisks.ReplaceAllString(extractedText, "")

	// 去除文本里的换行符
	extractedText = strings.ReplaceAll(extractedText, "\n", "")

	if len(extractedText) == 0 || len(extractedText) > 500 {
		return ""
	}
	return extractedText
}

func (m *MsgService) ExtractVoiceText(text string) string {
	// 如果有双引号包起来的内容,返回双引号包起来的内容
	re := regexp.MustCompile(`"([^"]*)"`)
	matches := re.FindStringSubmatch(text)
	if len(matches) > 1 {
		return matches[1]
	}

	re = regexp.MustCompile(`“(.*?)”`)
	matches = re.FindStringSubmatch(text)
	if len(matches) > 1 {
		return matches[1]
	}

	// 判断是否有[],**包裹的内容,如果有,就去掉包裹的内容
	re = regexp.MustCompile(`\[(.*?)]`)
	text = re.ReplaceAllString(text, "")

	re = regexp.MustCompile(`\*(.*?)\*`)
	text = re.ReplaceAllString(text, "")

	// 去除文本里的换行符
	text = strings.ReplaceAll(text, "\n", "")

	if len(text) == 0 || len(text) > 500 {
		return ""
	}
	return text
}

func (m *MsgService) msgCanVoice(text string) bool {
	return m.ExtractVoiceTextV3(text) != ""
}

// SaveConsumeRecord 说明：consumeAmount 都传正数,会根据consumeType判断最终是正还是负数
func (m *MsgService) SaveConsumeRecord(userId uint, aiRoleInfo *model.AiRole, consumeAmount int, consumeType model.ConsumeType) (err error) {
	var (
		userInfo                   *model.DigitalUser
		userService                DigitalUserService
		profileService             DigitalUserProfileService
		userProfile                *model.DigitalUserProfile
		flowerConsumeRecordService FlowerConsumeRecordService
		ctx                        = context.Background()
	)
	// 获取用户信息
	if userInfo, err = userService.GetById(ctx, userId); err != nil {
		return
	}
	// 查询用户资料信息
	if userProfile, err = profileService.GetByUserId(userId); err != nil {
		return
	}

	// 根据类型判断是加鲜花还是扣鲜花
	flowerAmount := userProfile.FlowerAmount + consumeAmount
	if _, exists := model.AddFlowerMap[consumeType]; !exists {
		flowerAmount = userProfile.FlowerAmount - consumeAmount
		consumeAmount = -1 * consumeAmount
	}

	if flowerAmount < 0 {
		vCompare := utils.CompareVersions(userInfo.Version, "3.0.0")
		if vCompare >= 0 {
			err = errs.FloShopErr
			return
		}
		err = errs.RequireBuyFloErr
		return
	}

	// 更新鲜花数量
	upData := map[string]interface{}{
		"flower_amount": flowerAmount,
		"updated_at":    time.Now(),
	}
	if err = global.DB.Model(&model.DigitalUserProfile{}).Where("digital_user_id = ?", userId).Updates(upData).Error; err != nil {
		return
	}

	// 记录消费记录
	if err = flowerConsumeRecordService.Create(&model.FlowerConsumeRecord{
		DigitalUserId: userId,
		AiId:          aiRoleInfo.Id,
		AiRoleId:      aiRoleInfo.RoleID,
		ConsumeType:   consumeType,
		OriginAmount:  userProfile.FlowerAmount,
		ConsumeAmount: consumeAmount,
	}); err != nil {
		return
	}

	return
}

// SaveVideoSimplifyConsumeRecord 说明：consumeAmount 都传正数,会根据consumeType判断最终是正还是负数
func (m *MsgService) SaveVideoSimplifyConsumeRecord(userId uint, consumeAmount int, consumeType model.VideoSimplifyConsumeType, videoTemplateId uint, userVideoId uint) (err error) {
	var (
		profileService DigitalUserProfileService
		userProfile    *model.DigitalUserProfile
		userService    DigitalUserService
		ctx            = context.Background()
		userInfo       *model.DigitalUser
	)

	// 获取用户信息
	if userInfo, err = userService.GetById(ctx, userId); err != nil {
		return
	}

	// 查询用户资料信息
	if userProfile, err = profileService.GetByUserId(userId); err != nil {
		return
	}

	// 根据类型判断是加鲜花还是扣鲜花
	flowerAmount := userProfile.FlowerAmount + consumeAmount
	if _, exists := model.VideoSimplifyAddFlowerMap[consumeType]; !exists {
		flowerAmount = userProfile.FlowerAmount - consumeAmount
		consumeAmount = -1 * consumeAmount
	}

	if flowerAmount < 0 {
		err = errs.RequireBuyFloErr
		return
	}

	if consumeAmount == 0 {
		return
	}

	var consumeRecordInfo model.VideoSimplifyConsumeRecord
	consumeRecordInfo.DigitalUserId = userId
	consumeRecordInfo.ConsumeType = consumeType
	consumeRecordInfo.OriginAmount = userProfile.FlowerAmount
	consumeRecordInfo.ConsumeAmount = consumeAmount
	consumeRecordInfo.TemplateId = videoTemplateId
	consumeRecordInfo.RelationId = userVideoId

	err = global.DB.Model(&model.VideoSimplifyConsumeRecord{}).Create(&consumeRecordInfo).Error
	if err != nil {
		return
	}

	if consumeType != model.VideoSimplifyConsumeTypeUserCreateImgToVideo && consumeType != model.VideoSimplifyConsumeTypeCreateFail {
		return
	}
	// 更新鲜花数量
	upData := map[string]interface{}{
		"flower_amount": flowerAmount,
		"updated_at":    time.Now(),
	}
	if err = global.DB.Model(&model.DigitalUserProfile{}).Where("digital_user_id = ?", userId).Updates(upData).Error; err != nil {
		global.LOG.Info("SaveVideoSimplifyConsumeRecord1", zap.Error(err))
		return
	}
	// 新增埋点
	if userInfo.DistinctID != "" {
		if consumeType == model.VideoSimplifyConsumeTypeUserCreateImgToVideo {
			go TdPointer(userInfo, userProfile, td.ServerEvent, map[string]interface{}{
				"event_tag":         "bonus_consume",
				"bonus_consume_num": consumeAmount,
				"source":            "Generation_bonus_consume_num",
			})
		}
		if consumeType == model.VideoSimplifyConsumeTypeCreateFail {
			go TdPointer(userInfo, userProfile, td.ServerEvent, map[string]interface{}{
				"event_tag":         "bonus_consume",
				"bonus_consume_num": consumeAmount,
				"source":            "generation_refund",
			})
		}

	}
	return
}

func (m *MsgService) SaveIntimateRecord(ctx context.Context, userId uint, aiRoleInfo *model.AiRole, intimateType model.IntimateType, intimateSubType model.IntimateSubType) (err error) {
	var (
		usersIntimateService       UsersIntimateService
		userService                DigitalUserService
		usersIntimate              *model.UsersIntimate
		userInfo                   *model.DigitalUser
		usersIntimateRecordService UsersIntimateRecordService
		aiId                       = aiRoleInfo.Id
	)

	// 查询亲密度表,不存在会创建一条记录
	if usersIntimate, err = usersIntimateService.GetByUidAndRoleid(userId, aiId); err != nil {
		return
	}

	// 根据类型+是否订阅拿到亲密度值
	if userInfo, err = userService.GetById(ctx, userId); err != nil {
		return
	}
	isSVip := userInfo.IsSVip()
	isSub := userInfo.IsSub()
	isChatVip := userInfo.IsChatVip()
	isContentVip := userInfo.IsContentVip()
	// isSub := utils.BoolToIntString(userInfo.IsSub())
	intimate := 1
	// intimateKey := fmt.Sprintf("%d-%s", intimateSubType, isSub)
	// if _, exists := model.IntimateSubTypeWithIntimate[intimateKey]; exists {
	// 	intimate = model.IntimateSubTypeWithIntimate[intimateKey]
	// }
	intimateUpgradeCfg := GetIntimateUpgradeCfg(ctx)
	upgradeCfg, upgradeCfgExist := intimateUpgradeCfg[intimateSubType]
	if upgradeCfgExist {
		if isSVip {
			intimate = upgradeCfg.SVip
		} else if isSub {
			intimate = upgradeCfg.Vip
		} else if isContentVip {
			intimate = upgradeCfg.ContentVip
		} else if isChatVip {
			intimate = upgradeCfg.ChatVip
		} else {
			intimate = upgradeCfg.Free
		}
	}

	// 记录到亲密度记录表
	if err = usersIntimateRecordService.Create(&model.UsersIntimateRecord{
		AiRoleId:      aiId,
		DigitalUserId: userId,
		IntimateType:  intimateType,
		SubType:       intimateSubType,
		Intimate:      intimate,
	}); err != nil {
		return
	}

	// 更新亲密度表
	resIntimate := usersIntimate.Intimate + uint(intimate)
	if intimateType == model.IntimateTypeReduce {
		if usersIntimate.Intimate <= uint(intimate) {
			resIntimate = 0
		} else {
			resIntimate = usersIntimate.Intimate - uint(intimate)
		}
	}

	var level uint = 1
	levelCfg := GetIntimateLevelCfg(ctx, userInfo.AppID)
	for _, row := range levelCfg {
		_level := row.Level
		if resIntimate >= row.Left && resIntimate <= row.Right {
			level = _level
			break
		}
	}
	usersIntimate.Intimate = resIntimate
	usersIntimate.UpdatedAt = time.Now()
	usersIntimate.LastInteractive = time.Now()
	levelChange := false
	if usersIntimate.Level != level {
		usersIntimate.Level = level
		levelChange = true
	}
	if err = usersIntimateService.Update(usersIntimate); err != nil {
		return
	}
	if levelChange {
		// 亲密度等级变化
		go m.SendIntimateUpgradeMsg(userInfo, aiRoleInfo, usersIntimate)
		m.RewardUserFunc(usersIntimate, userInfo)
	}
	return
}

func (m *MsgService) SaveIntimateGiftRecord(userInfo *model.DigitalUser, aiRoleInfo *model.AiRole, intimateType model.IntimateType, intimateSubType model.IntimateSubType, intimate int) (err error) {
	var (
		usersIntimateService       UsersIntimateService
		usersIntimate              *model.UsersIntimate
		usersIntimateRecordService UsersIntimateRecordService
		ctx                        = context.Background()
		userId                     = userInfo.Id
	)
	// 查询亲密度表,不存在会创建一条记录
	if usersIntimate, err = usersIntimateService.GetByUidAndRoleid(userId, aiRoleInfo.Id); err != nil {
		return
	}
	// 记录到亲密度记录表
	if err = usersIntimateRecordService.Create(&model.UsersIntimateRecord{
		AiRoleId:      aiRoleInfo.Id,
		DigitalUserId: userId,
		IntimateType:  intimateType,
		SubType:       intimateSubType,
		Intimate:      intimate,
	}); err != nil {
		return
	}

	// 更新亲密度表
	resIntimate := usersIntimate.Intimate + uint(intimate)
	if intimateType == model.IntimateTypeReduce {
		if usersIntimate.Intimate <= uint(intimate) {
			resIntimate = 0
		} else {
			resIntimate = usersIntimate.Intimate - uint(intimate)
		}
	}

	var level uint = 1
	levelCfg := GetIntimateLevelCfg(ctx, userInfo.AppID)
	for _, row := range levelCfg {
		_level := row.Level
		if resIntimate >= row.Left && resIntimate <= row.Right {
			level = _level
			break
		}
	}
	usersIntimate.Intimate = resIntimate
	usersIntimate.UpdatedAt = time.Now()
	usersIntimate.LastInteractive = time.Now()
	levelChange := false
	if usersIntimate.Level != level {
		usersIntimate.Level = level
		levelChange = true
	}
	if err = usersIntimateService.Update(usersIntimate); err != nil {
		return
	}
	if levelChange {
		// 亲密度等级变化
		go m.SendIntimateUpgradeMsg(userInfo, aiRoleInfo, usersIntimate)
		m.RewardUserFunc(usersIntimate, userInfo)
	}
	return
}

func (m *MsgService) SendIntimateUpgradeMsg(userInfo *model.DigitalUser, aiInfo *model.AiRole, usersIntimate *model.UsersIntimate) {
	var (
		err           error
		aiSendMsgByte []byte
		ctx           = context.Background()
		nowTime       = time.Now()
	)
	defer func() {
		if rErr := recover(); rErr != nil {
			global.LOG.Error("SendIntimateUpgradeMsg panic", zap.Any("panic", rErr))
		}
	}()
	// 孙宏伟需求 2024-12-24 新增app_id 4号包数据 亲密度功能
	if userInfo.AppID != 2 && userInfo.AppID != 1 && userInfo.AppID != 11 && userInfo.AppID != 13 && userInfo.AppID != 4 && userInfo.AppID != 10 && userInfo.AppID != 6 && userInfo.AppID != 7 && userInfo.AppID != 17 && userInfo.AppID != 21 && userInfo.AppID != 25 {
		return
	}

	verCompare340 := utils.CompareVersions(userInfo.Version, "3.4.0")
	verCompare364 := utils.CompareVersions(userInfo.Version, "3.6.4")
	verCompare363 := utils.CompareVersions(userInfo.Version, "3.6.3")
	verCompare365 := utils.CompareVersions(userInfo.Version, "3.6.5")

	if userInfo.AppID == 4 {
		verCompare340 = -1
	}
	if verCompare364 > 0 && userInfo.AppID == 4 {
		verCompare340 = 1
	}
	if userInfo.AppID == 10 {
		verCompare340 = -1
	}

	if verCompare363 > 0 && userInfo.AppID == 10 {
		verCompare340 = 1
	}

	if userInfo.AppID == 6 {
		verCompare340 = -1
	}
	if verCompare365 > 0 && userInfo.AppID == 6 {
		verCompare340 = 1
	}

	if userInfo.AppID == 7 {
		verCompare340 = -1
	}
	if verCompare365 > 0 && userInfo.AppID == 7 {
		verCompare340 = 1
	}

	if userInfo.AppID == 21 || userInfo.AppID == 25 {
		verCompare340 = 1
	}
	if verCompare340 < 0 {
		return
	}

	upgradeMsgCfg := GetIntimateUpgradeMsgCfg(ctx, userInfo.AppID)

	msgText := ""
	switch usersIntimate.Level {
	case 1:
		msgText = utils.RandList(upgradeMsgCfg.LevelOne)
	case 2:
		msgText = utils.RandList(upgradeMsgCfg.LevelTwo)
	case 3:
		msgText = utils.RandList(upgradeMsgCfg.LevelThree)
	case 4:
		msgText = utils.RandList(upgradeMsgCfg.LevelFour)
	case 5:
		msgText = utils.RandList(upgradeMsgCfg.LevelFive)
	}
	appInfo := global.AppMap[userInfo.AppID]
	messageId := uuid.NewString()
	msgInfo := &model.MsgInfo{
		MessageID:    messageId,
		Channel:      nil,
		SendTime:     nowTime.UnixMilli(),
		DeletedAt:    nil,
		MessageType:  model.MsgTypeIntimateLevelUpgradeDialog,
		SceneId:      0,
		SenderID:     aiInfo.RoleID,
		SenderName:   aiInfo.Nickname,
		ReceiverID:   fmt.Sprintf("%d", userInfo.Id),
		ReceiverName: userInfo.Nickname,
		MessageContent: model.MessageContent{
			Text:       msgText,
			OriginText: msgText,
			Avatar:     utils.S3Url(aiInfo.Avatar, userInfo.AppID),
		},
	}

	// 序列化消息
	if aiSendMsgByte, err = json.Marshal(msgInfo); err != nil {
		global.LOG.Error("json.Marshal error", zap.Any("err", err))
		return
	}
	// 写入MongoDB
	if err = m.CreateMsgRecord(ctx, msgInfo); err != nil {
		return
	}
	aiSendMsgJson := string(aiSendMsgByte)
	// 发消息到声网
	agoData := appInfo.AgoraData.Data()
	agoCli := agora.NewAgoraCli(agoData.AppId, agoData.AppCert, agoData.CustomId, agoData.CustomSecret)
	if _, err = agoCli.SendPeerMsg(msgInfo.SenderID, msgInfo.ReceiverID, aiSendMsgJson, true); err != nil {
		global.LOG.Error("SendPeerMsg error", zap.Any("err", err))
		return
	}

	if userInfo.AppID == 13 && !userInfo.IsAudit() && msgText != "" {
		// 发送一条文字消息
		textMsgInfo := &model.MsgInfo{
			MessageID:    messageId,
			Channel:      nil,
			SendTime:     nowTime.UnixMilli(),
			DeletedAt:    nil,
			MessageType:  model.MsgTypeTxt,
			SceneId:      0,
			SenderID:     aiInfo.RoleID,
			SenderName:   aiInfo.Nickname,
			ReceiverID:   fmt.Sprintf("%d", userInfo.Id),
			ReceiverName: userInfo.Nickname,
			MessageContent: model.MessageContent{
				Avatar:     utils.S3Url(aiInfo.Avatar, userInfo.AppID),
				Text:       msgText,
				OriginText: msgText,
			},
		}
		_ = m.SendMsgToAgora(textMsgInfo, appInfo)
	}

	go m.SendInviteVideoCallMsg(userInfo, aiInfo, usersIntimate)
	return
}

func (m *MsgService) SendInviteVideoCallMsg(userInfo *model.DigitalUser, aiInfo *model.AiRole, usersIntimate *model.UsersIntimate) {
	var (
		err     error
		nowTime = time.Now()
	)
	defer func() {
		if rErr := recover(); rErr != nil {
			global.LOG.Error("SendInviteVideoCallMsg panic", zap.Any("panic", rErr))
		}
	}()
	if userInfo.AppID != 13 {
		return
	}
	if userInfo.IsAudit() {
		return
	}
	if usersIntimate.Level != 4 {
		return
	}
	if aiInfo.RealAnchorId == 0 {
		return
	}
	appInfo := global.AppMap[userInfo.AppID]
	msgInfo := &model.MsgInfo{
		MessageID:    uuid.NewString(),
		Channel:      nil,
		SendTime:     nowTime.UnixMilli(),
		DeletedAt:    nil,
		MessageType:  model.MsgTypeAIInviteRealCall,
		SceneId:      0,
		SenderID:     aiInfo.RoleID,
		SenderName:   aiInfo.Nickname,
		ReceiverID:   fmt.Sprintf("%d", userInfo.Id),
		ReceiverName: userInfo.Nickname,
		MessageContent: model.MessageContent{
			Text:       "Invite you for a video chat",
			OriginText: "Invite you for a video chat",
		},
	}
	if err = m.SendMsgToAgora(msgInfo, appInfo); err != nil {
		global.LOG.Error("SendMsgToAgoraWithoutMongo error", zap.Any("err", err))
	}
	return
}

func (m *MsgService) SendMsgToAgora(msg *model.MsgInfo, appInfo model.App) (err error) {
	var (
		aiSendMsgByte []byte
		ctx           = context.Background()
	)
	// 序列化消息
	if aiSendMsgByte, err = json.Marshal(msg); err != nil {
		global.LOG.Error("json.Marshal error", zap.Any("err", err))
		return
	}
	// 写入MongoDB
	if err = m.CreateMsgRecord(ctx, msg); err != nil {
		return
	}
	aiSendMsgJson := string(aiSendMsgByte)
	// 发消息到声网
	agoData := appInfo.AgoraData.Data()
	agoCli := agora.NewAgoraCli(agoData.AppId, agoData.AppCert, agoData.CustomId, agoData.CustomSecret)
	if _, err = agoCli.SendPeerMsg(msg.SenderID, msg.ReceiverID, aiSendMsgJson, true); err != nil {
		global.LOG.Error("SendPeerMsg error", zap.Any("err", err))
		return
	}
	return
}

func (m *MsgService) SendMsgToAgoraWithoutMongo(msg *model.MsgInfo, appInfo model.App) (err error) {
	var (
		aiSendMsgByte []byte
	)
	// 序列化消息
	if aiSendMsgByte, err = json.Marshal(msg); err != nil {
		global.LOG.Error("json.Marshal error", zap.Any("err", err))
		return
	}
	aiSendMsgJson := string(aiSendMsgByte)
	// 发消息到声网
	agoData := appInfo.AgoraData.Data()
	agoCli := agora.NewAgoraCli(agoData.AppId, agoData.AppCert, agoData.CustomId, agoData.CustomSecret)
	if _, err = agoCli.SendPeerMsg(msg.SenderID, msg.ReceiverID, aiSendMsgJson, true); err != nil {
		global.LOG.Error("SendPeerMsg error", zap.Any("err", err))
		return
	}
	return
}

func (m *MsgService) HasSensitiveWordTextMask(msg string) bool {
	msgLower := strings.ToLower(msg)
	for _, s := range global.TextMaskMsgList {
		sLower := strings.ToLower(s)
		if strings.Contains(msgLower, sLower) {
			return true
		}
	}
	return false
}

func (m *MsgService) GetAiReplyUserCountToday(ctx context.Context, userId uint) int {
	var now = time.Now()
	dayStr := now.Format(time.DateOnly)
	replyAllCountKey := fmt.Sprintf(global.REPLY_USER_ALL_COUNT, dayStr, userId)
	replyCount, _ := global.REDIS.Get(ctx, replyAllCountKey).Int()
	return replyCount
}

func (m *MsgService) IncrAiReplyUserCount(ctx context.Context, userId uint) {
	var now = time.Now()
	dayStr := now.Format(time.DateOnly)
	replyAllCountKey := fmt.Sprintf(global.REPLY_USER_ALL_COUNT, dayStr, userId)
	global.REDIS.Incr(ctx, replyAllCountKey)
	global.REDIS.Expire(ctx, replyAllCountKey, 48*time.Hour)
}

func (m *MsgService) GetWatchAdMsgCountToday(ctx context.Context, userId uint) int {
	var now = time.Now()
	dayStr := now.Format(time.DateOnly)
	redisKey := fmt.Sprintf(global.USER_WATCH_AD_MSG_COUNT, dayStr, userId)
	num, _ := global.REDIS.Get(ctx, redisKey).Int()
	return num
}

func (m *MsgService) IncrWatchAdMsgCount(c *gin.Context, userId uint, num int64) (err error) {
	var (
		userInfo    *model.DigitalUser
		userService DigitalUserService
		appId       = utils.GetAppID(c)
		now         = time.Now()
		dayStr      = now.Format(time.DateOnly)
		redisKey    = fmt.Sprintf(global.USER_WATCH_AD_MSG_COUNT, dayStr, userId)
	)
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	appInfo := global.AppMap[appId]
	// 查询当日总共对话条数
	replyCount := m.GetAiReplyUserCountToday(c, userId)
	limitCount := appInfo.DetailConfig.Data().FreeMsgCount
	if userInfo.IsSub() {
		limitCount = appInfo.DetailConfig.Data().PaidMsgCount
	}
	// 加之前先设置初始值,这个初始值有可能不是0
	originNum := replyCount - limitCount
	if err = global.REDIS.Set(c, redisKey, originNum, 48*time.Hour).Err(); err != nil {
		return
	}
	if err = global.REDIS.IncrBy(c, redisKey, num).Err(); err != nil {
		return
	}
	err = global.REDIS.Expire(c, redisKey, 48*time.Hour).Err()
	return
}

func (m *MsgService) noSendList(ctx context.Context, hasSendMediaRedisKey string, hasSendMediaList []string, lv1List []response.AiRoleMediaVideoItem, lv2List []response.AiRoleMediaVideoItem, imgList []string) (lv1ListNew []response.AiRoleMediaVideoItem, lv2ListNew []response.AiRoleMediaVideoItem, imgListNew []string) {
	var (
		lv1StrArr       []string
		lv2StrArr       []string
		hasSendMediaMap = map[string]bool{}
	)
	for _, media := range hasSendMediaList {
		hasSendMediaMap[media] = true
	}
	for _, media := range lv1List {
		lv1StrArr = append(lv1StrArr, media.VideoUrl)
		if !hasSendMediaMap[media.VideoUrl] {
			lv1ListNew = append(lv1ListNew, media)
		}
	}
	for _, media := range lv2List {
		lv2StrArr = append(lv2StrArr, media.VideoUrl)
		if !hasSendMediaMap[media.VideoUrl] {
			lv2ListNew = append(lv2ListNew, media)
		}
	}
	for _, media := range imgList {
		if !hasSendMediaMap[media] {
			imgListNew = append(imgListNew, media)
		}
	}
	// 如果没有可发送的视频或图片,就返回原来的
	if len(lv1ListNew) == 0 && len(lv2ListNew) == 0 {
		// 1月16日军锋需求 去掉重复发送的视频
		// if len(lv1StrArr) > 0 {
		// 	global.REDIS.ZRem(ctx, hasSendMediaRedisKey, lv1StrArr)
		// }
		// lv1ListNew = lv1List
		// if len(lv2StrArr) > 0 {
		// 	global.REDIS.ZRem(ctx, hasSendMediaRedisKey, lv2StrArr)
		// }
		// lv2ListNew = lv2List
	}
	if len(imgListNew) == 0 {
		if len(imgList) > 0 {
			global.REDIS.ZRem(ctx, hasSendMediaRedisKey, imgList)
		}
		imgListNew = imgList
	}
	return
}

func (m *MsgService) checkRequireImg(msg string) bool {
	msgLower := strings.ToLower(msg)
	for _, s := range global.RequireImgMsgList {
		sLower := strings.ToLower(s)
		if strings.Contains(msgLower, sLower) {
			return true
		}
	}
	return false
}

func (m *MsgService) GetMsgContent(ctx context.Context, userInfo *model.DigitalUser, aiRoleData *model.AiRole, msgScene model.Scene, userSendMsg string, giftId uint) (msgType model.MsgType, msgContent model.MessageContent, err error) {
	var (
		imgUrl          string
		canUndress      bool
		videoItem       response.AiRoleMediaVideoItem
		mediaShowSer    MediaShowService
		giftAiImgInfo   *model.GiftAiImg
		giftAiImgSer    GiftAiImgService
		intimateInfo    *model.UsersIntimate
		userIntimateSer UsersIntimateService
		isAudit         = userInfo.IsAudit()
		isSub           = userInfo.IsSub()
		isSVip          = userInfo.IsSVip()
		isChatVip       = userInfo.IsChatVip()
		isContentVip    = userInfo.IsContentVip()
		hasSendKey      = fmt.Sprintf(global.MEDIA_SHOW_LIST, userInfo.Id, aiRoleData.Id)
	)
	userSendTotal := m.GetUserSendMsgTotal(ctx, userInfo.Id, aiRoleData.RoleID)
	appInfo := global.AppMap[userInfo.AppID]
	verCompare340 := utils.CompareVersions(userInfo.Version, "3.4.0")
	verCompare364 := utils.CompareVersions(userInfo.Version, "3.6.4")
	verCompare363 := utils.CompareVersions(userInfo.Version, "3.6.3")
	verCompare365 := utils.CompareVersions(userInfo.Version, "3.6.5")

	if intimateInfo, err = userIntimateSer.GetByUidAndRoleid(userInfo.Id, aiRoleData.Id); err != nil {
		return
	}
	// 初始化为文本消息,后续的逻辑会覆盖
	msgType = model.MsgTypeTxt
	msgContent.Avatar = utils.S3Url(aiRoleData.Avatar, userInfo.AppID)
	// 拿到配置的视频、图片
	lv1ImgList, _, lv1ImgToLv2Map, lv1VideoList, lv2VideoList := mediaShowSer.GetAiMediaConfData(aiRoleData)
	// global.LOG.Info("GetMsgContent", zap.Uint("userInfoID", userInfo.Id), zap.String("RoleID", aiRoleData.RoleID), zap.Any("lv1VideoList", lv1VideoList), zap.Any("lv2VideoList", lv2VideoList), zap.Any("imgList", imgList))
	// if profileInfo, err = profileSer.GetByUserId(userInfo.Id); err != nil {
	// 	return
	// }
	// // 用户和该AI角色的消息发送总数--->用户发送的数量
	// sendTotal := m.GetUserSendMsgTotal(ctx, userInfo.Id, aiRoleData.RoleID)
	// aiFuncCompare := utils.CompareVersions(userInfo.Version, "2.0.1")
	// if aiFuncCompare >= 0 {
	// 	appAiFuncSwitchCfg := global.AllAiFuncCfgMap[userInfo.AppID]
	// 	if !(sendTotal >= appAiFuncSwitchCfg.GiftMsgCount && isSub && profileInfo.Nsfw) {
	// 		// 30次 开放gift，asmr，视频和undress按照比例可以下发展示（同时需要具备会员资格，且开启了nsfw模式）
	// 		lv2VideoList = []response.AiRoleMediaVideoItem{}
	// 		canUndress = false
	// 	}
	// }
	hasSendMediaList := global.REDIS.SMembers(ctx, hasSendKey).Val()
	if len(hasSendMediaList) > 0 {
		// 如果已经发送过图片或视频,就不再发送
		lv1VideoList, lv2VideoList, lv1ImgList = m.noSendList(ctx, hasSendKey, hasSendMediaList, lv1VideoList, lv2VideoList, lv1ImgList)
	}
	if verCompare340 >= 0 && appInfo.VipMode == model.VipModeNormal {

		// 0级只能文字,1级可以文字或者图片,2级及以上没有限制
		if intimateInfo.Level == 0 {
			lv1ImgList = []string{}
			lv1VideoList = []response.AiRoleMediaVideoItem{}
			lv2VideoList = []response.AiRoleMediaVideoItem{}
		} else if intimateInfo.Level == 1 {
			lv1VideoList = []response.AiRoleMediaVideoItem{}
			lv2VideoList = []response.AiRoleMediaVideoItem{}
		}
	}
	if verCompare364 >= 0 && appInfo.VipMode == model.VipModeTwoVip && appInfo.Id == 4 {
		// 0级只能文字,1级可以文字或者图片,2级及以上没有限制
		if intimateInfo.Level == 0 {
			lv1ImgList = []string{}
			lv1VideoList = []response.AiRoleMediaVideoItem{}
			lv2VideoList = []response.AiRoleMediaVideoItem{}
		} else if intimateInfo.Level == 1 {
			lv1VideoList = []response.AiRoleMediaVideoItem{}
			lv2VideoList = []response.AiRoleMediaVideoItem{}
		}
	}

	if verCompare363 >= 0 && appInfo.VipMode == model.VipModeTwoVip && appInfo.Id == 10 {
		// 0级只能文字,1级可以文字或者图片,2级及以上没有限制
		if intimateInfo.Level == 0 {
			lv1ImgList = []string{}
			lv1VideoList = []response.AiRoleMediaVideoItem{}
			lv2VideoList = []response.AiRoleMediaVideoItem{}
		} else if intimateInfo.Level == 1 {
			lv1VideoList = []response.AiRoleMediaVideoItem{}
			lv2VideoList = []response.AiRoleMediaVideoItem{}
		}
	}

	if appInfo.Id == 13 {
		// 0级只能文字,1级可以文字或者图片,2级及以上没有限制
		if intimateInfo.Level == 0 {
			lv1ImgList = []string{}
			lv1VideoList = []response.AiRoleMediaVideoItem{}
			lv2VideoList = []response.AiRoleMediaVideoItem{}
		} else if intimateInfo.Level == 1 {
			lv1VideoList = []response.AiRoleMediaVideoItem{}
			lv2VideoList = []response.AiRoleMediaVideoItem{}
		} else if intimateInfo.Level == 2 {
			lv1VideoList = []response.AiRoleMediaVideoItem{}
			lv2VideoList = []response.AiRoleMediaVideoItem{}
		}
	}

	if appInfo.Id == 6 || appInfo.Id == 7 || appInfo.Id == 17 || appInfo.Id >= 21 {
		// 0级只能文字,1级可以文字或者图片,2级及以上没有限制
		if intimateInfo.Level == 0 {
			lv1ImgList = []string{}
			lv1VideoList = []response.AiRoleMediaVideoItem{}
			lv2VideoList = []response.AiRoleMediaVideoItem{}
		} else if intimateInfo.Level == 1 {
			lv1VideoList = []response.AiRoleMediaVideoItem{}
			lv2VideoList = []response.AiRoleMediaVideoItem{}
		}
	}

	lv1ImgListLen := len(lv1ImgList)
	if lv1ImgListLen > 0 {
		imgUrl = lv1ImgList[0]
	}

	switch msgScene {
	case model.SceneFirstMsg: // 第一条消息
		// 非审核用户
		if len(lv1VideoList) > 0 {
			videoItem = utils.RandList(lv1VideoList)
			msgType = model.MsgTypeTxtVideo
		} else if lv1ImgListLen > 0 {
			msgType = model.MsgTypeTxtImg
		}
		// 孙宏伟需求 2024-12-24 新增app_id 4号包数据 第一条消息
		if appInfo.VipMode == model.VipModeTwoVip && lv1ImgListLen > 0 && appInfo.Id != 4 && appInfo.Id != 10 && appInfo.Id != 13 && appInfo.Id != 6 && appInfo.Id != 7 && appInfo.Id != 17 {
			msgType = model.MsgTypeTxtImg
		}
	case model.SceneRemindMsg, model.SceneChatReplyMsg:
		if m.checkRequireImg(userSendMsg) && lv1ImgListLen > 0 {
			msgType = model.MsgTypeTxtImg
		} else {
			randNum := utils.RandIntn(101)
			// 回复消息概率
			replyRatio := GetReplyMsgRatioFree(ctx)
			if isSub || isSVip || isChatVip || isContentVip {
				// 付费用户概率
				replyRatio = GetReplyMsgRatioSub(ctx)
			}
			// 判断随机数在哪个区间
			if randNum >= replyRatio.TxtRatio[0] && randNum < replyRatio.TxtRatio[1] {
				msgType = model.MsgTypeTxt
			} else if randNum >= replyRatio.ImgRatio[0] && randNum < replyRatio.ImgRatio[1] && lv1ImgListLen > 0 {
				msgType = model.MsgTypeTxtImg
			} else if randNum >= replyRatio.VideoRatio[0] && randNum < replyRatio.VideoRatio[1] {
				if len(lv1VideoList) > 0 {
					msgType = model.MsgTypeTxtVideo
					videoItem = utils.RandList(lv1VideoList)
				} else if len(lv2VideoList) > 0 {
					msgType = model.MsgTypeTxtVideo
					videoItem = utils.RandList(lv2VideoList)
				}
			}
		}
	case model.SceneVideoMsg:
		if len(lv1VideoList) > 0 {
			msgType = model.MsgTypeTxtVideo
			videoItem = utils.RandList(lv1VideoList)
		} else if len(lv2VideoList) > 0 {
			msgType = model.MsgTypeTxtVideo
			videoItem = utils.RandList(lv2VideoList)
		}
	case model.SceneSeeUMsg: // SeeU消息
		if lv1ImgListLen > 0 {
			msgType = model.MsgTypeTxtImg
		}
	case model.SceneSendGift: // 礼物消息
		giftAiImgInfo, err = giftAiImgSer.SearchOnCache(context.Background(), &model.GiftAiImg{
			AiRoleId: aiRoleData.Id,
			GiftId:   giftId,
		})
		if err == nil && len(giftAiImgInfo.ImgList) > 0 {
			imgUrl = utils.RandList(giftAiImgInfo.ImgList)
			msgType = model.MsgTypeTxtImg
		}
	default:
	}
	if msgScene == model.SceneChatReplyMsg && userSendTotal == 1 && appInfo.VipMode == model.VipModeTwoVip && !isAudit && appInfo.Id != 4 && appInfo.Id != 10 && appInfo.Id != 13 && appInfo.Id != 6 && appInfo.Id != 7 && appInfo.Id != 17 {
		// 孙宏伟需求 2024-12-24 新增app_id 4号包数据 亲密度功能 第二条消息处理
		if len(lv1VideoList) > 0 {
			msgType = model.MsgTypeTxtVideo
			videoItem = utils.RandList(lv1VideoList)
		} else if len(lv2VideoList) > 0 {
			msgType = model.MsgTypeTxtVideo
			videoItem = utils.RandList(lv2VideoList)
		}
	}
	// 根据消息类型,设置msgContent内容
	switch msgType {
	case model.MsgTypeTxtImg:
		msgContent.ImgURL = utils.S3Url(imgUrl, userInfo.AppID)
		// 是否可以undress
		if isAudit {
			canUndress = false
		} else {
			var lv2Img = lv1ImgToLv2Map[imgUrl]
			canUndress = lv2Img != ""
		}
		if IsVipSVipUser(userInfo) {
			// 未订阅用户不可以undress
			verCompare320 := utils.CompareVersions(userInfo.Version, "3.2.0")
			if verCompare320 >= 0 && !isSub {
				canUndress = false
			}
			// 未订阅用户且亲密等级小于5不可以undress
			if verCompare340 >= 0 {
				// 小于5级不可以undress
				if intimateInfo.Level < 5 {
					canUndress = false
				} else {
					if !isSub && !isSVip {
						canUndress = false
					}
				}
			}
		} else {
			// 孙宏伟需求 2024-12-24 新增app_id 4号包数据 canundress 判断
			if verCompare364 > 0 && appInfo.Id == 4 {
				if intimateInfo.Level < 5 {
					canUndress = false
				}
			}
			// 孙宏伟需求 2024-12-24 新增app_id 4号包数据 canundress 判断
			if (verCompare363 > 0 && appInfo.Id == 10) || (verCompare365 > 0 && appInfo.Id == 6) || (verCompare365 > 0 && appInfo.Id == 7) || appInfo.Id == 17 || appInfo.Id >= 21 {
				if intimateInfo.Level < 5 {
					canUndress = false
				}
			}

		}
		m.AddSendMediaUrl(ctx, hasSendKey, imgUrl)
		msgContent.CanUndress = canUndress
	case model.MsgTypeTxtVideo:
		videoImg := utils.S3Url(videoItem.VideoImg, userInfo.AppID)
		videoUrl := utils.S3Url(videoItem.VideoUrl, userInfo.AppID)
		msgContent.ImgURL = videoImg
		msgContent.FileURL = videoUrl
		m.AddSendMediaUrl(ctx, hasSendKey, videoItem.VideoUrl)
	default:
	}

	// 根据消息类型,判断是否free
	switch msgType {
	case model.MsgTypeTxtImg:
		rewardUnlockImgCount, _ := m.GetRewardUnlockImgCount(userInfo.Id, aiRoleData.Id)
		if rewardUnlockImgCount > 0 {
			msgContent.Free = true
			// 减少解锁次数
			_ = m.RewardUnlockImg(userInfo.Id, aiRoleData.Id, -1)
		}
	case model.MsgTypeTxtVideo:
		rewardUnlockVideoCount, _ := m.GetRewardUnlockVideoCount(userInfo.Id, aiRoleData.Id)
		if rewardUnlockVideoCount > 0 {
			msgContent.Free = true
			// 减少解锁次数
			_ = m.RewardUnlockVideo(userInfo.Id, aiRoleData.Id, -1)
		}
	default:
	}

	return
}

func (m *MsgService) AddSendMediaUrl(ctx context.Context, redisKey, mediaUrl string) {
	global.REDIS.SAdd(ctx, redisKey, mediaUrl)
	global.REDIS.Expire(ctx, redisKey, time.Hour*24*30)
}

func (m *MsgService) execMsgTrain(ctx context.Context, msgApi model.MsgJob, sendMsgReq *request.RedisMsgReq) (err error) {
	var (
		replyMsg        string
		transMsg        string
		lastMsgInfo     *model.MsgInfo
		aiRoleInfo      *model.AiRole
		userInfo        *model.DigitalUser
		userProfileInfo *model.DigitalUserProfile
		userService     DigitalUserService
		profileService  DigitalUserProfileService
		aiRoleService   AiRoleService
		transSer        TranslateService
		fcmSer          FcmService
		messageType     model.MsgType
		msgContent      model.MessageContent
		now             = time.Now()
		dayStr          = now.Format(time.DateOnly)
	)
	tStartTime := time.Now()
	logger := global.LOG.With(zap.String("Name", msgApi.Name), zap.String("ApiUrl", msgApi.ApiUrl), zap.Any("sendMsgReq", sendMsgReq), zap.Any("StartTime", tStartTime))
	uuidStr := uuid.NewString()
	userIdStr := fmt.Sprintf("%d", sendMsgReq.UserId)
	roleIdStr := sendMsgReq.AiRoleId
	requireCost := sendMsgReq.RequireCost
	userId := sendMsgReq.UserId

	sqlStart := time.Now()
	// 查询用户信息
	if userInfo, err = userService.GetById(ctx, userId); err != nil {
		logger.Error("获取用户信息失败", zap.Error(err))
		return
	}
	isSVip := userInfo.IsSVip()
	isSub := userInfo.IsSub()
	isChatVip := userInfo.IsChatVip()
	isContentVip := userInfo.IsContentVip()
	// app信息
	appInfo := global.AppMap[userInfo.AppID]

	// App配置项
	freeReplyCount := appInfo.DetailConfig.Data().FreeMsgCount
	// replyImgRate := appInfo.DetailConfig.Data().AiReplyImgRate
	replyPerFlo := appInfo.DetailConfig.Data().AiReplyPerFlower
	if isSVip || isChatVip {
		replyPerFlo = 0
	}
	paidReplyCount := appInfo.DetailConfig.Data().PaidMsgCount
	maskMsgFlo := appInfo.DetailConfig.Data().MaskMsgPerFlower

	userAdMsgCount := m.GetWatchAdMsgCountToday(ctx, userId)

	freeReplyCount += userAdMsgCount
	paidReplyCount += userAdMsgCount

	replyLimitCount := freeReplyCount
	if IsVipSVipUser(userInfo) {
		if isSub {
			// 订阅用户的限制数量
			replyLimitCount = paidReplyCount
		}
	} else {
		if isChatVip {
			replyLimitCount = appInfo.DetailConfig.Data().ChatVipMsgCount
		} else if isContentVip {
			replyLimitCount = appInfo.DetailConfig.Data().ContentVipMsgCount
		}
	}

	// 查询用户Profile信息
	if userProfileInfo, err = profileService.GetByUserId(userId); err != nil {
		logger.Error("获取用户Profile信息出错", zap.Any("err", err))
		return
	}
	// 查询AI角色信息
	if aiRoleInfo, err = aiRoleService.GetByRoleId(roleIdStr); err != nil {
		logger.Error("获取AI角色信息出错", zap.Any("err", err))
		return
	}
	sqlLatency := time.Since(sqlStart)
	logger = logger.With(zap.Any("sqlLatency", sqlLatency))
	// 查询当日总共对话条数
	replyCount := m.GetAiReplyUserCountToday(ctx, userId)

	startTime := time.Now()
	// 查询用户和该AI角色的最新一条聊天记录,按照send_time倒序
	if lastMsgInfo, err = m.LastMsg(ctx, userIdStr, roleIdStr); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			err = nil
			lastMsgInfo = &model.MsgInfo{
				MessageID:    uuidStr,
				Channel:      nil,
				SendTime:     now.UnixMilli(),
				DeletedAt:    nil,
				MessageType:  model.MsgTypeTxt,
				SenderID:     userIdStr,
				SenderName:   userInfo.Nickname,
				ReceiverID:   roleIdStr,
				ReceiverName: aiRoleInfo.Nickname,
				MessageContent: model.MessageContent{
					Text: "Hi",
				},
			}
		} else {
			logger.Error("获取最后一条消息失败", zap.Any("err", err))
			return
		}
	}
	logger.Info("3.获取最后一条消息成功", zap.Any("lastMsgInfo", lastMsgInfo))
	mongoLatency := time.Since(startTime)
	logger = logger.With(zap.Any("mongoLatency", mongoLatency))

	messageId := lastMsgInfo.MessageID
	userSendMsg := lastMsgInfo.MessageContent.Text
	// 获取回复消息
	aiReqStart := time.Now()
	if replyMsg, err = m.getAiReplyMsg(ctx, msgApi, userSendMsg, aiRoleInfo, userInfo, sendMsgReq, userProfileInfo.Nsfw); err != nil {
		logger.Error("获取大模型回复消息失败", zap.Any("err", err))
		return
	}

	aiReqLatency := time.Since(aiReqStart)
	logger = logger.With(zap.Any("aiReqLatency", aiReqLatency))
	latency := time.Since(startTime)
	latencySecond := int(latency.Seconds())
	go TdPointer(userInfo, userProfileInfo, td.ServerEvent, map[string]interface{}{
		"event_tag":    "msg",
		"user_msg":     userSendMsg,
		"ai_replay":    replyMsg,
		"ai_role_id":   aiRoleInfo.RoleID,
		"ai_role_type": aiRoleInfo.RoleType,
		"time_long":    latencySecond,
	})
	// 解锁
	lockRedisKey := m.GetMsgLockRedisKey(sendMsgReq)
	_ = global.REDIS.Del(ctx, lockRedisKey).Val()

	logger.Info("4.获取回复消息成功&&消息已解锁", zap.String("userMsg", userSendMsg), zap.String("replyMsg", replyMsg))

	getMsgContentStart := time.Now()
	if requireCost == 0 {
		if sendMsgReq.GiftId != 0 {
			messageType, msgContent, err = m.GetMsgContent(ctx, userInfo, aiRoleInfo, model.SceneSendGift, userSendMsg, sendMsgReq.GiftId)
		} else {
			messageType, msgContent, err = m.GetMsgContent(ctx, userInfo, aiRoleInfo, model.SceneRemindMsg, userSendMsg, sendMsgReq.GiftId)
		}
	} else {
		messageType, msgContent, err = m.GetMsgContent(ctx, userInfo, aiRoleInfo, model.SceneChatReplyMsg, userSendMsg, sendMsgReq.GiftId)
	}
	getMsgContentLatency := time.Since(getMsgContentStart)
	logger = logger.With(zap.Any("getMsgContentLatency", getMsgContentLatency))

	// 检查是否可以生成语音
	msgContent.CanVoice = m.msgCanVoice(replyMsg)
	// 语音屏蔽打开 1_03军锋需求修改文字转语音
	// msgContent.CanVoice = false

	costStart := time.Now()
	msgIsFree := true
	hasSensitiveWord := false
	if requireCost == 1 {
		// 需要扣费
		if isSub || isChatVip {
			// 已订阅用户
			// 当回复用户数量大于免费回复次数时,需要扣费
			if replyCount > replyLimitCount {
				if userProfileInfo.FlowerAmount < replyPerFlo {
					// 玫瑰花数量不足,发送蒙版消息
					messageType = model.MsgTypeTxtMask
				} else {
					// 玫瑰花数量足,扣玫瑰花,发正常的文本消息,记录消费记录
					if err = m.SaveConsumeRecord(userId, aiRoleInfo, replyPerFlo, model.ConsumeTypeMaskMsg); err != nil {
						logger.Error("SaveConsumeRecord error", zap.Any("err", err))
						return
					}
					msgIsFree = false
				}
			}
		} else {
			// 未订阅用户
			// 当回复用户数量大于免费回复次数时,需要扣费
			if replyCount == replyLimitCount {
				if userProfileInfo.FlowerAmount < maskMsgFlo {
					// 玫瑰花数量不足,发送蒙版消息
					messageType = model.MsgTypeTxtMask
				} else {
					// 玫瑰花数量足,扣玫瑰花,发正常的文本消息,记录消费记录
					if err = m.SaveConsumeRecord(userId, aiRoleInfo, maskMsgFlo, model.ConsumeTypeMaskMsg); err != nil {
						logger.Error("SaveConsumeRecord error", zap.Any("err", err))
						return
					}
					msgIsFree = false
				}
			} else if replyCount > replyLimitCount {
				if userProfileInfo.FlowerAmount < replyPerFlo {
					// 玫瑰花数量不足,发送蒙版消息
					messageType = model.MsgTypeTxtMask
				} else {
					// 玫瑰花数量足,扣玫瑰花,发正常的文本消息,记录消费记录
					if err = m.SaveConsumeRecord(userId, aiRoleInfo, replyPerFlo, model.ConsumeTypeMaskMsg); err != nil {
						logger.Error("SaveConsumeRecord error", zap.Any("err", err))
						return
					}
					msgIsFree = false
				}
			}
		}

		// 1.12.0需求,非VIP权限用户在聊天页发敏感词 AI自动回复蒙版消息
		fantasyCompare := utils.CompareVersions(userInfo.Version, "3.1.0")
		// Fame Fantasy IOS 特殊处理
		if !(userInfo.AppID == 4 && fantasyCompare < 0) {
			if (!isSub && !isChatVip) && messageType != model.MsgTypeTxtMask {
				// 判断是否有敏感词
				if hasSensitiveWord = m.HasSensitiveWordTextMask(userSendMsg); hasSensitiveWord {
					messageType = model.MsgTypeTxtMask
				}
			}
		}
	}

	costLatency := time.Since(costStart)
	logger = logger.With(zap.Any("costLatency", costLatency))

	transStart := time.Now()
	if transMsg, err = transSer.MsgTranslate(ctx, appInfo, userInfo, replyMsg); err != nil {
		global.LOG.Error("TranslateText error", zap.Any("err", err))
	}
	transLatency := time.Since(transStart)
	logger = logger.With(zap.Any("transLatency", transLatency))
	msgContent.Text = transMsg
	msgContent.OriginText = replyMsg
	if sendMsgReq.GiftId != 0 {
		// 礼物回复消息不能undress
		msgContent.CanUndress = false
	}

	// 1-7日 pickMe需求 换个位置
	if userInfo.AppID == 13 && (aiRoleInfo.Gender == model.GenderTypeFemale && aiRoleInfo.RoleType != model.AiRoleTypeAnime) {
		// 用户和该AI角色的消息发送总数--->用户发送的数量
		sendTotal := m.GetUserSendMsgTotal(ctx, userInfo.Id, aiRoleInfo.RoleID)
		rBoxCfg := GetRequireImgBoxCfg(ctx, userInfo.AppID)
		msgText := utils.RandList(rBoxCfg.MsgList)
		if sendTotal%rBoxCfg.MsgStep == 0 {
			aiSendRImgMsg := &model.MsgInfo{
				MessageID:    uuid.NewString(),
				Channel:      nil,
				SendTime:     now.UnixMilli(),
				DeletedAt:    nil,
				MessageType:  model.MsgTypeRequireImgBox,
				SenderID:     roleIdStr,
				SenderName:   aiRoleInfo.Nickname,
				ReceiverID:   userIdStr,
				ReceiverName: userInfo.Nickname,
				MessageContent: model.MessageContent{
					Avatar:     utils.S3Url(aiRoleInfo.Avatar, userInfo.AppID),
					Text:       msgText,
					OriginText: msgText,
					ImgURL:     utils.S3Url(rBoxCfg.ImgUrl, userInfo.AppID),
				},
			}
			// 发送消息到声网
			if err = m.SendMsgToAgora(aiSendRImgMsg, appInfo); err != nil {
				logger.Error("SendMsgToAgora error", zap.Any("err", err))
				return
			}
		}
	}

	aiSendMsg := &model.MsgInfo{
		MessageID:      messageId,
		Channel:        nil,
		SendTime:       now.UnixMilli(),
		DeletedAt:      nil,
		MessageType:    messageType,
		SenderID:       roleIdStr,
		SenderName:     aiRoleInfo.Nickname,
		ReceiverID:     userIdStr,
		ReceiverName:   userInfo.Nickname,
		MessageContent: msgContent,
	}
	sendAgoraStart := time.Now()
	// 发送消息到声网
	if err = m.SendMsgToAgora(aiSendMsg, appInfo); err != nil {
		logger.Error("SendMsgToAgora error", zap.Any("err", err))
		return
	}
	// 2024-12-26 新增礼物引导 孙宏伟需求 消息push
	var guidedGiftMsg response.GuidedGiftGivingMsg
	guidedGiftMsg.UserId = userId
	guidedGiftMsg.RoleId = aiRoleInfo.Id
	guidedGiftMsg.AppId = userInfo.AppID
	guidedGiftMsg.AiRoleId = aiRoleInfo.RoleID
	// 将消息转换为 JSON
	logger.Info("guidedGiftMsg_save", zap.Any("msg", guidedGiftMsg))
	msgStr, guidedMsgErr := json.Marshal(guidedGiftMsg)
	if guidedMsgErr == nil {
		logger.Info("guidedGiftMsg_push", zap.Any("msg", guidedGiftMsg))
		global.REDIS.LPush(ctx, global.GUIDED_GIFT_GIVING_KEY, msgStr)
	}

	sendAgoraLatency := time.Since(sendAgoraStart)
	logger = logger.With(zap.Any("sendAgoraLatency", sendAgoraLatency))

	msgLatency := time.Since(tStartTime)
	logger = logger.With(zap.Any("msgLatency", msgLatency))

	logger.Info("5.send agora success")

	// 更新UserAiMsgCount
	go func() {
		var (
			msgCountSer UserAiMsgCountService
		)
		if err = msgCountSer.FindUpdate(ctx, userId, aiRoleInfo, map[string]interface{}{
			"msg_round_count": gorm.Expr("msg_round_count + 1"),
		}); err != nil {
			return
		}
	}()

	// 发送IOS应用通知
	fcmSer.PushReplyMsg(ctx, userInfo, aiRoleInfo, aiSendMsg)

	saveStart := time.Now()
	// 保存发送过的记录
	aiSendMediaSer := AiSendMediaService{}
	aiSendMediaRecord := model.AiSendMedia{
		DigitalUserId: userInfo.Id,
		AiRoleId:      aiRoleInfo.Id,
		MediaType:     0,
		MediaURL:      "",
		ThumbnailURL:  "",
		AsmrName:      "",
		SendTime:      now,
		UnlockState:   model.UnlockStateNo,
		UnlockTime:    nil,
	}
	if messageType == model.MsgTypeTxtImg || messageType == model.MsgTypeTxtImgVoice {
		// 发送的是图片
		aiSendMediaRecord.MediaType = model.MediaTypeImg
		aiSendMediaRecord.MediaURL = msgContent.ImgURL
		aiSendMediaRecord.ThumbnailURL = msgContent.ImgURL
		aiSendMediaRecord.CanUndress = msgContent.CanUndress
		aiSendMediaSer.SaveByMsg(&aiSendMediaRecord)
	} else if messageType == model.MsgTypeTxtVideo || messageType == model.MsgTypeTxtVideoVoice {
		// 发送的是视频
		aiSendMediaRecord.MediaType = model.MediaTypeVideo
		aiSendMediaRecord.MediaURL = msgContent.FileURL
		aiSendMediaRecord.ThumbnailURL = msgContent.ImgURL
		aiSendMediaSer.SaveByMsg(&aiSendMediaRecord)
	}
	// 更新亲密值
	intimateSubType := model.SubTypePaidMsg
	if msgIsFree {
		intimateSubType = model.SubTypeFreeMsg
	}
	_ = m.SaveIntimateRecord(ctx, userInfo.Id, aiRoleInfo, model.IntimateTypeAdd, intimateSubType)

	saveLatency := time.Since(saveStart)
	logger = logger.With(zap.Any("saveLatency", saveLatency))

	// redis里增加回复用户的数量,增加已聊过的AI角色ID
	if requireCost == 1 {
		// 1.12.0 AI自动回复蒙版消息，不算在回消息数量限制内
		if !hasSensitiveWord {
			// 回复用户的数量
			m.IncrAiReplyUserCount(ctx, userId)
		}
		// 已聊过的AI角色ID
		aiRoleLimitKey := fmt.Sprintf(global.AI_CHAT_ROLE_LIMIT_KEY, dayStr, userId)
		// 自创建角色不加入限制
		if aiRoleInfo.RoleType != model.AiRoleTypePrivate {
			if err = global.REDIS.SAdd(ctx, aiRoleLimitKey, roleIdStr).Err(); err != nil {
				logger.Error("SAdd aiRoleLimitKey error", zap.Any("err", err))
				return
			}
			// 设置过期时间
			if err = global.REDIS.Expire(ctx, aiRoleLimitKey, 48*time.Hour).Err(); err != nil {
				logger.Error("Expire aiRoleLimitKey error", zap.Any("err", err))
				return
			}
		}
	}

	t := time.Since(sqlStart) - sendMsgReq.Api3Time - transLatency
	if t > time.Second*5 && sendMsgReq.Api3Time != 0 {
		data, _ := json.Marshal(sendMsgReq)
		Alarm(ctx, "后端处理时间大于5s", fmt.Sprintf("%0.1f", t.Seconds()), string(data), "api")
	}
	if sendMsgReq.Api3Time != 0 {
		logger.Info("6.send msg end 11", zap.Any("timeall", t.Seconds()))
	} else {
		logger.Info("6.send msg end")
	}
	return
}

func (m *MsgService) execNotifyChargeTrain(ctx context.Context, taskStr string) (err error) {
	var (
		aiRoleInfo    *model.AiRole
		userInfo      *model.DigitalUser
		profileInfo   *model.DigitalUserProfile
		userService   DigitalUserService
		profileSer    DigitalUserProfileService
		aiRoleService AiRoleService
		rKeyExist     int64
		now           = time.Now()
		dayStr        = now.Format(time.DateOnly)
	)
	taskStrList := strings.Split(taskStr, "_")
	userIdStr := taskStrList[0]
	roleIdStr := taskStrList[1]
	userIdInt, _ := strconv.Atoi(userIdStr)
	userId := uint(userIdInt)
	// 查询用户信息
	if userInfo, err = userService.GetById(ctx, userId); err != nil {
		global.LOG.Error("userService GetById error", zap.Error(err))
		return
	}

	isAudit := userInfo.IsAudit()
	// isSVip := userInfo.IsSVip()
	// isSub := userInfo.IsSub()
	// isChatVip := userInfo.IsChatVip()
	// isContentVip := userInfo.IsContentVip()
	if profileInfo, err = profileSer.GetByUserId(userId); err != nil {
		return
	}
	if isAudit {
		return
	}

	// app信息
	appInfo := global.AppMap[userInfo.AppID]

	// App配置项
	noticeChargeSwitch := appInfo.DetailConfig.Data().NoticeChargeSwitch
	if !noticeChargeSwitch {
		return
	}
	// 查询AI角色信息
	if aiRoleInfo, err = aiRoleService.GetByRoleId(roleIdStr); err != nil {
		global.LOG.Error("GetByRoleId error", zap.Any("err", err))
		return
	}
	rKey := fmt.Sprintf(global.NOTICE_CHARGE_KEY, dayStr, userId, roleIdStr)
	if rKeyExist, _ = global.REDIS.Exists(ctx, rKey).Result(); rKeyExist > 0 {
		// 今日已催单
		return
	} else {
		// 设置过期时间
		if err = global.REDIS.Set(ctx, rKey, "1", 25*time.Hour).Err(); err != nil {
			global.LOG.Error("Set rKey error", zap.Any("err", err))
			return
		}
	}

	// 发送到消息处理队列
	redisMsgReq := &request.RedisMsgReq{
		UrlType:  m.GetMsgUrlType(userInfo, profileInfo),
		SendType: request.SendTypeNotifyCharge,
		UserId:   userInfo.Id,
		AiRoleId: aiRoleInfo.RoleID,
	}
	m.SendAiMsg(ctx, redisMsgReq)
	return
}

func (m *MsgService) CheckPushMsg(c *gin.Context) {
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Error("CheckPushMsg panic", zap.Any("err", err))
		}
	}()
	var (
		err         error
		msgText     string
		pushMsg     *model.PushMsg
		aiRoleInfo  *model.AiRole
		userInfo    *model.DigitalUser
		userService DigitalUserService
		userId      = utils.GetDigitalUserID(c)
		appVersion  = c.GetHeader("App-Version")
	)
	mLog := global.LOG.With(zap.Uint("userId", userId))
	pushVersionCompare := utils.CompareVersions(appVersion, "1.18.0")
	if pushVersionCompare < 0 {
		return
	}
	// 查询是否有要推送的消息
	if pushMsg, err = m.GetNowPushMsg(c); err != nil {
		return
	}
	pushMsgLockKey := fmt.Sprintf(global.PUSH_MSG_LOCK_KEY, userId, pushMsg.TimeRange[0], pushMsg.TimeRange[1])
	lockRes := global.REDIS.SetNX(c, pushMsgLockKey, "1", 23*time.Hour).Val()
	if !lockRes {
		mLog.Info("已推送过消息")
		return
	}
	// 查询用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		mLog.Error("userService GetById error", zap.Error(err))
		return
	}
	// 审核用户不推送消息
	if userInfo.IsAudit() {
		return
	}
	// 查询用户最近聊的AI角色
	if aiRoleInfo, err = m.GetLastChatAiRole(c, userId); err != nil {
		// mLog.Error("GetLastChatAiRole error", zap.Error(err))
		return
	}

	// 随机取一条消息
	randomMsg := pushMsg.MsgList[rand.Intn(len(pushMsg.MsgList))]
	// 替换消息中的变量
	msgText = m.ReplaceMsg(randomMsg, userInfo, aiRoleInfo)
	m.sendPushNotifyMsg(c, userInfo, aiRoleInfo, msgText)
}

func (m *MsgService) sendPushNotifyMsg(c *gin.Context, userInfo *model.DigitalUser, aiRoleInfo *model.AiRole, msgText string) {
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Error("sendPushNotifyMsg panic", zap.Any("err", err))
		}
	}()
	var (
		transMsg   string
		msgService MsgService
		transSer   TranslateService
		now        = time.Now()
		aiReplyMsg = msgText
		appInfo    = global.AppMap[userInfo.AppID]
	)
	messageType, msgContent, err := msgService.GetMsgContent(c, userInfo, aiRoleInfo, model.SceneChatReplyMsg, "", 0)
	if err != nil {
		global.LOG.Error("sendPushNotifyMsg GetReplyResource error", zap.Any("err", err))
		return
	}
	// 检查是否可以生成语音
	canVoice := m.msgCanVoice(aiReplyMsg)
	msgContent.Text = aiReplyMsg
	msgContent.CanVoice = canVoice
	// 语音屏蔽打开 1_03军锋需求修改文字转语音
	// msgContent.CanVoice = false

	if transMsg, err = transSer.MsgTranslate(c, appInfo, userInfo, aiReplyMsg); err != nil {
		global.LOG.Error("TranslateText error", zap.Any("err", err))
	}
	msgContent.Text = transMsg
	msgContent.OriginText = aiReplyMsg

	aiSendMsg := &model.MsgInfo{
		MessageID:      uuid.NewString(),
		Channel:        nil,
		SendTime:       now.UnixMilli(),
		DeletedAt:      nil,
		MessageType:    messageType,
		SenderID:       aiRoleInfo.RoleID,
		SenderName:     aiRoleInfo.Nickname,
		ReceiverID:     fmt.Sprintf("%d", userInfo.Id),
		ReceiverName:   userInfo.Nickname,
		MessageContent: msgContent,
	}
	if err = m.SendMsgToAgora(aiSendMsg, appInfo); err != nil {
		global.LOG.Error("sendPushNotifyMsg SendMsgToAgora error", zap.Any("err", err))
		return
	}
	aiSendMediaSer := AiSendMediaService{}
	aiSendMediaRecord := model.AiSendMedia{
		DigitalUserId: userInfo.Id,
		AiRoleId:      aiRoleInfo.Id,
		MediaType:     0,
		MediaURL:      "",
		ThumbnailURL:  "",
		AsmrName:      "",
		SendTime:      now,
		UnlockState:   model.UnlockStateNo,
		UnlockTime:    nil,
	}
	if messageType == model.MsgTypeTxtImg || messageType == model.MsgTypeTxtImgVoice {
		// 发送的是图片
		aiSendMediaRecord.MediaType = model.MediaTypeImg
		aiSendMediaRecord.MediaURL = msgContent.ImgURL
		aiSendMediaRecord.ThumbnailURL = msgContent.ImgURL
		aiSendMediaRecord.CanUndress = msgContent.CanUndress
		aiSendMediaSer.SaveByMsg(&aiSendMediaRecord)
	} else if messageType == model.MsgTypeTxtVideo || messageType == model.MsgTypeTxtVideoVoice {
		// 发送的是视频
		aiSendMediaRecord.MediaType = model.MediaTypeVideo
		aiSendMediaRecord.MediaURL = msgContent.FileURL
		aiSendMediaRecord.ThumbnailURL = msgContent.ImgURL
		aiSendMediaSer.SaveByMsg(&aiSendMediaRecord)
	}
}

func (m *MsgService) ReplaceMsg(msg string, userInfo *model.DigitalUser, aiRoleInfo *model.AiRole) string {
	msg = strings.Replace(msg, "{{nickname}}", userInfo.Nickname, -1)
	msg = strings.Replace(msg, "{{ainame}}", aiRoleInfo.Nickname, -1)
	return msg
}

func (m *MsgService) GetNowPushMsg(c *gin.Context) (pushMsg *model.PushMsg, err error) {
	var (
		clientTime    time.Time
		clientTimeStr = c.GetHeader("Client-Time")
		appVersion    = c.GetHeader("App-Version")
	)
	pushCompare := utils.CompareVersions(appVersion, "1.18.0")
	if pushCompare < 0 {
		return
	}
	if clientTimeStr == "" {
		err = errors.New("Client-Time is empty")
		return
	}
	if clientTime, err = time.Parse("20060102150405", clientTimeStr); err != nil {
		return
	}
	for _, pushMsg = range global.PushMsgList {
		if utils.TimeInRange(clientTime, pushMsg.TimeRange[0], pushMsg.TimeRange[1]) {
			break
		}
	}
	return
}

// GetLastChatAiRole 查询用户最近聊的一个AI角色
func (m *MsgService) GetLastChatAiRole(c *gin.Context, userId uint) (aiRoleInfo *model.AiRole, err error) {
	// 从MongoDB查询用户最近聊的一个AI角色
	var (
		msgInfo       *model.MsgInfo
		aiRoleService AiRoleService
		userIdStr     = fmt.Sprintf("%d", userId)
		filterMap     = bson.M{
			"sender_id":  userIdStr,
			"deleted_at": bson.M{"$eq": nil},
		}
	)
	if err = global.MONGO.Find(c, filterMap).Sort("-send_time").Limit(1).One(&msgInfo); err != nil {
		return
	}
	aiRoleInfo, err = aiRoleService.GetByRoleId(msgInfo.ReceiverID)
	return
}

func (m *MsgService) SendIdleChatMsg(ctx context.Context, userInfo *model.DigitalUser, aiRoleInfo *model.AiRole, appInfo model.App) {
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Error("SendIdleChatMsg panic", zap.Any("err", err))
		}
	}()

	var (
		transMsg   string
		transSer   TranslateService
		fcmSer     FcmService
		now        = time.Now()
		randIdx    = rand.Intn(len(global.IdleReplyList))
		aiReplyMsg = global.IdleReplyList[randIdx]
	)

	messageType, msgContent, err := m.GetMsgContent(ctx, userInfo, aiRoleInfo, model.SceneChatReplyMsg, "", 0)
	if err != nil {
		global.LOG.Error("GetReplyResource error", zap.Any("err", err))
		return
	}
	// 检查是否可以生成语音
	msgContent.Text = aiReplyMsg
	msgContent.CanVoice = m.msgCanVoice(aiReplyMsg)
	// 语音屏蔽打开 1_03军锋需求修改文字转语音
	// msgContent.CanVoice = false
	if transMsg, err = transSer.MsgTranslate(ctx, appInfo, userInfo, aiReplyMsg); err != nil {
		global.LOG.Error("TranslateText error", zap.Any("err", err))
	}
	msgContent.Text = transMsg
	msgContent.OriginText = aiReplyMsg

	aiSendMsg := &model.MsgInfo{
		MessageID:      uuid.NewString(),
		Channel:        nil,
		SendTime:       now.UnixMilli(),
		DeletedAt:      nil,
		MessageType:    messageType,
		SenderID:       aiRoleInfo.RoleID,
		SenderName:     aiRoleInfo.Nickname,
		ReceiverID:     fmt.Sprintf("%d", userInfo.Id),
		ReceiverName:   userInfo.Nickname,
		MessageContent: msgContent,
	}
	if err = m.SendMsgToAgora(aiSendMsg, appInfo); err != nil {
		global.LOG.Error("SendIdleChatMsg SendMsgToAgora error", zap.Any("err", err))
		return
	}
	// 发送IOS应用通知
	fcmSer.PushReplyMsg(ctx, userInfo, aiRoleInfo, aiSendMsg)

	aiSendMediaSer := AiSendMediaService{}
	aiSendMediaRecord := model.AiSendMedia{
		DigitalUserId: userInfo.Id,
		AiRoleId:      aiRoleInfo.Id,
		MediaType:     0,
		MediaURL:      "",
		ThumbnailURL:  "",
		AsmrName:      "",
		SendTime:      now,
		UnlockState:   model.UnlockStateNo,
		UnlockTime:    nil,
	}
	if messageType == model.MsgTypeTxtImg || messageType == model.MsgTypeTxtImgVoice {
		// 发送的是图片
		aiSendMediaRecord.MediaType = model.MediaTypeImg
		aiSendMediaRecord.MediaURL = msgContent.ImgURL
		aiSendMediaRecord.ThumbnailURL = msgContent.ImgURL
		aiSendMediaRecord.CanUndress = msgContent.CanUndress
		aiSendMediaSer.SaveByMsg(&aiSendMediaRecord)
	} else if messageType == model.MsgTypeTxtVideo || messageType == model.MsgTypeTxtVideoVoice {
		// 发送的是视频
		aiSendMediaRecord.MediaType = model.MediaTypeVideo
		aiSendMediaRecord.MediaURL = msgContent.FileURL
		aiSendMediaRecord.ThumbnailURL = msgContent.ImgURL
		aiSendMediaSer.SaveByMsg(&aiSendMediaRecord)
	}
}

func (m *MsgService) execIdleMsgTrainV1(ctx context.Context, taskStr string) (err error) {
	var (
		aiRoleInfo    *model.AiRole
		userInfo      *model.DigitalUser
		userService   DigitalUserService
		aiRoleService AiRoleService
	)
	taskStrList := strings.Split(taskStr, "_")
	userIdStr := taskStrList[0]
	roleIdStr := taskStrList[1]
	userIdInt, _ := strconv.Atoi(userIdStr)
	userId := uint(userIdInt)
	// 查询用户信息
	if userInfo, err = userService.GetById(ctx, userId); err != nil {
		global.LOG.Error("userService GetById error", zap.Error(err))
		return
	}
	appInfo := global.AppMap[userInfo.AppID]
	// 查询AI角色信息
	if aiRoleInfo, err = aiRoleService.GetByRoleId(roleIdStr); err != nil {
		global.LOG.Error("GetByRoleId error", zap.Any("err", err))
		return
	}
	go m.SendIdleChatMsg(ctx, userInfo, aiRoleInfo, appInfo)
	return
}

func (m *MsgService) MsgTrain() {
	// 索引变量，用于轮询选择接口
	for _, msgApi := range global.MsgJobMap {
		go m.MsgApiTrain(msgApi)
	}
}

func (m *MsgService) MsgApiTrain(msgApi model.MsgJob) {
	var (
		msgApiExist bool
		ctx         = context.Background()
		redisKey    = global.MAIN_QUEUE_AIMSG
	)
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Error("MsgApiTrain panic", zap.Any("err", err), zap.Any("msgApi", msgApi))
		}
	}()
	for {
		time.Sleep(time.Millisecond * 100)
		// 从全局map重新获取msgApi,重载刷新
		msgApi, msgApiExist = global.MsgJobMap[msgApi.Id]
		if !msgApiExist {
			time.Sleep(time.Second * 10)
			continue
		}
		// 检查该job的状态
		if msgApi.Status == model.SwitchStatusDisabled {
			time.Sleep(time.Second * 10)
			continue
		}

		// 从 Redis 有序集合中获取消息
		redisMsgList, err := global.REDIS.ZRange(ctx, redisKey, 0, -1).Result()
		if err != nil {
			global.LOG.Error("ZRevRange error", zap.Any("err", err))
			continue
		}
		redisMsgCount := len(redisMsgList)
		if redisMsgCount == 0 {
			continue
		}
		// 消息过多,发送钉钉通知
		m.CheckSendDDNotifyMsgToMany(redisMsgCount)

		var (
			sendMsgReq *request.RedisMsgReq
			taskStr    = redisMsgList[0]
		)
		if err = json.Unmarshal([]byte(taskStr), &sendMsgReq); err != nil {
			if _, err = global.REDIS.ZRem(ctx, redisKey, taskStr).Result(); err != nil {
				global.LOG.Error("删除消息key出错", zap.Any("err", err), zap.String("taskStr", taskStr))
			}
			continue
		}

		// 这里只处理v1的或者0的
		// if sendMsgReq.UrlType != 1 && sendMsgReq.UrlType != 0 {
		// 	continue
		// }

		global.LOG.Info("1.取出消息", zap.Any("sendMsgReq", sendMsgReq))
		// 对该taskStr加锁

		lockRedisKey := m.GetMsgLockRedisKey(sendMsgReq)
		lockRes := global.REDIS.SetNX(ctx, lockRedisKey, "1", 10*time.Second).Val()
		if !lockRes {
			if _, err = global.REDIS.ZRem(ctx, redisKey, taskStr).Result(); err != nil {
				global.LOG.Error("删除消息key出错", zap.Any("err", err), zap.Any("sendMsgReq", sendMsgReq))
			}
			continue
		}
		global.LOG.Info("2.消息已加锁", zap.Any("sendMsgReq", sendMsgReq))
		// 删除掉该消息
		if _, err = global.REDIS.ZRem(ctx, redisKey, taskStr).Result(); err != nil {
			global.LOG.Error("删除消息key出错", zap.Any("err", err), zap.Any("sendMsgReq", sendMsgReq))
		}
		// 消息处理
		switch sendMsgReq.SendType {
		case request.SendTypeVoiceCall:
			// 语音通话
			err = m.execVoiceCallTrain(ctx, msgApi, sendMsgReq)
		case request.SendTypeUserQuickReply:
			// 用户获取快捷回复
			err = m.execUserQuickReply(ctx, msgApi, sendMsgReq)
		default:
			err = m.execMsgTrain(ctx, msgApi, sendMsgReq)
		}
		if err != nil {
			// 消息解锁
			_ = global.REDIS.Del(ctx, lockRedisKey).Val()
			global.LOG.Error("MsgApiTrain error", zap.Any("err", err))
		}
	}
}

func (m *MsgService) SlowMsgDDLock() bool {
	ctx := context.Background()
	return global.REDIS.SetNX(ctx, global.MSG_SEC_DIFF_LOCK, "1", time.Hour).Val()
}

func (m *MsgService) DDLockMsgMany() bool {
	ctx := context.Background()
	return global.REDIS.SetNX(ctx, global.MSG_MANY_LOCK, "1", time.Minute*5).Val()
}

func (m *MsgService) CheckSendDDNotifySlowMsg(appInfo model.App, userInfo *model.DigitalUser, aiRoleInfo *model.AiRole, userSendMsg, aiMsg *model.MsgInfo, msgApi model.MsgJob) {
	var (
		err         error
		resBody     string
		now         = time.Now()
		nowStr      = now.Format(time.DateTime)
		sendTime    = time.UnixMilli(userSendMsg.SendTime)
		sendTimeStr = sendTime.Format(time.DateTime)
		timeSec     = int(now.Sub(sendTime).Seconds())
		data        = notify.Message{Msgtype: ""}
		token       = global.CONFIG.DDnotify.Token
		secret      = global.CONFIG.DDnotify.Secret
	)
	if token == "" || secret == "" {
		return
	}
	global.LOG.Info(msgApi.Name, zap.Int("时间差值", timeSec))
	if timeSec < 30 || timeSec > 300 {
		return
	}
	if !m.SlowMsgDDLock() {
		return
	}

	var sendText = `
** 回复延迟秒数: %d **
- 客户端: %s
- 用户ID: %d
- 用户国家: %s
- 是否订阅: %v
- 请求脚本: %s
- 请求地址: %s
- 发送时间: %s
- 当前时间: %s
- 用户消息: %s
- 回复消息: %s
`
	sendText = fmt.Sprintf(sendText,
		timeSec,
		appInfo.Name,
		userInfo.Id,
		userInfo.CountryCode,
		userInfo.IsSub(),
		msgApi.Name,
		msgApi.ApiUrl,
		sendTimeStr,
		nowStr,
		userSendMsg.MessageContent.Text,
		aiMsg.MessageContent.Text,
	)
	data.Markdown.Title = "回复消息过慢"
	data.Msgtype = "markdown"
	data.Markdown.Text = sendText
	resBody, err = notify.SendDingTalk(data, token, secret)
	if err != nil {
		global.LOG.Error("发送钉钉通知 error", zap.Any("err", err), zap.String("resBody", resBody))
		return
	}
}

func (m *MsgService) CheckSendDDNotifyMsgToMany(msgCount int) {
	// 可用服务器数量
	var jobCount int
	for _, msgJob := range global.MsgJobMap {
		if msgJob.Status == model.SwitchStatusNormal {
			jobCount++
		}
	}
	warnCount := jobCount * 5
	if msgCount < warnCount {
		return
	}

	var (
		err     error
		resBody string
		data    = notify.Message{Msgtype: ""}
		token   = global.CONFIG.DDnotify.Token
		secret  = global.CONFIG.DDnotify.Secret
	)
	if token == "" || secret == "" {
		return
	}
	if !m.DDLockMsgMany() {
		return
	}
	var sendText = `
** 队列中消息数量: %d **
** 当前开启的模型数量%d **
预警值: %d
消息过多,请酌情增加大模型机器
`
	sendText = fmt.Sprintf(sendText, msgCount, jobCount, warnCount)
	data.Markdown.Title = fmt.Sprintf("队列中消息数量: %d", msgCount)
	data.Msgtype = "markdown"
	data.Markdown.Text = sendText
	resBody, err = notify.SendDingTalk(data, token, secret)
	if err != nil {
		global.LOG.Error("发送钉钉通知 error", zap.Any("err", err), zap.String("resBody", resBody))
		return
	}
}

func (m *MsgService) GetMsgLockRedisKey(redisMsgReq *request.RedisMsgReq) (lockRedisKey string) {
	msgKey := fmt.Sprintf("%d_%s_%d", redisMsgReq.UserId, redisMsgReq.AiRoleId, redisMsgReq.SendType)
	lockRedisKey = fmt.Sprintf(global.MSG_LOCK_KEY, msgKey)
	return
}

func (m *MsgService) MsgLock(redisMsgReq *request.RedisMsgReq) (isLock bool) {
	lockRedisKey := m.GetMsgLockRedisKey(redisMsgReq)
	lockExist := global.REDIS.Exists(context.Background(), lockRedisKey).Val()
	return lockExist > 0
}

func (m *MsgService) SendAiMsg(ctx context.Context, redisMsgReq *request.RedisMsgReq) {
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Error("SendAiMsg panic", zap.Any("err", err))
		}
	}()
	redisMsgReq.UUid = uuid.NewString()
	if m.MsgLock(redisMsgReq) {
		return
	}
	sendMsgReqBytes, jsonErr := json.Marshal(redisMsgReq)
	if jsonErr != nil {
		return
	}
	memberStr := string(sendMsgReqBytes)
	score := time.Now().UnixMilli()

	// 语音通话优先级最高
	if redisMsgReq.SendType == request.SendTypeVoiceCall {
		score = score - 10000
	}
	// 用户获取快捷回复的优先级提高
	if redisMsgReq.SendType == request.SendTypeUserQuickReply {
		score = score - 1000000
	}
	if err := global.REDIS.ZAdd(ctx, global.MAIN_QUEUE_AIMSG, redis.Z{Score: float64(score), Member: memberStr}).Err(); err != nil {
		global.LOG.Error("sendAiMsg ZAdd error", zap.Any("err", err))
		return
	}
}

func (m *MsgService) DelSendAiMsg(ctx context.Context, redisMsgReq *request.RedisMsgReq) {
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Error("DelSendAiMsg panic", zap.Any("err", err))
		}
	}()
	if m.MsgLock(redisMsgReq) {
		return
	}
	sendMsgReqBytes, jsonErr := json.Marshal(redisMsgReq)
	if jsonErr != nil {
		return
	}
	memberStr := string(sendMsgReqBytes)
	if err := global.REDIS.ZRem(ctx, global.MAIN_QUEUE_AIMSG, memberStr).Err(); err != nil {
		global.LOG.Error("DelSendAiMsg ZRem error", zap.Any("err", err))
		return
	}
}

func (m *MsgService) getRandomMsg() string {
	return global.RANDOM_AI_MSG_LIST[rand.Intn(len(global.RANDOM_AI_MSG_LIST))]
}

func (m *MsgService) SendAuditAiMsg(ctx context.Context, userInfo *model.DigitalUser, aiRoleInfo *model.AiRole, appInfo model.App, userSendMsg *model.MsgInfo) {
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Error("SendAuditAiMsg panic", zap.Any("err", err))
		}
	}()
	aiReplyMsg := m.getRandomMsg()
	// 检查是否可以生成语音
	canVoice := m.msgCanVoice(aiReplyMsg)
	aiSendMsg := &model.MsgInfo{
		MessageID:    uuid.NewString(),
		Channel:      nil,
		SendTime:     time.Now().UnixMilli(),
		DeletedAt:    nil,
		MessageType:  model.MsgTypeTxt,
		SenderID:     aiRoleInfo.RoleID,
		SenderName:   aiRoleInfo.Nickname,
		ReceiverID:   fmt.Sprintf("%d", userInfo.Id),
		ReceiverName: userInfo.Nickname,
		MessageContent: model.MessageContent{
			Text:     aiReplyMsg,
			CanVoice: canVoice,
			Avatar:   utils.S3Url(aiRoleInfo.Avatar, userInfo.AppID),
		},
	}
	// 语音屏蔽打开 1_03军锋需求修改文字转语音
	// aiSendMsg.MessageContent.CanVoice = false
	if err := m.SendMsgToAgora(aiSendMsg, appInfo); err != nil {
		return
	}
	// 保存亲密度
	_ = m.SaveIntimateRecord(ctx, userInfo.Id, aiRoleInfo, model.IntimateTypeAdd, model.SubTypeFreeMsg)
}

func (m *MsgService) SendAiSubMsg(ctx context.Context, userInfo *model.DigitalUser, aiRoleInfo *model.AiRole, appInfo model.App) {
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Error("SendAiSubMsg panic", zap.Any("err", err))
		}
	}()
	aiReplyMsg := global.BuyChatMsg
	if appInfo.Id == 4 || appInfo.Id == 8 {
		aiReplyMsg = global.ChatLimitMsg2
	}
	aiSendMsg := &model.MsgInfo{
		MessageID:    uuid.NewString(),
		Channel:      nil,
		SendTime:     time.Now().UnixMilli(),
		DeletedAt:    nil,
		MessageType:  model.MsgTypeRequireSub,
		SenderID:     aiRoleInfo.RoleID,
		SenderName:   aiRoleInfo.Nickname,
		ReceiverID:   fmt.Sprintf("%d", userInfo.Id),
		ReceiverName: userInfo.Nickname,
		MessageContent: model.MessageContent{
			Text:   aiReplyMsg,
			Avatar: utils.S3Url(aiRoleInfo.Avatar, userInfo.AppID),
		},
	}
	vCompare := utils.CompareVersions(userInfo.Version, "3.1.0")
	if vCompare >= 0 {
		aiSendMsg.MessageType = model.MsgTypeJump
		jumpList := []model.JumpInfo{
			{
				JumpType: model.JumpTypeVipSub,
				Keyword:  "Subscription",
			},
			{
				JumpType: model.JumpTypeCoinShop,
				Keyword:  "Buy Coins",
			},
		}
		if appInfo.Id != 4 && appInfo.Id != 8 {
			jumpList = append(jumpList, model.JumpInfo{
				JumpType: model.JumpTypeWatchAd,
				Keyword:  "Watch AD",
			})
		}
		jumpListBytes, jsonErr := json.Marshal(jumpList)
		if jsonErr != nil {
			global.LOG.Error("SendAiSubMsg json.Marshal error", zap.Any("err", jsonErr))
			return
		}
		jumpListStr := string(jumpListBytes)
		aiSendMsg.MessageContent.JumpStr = utils.Pointer(jumpListStr)
	}
	if err := m.SendMsgToAgora(aiSendMsg, appInfo); err != nil {
		return
	}
}

func (m *MsgService) SendJumpMsg(ctx context.Context, reqData *req.SendJumpMsgReq) (err error) {
	defer func() {
		if rErr := recover(); err != nil {
			global.LOG.Error("SendJumpMsg panic", zap.Any("err", rErr))
		}
	}()
	var (
		jumpListBytes []byte
		userInfo      *model.DigitalUser
		userSer       DigitalUserService
		aiRoleInfo    *model.AiRole
		aiSer         AiRoleService
	)
	// 查询用户信息
	if userInfo, err = userSer.GetById(ctx, reqData.UserId); err != nil {
		global.LOG.Error("SendJumpMsg userSer GetById error", zap.Error(err))
		return
	}
	appInfo := global.AppMap[userInfo.AppID]
	// 查询AI角色信息
	if aiRoleInfo, err = aiSer.GetByRoleId(reqData.AiRoleId); err != nil {
		global.LOG.Error("SendJumpMsg aiSer GetByRoleId error", zap.Error(err))
		return
	}
	vCompare := utils.CompareVersions(userInfo.Version, "3.1.0")
	if vCompare < 0 {
		global.LOG.Error("SendJumpMsg CompareVersions Less", zap.Any("userVersion", userInfo.Version))
		return
	}
	aiSendMsg := &model.MsgInfo{
		MessageID:    uuid.NewString(),
		Channel:      nil,
		SendTime:     time.Now().UnixMilli(),
		DeletedAt:    nil,
		MessageType:  model.MsgTypeJump,
		SenderID:     aiRoleInfo.RoleID,
		SenderName:   aiRoleInfo.Nickname,
		ReceiverID:   fmt.Sprintf("%d", userInfo.Id),
		ReceiverName: userInfo.Nickname,
		MessageContent: model.MessageContent{
			Text:   "Sweetie, our conversation has reached the daily limit. You can continue by:\nSubscription (unlock more private content)\nBuy Coins (unlock more private content)\nWatch AD (add 5 conversations),goto GoogleStore",
			Avatar: utils.S3Url(aiRoleInfo.Avatar, userInfo.AppID),
		},
	}
	jumpList := []model.JumpInfo{
		{
			JumpType: model.JumpTypeWebUrl,
			Keyword:  "GoogleStore",
			JumpUrl:  "https://play.google.com/store/apps/details?id=com.aipersona.camera",
		},
		{
			JumpType: model.JumpTypeVipSub,
			Keyword:  "Subscription",
		},
		{
			JumpType: model.JumpTypeCoinShop,
			Keyword:  "Buy Coins",
		},
	}
	if appInfo.Id != 4 {
		jumpList = append(jumpList, model.JumpInfo{
			JumpType: model.JumpTypeWatchAd,
			Keyword:  "Watch AD",
		})
	}
	if jumpListBytes, err = json.Marshal(jumpList); err != nil {
		global.LOG.Error("json.Marshal error", zap.Any("err", err))
		return
	}
	jumpListStr := string(jumpListBytes)
	aiSendMsg.MessageContent.JumpStr = utils.Pointer(jumpListStr)
	if err = m.SendMsgToAgora(aiSendMsg, appInfo); err != nil {
		global.LOG.Error("SendMsgToAgora error", zap.Any("err", err))
		return
	}
	return
}

func (m *MsgService) SendResetChatMsg(ctx context.Context, userInfo *model.DigitalUser, aiRoleInfo *model.AiRole, appInfo model.App) {
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Error("SendResetChatMsg panic", zap.Any("err", err))
		}
	}()
	aiReplyMsg := "Chat has been reset."
	aiSendMsg := &model.MsgInfo{
		MessageID:    uuid.NewString(),
		Channel:      nil,
		SendTime:     time.Now().UnixMilli(),
		DeletedAt:    nil,
		MessageType:  model.MsgTypeTxt,
		SenderID:     aiRoleInfo.RoleID,
		SenderName:   aiRoleInfo.Nickname,
		ReceiverID:   fmt.Sprintf("%d", userInfo.Id),
		ReceiverName: userInfo.Nickname,
		MessageContent: model.MessageContent{
			Text:   aiReplyMsg,
			Avatar: utils.S3Url(aiRoleInfo.Avatar, userInfo.AppID),
		},
	}
	if err := m.SendMsgToAgora(aiSendMsg, appInfo); err != nil {
		return
	}
}

func (m *MsgService) SendCloseFlowerMsg(ctx context.Context, userInfo *model.DigitalUser, aiRoleInfo *model.AiRole, appInfo model.App) {
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Error("SendCloseFlowerMsg panic", zap.Any("err", err))
		}
	}()
	aiReplyMsg := global.BuyChatMsg

	aiSendMsg := &model.MsgInfo{
		MessageID:    uuid.NewString(),
		Channel:      nil,
		SendTime:     time.Now().UnixMilli(),
		DeletedAt:    nil,
		MessageType:  model.MsgTypeRequireFlower,
		SenderID:     aiRoleInfo.RoleID,
		SenderName:   aiRoleInfo.Nickname,
		ReceiverID:   fmt.Sprintf("%d", userInfo.Id),
		ReceiverName: userInfo.Nickname,
		MessageContent: model.MessageContent{
			Text:   aiReplyMsg,
			Avatar: utils.S3Url(aiRoleInfo.Avatar, userInfo.AppID),
		},
	}
	if err := m.SendMsgToAgora(aiSendMsg, appInfo); err != nil {
		return
	}
}

func (m *MsgService) SendCloseChatMsg(ctx context.Context, userInfo *model.DigitalUser, aiRoleInfo *model.AiRole, appInfo model.App) {
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Error("SendCloseChatMsg panic", zap.Any("err", err))
		}
	}()
	aiReplyMsg := global.BuyChatMsg

	aiSendMsg := &model.MsgInfo{
		MessageID:    uuid.NewString(),
		Channel:      nil,
		SendTime:     time.Now().UnixMilli(),
		DeletedAt:    nil,
		MessageType:  model.MsgTypeRequireChat,
		SenderID:     aiRoleInfo.RoleID,
		SenderName:   aiRoleInfo.Nickname,
		ReceiverID:   fmt.Sprintf("%d", userInfo.Id),
		ReceiverName: userInfo.Nickname,
		MessageContent: model.MessageContent{
			Text:   aiReplyMsg,
			Avatar: utils.S3Url(aiRoleInfo.Avatar, userInfo.AppID),
		},
	}
	if err := m.SendMsgToAgora(aiSendMsg, appInfo); err != nil {
		return
	}
}

func (m *MsgService) SendGift(c *gin.Context, reqData *request.SendGiftReq) (res gin.H, err error) {
	var (
		userId          = utils.GetDigitalUserID(c)
		flowerAmountNow int
		aiRoleInfo      *model.AiRole
		userInfo        *model.DigitalUser
		userProfileInfo *model.DigitalUserProfile
		intimateInfo    *model.UsersIntimate
		userService     DigitalUserService
		msgCountSer     UserAiMsgCountService
		aiRoleService   AiRoleService
		profileService  DigitalUserProfileService
		userIntimateSer UsersIntimateService
	)
	// 查询用户Profile信息
	if userProfileInfo, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	// 查询礼物的具体信息
	giftItem, giftItemExist := global.AllGiftMap[reqData.GiftId]
	consumeCoin := giftItem.Consume
	if !giftItemExist {
		err = fmt.Errorf("gift(%d) is not exist", reqData.GiftId)
		return
	}
	rewardGiftCount, _ := m.GetRewardGiftCount(userId, giftItem.Id)

	if userProfileInfo.FlowerAmount < giftItem.Consume && rewardGiftCount < 1 {
		err = errs.FloShopErr
		return
	}
	// 查询用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	// 获取airole信息
	if aiRoleInfo, err = aiRoleService.GetByRoleId(reqData.AiRoleId); err != nil {
		return
	}

	// 查询亲密值
	// 获取亲密值记录
	if intimateInfo, err = userIntimateSer.GetByUidAndRoleid(userId, aiRoleInfo.Id); err != nil {
		return
	}

	useFreeGift := false

	err = global.DB.Transaction(func(tx *gorm.DB) (tErr error) {
		// 如果有免费赠送数量,那么就消耗0金币
		if rewardGiftCount > 0 && intimateInfo.Level >= 3 {
			useFreeGift = true
			consumeCoin = 0
		}
		go TdPointer(userInfo, userProfileInfo, td.ServerEvent, map[string]interface{}{
			"event_tag":         "coins_consume",
			"coins_consume_num": -consumeCoin,
			"ai_role_id":        aiRoleInfo.RoleID,
			"ai_role_type":      aiRoleInfo.RoleType,
			"gift_id":           giftItem.Id,
			"source":            "gift_consume",
		})
		flowerAmountNow = userProfileInfo.FlowerAmount - consumeCoin
		// 创建FlowerConsumeRecord记录
		var fRecord = &model.FlowerConsumeRecord{
			DigitalUserId: userId,
			AiId:          aiRoleInfo.Id,
			AiRoleId:      aiRoleInfo.RoleID,
			ConsumeType:   model.ConsumeTypeSendGift,
			RelationID:    fmt.Sprintf("%d", giftItem.Id),
			OriginAmount:  userProfileInfo.FlowerAmount,
			ConsumeAmount: consumeCoin,
		}
		if tErr = tx.Model(&model.FlowerConsumeRecord{}).Create(fRecord).Error; tErr != nil {
			return
		}
		updateMap := map[string]interface{}{
			"flower_amount": flowerAmountNow,
		}
		db := tx.Model(&model.DigitalUserProfile{}).Where("id = ? AND flower_amount = ?", userProfileInfo.Id, userProfileInfo.FlowerAmount).Updates(updateMap)
		if tErr = db.Error; tErr != nil {
			return
		}
		if db.RowsAffected == 0 {
			tErr = fmt.Errorf("update userProfile failed")
			return
		}
		return
	})

	if err != nil {
		return
	}
	// 保存亲密度
	if err = m.SaveIntimateGiftRecord(userInfo, aiRoleInfo, model.IntimateTypeAdd, model.SubTypeSendGift, giftItem.Intimate); err != nil {
		return
	}
	// 更新UserAiMsgCount
	if err = msgCountSer.FindUpdate(c, userId, aiRoleInfo, map[string]interface{}{
		"send_gift_count": gorm.Expr("send_gift_count + 1"),
	}); err != nil {
		return
	}
	res = gin.H{
		"flower_amount": flowerAmountNow,
	}
	if useFreeGift {
		_ = m.RewardGift(userInfo, giftItem.Id, -1)
	}
	return
}

func (m *MsgService) SendSeeuMsg(c *gin.Context, userInfo *model.DigitalUser, aiRoleInfo *model.AiRole) (err error) {
	var (
		// countMax   int
		// mediaCount int64
		appInfo, _ = global.AppMap[userInfo.AppID]
		userIdStr  = fmt.Sprintf("%d", userInfo.Id)
	)
	// countMax = appInfo.DetailConfig.Data().FreeSeeuCountMax
	// // 查询see u场景下发送的数量
	// rKey := fmt.Sprintf(global.MEDIA_SHOW_LIST, userInfo.Id, aiRoleInfo.Id)
	// if mediaCount, err = global.REDIS.SCard(context.Background(), rKey).Result(); err != nil {
	// 	return
	// }
	//
	// if int(mediaCount) >= countMax {
	// 	if userInfo.IsSub() {
	// 		err = errors.New("the total number of free photos have been used up")
	// 	} else {
	// 		err = errs.RequireSubErr
	// 	}
	// 	return
	// }
	msgType, msgContent, err1 := m.GetMsgContent(c, userInfo, aiRoleInfo, model.SceneSeeUMsg, "", 0)
	if err1 != nil {
		err = err1
		return
	}
	msgContent.Text = utils.RandList(global.RANDOM_SEEU_MSG_LIST)
	now := time.Now()
	aiSendMsgInfo := &model.MsgInfo{
		MessageID:      uuid.NewString(),
		Channel:        nil,
		SendTime:       now.UnixMilli(),
		DeletedAt:      nil,
		MessageType:    msgType,
		SenderID:       aiRoleInfo.RoleID,
		SenderName:     aiRoleInfo.Nickname,
		ReceiverID:     userIdStr,
		ReceiverName:   userInfo.Nickname,
		MessageContent: msgContent,
	}
	if err = m.SendMsgToAgora(aiSendMsgInfo, appInfo); err != nil {
		return
	}
	// 保存发送过的记录
	aiSendMediaSer := AiSendMediaService{}
	aiSendMediaRecord := model.AiSendMedia{
		DigitalUserId: userInfo.Id,
		AiRoleId:      aiRoleInfo.Id,
		MediaType:     model.MediaTypeImg,
		MediaURL:      msgContent.ImgURL,
		ThumbnailURL:  msgContent.ImgURL,
		AsmrName:      "",
		SendTime:      now,
		UnlockState:   model.UnlockStateNo,
		UnlockTime:    nil,
	}
	if msgType == model.MsgTypeTxtImg {
		// if userInfo.IsSub() {
		// 	aiSendMediaRecord.UnlockState = model.UnlockStateYes
		// 	aiSendMediaRecord.UnlockTime = &now
		// }
	} else if msgType == model.MsgTypeTxtVideo {
		aiSendMediaRecord.MediaType = model.MediaTypeVideo
		aiSendMediaRecord.MediaURL = msgContent.FileURL
		aiSendMediaSer.SaveByMsg(&aiSendMediaRecord)
	}
	aiSendMediaSer.SaveByMsg(&aiSendMediaRecord)
	return
}

func (m *MsgService) GetRequireImgRdsKey(s3DirPath string) (rdsKey string) {
	rdsKey = fmt.Sprintf(global.REQUIRE_IMG_RDS_KEY, s3DirPath)
	return
}

func (m *MsgService) GetRequireImgArrFromRds(s3DirPath string) (fileArr []string) {
	rdsKey := m.GetRequireImgRdsKey(s3DirPath)
	fileArr, _ = global.REDIS.LRange(context.Background(), rdsKey, 0, -1).Result()
	return
}

func (m *MsgService) DelRequireImgArrFromRds(s3DirPath string) (err error) {
	rdsKey := m.GetRequireImgRdsKey(s3DirPath)
	err = global.REDIS.Del(context.Background(), rdsKey).Err()
	return
}

func (m *MsgService) SetRequireImgArrFromRds(s3DirPath string, fileArr []string) (err error) {
	rdsKey := m.GetRequireImgRdsKey(s3DirPath)
	ctx := context.Background()
	// 先删除
	if err = global.REDIS.Del(ctx, rdsKey).Err(); err != nil {
		return
	}
	// 再添加
	if err = global.REDIS.LPush(ctx, rdsKey, fileArr).Err(); err != nil {
		return
	}
	// 设置过期时间10分钟
	if err = global.REDIS.Expire(ctx, rdsKey, time.Minute*10).Err(); err != nil {
		return
	}

	return
}

func (m *MsgService) UserBodyImgArrRdsKey(userId, aiId uint) (rdsKey string) {
	rdsKey = fmt.Sprintf(global.USER_BODY_IMG_ARR_RDS_KEY, aiId, userId)
	return
}

func (m *MsgService) GetUserBodyImgArr(userId, aiId uint) (fileArr []string) {
	ctx := context.Background()
	rdsKey := m.UserBodyImgArrRdsKey(userId, aiId)
	fileArr, _ = global.REDIS.LRange(ctx, rdsKey, 0, -1).Result()
	return
}

func (m *MsgService) SetUserBodyImgArr(userId, aiId uint, fileKey string) (err error) {
	rdsKey := m.UserBodyImgArrRdsKey(userId, aiId)
	ctx := context.Background()
	if err = global.REDIS.LPush(ctx, rdsKey, fileKey).Err(); err != nil {
		return
	}
	return
}

func (m *MsgService) SendBodyImgMsgNew(c *gin.Context, userInfo *model.DigitalUser, aiRoleInfo *model.AiRole, reqData request.GetAiFuncContentReq) (err error) {
	var (
		imgSer     ImgToImgTrainService
		s3V2Client = GetS3ClientV2(c)
	)
	appInfo := global.AppMap[userInfo.AppID]
	bodyImgItem, bodyImgItemExist := global.AllAiBodyImg[reqData.BodyImgId]
	if !bodyImgItemExist {
		err = fmt.Errorf("bodyImgId(%d) not found", reqData.BodyImgId)
		return
	}
	dirPath := bodyImgItem.S3Dir
	gLog := global.LOG.With(zap.String("func", "SendBodyImgMsg"), zap.Any("reqData", reqData), zap.Any("bodyImgItem", bodyImgItem), zap.Uint("userInfo.Id", userInfo.Id), zap.Any("aiRoleInfo", aiRoleInfo))
	gLog = gLog.With(zap.String("dirPath", dirPath))
	if dirPath == "" {
		gLog.Error("索要照片,位置图文件夹为空")
		return
	}
	// 获取这个路径的图片
	// 从redis获取文件列表
	dirFileArr := m.GetRequireImgArrFromRds(dirPath)
	if len(dirFileArr) == 0 {
		// 使用s3获取文件列表
		if dirFileArr, err = s3V2Client.ListFilesInS3Path(dirPath); err != nil {
			gLog.Error("ListFilesInS3Path error", zap.Error(err))
			return
		}
		var noUndressArr []string
		for _, iPath := range dirFileArr {
			// 判断iPath里面是否含有undress,有就跳过
			if strings.Contains(iPath, "undress") {
				continue
			}
			noUndressArr = append(noUndressArr, iPath)
		}
		dirFileArr = noUndressArr
		_ = m.SetRequireImgArrFromRds(dirPath, dirFileArr)
	}
	// 获取用户已使用的key
	userBodyImgArr := m.GetUserBodyImgArr(userInfo.Id, aiRoleInfo.Id)
	var userBodyImgKeyMap = map[string]bool{}
	for _, userBodyImgKey := range userBodyImgArr {
		userBodyImgKeyMap[userBodyImgKey] = true
	}
	var bodyImgKey = ""
	// for _, s := range dirFileArr {
	// 	// 判断是否已经使用过
	// 	if !userBodyImgKeyMap[s] {
	// 		bodyImgKey = s
	// 		break
	// 	}
	// }

	// 12-27 图片随机出

	var imgKeyList []string
	for _, s := range dirFileArr {
		// 判断是否已经使用过 没用过就放入到切片中
		if !userBodyImgKeyMap[s] {
			imgKeyList = append(imgKeyList, s)
		}
	}
	bodyImgKey = utils.RandList(imgKeyList)

	if bodyImgKey == "" {
		// 钉钉预警
		ddNotifyMsg := fmt.Sprintf(`## 位置照片不足
- [应用名称: %s](https://aiadmin.aigirlchat.net/#/layout/backend_app/app)
- [应用ID: %d](https://aiadmin.aigirlchat.net/#/layout/backend_app/app)
- [用户ID: %d](https://aiadmin.aigirlchat.net/#/layout/backendUser/digitalUser?id=%d)
- [角色ID: %d](https://aiadmin.aigirlchat.net/#/layout/backendAi/aiRole?id=%d)
- [角色RoleID: %s](https://aiadmin.aigirlchat.net/#/layout/backendAi/aiRole?role_id=%s)
- 位置名称: %s
- [蒙版库前缀: %s](https://us-west-2.console.aws.amazon.com/s3/buckets/cashbox-ai?region=us-west-2&bucketType=general&prefix=%s&showversions=false)
`,
			appInfo.Name,
			appInfo.Id,
			userInfo.Id, userInfo.Id,
			aiRoleInfo.Id, aiRoleInfo.Id,
			aiRoleInfo.RoleID, aiRoleInfo.RoleID,
			bodyImgItem.BodyName,
			bodyImgItem.S3Dir, bodyImgItem.S3Dir,
		)
		SendDDNotice(ddNotifyMsg)
		rdsKey := m.UserBodyImgArrRdsKey(userInfo.Id, aiRoleInfo.Id)
		// 如果都使用过,那么就删除这个key
		global.REDIS.Del(c, rdsKey)
		bodyImgKey = utils.RandList(dirFileArr)
	}
	requireImgReq := &request.RequireImgReq{
		UserId:     userInfo.Id,
		AiRoleId:   aiRoleInfo.RoleID,
		BodyImgId:  reqData.BodyImgId,
		BodyImgKey: bodyImgKey,
	}
	imgSer.SetRequireImgTask(requireImgReq)
	return
}

func (m *MsgService) SendAsmrMsg(userId, aiId uint) (err error) {
	var (
		userInfo   *model.DigitalUser
		userSer    DigitalUserService
		aiRoleInfo *model.AiRole
		aiRoleSer  AiRoleService
		ctx        = context.Background()
	)
	// 获取用户信息
	if userInfo, err = userSer.GetById(ctx, userId); err != nil {
		return
	}
	if aiRoleInfo, err = aiRoleSer.GetById(ctx, aiId); err != nil {
		return
	}
	if len(aiRoleInfo.AsmrList) == 0 {
		err = errors.New("asmr list is empty")
		return
	}
	verCompare320 := utils.CompareVersions(userInfo.Version, "3.2.0")
	isSub := userInfo.IsSub()
	isChatVip := userInfo.IsChatVip()
	if IsVipSVipUser(userInfo) {
		if !isSub && verCompare320 >= 0 {
			err = errs.RequireSubErr
			return
		}
	} else {
		if !isChatVip {
			err = errs.ChatVipErr
			return
		}
	}
	if err = m.SendAsmrTypeMsg(userInfo, aiRoleInfo); err != nil {
		return
	}
	return
}

func (m *MsgService) SendAsmrTypeMsg(userInfo *model.DigitalUser, aiRoleInfo *model.AiRole) (err error) {
	var (
		aiRoleFuncInfo        *model.UserAiRoleFunc
		userAiRoleFuncService UserAiRoleFuncService
		appInfo, _            = global.AppMap[userInfo.AppID]
		userIdStr             = fmt.Sprintf("%d", userInfo.Id)
	)
	if userInfo.IsAudit() {
		return
	}
	if len(aiRoleInfo.AsmrList) == 0 {
		err = errors.New("asmr list is empty")
		return
	}
	if aiRoleFuncInfo, err = userAiRoleFuncService.GetOne(userInfo.Id, aiRoleInfo.Id); err != nil {
		return
	}
	var sendInfo datatypes.JSONMap
	for _, jsonMap := range aiRoleInfo.AsmrList {
		asmrFile := jsonMap["file_url"].(string)
		// 判断asmrFile是否存在aiRoleFuncInfo.SendAsmrList
		if !utils.InArray(asmrFile, aiRoleFuncInfo.SendAsmrList) {
			sendInfo = jsonMap
			break
		}
	}
	if sendInfo == nil {
		err = errors.New("the total number of ASMR audios have been used up")
		return
	}
	fileName := sendInfo["file_name"].(string)
	fileName = fmt.Sprintf("ASMR_%s", fileName)
	fileUrl := sendInfo["file_url"].(string)
	fileFullUrl := utils.S3Url(fileUrl, userInfo.AppID)
	if aiRoleFuncInfo.SendAsmrList == nil {
		aiRoleFuncInfo.SendAsmrList = []string{}
	}
	aiRoleFuncInfo.SendAsmrList = append(aiRoleFuncInfo.SendAsmrList, fileUrl)
	if err = userAiRoleFuncService.Update(aiRoleFuncInfo); err != nil {
		return
	}
	// 查询是否能免费发送ASMR
	rewardAsmrCount, _ := m.GetRewardAsmrCount(userInfo.Id, aiRoleInfo.Id)
	now := time.Now()
	aiSendMsgInfo := &model.MsgInfo{
		MessageID:    uuid.NewString(),
		Channel:      nil,
		SendTime:     now.UnixMilli(),
		DeletedAt:    nil,
		MessageType:  model.MsgTypeASMR,
		SenderID:     aiRoleInfo.RoleID,
		SenderName:   aiRoleInfo.Nickname,
		ReceiverID:   userIdStr,
		ReceiverName: userInfo.Nickname,
		MessageContent: model.MessageContent{
			Text:       "Darling, just sit back, close your eyes, and let my voice guide you into dreamland",
			OriginText: "Darling, just sit back, close your eyes, and let my voice guide you into dreamland",
			Free:       rewardAsmrCount > 0,
			Avatar:     utils.S3Url(aiRoleInfo.Avatar, userInfo.AppID),
			FileName:   fileName,
			FileURL:    fileFullUrl,
		},
	}
	if err = m.SendMsgToAgora(aiSendMsgInfo, appInfo); err != nil {
		return
	}
	// 保存发送过的记录
	aiSendMediaSer := AiSendMediaService{}
	aiSendMediaRecord := model.AiSendMedia{
		DigitalUserId: userInfo.Id,
		AiRoleId:      aiRoleInfo.Id,
		MediaType:     model.MediaTypeAsmr,
		MediaURL:      fileFullUrl,
		ThumbnailURL:  "",
		AsmrName:      fileName,
		SendTime:      now,
		UnlockState:   model.UnlockStateNo,
		UnlockTime:    nil,
	}
	aiSendMediaSer.SaveByMsg(&aiSendMediaRecord)

	// 判断是否能免费发送ASMR,有的话就减去一次
	if aiSendMsgInfo.MessageContent.Free && rewardAsmrCount > 0 {
		_ = m.RewardAsmr(userInfo.Id, aiRoleInfo.Id, -1)
	}
	return
}

func (m *MsgService) NotifyChargeTrain() {
	var (
		ctx      = context.Background()
		redisKey = global.NOTIFY_CHARGE_QUEUE_KEY
	)
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Error("NotifyChargeTrain panic", zap.Any("err", err))
		}
	}()
	for {
		time.Sleep(time.Second)
		now := time.Now()
		nowUnix := now.Unix()
		filter := &redis.ZRangeBy{
			Min:    "0",
			Max:    fmt.Sprintf("%d", nowUnix),
			Offset: 0,
			Count:  0,
		}
		// 从 Redis 有序集合中获取消息
		redisMsgList, err := global.REDIS.ZRangeByScore(ctx, redisKey, filter).Result()
		if err != nil {
			global.LOG.Error("ZRevRange error", zap.Any("err", err))
			continue
		}
		if len(redisMsgList) == 0 {
			continue
		}
		taskStr := redisMsgList[0]
		// 消息处理
		if err = m.execNotifyChargeTrain(ctx, taskStr); err != nil {
			global.LOG.Error("execNotifyChargeTrain error", zap.Any("err", err))
		}
		// 删除已处理的消息
		if _, err = global.REDIS.ZRem(ctx, redisKey, taskStr).Result(); err != nil {
			global.LOG.Error("ZRem error", zap.Any("err", err))
		}
	}

}

func (m *MsgService) IdleMsgTrain() {
	var (
		ctx      = context.Background()
		redisKey = global.IDLE_MSG_QUEUE_KEY
	)
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Error("NotifyChargeTrain panic", zap.Any("err", err))
		}
	}()
	for {
		time.Sleep(time.Second)
		now := time.Now()
		// 从配置中获取空闲检测秒数
		idleSecond := global.APPOtherCfg.IdleSecond
		secAgo := now.Add(-time.Second * time.Duration(idleSecond)).Unix()
		filter := &redis.ZRangeBy{
			Min:    "-inf",
			Max:    fmt.Sprintf("%d", secAgo),
			Offset: 0,
			Count:  0,
		}
		// 从 Redis 有序集合中获取消息
		redisMsgList, err := global.REDIS.ZRevRangeByScore(ctx, redisKey, filter).Result()
		if err != nil {
			global.LOG.Error("ZRevRange error", zap.Any("err", err))
			continue
		}
		if len(redisMsgList) == 0 {
			continue
		}
		taskStr := redisMsgList[0]
		// 消息处理
		if err = m.execIdleMsgTrainV1(ctx, taskStr); err != nil {
			global.LOG.Error("IdleMsgTrain error", zap.Any("err", err))
		}
		// 删除已处理的消息
		if _, err = global.REDIS.ZRem(ctx, redisKey, taskStr).Result(); err != nil {
			global.LOG.Error("ZRem error", zap.Any("err", err))
		}
	}

}

func (m *MsgService) LivePushMsgTrain() {
	var (
		ctx      = context.Background()
		redisKey = global.LIVE_PUSH_MSG_QUEUE_KEY
	)
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Error("LivePushMsgTrain panic", zap.Any("err", err))
		}
	}()
	for {
		time.Sleep(time.Second)
		// 从 Redis 有序集合中获取消息
		redisMsgList, err := global.REDIS.ZRange(ctx, redisKey, 0, -1).Result()
		if err != nil {
			global.LOG.Error("ZRevRange error", zap.Any("err", err))
			continue
		}
		if len(redisMsgList) == 0 {
			continue
		}
		taskStr := redisMsgList[0]
		// 删除消息
		if _, err = global.REDIS.ZRem(ctx, redisKey, taskStr).Result(); err != nil {
			global.LOG.Error("ZRem error", zap.Any("err", err))
			continue
		}
		var msgReq request.LivePushMsgReq
		if err = json.Unmarshal([]byte(taskStr), &msgReq); err != nil {
			global.LOG.Error("json.Unmarshal error", zap.Any("err", err))
			continue
		}
		// 消息处理
		if err = m.execLivePushMsgTrain(ctx, &msgReq); err != nil {
			global.LOG.Error("LivePushMsgTrain error", zap.Any("err", err))
		}
	}

}

func (m *MsgService) GetMsgUrlType(userInfo *model.DigitalUser, profileInfo *model.DigitalUserProfile) int {
	var urlType = 0
	if userInfo.UserGroup == 1 {
		urlType = 1
	} else {
		if profileInfo.Nsfw {
			urlType = 2
		} else {
			urlType = 3
		}
	}
	return urlType
}
