package backend

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/utils"
	"context"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"time"
)

func (m *LiveInfoService) UpdateLiveInfo(c *gin.Context, reqData *request.UpdateLiveInfoReq) (err error) {
	var (
		liveInfo *model.LiveInfo
		userId   = utils.GetDigitalUserID(c)
		appId    = utils.GetAppID(c)
	)
	var liveReceiveGiftService LiveReceiveGiftService
	if liveInfo, err = m.GetById(c, reqData.Id); err != nil {
		return
	}
	if liveInfo.DigitalUserId != userId {
		err = fmt.Errorf("live info not belong to user")
		return
	}
	updateMap := map[string]interface{}{
		"live_second":    reqData.LiveSecond,
		"like_count":     reqData.LikeCount,
		"actual_viewers": reqData.ActualViewers,
		"comment_count":  reqData.CommentCount,
	}
	if err = m.UpdateById(c, reqData.Id, updateMap); err != nil {
		return
	}
	// 更新礼物数量
	for _, item := range reqData.GiftItems {
		_, liveGiftItemExist := global.LiveGiftMap[item.Id]
		if !liveGiftItemExist {
			err = fmt.Errorf("gift not exist, giftId: %d", item.Id)
			return
		}
		editObj := model.LiveReceiveGift{
			AppID:      appId,
			LiveInfoID: liveInfo.Id,
			LiveUserID: userId,
			GiftID:     item.Id,
			GiftCount:  item.Num,
		}
		if err = liveReceiveGiftService.CreateOrFindUpdate(c, &editObj); err != nil {
			return
		}
	}

	return
}

func (m *LiveInfoService) CreateLiveInfo(c *gin.Context, reqData *request.CreateLiveInfoReq) (res *model.LiveInfo, err error) {
	var (
		userInfo    *model.DigitalUser
		userSer     DigitalUserService
		profileInfo *model.DigitalUserProfile
		profileSer  DigitalUserProfileService
		userId      = utils.GetDigitalUserID(c)
		isFree      = true
		totalPrice  = 0
	)
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		return
	}
	// isAudit := userInfo.IsAudit()
	// isSVip := userInfo.IsSVip()
	isSub := userInfo.IsSub()
	isChatVip := userInfo.IsChatVip()
	isContentVip := userInfo.IsContentVip()
	appInfo := global.AppMap[userInfo.AppID]
	if profileInfo, err = profileSer.GetByUserId(userId); err != nil {
		return
	}
	// 直播人数
	viewerItem, viewerItemExist := global.LivePeopleMap[reqData.ViewerId]
	if !viewerItemExist {
		err = fmt.Errorf("viewer not exist, viewerId: %d", reqData.ViewerId)
		return
	}
	if viewerItem.Price > 0 {
		isFree = false
	}
	// 总价格
	totalPrice += viewerItem.Price
	// 直播礼物
	for _, liveGiftId := range reqData.GiftIds {
		liveGiftItem, liveGiftItemExist := global.LiveGiftMap[liveGiftId]
		if !liveGiftItemExist {
			err = fmt.Errorf("gift not exist, giftId: %d", liveGiftId)
			return
		}
		if liveGiftItem.Price > 0 {
			isFree = false
		}
		// 总价格
		totalPrice += liveGiftItem.Price
	}
	if (isSub || isContentVip || isChatVip) && totalPrice > 0 {
		// 订阅用户直播价格减半
		totalPrice = totalPrice / 2
	}
	if isFree {
		userLiveCount := m.UserDayLiveCount(c, userId)
		dayLimitCount := appInfo.DetailConfig.Data().FreeLiveNum
		if isSub || isContentVip || isChatVip {
			dayLimitCount = appInfo.DetailConfig.Data().SubLiveNum
		}
		remainCount := dayLimitCount - userLiveCount
		if remainCount <= 0 {
			if IsVipSVipUser(userInfo) {
				if !isSub {
					err = errs.RequireSubErr
					return
				}
			} else {
				err = errs.ChatVipErr
				return
			}
			err = fmt.Errorf("free live count is used up, dayLimitCount: %d, userLiveCount: %d", dayLimitCount, userLiveCount)
			return
		}
	} else {
		// 不免费,检查鲜花数量是否足够
		if profileInfo.FlowerAmount < totalPrice {
			err = errs.FloShopErr
			return
		}
	}
	liveInfoRec := model.LiveInfo{
		AppID:          userInfo.AppID,
		DigitalUserId:  userInfo.Id,
		RequireViewers: viewerItem.Count,
		ArriveMinute:   reqData.ArrivalMin,
		GiftIds:        reqData.GiftIds,
		TotalPrice:     totalPrice,
		LiveSecond:     0,
		LikeCount:      0,
		ActualViewers:  0,
		CommentCount:   0,
	}
	// 创建直播信息
	err = global.DB.Transaction(func(tx *gorm.DB) (tErr error) {
		if tErr = tx.Create(&liveInfoRec).Error; tErr != nil {
			return
		}
		// 扣除鲜花
		if !isFree {
			// 创建FlowerConsumeRecord记录
			var fRecord = &model.FlowerConsumeRecord{
				DigitalUserId: userId,
				AiId:          0,
				AiRoleId:      "",
				ConsumeType:   model.ConsumeTypeLive,
				RelationID:    fmt.Sprintf("%d", userId),
				OriginAmount:  profileInfo.FlowerAmount,
				ConsumeAmount: totalPrice,
			}
			if tErr = tx.Model(&model.FlowerConsumeRecord{}).Create(fRecord).Error; tErr != nil {
				return
			}
			updateMap := map[string]interface{}{
				"flower_amount": gorm.Expr("flower_amount - ?", totalPrice),
			}
			if tErr = tx.Model(&model.DigitalUserProfile{}).Where("id = ?", profileInfo.Id).Updates(updateMap).Error; tErr != nil {
				return
			}
		}
		// 增加当日直播次数
		if tErr = m.UserDayLiveIncr(c, userId); tErr != nil {
			return
		}
		return
	})
	if err != nil {
		return
	}
	return &liveInfoRec, nil
}

func (m *LiveInfoService) LiveConfig(c *gin.Context) (res response.LiveConfigRes, err error) {
	var (
		userInfo   *model.DigitalUser
		userSer    DigitalUserService
		viewerList []response.LiveViewerItem
		giftList   []response.LiveGiftItem
		appId      = utils.GetAppID(c)
		userId     = utils.GetDigitalUserID(c)
	)
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		return
	}
	appInfo := global.AppMap[appId]
	userLiveCount := m.UserDayLiveCount(c, userId)
	dayLimitCount := appInfo.DetailConfig.Data().FreeLiveNum
	if userInfo.IsSub() {
		dayLimitCount = appInfo.DetailConfig.Data().SubLiveNum
	}
	for _, item := range global.LivePeopleList {
		itemObj := response.LiveViewerItem{
			Id:    item.Id,
			Count: item.Count,
			Price: item.Price,
			Free:  item.Price == 0,
		}
		itemObj.CountStr = utils.FormatNumberV2(item.Count)
		viewerList = append(viewerList, itemObj)
	}
	for _, item := range global.LiveGiftList {
		itemObj := response.LiveGiftItem{
			Id:      item.Id,
			Name:    item.Name,
			Price:   item.Price,
			Free:    item.Price == 0,
			IconUrl: utils.S3Url(item.IconUrl, appId),
			SvgaUrl: utils.S3Url(item.SvgaUrl, appId),
		}
		giftList = append(giftList, itemObj)
	}
	remainCount := dayLimitCount - userLiveCount
	if remainCount < 0 {
		remainCount = 0
	}
	res = response.LiveConfigRes{
		ViewerList:    viewerList,
		GiftList:      giftList,
		MinMinute:     1,
		MaxMinute:     60,
		DayLimitCount: dayLimitCount,
		DayUsedCount:  userLiveCount,
		RemainCount:   remainCount,
	}
	return
}

func (m *LiveInfoService) LiveAiRoleRank(c *gin.Context) (res response.LiveAiRoleRankRes, err error) {
	var (
		userInfo   *model.DigitalUser
		userSer    DigitalUserService
		aiRoleList []*response.AiRoleListV2Res
		userId     = utils.GetDigitalUserID(c)
		appId      = utils.GetAppID(c)
	)
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		return
	}
	isAudit := userInfo.IsAudit()
	db := global.DB.Model(&model.AiRole{})
	db = db.Where("role_type = ?", model.AiRoleTypePublic)
	if isAudit {
		db = db.Where("examine = ?", int(model.ExamineTypeAudit))
		db = db.Where("app_id = ?", userInfo.AppID)
	} else {
		db = db.Where("examine = ?", int(model.ExamineTypeNormal))
	}
	db = db.Limit(100)
	db = db.Order("sort DESC")
	err = db.Find(&aiRoleList).Error
	if err != nil {
		return
	}
	var newRes []*response.AiRoleListV2Res
	if len(aiRoleList) == 0 {
		return
	}
	if len(aiRoleList) > 10 {
		aiRoleList = utils.RandSelect(aiRoleList, 10)
	}
	for _, v2Res := range aiRoleList {
		if v2Res.Avatar != "" {
			v2Res.Avatar = utils.S3Url(v2Res.Avatar, appId)
		}
		if v2Res.GifUrl != "" {
			v2Res.GifUrl = utils.S3Url(v2Res.GifUrl, appId)
		}
		if v2Res.WebThumbnail != "" {
			v2Res.WebThumbnail = utils.S3Url(v2Res.WebThumbnail, appId)
		}
		if v2Res.FirstImg != "" {
			v2Res.FirstImg = utils.S3Url(v2Res.FirstImg, appId)
		}
		if v2Res.ChatBG != "" {
			v2Res.ChatBG = utils.S3Url(v2Res.ChatBG, appId)
		}
		if v2Res.FirstVoice != "" {
			v2Res.FirstVoice = utils.S3Url(v2Res.FirstVoice, appId)
		}
		v2Res.CountStr = utils.FormatNumber(v2Res.ChatCount)
		if isAudit {
			v2Res.RolePlay = model.SwitchStatusDisabled
		}
		newRes = append(newRes, v2Res)
	}
	res.List = newRes
	return
}

func (m *LiveInfoService) LiveAiRoleRankV2(c *gin.Context, reqData *request.LiveAiRoleRankV2Req) (res response.LiveAiRoleRankRes, err error) {
	var (
		liveInfo   *model.LiveInfo
		fansList   []*model.LiveReceiveFans
		fansSer    LiveReceiveFansService
		userInfo   *model.DigitalUser
		userSer    DigitalUserService
		aiRoleList []*response.AiRoleListV2Res
		userId     = utils.GetDigitalUserID(c)
		appId      = utils.GetAppID(c)
	)
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		return
	}
	// 查询直播记录
	if liveInfo, err = m.GetById(c, reqData.LiveInfoId); err != nil {
		return
	}
	if liveInfo.DigitalUserId != userId {
		err = fmt.Errorf("live info not belong to user")
		return
	}
	isAudit := userInfo.IsAudit()
	db := global.DB.Model(&model.AiRole{})
	db = db.Where("role_type = ?", model.AiRoleTypeFans)
	if isAudit {
		db = db.Where("examine = ?", int(model.ExamineTypeAudit))
		db = db.Where("app_id = ?", userInfo.AppID)
	} else {
		db = db.Where("examine = ?", int(model.ExamineTypeNormal))
	}
	db = db.Limit(100)
	db = db.Order("sort DESC")
	err = db.Find(&aiRoleList).Error
	if err != nil {
		return
	}
	var newRes []*response.AiRoleListV2Res
	if len(aiRoleList) == 0 {
		return
	}
	if len(aiRoleList) > 10 {
		aiRoleList = utils.RandSelect(aiRoleList, 10)
	}
	for vRank, v2Res := range aiRoleList {
		if v2Res.Avatar != "" {
			v2Res.Avatar = utils.S3Url(v2Res.Avatar, appId)
		}
		if v2Res.GifUrl != "" {
			v2Res.GifUrl = utils.S3Url(v2Res.GifUrl, appId)
		}
		if v2Res.WebThumbnail != "" {
			v2Res.WebThumbnail = utils.S3Url(v2Res.WebThumbnail, appId)
		}
		if v2Res.FirstImg != "" {
			v2Res.FirstImg = utils.S3Url(v2Res.FirstImg, appId)
		}
		if v2Res.ChatBG != "" {
			v2Res.ChatBG = utils.S3Url(v2Res.ChatBG, appId)
		}
		if v2Res.FirstVoice != "" {
			v2Res.FirstVoice = utils.S3Url(v2Res.FirstVoice, appId)
		}
		v2Res.CountStr = utils.FormatNumber(v2Res.ChatCount)
		if isAudit {
			v2Res.RolePlay = model.SwitchStatusDisabled
		}
		fansList = append(fansList, &model.LiveReceiveFans{
			AppID:      appId,
			LiveInfoID: reqData.LiveInfoId,
			LiveUserID: userId,
			AiId:       v2Res.Id,
			Rank:       uint(vRank + 1),
		})
		newRes = append(newRes, v2Res)
	}
	if len(fansList) > 0 {
		if err = fansSer.SaveBatch(c, fansList); err != nil {
			return
		}
	}
	res.List = newRes
	return
}

func (m *LiveInfoService) UpdateLiveRank(c *gin.Context, reqData *request.UpdateLiveRankReq) (err error) {
	var (
		userInfo *model.DigitalUser
		userSer  DigitalUserService
		userId   = utils.GetDigitalUserID(c)
	)
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		return
	}
	global.LOG.Info("UpdateLiveRank", zap.Any("userInfo", userInfo))
	for _, roleId := range reqData.AiRoleIds {
		redisMsgReq := request.LivePushMsgReq{
			LiveInfoId: reqData.Id,
			UserId:     userInfo.Id,
			AiRoleId:   roleId,
		}
		sendMsgReqBytes, jsonErr := json.Marshal(redisMsgReq)
		if jsonErr != nil {
			global.LOG.Error("UpdateLiveRank error", zap.Any("jsonErr", jsonErr))
			continue
		}
		memberStr := string(sendMsgReqBytes)
		score := time.Now().UnixMilli()
		if err = global.REDIS.ZAdd(c, global.LIVE_PUSH_MSG_QUEUE_KEY, redis.Z{Score: float64(score), Member: memberStr}).Err(); err != nil {
			global.LOG.Error("UpdateLiveRank error", zap.Any("err", err))
			continue
		}
	}
	return
}

func (m *LiveInfoService) UserDayLiveCountRdsKey(userId uint) (rdsKey string) {
	dayStr := time.Now().Format(time.DateOnly)
	rdsKey = fmt.Sprintf(global.UserDayLiveCountRdsKey, dayStr, userId)
	return
}

func (m *LiveInfoService) UserDayLiveCount(ctx context.Context, userId uint) (dayCount int) {
	rdsKey := m.UserDayLiveCountRdsKey(userId)
	dayCount, _ = global.REDIS.Get(ctx, rdsKey).Int()
	return
}

func (m *LiveInfoService) UserDayLiveIncr(ctx context.Context, userId uint) (err error) {
	rdsKey := m.UserDayLiveCountRdsKey(userId)
	err = global.REDIS.Incr(ctx, rdsKey).Err()
	if err != nil {
		return
	}
	err = global.REDIS.Incr(ctx, fmt.Sprintf("ai:UserDayLive:userid:%d", userId)).Err()
	return
}

func (m *LiveInfoService) UserDayLiveReduce(ctx context.Context, userId uint) (err error) {
	rdsKey := m.UserDayLiveCountRdsKey(userId)
	err = global.REDIS.IncrBy(ctx, rdsKey, -1).Err()
	return
}
