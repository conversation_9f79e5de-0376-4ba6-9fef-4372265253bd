package backend

import (
	"aimsg-server/global"
	"aimsg-server/utils"
	"cloud.google.com/go/translate"
	"context"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
	"golang.org/x/text/language"
	"strings"
	"time"
)

type TranslateService struct{}

type WorkflowResponse struct {
	TaskID        string `json:"task_id"`
	WorkflowRunID string `json:"workflow_run_id"`
	Data          struct {
		ID         string `json:"id"`
		WorkflowID string `json:"workflow_id"`
		Status     string `json:"status"`
		Outputs    struct {
			Text string `json:"text"`
		} `json:"outputs"`
		Error       interface{} `json:"error"`
		ElapsedTime float64     `json:"elapsed_time"`
		TotalTokens int         `json:"total_tokens"`
		TotalSteps  int         `json:"total_steps"`
		CreatedAt   int64       `json:"created_at"`
		FinishedAt  int64       `json:"finished_at"`
	} `json:"data"`
}

func (m *TranslateService) MsgTranslate(ctx context.Context, appInfo model.App, userInfo *model.DigitalUser, sendMsgText string) (transMsg string, err error) {
	transMsg = sendMsgText
	if appInfo.DetailConfig.Data().MsgTranslate && !userInfo.IsAudit() {
		// 翻译
		if userInfo.Language != "" {
			targetLanguage := m.ToIso(userInfo.Language)
			if targetLanguage != "en" {
				if true {
					transStart := time.Now()
					if transMsg, err = m.TranslateText2(ctx, targetLanguage, sendMsgText); err != nil {
						global.LOG.Error("TranslateText error", zap.Any("err", err))
					}
					transLatency := time.Since(transStart)
					if transLatency > time.Second*5 {
						data, _ := json.Marshal(map[string]interface{}{
							"msg":      sendMsgText,
							"language": userInfo.Language,
						})
						Alarm(ctx, "大模型翻译时间大于5s", fmt.Sprintf("%0.1f", transLatency.Seconds()), string(data), "api_dmx")
					}
				} else {
					if transMsg, err = m.TranslateText(ctx, targetLanguage, sendMsgText); err != nil {
						global.LOG.Error("TranslateText error", zap.Any("err", err))
					}
				}
				global.LOG.Info("translateText", zap.String("userLanguage", userInfo.Language), zap.String("targetLanguage", targetLanguage))
			}
		}
	}
	return
}

func (m *TranslateService) TranslateText2(ctx context.Context, targetLanguage, text string) (translateText string, err error) {
	client := resty.New()
	var result WorkflowResponse

	_, err = client.R().
		SetHeader("Authorization", "Bearer app-acVpm5kNk2gbggGsLosYJlWi").
		SetHeader("Content-Type", "application/json").
		SetBody(map[string]interface{}{
			"inputs": map[string]interface{}{
				"query":    text,
				"language": targetLanguage,
			},
			"response_mode": "blocking",
			"user":          "chen_1",
		}).
		SetResult(&result). // 反序列化到结构体
		Post("https://ai.socialize-dify.online/v1/workflows/run")
	if err != nil {
		global.LOG.Error("TranslateText error", zap.Any("targetLanguage", targetLanguage), zap.Any("text", text))
		return
	}
	if result.Data.Status != "succeeded" {
		global.LOG.Error("TranslateText data error", zap.Any("targetLanguage", targetLanguage), zap.Any("text", text))
		translateText = text
	} else {
		translateText = result.Data.Outputs.Text
	}
	return
}

func (m *TranslateService) TranslateText(ctx context.Context, targetLanguage, text string) (translateText string, err error) {
	var (
		transResp []translate.Translation
		lang      language.Tag
	)
	translateText = text
	// 转换目标语言
	target := m.ToIso(targetLanguage)
	// 解析成语言标签
	if lang, err = language.Parse(target); err != nil {
		return
	}
	// 翻译
	if transResp, err = global.TranslateClient.Translate(ctx, []string{text}, lang, nil); err != nil {
		return
	}
	if len(transResp) == 0 {
		err = fmt.Errorf("translate text failed")
		return
	}
	translateText = transResp[0].Text
	if translateText == "" {
		translateText = text
	}
	return
}

func (m *TranslateService) ToIso(targetLanguage string) string {
	targetLanguage = strings.ToLower(targetLanguage)
	targetLanguageSplit := strings.Split(strings.Replace(targetLanguage, "_", "-", -1), "-")[0]
	languages := []string{"af", "sq", "am", "ar", "hy", "az", "eu", "be", "bn", "bs", "bg", "ca", "ceb", "ny", "zh-cn", "zh-tw", "co", "hr", "cs", "da", "nl", "en", "eo", "et", "fil", "fi", "fr", "fy", "gl", "ka", "de", "el", "gu", "ht", "ha", "haw", "he", "hi", "hmn", "hu", "is", "ig", "id", "ga", "it", "ja", "jv", "kn", "kk", "km", "rw", "ko", "ku", "ky", "lo", "la", "lv", "lt", "lb", "mk", "mg", "ms", "ml", "mt", "mi", "mr", "mn", "my", "ne", "no", "or", "ps", "fa", "pl", "pt", "pa", "ro", "ru", "sm", "gd", "sr", "st", "sn", "sd", "si", "sk", "sl", "so", "es", "su", "sw", "sv", "tg", "ta", "tt", "te", "th", "tr", "tk", "uk", "ur", "ug", "uz", "vi", "cy", "xh", "yi", "yo", "zu", "he", "zh"}
	if utils.IsContain(languages, targetLanguage) {
		return targetLanguage
	} else if utils.IsContain(languages, targetLanguageSplit) {
		return targetLanguageSplit
	} else {
		return "en"
	}
}
