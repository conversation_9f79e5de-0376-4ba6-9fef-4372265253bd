package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"aimsg-server/utils"
	"context"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm/clause"
	"time"
)

type AiRoleMediaService struct{}

func (m *AiRoleMediaService) Create(reqData *model.AiRoleMedia) (err error) {
	err = global.DB.Model(&model.AiRoleMedia{}).Create(reqData).Error
	return
}

func (m *AiRoleMediaService) CreateMany(reqData []*model.AiRoleMedia) (err error) {
	err = global.DB.Model(&model.AiRoleMedia{}).Create(reqData).Error
	return
}

func (m *AiRoleMediaService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.AiRoleMedia{}).Where("id = ?", id).Delete(&model.AiRoleMedia{}).Error
	return
}

func (m *AiRoleMediaService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.AiRoleMedia{}).Where("id IN (?)", reqData.Ids).Delete(&model.AiRoleMedia{}).Error
	return
}

func (m *AiRoleMediaService) Update(reqData *model.AiRoleMedia) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.AiRoleMedia{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	return
}

func (m *AiRoleMediaService) GetById(id uint) (res *model.AiRoleMedia, err error) {
	err = global.DB.Model(&model.AiRoleMedia{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *AiRoleMediaService) GetAll() (resList []*model.AiRoleMedia, err error) {
	err = global.DB.Model(&model.AiRoleMedia{}).Find(&resList).Error
	return
}

func (m *AiRoleMediaService) SearchOne(reqData *model.AiRoleMedia) (resInfo *model.AiRoleMedia, err error) {
	db := global.DB.Model(&model.AiRoleMedia{})
	if reqData.MediaType != 0 {
		db = db.Where("media_type = ?", reqData.MediaType)
	}
	if reqData.AiRoleId != 0 {
		db = db.Where("ai_role_id = ?", reqData.AiRoleId)
	}
	if reqData.MediaLevel != 0 {
		db = db.Where("media_level = ?", reqData.MediaLevel)
	}
	if reqData.MediaURL != "" {
		db = db.Where("media_url = ?", reqData.MediaURL)
	}
	err = db.First(&resInfo).Error
	return
}

func (m *AiRoleMediaService) SearchAll(ctx context.Context, reqData *model.AiRoleMedia) (resList []*model.AiRoleMedia, err error) {
	var (
		reqDataBytes []byte
		resListBytes []byte
		cacheStr     string
		cacheKey     = "AiRoleMedia:SearchAll:%s"
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	reqDataMd5 := utils.MD5(string(reqDataBytes))
	cacheKey = fmt.Sprintf(cacheKey, reqDataMd5)
	// 从redis获取缓存
	if cacheStr = global.REDIS.Get(ctx, cacheKey).Val(); cacheStr != "" {
		if err = json.Unmarshal([]byte(cacheStr), &resList); err == nil {
			return
		}
	}
	db := global.DB.Model(&model.AiRoleMedia{})
	if reqData.MediaType != 0 {
		db = db.Where("media_type = ?", reqData.MediaType)
	}
	if reqData.AiRoleId != 0 {
		db = db.Where("ai_role_id = ?", reqData.AiRoleId)
	}
	if reqData.MediaLevel != 0 {
		db = db.Where("media_level = ?", reqData.MediaLevel)
	}
	if reqData.MediaURL != "" {
		db = db.Where("media_url = ?", reqData.MediaURL)
	}
	db.Order("sort desc, id asc")
	err = db.Find(&resList).Error
	if err != nil {
		return
	}
	// 设置缓存
	if len(resList) == 0 {
		return
	}
	if resListBytes, err = json.Marshal(resList); err != nil {
		return
	}
	resListStr := string(resListBytes)
	if err = global.REDIS.SetEx(ctx, cacheKey, resListStr, time.Hour).Err(); err != nil {
		return
	}
	return
}

func (m *AiRoleMediaService) GetList(info req.AiRoleMediaSearch) (resList []*model.AiRoleMedia, total int64, err error) {
	db := global.DB.Model(&model.AiRoleMedia{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.MediaType != 0 {
		db = db.Where("media_type = ?", info.MediaType)
	}
	if info.AiRoleId != 0 {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
