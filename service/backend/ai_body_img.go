package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
)

type AiBodyImgService struct{}

func (m *AiBodyImgService) Create(ctx context.Context, reqData *model.AiBodyImg) (err error) {
	var (
		createItems []*model.AiBodyImg
	)
	if reqData.AppID == 0 {
		for appId := range global.AppMap {
			createItems = append(createItems, &model.AiBodyImg{
				AppID:         appId,
				BodyName:      reqData.BodyName,
				DiyConfigType: reqData.DiyConfigType,
				ShowStatus:    reqData.ShowStatus,
				S3Dir:         reqData.S3Dir,
				Icon:          reqData.Icon,
				Sort:          reqData.Sort,
			})
		}
	} else {
		createItems = append(createItems, reqData)
	}
	err = global.DB.Model(&model.AiBodyImg{}).Create(createItems).Error
	go m.SyncAll()
	return
}

func (m *AiBodyImgService) DeleteById(ctx context.Context, id uint) (err error) {
	err = global.DB.Model(&model.AiBodyImg{}).Where("id = ?", id).Delete(&model.AiBodyImg{}).Error
	go m.SyncAll()
	return
}

func (m *AiBodyImgService) DeleteByIds(ctx context.Context, reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.AiBodyImg{}).Where("id IN (?)", reqData.Ids).Delete(&model.AiBodyImg{}).Error
	go m.SyncAll()
	return
}

func (m *AiBodyImgService) Update(ctx context.Context, reqData *model.AiBodyImg) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return
	}
	err = global.DB.Model(&model.AiBodyImg{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	go m.SyncAll()
	return
}

func (m *AiBodyImgService) GetById(ctx context.Context, id uint) (res *model.AiBodyImg, err error) {
	err = global.DB.Model(&model.AiBodyImg{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *AiBodyImgService) GetAll() (resList []*model.AiBodyImg, err error) {
	err = global.DB.Model(&model.AiBodyImg{}).Find(&resList).Order("sort DESC").Error
	return
}

func (m *AiBodyImgService) FindShowAll() (resList []*model.AiBodyImg, err error) {
	err = global.DB.Model(&model.AiBodyImg{}).Where("show_status = 1").Find(&resList).Order("sort DESC").Error
	return
}

func (m *AiBodyImgService) SyncAll() {
	var (
		err       error
		dbResList []*model.AiBodyImg
		allMap    = map[uint]*model.AiBodyImg{}
		allAppMap = map[uint]map[model.DiyConfigType][]*model.AiBodyImg{}
	)
	dbResList, err = m.FindShowAll()
	if err != nil {
		global.LOG.Error("SyncAll 失败", zap.Error(err))
		return
	}
	for _, item := range dbResList {
		allMap[item.Id] = item
		if _, ok := allAppMap[item.AppID]; !ok {
			allAppMap[item.AppID] = map[model.DiyConfigType][]*model.AiBodyImg{}
		}
		allAppMap[item.AppID][item.DiyConfigType] = append(allAppMap[item.AppID][item.DiyConfigType], item)
	}
	global.AllAiBodyImg = allMap
	global.AllAppAiBodyImg = allAppMap
}

func (m *AiBodyImgService) SearchOne(ctx context.Context, info *model.AiBodyImg) (res *model.AiBodyImg, err error) {
	db := global.DB.Model(&model.AiBodyImg{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.BodyName != "" {
		db = db.Where("body_name LIKE ?", "%"+info.BodyName+"%")
	}
	if info.DiyConfigType != 0 {
		db = db.Where("diy_config_type = ?", info.DiyConfigType)
	}
	if info.ShowStatus != 0 {
		db = db.Where("show_status = ?", info.ShowStatus)
	}
	if err = db.First(&res).Error; err != nil {
		return
	}
	return
}

func (m *AiBodyImgService) SearchCount(ctx context.Context, info *model.AiBodyImg) (total int64, err error) {
	db := global.DB.Model(&model.AiBodyImg{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.BodyName != "" {
		db = db.Where("body_name LIKE ?", "%"+info.BodyName+"%")
	}
	if info.DiyConfigType != 0 {
		db = db.Where("diy_config_type = ?", info.DiyConfigType)
	}
	if info.ShowStatus != 0 {
		db = db.Where("show_status = ?", info.ShowStatus)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	return
}

func (m *AiBodyImgService) GetList(ctx context.Context, info req.AiBodyImgSearch) (resList []*model.AiBodyImg, total int64, err error) {
	db := global.DB.Model(&model.AiBodyImg{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.BodyName != "" {
		db = db.Where("body_name LIKE ?", "%"+info.BodyName+"%")
	}
	if info.DiyConfigType != 0 {
		db = db.Where("diy_config_type = ?", info.DiyConfigType)
	}
	if info.ShowStatus != 0 {
		db = db.Where("show_status = ?", info.ShowStatus)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
