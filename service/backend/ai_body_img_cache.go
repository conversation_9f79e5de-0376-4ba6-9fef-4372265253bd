package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"errors"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type AiBodyImgCacheService struct{}

func (m *AiBodyImgCacheService) Create(ctx context.Context, reqData *model.AiBodyImgCache) (err error) {
	err = global.DB.Model(&model.AiBodyImgCache{}).Create(reqData).Error
	return
}

func (m *AiBodyImgCacheService) Save(ctx context.Context, reqData *model.AiBodyImgCache) (err error) {
	var (
		searchOneRes *model.AiBodyImgCache
	)
	searchOneRes, err = m.SearchOne(ctx, &model.AiBodyImgCache{
		AiId:       reqData.AiId,
		AiRoleId:   reqData.AiRoleId,
		BodyImgKey: reqData.BodyImgKey,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return m.Create(ctx, reqData)
		}
		return
	}
	reqData.Id = searchOneRes.Id
	reqData.CreatedAt = searchOneRes.CreatedAt
	reqData.UpdatedAt = searchOneRes.UpdatedAt
	if err = m.Update(ctx, reqData); err != nil {
		return
	}
	return
}

func (m *AiBodyImgCacheService) DeleteById(ctx context.Context, id uint) (err error) {
	err = global.DB.Model(&model.AiBodyImgCache{}).Where("id = ?", id).Delete(&model.AiBodyImgCache{}).Error
	return
}

func (m *AiBodyImgCacheService) DeleteByIds(ctx context.Context, reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.AiBodyImgCache{}).Where("id IN (?)", reqData.Ids).Delete(&model.AiBodyImgCache{}).Error
	return
}

func (m *AiBodyImgCacheService) Update(ctx context.Context, reqData *model.AiBodyImgCache) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return
	}
	err = global.DB.Model(&model.AiBodyImgCache{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	return
}

func (m *AiBodyImgCacheService) GetById(ctx context.Context, id uint) (res *model.AiBodyImgCache, err error) {
	err = global.DB.Model(&model.AiBodyImgCache{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *AiBodyImgCacheService) GetAll(ctx context.Context) (resList []*model.AiBodyImgCache, err error) {
	err = global.DB.Model(&model.AiBodyImgCache{}).Find(&resList).Error
	return
}

func (m *AiBodyImgCacheService) SearchOne(ctx context.Context, info *model.AiBodyImgCache) (res *model.AiBodyImgCache, err error) {
	db := global.DB.Model(&model.AiBodyImgCache{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.BodyImgKey != "" {
		db = db.Where("body_img_key = ?", info.BodyImgKey)
	}
	if info.UndressImgKey != "" {
		db = db.Where("undress_img_key = ?", info.UndressImgKey)
	}
	if err = db.First(&res).Error; err != nil {
		return
	}
	return
}

func (m *AiBodyImgCacheService) SearchCount(ctx context.Context, info *model.AiBodyImgCache) (total int64, err error) {
	db := global.DB.Model(&model.AiBodyImgCache{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	return
}

func (m *AiBodyImgCacheService) GetList(ctx context.Context, info req.AiBodyImgCacheSearch) (resList []*model.AiBodyImgCache, total int64, err error) {
	db := global.DB.Model(&model.AiBodyImgCache{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
