package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/backend/request/btc"
	reqBtc "aimsg-server/model/backend/request/btc"
	"context"
	"encoding/json"
	"time"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
)

func (m *BtcService) GetBtcTrades(ctx context.Context, req *btc.GetBtcTradesRequest) (*btc.GetBtcTradesResponse, error) {
	query := global.DB.Model(&model.BtcWithdrawalRecord{}).
		Joins("LEFT JOIN btc_trades ON btc_trades.withdrawal_id = btc_withdrawal_records.id")

	if len(req.AppId) > 0 {
		query = query.Where("btc_withdrawal_records.app_id IN (?)", req.AppId)
	}

	if req.UserID != nil {
		query = query.Where("btc_withdrawal_records.user_id = ?", *req.UserID)
	}
	if req.WithdrawalId != nil {
		query = query.Where("btc_withdrawal_records.id = ?", *req.WithdrawalId)
	}
	if req.ChannelOrderNo != nil {
		query = query.Where("btc_trades.channel_order_no = ?", *req.ChannelOrderNo)
	}
	if req.AccountNo != nil {
		query = query.Where("btc_withdrawal_records.withdrawal_address = ?", *req.AccountNo)
	}
	if req.TradeStatus != nil {
		if *req.TradeStatus == 0 {
			query = query.Where("btc_withdrawal_records.withdrawal_status = ?", 1)
		} else {
			query = query.Where("btc_trades.trade_status = ?", *req.TradeStatus)
		}
	}
	if req.StartAt != nil {
		query = query.Where("btc_withdrawal_records.created_at >= ?", *req.StartAt)
	}
	if req.EndAt != nil {
		query = query.Where("btc_withdrawal_records.created_at <= ?", *req.EndAt)
	}

	var total int64 = 0
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	query.Order("btc_withdrawal_records.created_at desc").
		Limit(req.PageSize).Offset((req.Page - 1) * req.PageSize)

	btcWithdrawals := []*model.BtcWithdrawalRecord{}
	if err := query.Find(&btcWithdrawals).Error; err != nil {
		return nil, err
	}

	var u DigitalUserService
	var appService AppService
	records := make([]*reqBtc.BtcTradesItem, len(btcWithdrawals))
	for i, btcWithdrawal := range btcWithdrawals {
		btcTrade := &model.BtcTrade{}
		global.DB.Model(&model.BtcTrade{}).
			Where("withdrawal_id = ?", btcWithdrawal.ID).
			First(btcTrade)
		if btcTrade.Id == 0 {
			btcTrade.TradeStatus = 0
		}

		var digitalUser *model.DigitalUser
		var app *model.App
		var appName string
		if btcWithdrawal.UserID != 0 {
			digitalUser, _ = u.GetById(ctx, btcWithdrawal.UserID)
			if digitalUser != nil {
				app, _ = appService.GetById(digitalUser.AppID)
				appName = app.Name
			}
		}

		var reqData *reqBtc.CashOutReq
		if err := json.Unmarshal([]byte(btcWithdrawal.Req), &reqData); err != nil {
			global.LOG.Error("json.Unmarshal btcWithdrawal.Req failed", zap.Error(err))
			return nil, err
		}
		// 确定提现方式
		method := "Invoice"
		if reqData.Method.Id == 2 {
			method = "Lightning Address"
		}

		var performedAt *string
		if btcTrade.PerformedAt != nil {
			_performedAt := btcTrade.PerformedAt.Format(time.RFC3339)
			performedAt = &_performedAt
		}
		records[i] = &reqBtc.BtcTradesItem{
			ID:               btcWithdrawal.ID,
			AppId:            btcWithdrawal.AppId,
			AppName:          appName,
			UserID:           btcWithdrawal.UserID,
			Amount:           btcWithdrawal.Amount,
			WithdrawalMethod: btcWithdrawal.WithdrawalMethod,
			WithdrawalStatus: btcWithdrawal.WithdrawalStatus,
			TradeStatus:      btcTrade.TradeStatus,
			ChannelOrderNo:   btcTrade.ChannelOrderNo,
			Currency:         btcTrade.Currency,
			Address:          btcWithdrawal.WithdrawalAddress,
			Network:          btcWithdrawal.WithdrawalNetwork,
			Req:              json.RawMessage(btcWithdrawal.Req),
			MethodId:         reqData.Method.Id,
			Method:           method,
			WithdrawalTime:   btcWithdrawal.WithdrawalTime.Format(time.RFC3339),
			Message:          btcTrade.Message,
			CreatedAt:        btcWithdrawal.CreatedAt.Format(time.RFC3339),
			UpdatedAt:        btcWithdrawal.UpdatedAt.Format(time.RFC3339),
			TradePerformedAt: performedAt,
		}
	}

	return &btc.GetBtcTradesResponse{
		List:  records,
		Total: total,
	}, nil
}
