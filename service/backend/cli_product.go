package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/utils"
	"context"
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"math/rand"
	"time"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
)

var (
	SubSlideList = []response.SlideData{
		{
			SlideType: 1,
			Title:     "Unlimited Chatting",
			Subtitle:  "Unlock more AI Girlfriend characters",
			BannerUrl: utils.S3Url("public/banner/banner3_1.png", 0),
		},
		{
			SlideType: 2,
			Title:     "Unlimited Viewing of Private Photos",
			Subtitle:  "Unlock more permission to view private photos",
			BannerUrl: utils.S3Url("public/banner/banner3_2.png", 0),
		},
		{
			SlideType: 3,
			Title:     "Unlimited Viewing of Private Messages",
			Subtitle:  "Unlock more private and romantic messaging",
			BannerUrl: utils.S3Url("public/banner/banner4_3.png", 0),
		},
	}
)

// 执行降级的逻辑
func (m *ProductService) PriceOff(c *gin.Context, lastType, currentType model.ProductInfoType, page global.PageName, userInfo *model.DigitalUser, reqData *request.GetSubListReq) (response.GetSubListRespV2, error) {
	verCompare390 := utils.CompareVersions(userInfo.Version, "3.9.0")

	subProductListParams := global.SubProductListParams{
		IsAuditMode:   false,
		Page:          page,
		IsDialogCount: false,
	}

	// 取出上个级别的信息
	subProductListParams.InfoType = lastType
	lastList, err := m.SubProductList(c, subProductListParams, userInfo, reqData)
	if err != nil {
		return lastList, err
	}

	// 取出当前级别的信息
	subProductListParams.InfoType = currentType
	subProductListParams.IsDialogCount = true
	currentList, err := m.SubProductList(c, subProductListParams, userInfo, reqData)
	if err != nil {
		return currentList, err
	}

	if len(currentList.ProductList) == 0 {
		return currentList, fmt.Errorf("current products empty")
	}
	// if len(lastList.ProductList) != len(currentList.ProductList) {
	// 	return currentList, fmt.Errorf("last and current products number not eq")
	// }

	// diff算出降价的提示数据
	for index, currentProduct := range currentList.ProductList {
		if index <= len(lastList.ProductList)-1 {
			lastProduct := lastList.ProductList[index]
			offRatio := lastProduct.USDPrice.Sub(currentProduct.USDPrice).DivRound(lastProduct.USDPrice, 2).Mul(decimal.NewFromInt(100))
			currentList.ProductList[index].PayBtnText = fmt.Sprintf(global.BuyBtnTextForNotAuditAndPriceOff, "$"+currentProduct.USDPrice.String())
			if verCompare390 >= 0 {
				currentList.ProductList[index].PayBtnText = "Subscribe"
			}
			currentList.ProductList[index].PriceOffLefText = fmt.Sprintf("Get %s%s Off!", offRatio.String(), "%")
			currentList.ProductList[index].PriceOffRightText = "Today only"
		}
	}

	// 记录第一次出现倒计时的时间
	userId := utils.GetDigitalUserID(c)
	rKey := global.GetSubDialogHashKey(userId)
	secondKey := fmt.Sprintf(global.SUB_DIALOG_COUNTDOWN_TIME, page)
	countdownTime, _ := global.REDIS.HGet(c, rKey, secondKey).Int64()
	if countdownTime == 0 {
		nowTime := time.Now().Unix()
		global.REDIS.HSet(c, rKey, secondKey, nowTime)
		global.REDIS.Expire(c, rKey, time.Hour*24)
		countdownTime = nowTime
	}

	// 每个页面每天只倒计时一次
	now := time.Now().Unix()
	if countdownTime+global.SubDialogCountdownSeconds > now {
		countdownSeconds := global.SubDialogCountdownSeconds - (now - countdownTime)
		currentList.CountdownLeftText = "Offer ends in"
		currentList.CountdownRightSecond = countdownSeconds
	}

	return currentList, nil
}

func (m *ProductService) getGroupTwoSubProduct() (res []*model.Product) {
	p1, p1Exist := global.AllProductMap[42]
	if p1Exist {
		res = append(res, p1)
	}
	p2, p2Exist := global.AllProductMap[43]
	if p2Exist {
		res = append(res, p2)
	}
	p3, p3Exist := global.AllProductMap[44]
	if p3Exist {
		res = append(res, p3)
	}
	return
}
func (m *ProductService) getGroupOneSubProduct() (res []*model.Product) {
	p1, p1Exist := global.AllProductMap[42]
	if p1Exist {
		res = append(res, p1)
	}
	p2, p2Exist := global.AllProductMap[43]
	if p2Exist {
		res = append(res, p2)
	}
	p3, p3Exist := global.AllProductMap[44]
	if p3Exist {
		res = append(res, p3)
	}
	return
}

// 不执行降级逻辑的商品信息获取
func (m *ProductService) SubProductList(c *gin.Context, params global.SubProductListParams, userInfo *model.DigitalUser, reqData *request.GetSubListReq) (res response.GetSubListRespV2, err error) {
	var (
		slideData       []response.SlideData
		productList     []*model.Product
		productInfoList []*model.ProductInfo
		resProductList  []response.SubProductV2
		isDialogCount   = params.IsDialogCount
		appId           = utils.GetAppID(c)
		isAudit         = userInfo.IsAudit()
	)
	verCompare390 := utils.CompareVersions(userInfo.Version, "3.9.0")
	appInfo := global.AppMap[appId]
	bannerPlanOneImgItem := global.AppImgMap[appId][model.ImgConfigTypeSubDialogBannerPlanOne]
	bannerPlanTwoImgItem := global.AppImgMap[appId][model.ImgConfigTypeSubDialogBannerPlanTwo]
	bannerPlanOne := bannerPlanOneImgItem.NormalList
	bannerPlanTwo := bannerPlanTwoImgItem.NormalList
	if isAudit {
		bannerPlanOne = bannerPlanOneImgItem.AuditList
		bannerPlanTwo = bannerPlanTwoImgItem.AuditList
	}
	// vip弹窗方案
	vipConfig := GetVipDialogPlanConfig(c)

	// db := global.DB.Table("product_info AS pi")
	// db = db.Select("p.*")
	// db = db.Joins("LEFT JOIN product AS p ON pi.product_id = p.id")
	// db = db.Where("pi.app_id = ?", appId)
	// db = db.Where("pi.info_type = ?", infoType)
	// if appId == 1 && versionCompare < 0 {
	// 	db = db.Unscoped()
	// }
	// if err = db.Order("pi.sort DESC").Find(&productList).Error; err != nil {
	// 	return
	// }

	// 查询商品
	productInfoList = global.AppInfoTypeListMap[appId][params.InfoType].ProductInfoList
	productList = global.AppInfoTypeListMap[appId][params.InfoType].ProductList

	// 需求文档地址 https://wtkbykkfmb.feishu.cn/docx/GIDDdFWRQoEWiFxBMyXcPRVynwj?from=from_copylink
	inStrage := userInfo.AppID == 2
	inStrage = false
	if inStrage {
		var productListNew []*model.Product
		if userInfo.UserGroup == 1 {
			month1999, month1999Exist := global.AllProductMap[43]
			if month1999Exist {
				productListNew = append(productListNew, month1999)
			}
			year3999, year3999Exist := global.AllProductMap[44]
			if year3999Exist {
				productListNew = append(productListNew, year3999)
			}
		} else {
			month1499, month1499Exist := global.AllProductMap[46]
			if month1499Exist {
				productListNew = append(productListNew, month1499)
			}
			year2999, year2999Exist := global.AllProductMap[47]
			if year2999Exist {
				productListNew = append(productListNew, year2999)
			}
		}
		productList = productListNew
		// 修改排序, 月订阅在前, 年订阅在后
		if len(productList) == 2 {
			var sortProductList []*model.Product
			var monthSubList []*model.Product
			var yearSubList []*model.Product
			for _, pItem := range productList {
				if pItem.ProductType == model.PTSubMonth {
					monthSubList = append(monthSubList, pItem)
				} else if pItem.ProductType == model.PTSubYear {
					yearSubList = append(yearSubList, pItem)
				}
			}
			// 将月订阅在前，年订阅在后
			sortProductList = append(sortProductList, monthSubList...)
			sortProductList = append(sortProductList, yearSubList...)
			// 如果需要替换原来的 productList，可以在这里进行赋值
			productList = sortProductList
		}
	}
	if len(productList) == 0 {
		return
	}

	res.SlideData = SubSlideList
	res.WaitSeconds = vipConfig.CloseWait

	res.DialogPlan = 2
	haveChecked := false
	for pIndex, product := range productList {
		productInfoRecord := productInfoList[pIndex]
		var days int64 = 1
		subProductItem := response.SubProductV2{
			Id:          product.Id,
			Name:        product.Name,
			Description: product.Description,
			GooID:       product.GooID,
			IOSID:       product.IOSID,
			ProductType: product.ProductType,
			OriginPrice: product.OriginPrice,
			USDPrice:    product.USDPrice,
			Bonus:       product.Bonus,
		}
		switch product.ProductType {
		case model.PTSubYear:
			subProductItem.TopLabel = "BEST VALUE"
			days = 365
		case model.PTSubMonth:
			subProductItem.TopLabel = "MOST POPULAR"
			// subProductItem.Hot = true
			days = 30
		case model.PTSubWeek:
			days = 7
		default:
			days = 1
		}
		if !haveChecked && productInfoRecord.Checked {
			subProductItem.Hot = true
			haveChecked = true
		}
		dayCost := product.USDPrice.Div(decimal.NewFromInt(days)).Round(2)

		subProductItem.DayCost = dayCost
		subProductItem.PriceTip = fmt.Sprintf("$%s/day", dayCost.String())
		subProductItem.PayBtnText = "Get It Now"
		if verCompare390 >= 0 {
			subProductItem.PayBtnText = "Subscribe"
		}
		resProductList = append(resProductList, subProductItem)
	}
	if !haveChecked {
		for rIndex, v2 := range resProductList {
			if v2.ProductType == model.PTSubMonth {
				resProductList[rIndex].Hot = true
				break
			}
		}
	}

	if inStrage {
		// 默认选中年
		for rIndex, v2 := range resProductList {
			if v2.ProductType == model.PTSubYear {
				resProductList[rIndex].Hot = true
			} else {
				resProductList[rIndex].Hot = false
			}
		}
	}

	res.ProductList = resProductList

	// 需要记录弹窗的次数
	if isDialogCount {
		page := params.Page
		userId := utils.GetDigitalUserID(c)
		rKey := global.GetSubDialogHashKey(userId)
		secondKey := fmt.Sprintf(global.SUB_DIALOG_PAGE_COUNT, page)
		dialogCount, _ := global.REDIS.HGet(c, rKey, secondKey).Int()
		if dialogCount == 0 {
			global.REDIS.Expire(c, rKey, global.SUB_DIALOG_HASH_KEY_EXPIRE*time.Second)
		}
		global.REDIS.HSet(c, rKey, secondKey, dialogCount+1)
	}

	versionLessThan := utils.CompareVersions(userInfo.Version, "1.13.0")
	if versionLessThan >= 0 {
		randInt := rand.Intn(101) // 随机生成一个0-100的整数
		if isAudit {
			// 审核模式下，固定弹窗方案1
			res.DialogPlan = 1
		} else {
			res.DialogPlan = vipConfig.GetPlan(randInt)
		}
		if appId == 13 {
			res.DialogPlan = 2
		}
		if res.DialogPlan == 1 {
			for i, s := range bannerPlanOne {
				if i < len(SubSlideList) {
					SubSlideList[i].BannerUrl = s
				}
			}
		} else if res.DialogPlan == 2 {
			for _, s := range bannerPlanTwo {
				slideData = append(slideData, response.SlideData{BannerUrl: s})
			}
			res.SlideData = slideData
		}
		pageBgPlan := 1
		res.PageBgUrl = utils.S3Url("public/banner/vip_banner_1.png", appId)
		if rand.Float64() < 0.5 {
			pageBgPlan = 2
		}
		res.PageBgPlan = pageBgPlan
		if userInfo.IsAudit() {
			res.Tip = GetVipDialogPlanTwoAuditTip(c, userInfo.AppID)
		} else {
			res.Tip = GetVipDialogPlanTwoTip(c, userInfo.AppID)
			res.Tip.TipWeek = appInfo.DetailConfig.Data().WeekSendTxt
			res.Tip.TipMonth = appInfo.DetailConfig.Data().MonthSendTxt
			res.Tip.TipYear = appInfo.DetailConfig.Data().YearSendTxt
		}
		// 处理返回值的icon地址
		res.Tip.WeekTipV2.BgIcon = utils.S3Url(res.Tip.WeekTipV2.BgIcon, appId)
		res.Tip.WeekTipV2.AddCoinIcon = utils.S3Url(res.Tip.WeekTipV2.AddCoinIcon, appId)
		res.Tip.MonthTipV2.BgIcon = utils.S3Url(res.Tip.MonthTipV2.BgIcon, appId)
		res.Tip.MonthTipV2.AddCoinIcon = utils.S3Url(res.Tip.MonthTipV2.AddCoinIcon, appId)
		res.Tip.YearTipV2.BgIcon = utils.S3Url(res.Tip.YearTipV2.BgIcon, appId)
		res.Tip.YearTipV2.AddCoinIcon = utils.S3Url(res.Tip.YearTipV2.AddCoinIcon, appId)
		for i, benefitsItem := range res.Tip.WeekTipV2.BenefitsList {
			res.Tip.WeekTipV2.BenefitsList[i].Icon = utils.S3Url(benefitsItem.Icon, appId)
		}
		for i, benefitsItem := range res.Tip.MonthTipV2.BenefitsList {
			res.Tip.MonthTipV2.BenefitsList[i].Icon = utils.S3Url(benefitsItem.Icon, appId)
		}
		for i, benefitsItem := range res.Tip.YearTipV2.BenefitsList {
			res.Tip.YearTipV2.BenefitsList[i].Icon = utils.S3Url(benefitsItem.Icon, appId)
		}
	}

	return
}

func (m *ProductService) FlowerProductList(c *gin.Context, source string) (res response.FlowerProductListRes, err error) {
	var (
		showAds         bool
		tip             string
		profileService  DigitalUserProfileService
		userInfo        *model.DigitalUser
		userProfile     *model.DigitalUserProfile
		userService     DigitalUserService
		productInfoList []*model.ProductInfo
		productList     []*model.Product
		resProductList  []response.FlowerProductListItem
		floConsume      int
		appId           = utils.GetAppID(c)
		userId          = utils.GetDigitalUserID(c)
		now             = time.Now()
		dayStr          = now.Format(time.DateOnly)
		rKey            = fmt.Sprintf(global.FLO_DIALOG_SHOW_KEY, dayStr, userId)
	)
	// 获取App信息
	appInfo, exist := global.AppMap[appId]
	if !exist {
		err = fmt.Errorf("app(%d) not found", appId)
		return
	}
	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	// 获取用户profile信息
	if userProfile, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	sub := userInfo.IsSub()

	voiceFlo := appInfo.DetailConfig.Data().FreeGenVoicePerFlower
	replyFlo := appInfo.DetailConfig.Data().AiReplyPerFlower
	maskPhotoFlo := appInfo.DetailConfig.Data().MaskPhotoFlower
	undressFlo := appInfo.DetailConfig.Data().FreeUndressPerFlower
	asmrFlo := appInfo.DetailConfig.Data().FreeAsmrPerFlower
	videoFlo := appInfo.DetailConfig.Data().FreeGenVideoPerFlower
	if sub {
		voiceFlo = appInfo.DetailConfig.Data().SubGenVoicePerFlower
		undressFlo = appInfo.DetailConfig.Data().SubUndressPerFlower
		asmrFlo = appInfo.DetailConfig.Data().SubAsmrPerFlower
		videoFlo = appInfo.DetailConfig.Data().SubGenVideoPerFlower
	}

	// 查询商品
	productInfoList = global.AppInfoTypeListMap[appId][model.ProductInfoTypeFlower].ProductInfoList
	productList = global.AppInfoTypeListMap[appId][model.ProductInfoTypeFlower].ProductList
	if len(productList) == 0 {
		return
	}
	haveChecked := false
	for pIndex, product := range productList {
		productInfoRecord := productInfoList[pIndex]
		subProductItem := response.FlowerProductListItem{
			Id:          product.Id,
			Name:        product.Name,
			Description: product.Description,
			GooID:       product.GooID,
			IOSID:       product.IOSID,
			ProductType: product.ProductType,
			Amount:      product.Amount,
			OriginPrice: product.OriginPrice,
			USDPrice:    product.USDPrice,
			TopLabel:    "",
			Hot:         false,
		}
		subProductItem.Amount = product.Amount + product.RewardAmount
		if pIndex == 1 {
			subProductItem.TopLabel = "MOST POPULAR"
		} else if pIndex == 2 {
			subProductItem.TopLabel = "BEST VALUE"
		}
		if !haveChecked && productInfoRecord.Checked {
			subProductItem.TopLabel = "MOST POPULAR"
			subProductItem.Hot = true
			haveChecked = true
		}
		resProductList = append(resProductList, subProductItem)
	}
	if !haveChecked {
		for rIndex, product := range resProductList {
			if product.GooID == "10012" || product.IOSID == "10002" {
				resProductList[rIndex].Hot = true
				break
			}
		}
	}
	flowerTipConfig := GetFlowerDialogTipConfig(c)
	switch source {
	case "play_voice":
		tip = fmt.Sprintf("%d %s", voiceFlo, flowerTipConfig.PlayVoice)
		floConsume = voiceFlo
	case "mask_msg":
		tip = fmt.Sprintf("%d %s", maskPhotoFlo, flowerTipConfig.MaskMsg)
		floConsume = maskPhotoFlo
	case "send_msg":
		tip = fmt.Sprintf("%d %s", replyFlo, flowerTipConfig.SendMsg)
		floConsume = replyFlo
	case "undress":
		tip = fmt.Sprintf("%d %s", undressFlo, flowerTipConfig.Undress)
		floConsume = undressFlo
	case "asmr":
		tip = fmt.Sprintf("%d %s", asmrFlo, flowerTipConfig.Asmr)
		floConsume = asmrFlo
	case "play_video":
		tip = fmt.Sprintf("%d %s", videoFlo, flowerTipConfig.PlayVideo)
		floConsume = videoFlo
	default:
		tip = "5 Per-replay"
		floConsume = 5
	}
	// 记录弹窗次数
	global.REDIS.Incr(c, rKey)
	global.REDIS.Expire(c, rKey, time.Hour*25)

	// 是否显示广告
	showAds = ShowMsgAds(c, userInfo, userProfile, floConsume)

	res = response.FlowerProductListRes{
		Title:        "Gold Coin Shop",
		FlowerAmount: userProfile.FlowerAmount,
		ShopTip: response.ShopTip{
			Content:   " ",
			ButtonTxt: "subscribe",
		},
		FlowerCostTip: tip,
		ShowAds:       showAds,
		List:          resProductList,
	}
	return
}

func (m *ProductService) ChatProductList(c *gin.Context) (res response.ChatProductListRes, err error) {
	var (
		productInfoList []*model.ProductInfo
		productList     []*model.Product
		resProductList  []response.ChatProductListItem
		appId           = utils.GetAppID(c)
	)
	// 查询商品
	productInfoList = global.AppInfoTypeListMap[appId][model.ProductInfoTypeChat].ProductInfoList
	productList = global.AppInfoTypeListMap[appId][model.ProductInfoTypeChat].ProductList
	if len(productList) == 0 {
		return
	}
	haveChecked := false
	for pIndex, product := range productList {
		productInfoRecord := productInfoList[pIndex]
		productItem := response.ChatProductListItem{
			Id:          product.Id,
			Name:        product.Name,
			Description: product.Description,
			GooID:       product.GooID,
			IOSID:       product.IOSID,
			ProductType: product.ProductType,
			Amount:      product.Amount,
			OriginPrice: product.OriginPrice,
			USDPrice:    product.USDPrice,
			TopLabel:    "",
			Hot:         false,
		}
		productItem.Amount = product.Amount + product.RewardAmount
		perPrice := product.USDPrice.Div(decimal.NewFromInt(int64(product.Amount)))
		truncatedPrice := perPrice.Truncate(2)
		productItem.PerTip = fmt.Sprintf("$%s/ea", truncatedPrice.String())

		if pIndex == 1 {
			productItem.TopLabel = "SAVE 20%"
		} else if pIndex == 2 {
			productItem.TopLabel = "SAVE 40%"
		}
		if !haveChecked && productInfoRecord.Checked {
			productItem.Hot = true
			haveChecked = true
		}

		// if product.Amount == 5 {
		// 	productItem.TopLabel = "SAVE 20%"
		// 	productItem.Hot = true
		// } else if product.Amount == 10 {
		// 	productItem.TopLabel = "SAVE 40%"
		// }
		resProductList = append(resProductList, productItem)
	}
	res = response.ChatProductListRes{
		List: resProductList,
	}
	return
}

func (m *ProductService) FlowerShop(c *gin.Context) (res response.FlowerShopRes, err error) {
	var (
		profileService DigitalUserProfileService
		userInfo       *model.DigitalUser
		userProfile    *model.DigitalUserProfile
		userService    DigitalUserService
		productList    []model.Product
		resProductList []response.FlowerShopItem
		appId          = utils.GetAppID(c)
		userId         = utils.GetDigitalUserID(c)
		appVersion     = c.GetHeader("App-Version")
		versionCompare = utils.CompareVersions(appVersion, "1.18.0")
	)
	// 获取App信息
	appInfo, exist := global.AppMap[appId]
	if !exist {
		err = fmt.Errorf("app(%d) not found", appId)
		return
	}
	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	// 获取用户profile信息
	if userProfile, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	highPriceShow := appInfo.DetailConfig.Data().FloHighPriceShow
	// 查询商品
	if err = global.DB.Model(&model.Product{}).Where("app_id = ? AND good_type = ? AND product_type = ?", appId, model.GTGood, model.PTFlowers).Order("sort DESC").Find(&productList).Error; err != nil {
		return
	}
	if len(productList) == 0 {
		return
	}
	for _, product := range productList {
		subProductItem := response.FlowerShopItem{
			Id:           product.Id,
			Name:         product.Name,
			Description:  product.Description,
			GooID:        product.GooID,
			IOSID:        product.IOSID,
			Amount:       product.Amount,
			RewardAmount: product.RewardAmount,
			OriginPrice:  product.OriginPrice,
			USDPrice:     product.USDPrice,
			Hot:          false,
		}
		if versionCompare < 0 {
			subProductItem.Amount = product.Amount + product.RewardAmount
		}
		if product.GooID == "10012" || product.IOSID == "10002" {
			subProductItem.Hot = true
		}
		if product.GooID == "10010" || product.IOSID == "10007" {
			// 1.9.0需求地址 https://mz2lv9.axshare.com/#id=p7l1tt
			// 低价商品,审核用户才显示.
			if userInfo.IsAudit() {
				resProductList = append(resProductList, subProductItem)
			} else {
				continue
			}
		} else if product.GooID == "10017" || product.GooID == "10018" || product.GooID == "10019" || product.IOSID == "10008" || product.IOSID == "10009" || product.IOSID == "10000" {
			// 1.9.0需求地址 https://mz2lv9.axshare.com/#id=p7l1tt
			// 1.10.0需求地址 https://x2lw0m.axshare.com/#id=o8rqtt
			// 高价商品,审核用户显示 非审核模式,后台可配置是否显示.
			if userInfo.IsAudit() || highPriceShow {
				resProductList = append(resProductList, subProductItem)
			} else {
				continue
			}
		} else {
			resProductList = append(resProductList, subProductItem)
		}
	}
	res = response.FlowerShopRes{
		FlowerAmount: userProfile.FlowerAmount,
		List:         resProductList,
	}
	return
}

func (m *ProductService) FlowerShopV2(c *gin.Context) (res response.FlowerShopV2Res, err error) {
	var (
		profileService  DigitalUserProfileService
		userInfo        *model.DigitalUser
		userProfile     *model.DigitalUserProfile
		userService     DigitalUserService
		productInfoList []*model.ProductInfo
		productList     []*model.Product
		resProductList  []response.FlowerShopItem
		appId           = utils.GetAppID(c)
		userId          = utils.GetDigitalUserID(c)
		now             = time.Now()
		dayStr          = now.Format(time.DateOnly)
		rKey            = fmt.Sprintf(global.FLO_DIALOG_SHOW_KEY, dayStr, userId)
		appVersion      = c.GetHeader("App-Version")
		versionCompare  = utils.CompareVersions(appVersion, "1.18.0")
	)
	// 获取App信息
	appInfo, exist := global.AppMap[appId]
	if !exist {
		err = fmt.Errorf("app(%d) not found", appId)
		return
	}
	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	isAudit := userInfo.IsAudit()
	// 获取用户profile信息
	if userProfile, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	highPriceShow := appInfo.DetailConfig.Data().FloHighPriceShow
	// 查询商品
	productInfoList = global.AppInfoTypeListMap[appId][model.ProductInfoTypeFlowerShop].ProductInfoList
	productList = global.AppInfoTypeListMap[appId][model.ProductInfoTypeFlowerShop].ProductList
	if len(productList) == 0 {
		return
	}
	haveChecked := false
	for pIndex, product := range productList {
		productInfoRecord := productInfoList[pIndex]
		subProductItem := response.FlowerShopItem{
			Id:           product.Id,
			Name:         product.Name,
			Description:  product.Description,
			GooID:        product.GooID,
			IOSID:        product.IOSID,
			Amount:       product.Amount,
			RewardAmount: product.RewardAmount,
			OriginPrice:  product.OriginPrice,
			USDPrice:     product.USDPrice,
			Hot:          false,
		}
		// 策略
		if userInfo.AppID == 1 && userInfo.UserGroup == 2 && product.GooID == "10011" {
			continue
		}
		if versionCompare < 0 {
			subProductItem.Amount = product.Amount + product.RewardAmount
		}
		if !haveChecked && productInfoRecord.Checked {
			subProductItem.Hot = true
			haveChecked = true
		}
		if product.GooID == "10010" || product.IOSID == "10007" {
			// 1.9.0需求地址 https://mz2lv9.axshare.com/#id=p7l1tt
			// 低价商品,审核用户才显示.
			if userInfo.IsAudit() {
				resProductList = append(resProductList, subProductItem)
			} else {
				continue
			}
		} else if product.GooID == "10017" || product.GooID == "10018" || product.GooID == "10019" || product.IOSID == "10008" || product.IOSID == "10009" || product.IOSID == "10000" {
			// 1.9.0需求地址 https://mz2lv9.axshare.com/#id=p7l1tt
			// 1.10.0需求地址 https://x2lw0m.axshare.com/#id=o8rqtt
			// 高价商品,审核用户显示 非审核模式,后台可配置是否显示.
			if userInfo.IsAudit() || highPriceShow {
				resProductList = append(resProductList, subProductItem)
			} else {
				continue
			}
		} else {
			resProductList = append(resProductList, subProductItem)
		}
	}

	// 免费获得Flower入口是否显示
	getFreeFloShow := false
	if isAudit {
		getFreeFloShow = appInfo.DetailConfig.Data().GetFreeFloAudit
	} else {
		/*
			【非审核模式】 后台可配置是否显示，【1 显示， 0 不显示】 默认配置 0
			【非审核模式】 当配置 1，再判断用户关闭Flowers弹窗【聊天页触发的】
			用户累计关闭Flowers弹窗次数达到 X 次时，显示 免费获得Flower 入口。
			默认值：15
		*/
		// 获取用户累计关闭Flowers弹窗次数
		showCount, _ := global.REDIS.Get(c, rKey).Int()
		getFreeFloShow = appInfo.DetailConfig.Data().GetFreeFloNormal && showCount >= appInfo.DetailConfig.Data().ShowCloseFloCount
	}

	res = response.FlowerShopV2Res{
		GetFreeFloShow: getFreeFloShow,
		FlowerAmount:   userProfile.FlowerAmount,
		List:           resProductList,
	}
	// 查询SVip商品
	sVipProductList := global.AppInfoTypeListMap[appId][model.ProductInfoTypeSVip].ProductList
	verCompare320 := utils.CompareVersions(appVersion, "3.2.0")
	if len(sVipProductList) > 0 && verCompare320 < 0 {
		// 获取文本配置
		appCfg := GetFlowerShopSVipTextListCfg(c, appId)
		if isAudit {
			appCfg = GetFlowerShopSVipTextListAuditCfg(c, appId)
		}
		sVipProduct := sVipProductList[0]
		floCount := 0
		if sVipProduct.ProductType == model.PTSVipWeek {
			floCount = appInfo.DetailConfig.Data().SVipWeekSendFlo
		} else if sVipProduct.ProductType == model.PTSVipMonth {
			floCount = appInfo.DetailConfig.Data().SVipMonthSendFlo
		} else if sVipProduct.ProductType == model.PTSVip {
			floCount = appInfo.DetailConfig.Data().SVipYearSendFlo
		}
		res.FloCount = floCount
		res.SVipInfo = &response.SVipProductInfo{
			Id:           sVipProduct.Id,
			Name:         sVipProduct.Name,
			Description:  sVipProduct.Description,
			GooID:        sVipProduct.GooID,
			IOSID:        sVipProduct.IOSID,
			Amount:       sVipProduct.Amount,
			GoodType:     sVipProduct.GoodType,
			ProductType:  sVipProduct.ProductType,
			RewardAmount: sVipProduct.RewardAmount,
			OriginPrice:  sVipProduct.OriginPrice,
			USDPrice:     sVipProduct.USDPrice,
			TextList:     appCfg.Tips,
		}
	}

	return
}

func (m *ProductService) WebCoinShop(c *gin.Context) (res response.WebCoinShopRes, err error) {
	var (
		productInfoList []*model.ProductInfo
		productList     []*model.Product
		resProductList  []response.FlowerShopItem
		appId           = utils.GetAppID(c)
	)
	// 查询商品
	productInfoList = global.AppInfoTypeListMap[appId][model.ProductInfoTypeFlowerShop].ProductInfoList
	productList = global.AppInfoTypeListMap[appId][model.ProductInfoTypeFlowerShop].ProductList
	if len(productList) == 0 {
		return
	}
	haveChecked := false
	for pIndex, product := range productList {
		productInfoRecord := productInfoList[pIndex]
		subProductItem := response.FlowerShopItem{
			Id:           product.Id,
			Name:         product.Name,
			Description:  product.Description,
			GooID:        product.GooID,
			IOSID:        product.IOSID,
			Amount:       product.Amount,
			RewardAmount: product.RewardAmount,
			OriginPrice:  product.OriginPrice,
			USDPrice:     product.USDPrice,
			Hot:          false,
		}
		if !haveChecked && productInfoRecord.Checked {
			subProductItem.Hot = true
			haveChecked = true
		}
		resProductList = append(resProductList, subProductItem)
	}
	res = response.WebCoinShopRes{
		List: resProductList,
	}
	return
}

func (m *ProductService) VoiceProductList(c *gin.Context, reqData request.AiRoleIdReq) (res response.VoiceProductList, err error) {
	var (
		aiRoleInfo      *model.AiRole
		productInfoList []*model.ProductInfo
		productList     []*model.Product
		productItems    []response.ProductItem
		aiRoleSer       AiRoleService
		voiceSer        VoiceCallService
		msgService      MsgService
		appId           = utils.GetAppID(c)
	)
	// 获取AI角色信息
	if aiRoleInfo, err = aiRoleSer.GetByRoleId(reqData.AiRoleId); err != nil {
		return
	}
	appVoiceCallCfg := GetAppVoiceCallCfg(c, appId)
	floNoEnoughMsg := utils.RandList(appVoiceCallCfg.FloNoEnoughMsgs)
	msgVoiceKey, idleMsgKeyExist := voiceSer.VoiceCache(c, floNoEnoughMsg, aiRoleInfo)
	if !idleMsgKeyExist {
		voiceUuidStr := uuid.NewString()
		msgVoiceKey, _ = msgService.ElevenLabsTts(voiceUuidStr, floNoEnoughMsg, aiRoleInfo)
	}
	productInfoList = global.AppInfoTypeListMap[appId][model.ProductInfoTypeFlowerVoiceCall].ProductInfoList
	productList = global.AppInfoTypeListMap[appId][model.ProductInfoTypeFlowerVoiceCall].ProductList
	if len(productList) == 0 {
		return
	}
	haveChecked := false
	for pIndex, product := range productList {
		productInfoRecord := productInfoList[pIndex]
		item := response.ProductItem{
			Id:           product.Id,
			Name:         product.Name,
			Description:  product.Description,
			GooID:        product.GooID,
			IOSID:        product.IOSID,
			Amount:       product.Amount,
			RewardAmount: product.RewardAmount,
			OriginPrice:  product.OriginPrice,
			USDPrice:     product.USDPrice,
			Bonus:        product.Bonus,
			Hot:          false,
		}
		if !haveChecked && productInfoRecord.Checked {
			item.Hot = true
			haveChecked = true
		}
		productItems = append(productItems, item)
	}
	res = response.VoiceProductList{
		Msg:      floNoEnoughMsg,
		MsgVoice: utils.S3Url(msgVoiceKey, appId),
		List:     productItems,
	}
	return
}

func (m *ProductService) SVipDialog(c *gin.Context) (res response.SVipDialogRes, err error) {
	var (
		userInfo    *model.DigitalUser
		userService DigitalUserService
		productList []*model.Product
		appId       = utils.GetAppID(c)
		userId      = utils.GetDigitalUserID(c)
	)
	appInfo := global.AppMap[appId]
	sVipDialogImgItem := global.AppImgMap[appId][model.ImgConfigTypeSVipDialog]
	imgList := sVipDialogImgItem.NormalList
	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	isAudit := userInfo.IsAudit()
	appCfg := GetSVipDialogCfg(c, appId)
	if isAudit {
		imgList = sVipDialogImgItem.AuditList
		appCfg = GetSVipDialogAuditCfg(c, appId)
	}
	productList = global.AppInfoTypeListMap[appId][model.ProductInfoTypeSVip].ProductList
	if len(productList) == 0 {
		return
	}
	sVipProduct := productList[0]
	floCount := 0
	if sVipProduct.ProductType == model.PTSVipWeek {
		floCount = appInfo.DetailConfig.Data().SVipWeekSendFlo
	} else if sVipProduct.ProductType == model.PTSVipMonth {
		floCount = appInfo.DetailConfig.Data().SVipMonthSendFlo
	} else if sVipProduct.ProductType == model.PTSVip {
		floCount = appInfo.DetailConfig.Data().SVipYearSendFlo
	}
	res = response.SVipDialogRes{
		Title:    appCfg.Title,
		FloCount: floCount,
		TipList:  appCfg.TipList,
		BgList:   imgList,
		SVipInfo: response.SVipProductInfo{
			Id:           sVipProduct.Id,
			Name:         sVipProduct.Name,
			Description:  sVipProduct.Description,
			GooID:        sVipProduct.GooID,
			IOSID:        sVipProduct.IOSID,
			Amount:       sVipProduct.Amount,
			GoodType:     sVipProduct.GoodType,
			ProductType:  sVipProduct.ProductType,
			RewardAmount: sVipProduct.RewardAmount,
			OriginPrice:  sVipProduct.OriginPrice,
			USDPrice:     sVipProduct.USDPrice,
			TextList:     nil,
		},
	}
	return
}

func (m *ProductService) SVipProductList(c *gin.Context, reqData request.SVipProductListReq) (res response.SVipProductListRes, err error) {
	var (
		userInfo       *model.DigitalUser
		userService    DigitalUserService
		resProductList []response.SVIPProduct
	)
	appId := utils.GetAppID(c)
	userId := utils.GetDigitalUserID(c)
	sVipDialogImgItem := global.AppImgMap[appId][model.ImgConfigTypeSVipDialog]
	imgList := sVipDialogImgItem.NormalList
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	verCompare390 := utils.CompareVersions(userInfo.Version, "3.9.0")
	isAudit := userInfo.IsAudit()
	if isAudit {
		imgList = sVipDialogImgItem.AuditList
	}
	productInfoList := global.AppInfoTypeListMap[appId][model.ProductInfoTypeSVipNew].ProductInfoList
	productList := global.AppInfoTypeListMap[appId][model.ProductInfoTypeSVipNew].ProductList
	// AppId 2 组1 去掉周订阅
	// if userInfo.CreatedAt.After(global.StageOneTime) && userInfo.AppID == 2 && userInfo.UserGroup == 1 {
	// 	var productListNew []*model.Product
	// 	for _, pItem := range productList {
	// 		if pItem.ProductType != model.PTSVipWeek {
	// 			productListNew = append(productListNew, pItem)
	// 		}
	// 	}
	// 	productList = productListNew
	// }
	if len(productList) == 0 {
		return
	}
	productIDCheckedMap := map[uint]bool{}
	for _, i := range productInfoList {
		productIDCheckedMap[i.ProductID] = i.Checked
	}
	for _, product := range productList {
		var days int64 = 1
		svipItem := response.SVIPProduct{
			Id:          product.Id,
			Name:        product.Name,
			Description: product.Description,
			GooID:       product.GooID,
			IOSID:       product.IOSID,
			ProductType: product.ProductType,
			OriginPrice: product.OriginPrice,
			USDPrice:    product.USDPrice,
			Bonus:       product.Bonus,
		}
		switch product.ProductType {
		case model.PTSVip:
			svipItem.TopLabel = "BEST VALUE"
			days = 365
		case model.PTSVipMonth:
			svipItem.TopLabel = "MOST POPULAR"
			days = 30
		case model.PTSVipWeek:
			days = 7
		default:
			days = 1
		}
		svipItem.Hot = productIDCheckedMap[product.Id]
		dayCost := product.USDPrice.Div(decimal.NewFromInt(days)).Round(2)
		svipItem.DayCost = dayCost
		svipItem.PriceTip = fmt.Sprintf("$%s/day", dayCost.String())
		svipItem.PayBtnText = fmt.Sprintf("Pay %s now", product.USDPrice.String())
		if verCompare390 >= 0 {
			svipItem.PayBtnText = "Upgrade"
		}
		resProductList = append(resProductList, svipItem)
	}
	res.BgList = imgList
	res.Tip = GetSVipDialogTip(c, appId)
	if isAudit {
		res.Tip = GetSVipDialogTipAudit(c, appId)
	}
	// 处理返回值的icon地址
	res.Tip.WeekTipV2.BgIcon = utils.S3Url(res.Tip.WeekTipV2.BgIcon, appId)
	res.Tip.WeekTipV2.AddCoinIcon = utils.S3Url(res.Tip.WeekTipV2.AddCoinIcon, appId)
	res.Tip.MonthTipV2.BgIcon = utils.S3Url(res.Tip.MonthTipV2.BgIcon, appId)
	res.Tip.MonthTipV2.AddCoinIcon = utils.S3Url(res.Tip.MonthTipV2.AddCoinIcon, appId)
	res.Tip.YearTipV2.BgIcon = utils.S3Url(res.Tip.YearTipV2.BgIcon, appId)
	res.Tip.YearTipV2.AddCoinIcon = utils.S3Url(res.Tip.YearTipV2.AddCoinIcon, appId)
	for i, tipItem := range res.Tip.TipWeek {
		res.Tip.TipWeek[i].Icon = utils.S3Url(tipItem.Icon, appId)
	}
	for i, tipItem := range res.Tip.TipMonth {
		res.Tip.TipMonth[i].Icon = utils.S3Url(tipItem.Icon, appId)
	}
	for i, tipItem := range res.Tip.TipYear {
		res.Tip.TipYear[i].Icon = utils.S3Url(tipItem.Icon, appId)
	}
	for i, benefitsItem := range res.Tip.WeekTipV2.BenefitsList {
		res.Tip.WeekTipV2.BenefitsList[i].Icon = utils.S3Url(benefitsItem.Icon, appId)
	}
	for i, benefitsItem := range res.Tip.MonthTipV2.BenefitsList {
		res.Tip.MonthTipV2.BenefitsList[i].Icon = utils.S3Url(benefitsItem.Icon, appId)
	}
	for i, benefitsItem := range res.Tip.YearTipV2.BenefitsList {
		res.Tip.YearTipV2.BenefitsList[i].Icon = utils.S3Url(benefitsItem.Icon, appId)
	}
	v2Cfg := GetSVipDialogV2Cfg(c, appId)
	if verCompare390 >= 0 {
		v2Cfg = GetSVipDialogV390Cfg(c, appId)
	}
	v2Cfg.BgIcon = utils.S3Url(v2Cfg.BgIcon, appId)
	for i, cfgItem := range v2Cfg.WeekList {
		v2Cfg.WeekList[i].Icon = utils.S3Url(cfgItem.Icon, appId)
		v2Cfg.WeekList[i].Img = utils.S3Url(cfgItem.Img, appId)
	}
	for i, cfgItem := range v2Cfg.MonthList {
		v2Cfg.MonthList[i].Icon = utils.S3Url(cfgItem.Icon, appId)
		v2Cfg.MonthList[i].Img = utils.S3Url(cfgItem.Img, appId)
	}
	for i, cfgItem := range v2Cfg.YearList {
		v2Cfg.YearList[i].Icon = utils.S3Url(cfgItem.Icon, appId)
		v2Cfg.YearList[i].Img = utils.S3Url(cfgItem.Img, appId)
	}
	res.TipV2 = v2Cfg
	if reqData.Position != 0 && !userInfo.IsAudit() {
		res.HalfDialog = true
		for i, bannerItem := range v2Cfg.WeekList {
			if bannerItem.Position == reqData.Position {
				res.TipV2Index = i
				break
			}
		}
	}
	res.ProductList = resProductList
	return
}

func (m *ProductService) ChatVipProductList(c *gin.Context, reqData request.ChatVipProductListReq) (res response.ChatVipProductListRes, err error) {
	var (
		userInfo        *model.DigitalUser
		userService     DigitalUserService
		resProductList  []response.ChatVIPProduct
		productInfoType = model.ProductInfoTypeChatVipDialog
		appVersion      = c.GetHeader("App-Version")
	)
	appId := utils.GetAppID(c)
	userId := utils.GetDigitalUserID(c)
	sVipDialogImgItem := global.AppImgMap[appId][model.ImgConfigTypeChatVipDialog]
	imgList := sVipDialogImgItem.NormalList
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	// 获取订阅低价国家配置
	subLowCountryCfg := GetSubLowCountry(c)
	// 判断用户是否是订阅低价国家
	isSubLowCountry := subLowCountryCfg[userInfo.CountryCode]
	if isSubLowCountry {
		productInfoType = model.ProductInfoTypeChatVipLowCountry
	}
	isAudit := userInfo.IsAudit()
	if isAudit {
		imgList = sVipDialogImgItem.AuditList
	}
	productInfoList := global.AppInfoTypeListMap[appId][productInfoType].ProductInfoList
	productList := global.AppInfoTypeListMap[appId][productInfoType].ProductList
	if len(productList) == 0 {
		return
	}
	productIDCheckedMap := map[uint]bool{}
	for _, i := range productInfoList {
		productIDCheckedMap[i.ProductID] = i.Checked
	}
	for _, product := range productList {
		var days int64 = 1
		svipItem := response.ChatVIPProduct{
			Id:          product.Id,
			Name:        product.Name,
			Description: product.Description,
			GooID:       product.GooID,
			IOSID:       product.IOSID,
			ProductType: product.ProductType,
			OriginPrice: product.OriginPrice,
			USDPrice:    product.USDPrice,
		}
		switch product.ProductType {
		case model.PTChatVipYear:
			svipItem.TopLabel = "BEST VALUE"
			days = 365
		case model.PTChatVipMonth:
			svipItem.TopLabel = "MOST POPULAR"
			days = 30
		case model.PTChatVipWeek:
			days = 7
		default:
			days = 1
		}
		svipItem.Hot = productIDCheckedMap[product.Id]
		dayCost := product.USDPrice.Div(decimal.NewFromInt(days)).Round(2)
		svipItem.DayCost = dayCost
		svipItem.PriceTip = fmt.Sprintf("$%s/day", dayCost.String())
		svipItem.PayBtnText = fmt.Sprintf("Pay %s now", product.USDPrice.String())
		resProductList = append(resProductList, svipItem)
	}
	res.BgList = imgList
	res.Tip = GetChatVipDialogTip(c, appId)
	if isAudit {
		res.Tip = GetChatVipDialogTipAudit(c, appId)
	}
	app4IsOld := 1
	t2 := time.Date(2025, 4, 9, 0, 0, 0, 0, time.UTC)
	if userInfo.CreatedAt.After(t2) {
		app4IsOld = 2
	}
	verCompare391 := utils.CompareVersions(appVersion, "3.9.1")
	if appId == 4 && verCompare391 == 1 && app4IsOld == 2 {
		res.Tip = GetChatVipDialogTip2(c, appId)
	}
	v2Cfg := GetChatVipDialogV2Cfg(c, appId)
	res.TipV2 = v2Cfg
	if reqData.Position != 0 && !userInfo.IsAudit() {
		res.HalfDialog = true
		for i, bannerItem := range v2Cfg.WeekList {
			if bannerItem.Position == reqData.Position {
				res.TipV2Index = i
				break
			}
		}
	}
	res.ProductList = resProductList
	return
}
func GetChatVipDialogTip2(ctx context.Context, appId uint) (rdsCfg response.ChatVipDialogTip) {
	str := `{"tip_month":[{"content":"Unlock Virtual Live","icon":"https://images.aipersona.cloud/public/image/f0278361-4248-4aff-b208-d87312e6b1a9.png"},{"content":"Unlimited free fans chat","icon":"https://images.aipersona.cloud/public/image/80a0b792-cef2-4ee2-ae62-d1004ad8d04e.png"},{"content":"One-time gift of 300 coins for you","icon":"https://images.aipersona.cloud/public/image/95cd995b-4028-4873-9b0e-d73aa47f17d7.png"}],"tip_week":[{"content":"Unlock Virtual Live","icon":"https://images.aipersona.cloud/public/image/f0278361-4248-4aff-b208-d87312e6b1a9.png"},{"content":"Unlimited free fans chat","icon":"https://images.aipersona.cloud/public/image/80a0b792-cef2-4ee2-ae62-d1004ad8d04e.png"},{"content":"One-time gift of 150 coins for you","icon":"https://images.aipersona.cloud/public/image/95cd995b-4028-4873-9b0e-d73aa47f17d7.png"}],"tip_year":[{"content":"Unlock Virtual Live","icon":"https://images.aipersona.cloud/public/image/f0278361-4248-4aff-b208-d87312e6b1a9.png"},{"content":"Unlimited free fans chat","icon":"https://images.aipersona.cloud/public/image/80a0b792-cef2-4ee2-ae62-d1004ad8d04e.png"},{"content":"One-time gift of 400 coins for you","icon":"https://images.aipersona.cloud/public/image/95cd995b-4028-4873-9b0e-d73aa47f17d7.png"}],"title":"Enjoy virtual live experience"}`
	_ = json.Unmarshal([]byte(str), &rdsCfg)
	return
}

func (m *ProductService) ContentVipProductList(c *gin.Context, reqData request.ContentVipProductListReq) (res response.ContentVipProductListRes, err error) {
	var (
		userInfo        *model.DigitalUser
		userService     DigitalUserService
		resProductList  []response.ContentVIPProduct
		productInfoType = model.ProductInfoTypeContentVipDialog
		appVersion      = c.GetHeader("App-Version")
	)
	appId := utils.GetAppID(c)
	userId := utils.GetDigitalUserID(c)
	sVipDialogImgItem := global.AppImgMap[appId][model.ImgConfigTypeContentVipDialog]
	imgList := sVipDialogImgItem.NormalList
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	// 获取订阅低价国家配置
	subLowCountryCfg := GetSubLowCountry(c)
	// 判断用户是否是订阅低价国家
	isSubLowCountry := subLowCountryCfg[userInfo.CountryCode]
	if isSubLowCountry {
		productInfoType = model.ProductInfoTypeContentVipLowCountry
	}
	isAudit := userInfo.IsAudit()
	if isAudit {
		imgList = sVipDialogImgItem.AuditList
	}
	productInfoList := global.AppInfoTypeListMap[appId][productInfoType].ProductInfoList
	productList := global.AppInfoTypeListMap[appId][productInfoType].ProductList
	if len(productList) == 0 {
		return
	}
	productIDCheckedMap := map[uint]bool{}
	for _, i := range productInfoList {
		productIDCheckedMap[i.ProductID] = i.Checked
	}
	for _, product := range productList {
		var days int64 = 1
		svipItem := response.ContentVIPProduct{
			Id:          product.Id,
			Name:        product.Name,
			Description: product.Description,
			GooID:       product.GooID,
			IOSID:       product.IOSID,
			ProductType: product.ProductType,
			OriginPrice: product.OriginPrice,
			USDPrice:    product.USDPrice,
		}
		switch product.ProductType {
		case model.PTContentVipYear:
			svipItem.TopLabel = "BEST VALUE"
			days = 365
		case model.PTContentVipMonth:
			svipItem.TopLabel = "MOST POPULAR"
			days = 30
		case model.PTContentVipWeek:
			days = 7
		default:
			days = 1
		}
		svipItem.Hot = productIDCheckedMap[product.Id]
		dayCost := product.USDPrice.Div(decimal.NewFromInt(days)).Round(2)
		svipItem.DayCost = dayCost
		svipItem.PriceTip = fmt.Sprintf("$%s/day", dayCost.String())
		svipItem.PayBtnText = fmt.Sprintf("Pay %s now", product.USDPrice.String())
		resProductList = append(resProductList, svipItem)
	}
	res.BgList = imgList
	res.Tip = GetContentVipDialogTip(c, appId)
	if isAudit {
		res.Tip = GetContentVipDialogTipAudit(c, appId)
	}
	app4IsOld := 1
	t2 := time.Date(2025, 4, 9, 0, 0, 0, 0, time.UTC)
	if userInfo.CreatedAt.After(t2) {
		app4IsOld = 2
	}
	verCompare391 := utils.CompareVersions(appVersion, "3.9.1")
	if appId == 4 && verCompare391 == 1 && app4IsOld == 2 {
		res.Tip = GetContentVipDialogTip2(c, appId)
	}
	v2Cfg := GetContentVipDialogV2Cfg(c, appId)
	res.TipV2 = v2Cfg
	if reqData.Position != 0 && !userInfo.IsAudit() {
		res.HalfDialog = true
		for i, bannerItem := range v2Cfg.WeekList {
			if bannerItem.Position == reqData.Position {
				res.TipV2Index = i
				break
			}
		}
	}
	res.ProductList = resProductList
	return
}

func GetContentVipDialogTip2(c context.Context, appId uint) (rdsCfg response.ContentVipDialogTip) {
	str := `{"tip_month":[{"content":"Unlimited AI image generation","icon":"https://images.aipersona.cloud/public/image/72a6225f-f4ca-4339-87fe-168a79d03de5.png"},{"content":"Unlock theme style image generation","icon":"https://images.aipersona.cloud/public/image/c48e9a07-6202-4645-924d-832d52038582.png"},{"content":"One-time gift of 200 coins for you","icon":"https://images.aipersona.cloud/public/image/508fadab-7ed4-4d91-b298-ab5cbcff0928.png"}],"tip_week":[{"content":"Unlimited AI image generation","icon":"https://images.aipersona.cloud/public/image/72a6225f-f4ca-4339-87fe-168a79d03de5.png"},{"content":"Unlock theme style image generation","icon":"https://images.aipersona.cloud/public/image/c48e9a07-6202-4645-924d-832d52038582.png"},{"content":"One-time gift of 100 coins for you","icon":"https://images.aipersona.cloud/public/image/508fadab-7ed4-4d91-b298-ab5cbcff0928.png"}],"tip_year":[{"content":"Unlimited AI image generation","icon":"https://images.aipersona.cloud/public/image/72a6225f-f4ca-4339-87fe-168a79d03de5.png"},{"content":"Unlock theme style image generation","icon":"https://images.aipersona.cloud/public/image/c48e9a07-6202-4645-924d-832d52038582.png"},{"content":"One-time gift of 300 coins for you","icon":"https://images.aipersona.cloud/public/image/508fadab-7ed4-4d91-b298-ab5cbcff0928.png"}],"title":"Enjoy photo generation"}`
	_ = json.Unmarshal([]byte(str), &rdsCfg)
	return
}

func (m *ProductService) ClearSubList(c *gin.Context, reqData *request.ClearSubReq) (res response.ClearSubListRes, err error) {
	appId := utils.GetAppID(c)
	productInfoList := global.AppInfoTypeListMap[appId][reqData.ProductInfoType].ProductInfoList
	productList := global.AppInfoTypeListMap[appId][reqData.ProductInfoType].ProductList
	if len(productList) == 0 {
		return
	}
	// 选中的商品
	productIDCheckedMap := map[uint]bool{}
	for _, i := range productInfoList {
		productIDCheckedMap[i.ProductID] = i.Checked
	}
	for _, product := range productList {
		// 兼容接口
		if product.Id == 328 && reqData.ProductInfoType == 22 {
			continue
		}

		productItem := response.ProductItem{
			Id:           product.Id,
			Name:         product.Name,
			Description:  product.Description,
			GooID:        product.GooID,
			IOSID:        product.IOSID,
			Amount:       product.Amount,
			RewardAmount: product.RewardAmount,
			OriginPrice:  product.OriginPrice,
			USDPrice:     product.USDPrice,
			Bonus:        product.Bonus,
			Hot:          false,
		}
		// 设置商品选中
		productItem.Hot = productIDCheckedMap[product.Id]
		res.ProductList = append(res.ProductList, productItem)
	}
	return
}

func (m *ProductService) ClearSubListV1(c *gin.Context, reqData *request.ClearSubReq) (res response.ClearSubListResV1, err error) {
	appId := utils.GetAppID(c)
	// appId = uint(14)
	userId := utils.GetDigitalUserID(c)
	// userId = uint(90001052)
	productInfoList := global.AppInfoTypeListMap[appId][reqData.ProductInfoType].ProductInfoList
	productList := global.AppInfoTypeListMap[appId][reqData.ProductInfoType].ProductList
	if len(productList) == 0 {
		return
	}
	// todo 通过用户id 查询这个用户是否周订阅
	var count int64

	// 执行查询
	err = global.DB.Table("orders as o").
		Joins("left join product as p on o.product_id = p.id").
		Where("o.deleted_at IS NULL").
		Where("o.digital_user_id = ?", userId).
		Where("p.product_type = ?", model.ProductInfoTypeSub).
		Where("o.order_status = ?", model.OSPayGoodSuccess).
		Count(&count).Error

	if err != nil {
		count = 1
	}
	// 会员权益
	var equityList []response.VipEquity
	equityList = append(equityList, response.VipEquity{
		IsHave:  true,
		Content: "Similar photo cleaning",
	})

	equityList = append(equityList, response.VipEquity{
		IsHave:  true,
		Content: "Similar video cleaning",
	})

	equityList = append(equityList, response.VipEquity{
		IsHave:  true,
		Content: "Slide cleaning, more focused",
	})

	equityList = append(equityList, response.VipEquity{
		IsHave:  true,
		Content: "Unlimited cleaning quantity",
	})

	res.VipEquityList = equityList
	today := time.Now()
	// 选中的商品
	productIDCheckedMap := map[uint]bool{}
	for _, i := range productInfoList {
		productIDCheckedMap[i.ProductID] = i.Checked
	}
	for _, product := range productList {
		subTitle := "Start Now"
		tag := ""
		renewDate := ""
		free := product.USDPrice
		// 如果是年订阅
		if product.ProductType == model.PTSubYear {
			// 计算续期时间 并展示续期价格
			// 获取今天的日期
			// 加一年
			oneYearLater := today.AddDate(1, 0, 0)
			// 格式化日期为 "January 16, 2025"
			renewDate = oneYearLater.Format("January 2, 2006")
			// 增加权益
			// 增加标签
			tag = "SAVE 80%"

		}
		// 如果是周订阅
		if product.ProductType == model.PTSubWeek {
			// 计算续期时间 并展示续期价格
			oneWeekLater := today.AddDate(0, 0, 7)
			// 格式化日期为 "January 16, 2025"
			renewDate = oneWeekLater.Format("January 2, 2006")
			// 如果是首次周订阅 需要展示免费价格  续期价格正常
			if count == 0 {
				free = decimal.NewFromInt(0)
				tag = "7 DAYS FREE"
				subTitle = "Start my 7-day free trial"
			}
		}
		productItem := response.ProductItemV1{
			Id:           product.Id,
			Name:         product.Name,
			Description:  product.Description,
			GooID:        product.GooID,
			IOSID:        product.IOSID,
			Amount:       product.Amount,
			RewardAmount: product.RewardAmount,
			OriginPrice:  product.OriginPrice,
			USDPrice:     product.USDPrice,
			Bonus:        product.Bonus,
			Hot:          false,
			RenewDate:    renewDate,
			Tag:          tag,
			Free:         free,
			SubTitle:     subTitle,
		}
		// 设置商品选中
		productItem.Hot = productIDCheckedMap[product.Id]
		res.ProductList = append(res.ProductList, productItem)
	}
	return
}

func (m *ProductService) SubProductListV3(c *gin.Context, reqData *request.GetSubListReq) (res response.GetSubListRespV2, err error) {
	var (
		isSubLowCountry bool
		userInfo        *model.DigitalUser
		userService     DigitalUserService
		now             = time.Now()
		isDown          bool
		originInfoType  = model.ProductInfoTypeSubLv2
		productInfoType = model.ProductInfoTypeSubLv2
		userId          = utils.GetDigitalUserID(c)
		rKey            = global.GetSubDialogHashKey(userId)
		secondKey       = fmt.Sprintf(global.SUB_DIALOG_PAGE_COUNT, reqData.PageName)
	)
	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	verCompare390 := utils.CompareVersions(userInfo.Version, "3.9.0")
	// 用户已注册天数
	registerDays := utils.DaysBetween(userInfo.CreatedAt, now)
	// 获取订阅低价国家配置
	subLowCountryCfg := GetSubLowCountry(c)
	// 判断用户是否是订阅低价国家
	isSubLowCountry = subLowCountryCfg[userInfo.CountryCode]
	if isSubLowCountry {
		productInfoType = model.ProductInfoTypeSubLowCountryLv2
		originInfoType = model.ProductInfoTypeSubLowCountryLv2
	}

	// 获取商品信息查询参数
	subProductListParams := global.SubProductListParams{
		IsAuditMode:   userInfo.IsAudit(),
		IsDialogCount: true,
		Page:          reqData.PageName,
	}
	// 获取订阅降价配置
	vipDownCfg := GetVipDialogDownConfig(c)
	// 降价方案是开启状态
	if vipDownCfg.PlanSwitch {
		// 查询用户订阅弹窗次数
		subDialogShowCount, _ := global.REDIS.HGet(c, rKey, secondKey).Int()
		if subDialogShowCount == 0 {
			global.REDIS.Expire(c, rKey, global.SUB_DIALOG_HASH_KEY_EXPIRE*time.Second)
		}
		strategyCount := vipDownCfg.AfterShowCount
		if registerDays == 0 {
			strategyCount = vipDownCfg.D0ShowCount
		}
		if subDialogShowCount >= strategyCount {
			isDown = true
			subProductListParams.IsDialogCount = true
			productInfoType = model.ProductInfoTypeSubLv1
			// 如果是订阅低价国家, 降级为订阅低价国家的配置
			if isSubLowCountry {
				productInfoType = model.ProductInfoTypeSubLowCountryLv1
			}
		}
	}

	// 根据productInfoType获取商品信息
	subProductListParams.InfoType = productInfoType
	// 审核模式用户不执行降价
	if userInfo.IsAudit() {
		subProductListParams.InfoType = model.ProductInfoTypeSubAudit
		res, err = m.SubProductList(c, subProductListParams, userInfo, reqData)
	} else {
		if isDown {
			// 降价逻辑
			res, err = m.PriceOff(c, originInfoType, productInfoType, reqData.PageName, userInfo, reqData)
		} else {
			// 非降价逻辑
			res, err = m.SubProductList(c, subProductListParams, userInfo, reqData)
		}
	}
	if err != nil {
		return
	}
	v2Cfg := GetVipDialogV2Cfg(c, userInfo.AppID)
	if verCompare390 >= 0 {
		v2Cfg = GetVipDialogV390Cfg(c, userInfo.AppID)
	}
	v2Cfg.BgIcon = utils.S3Url(v2Cfg.BgIcon, userInfo.AppID)
	for i, cfgItem := range v2Cfg.WeekList {
		v2Cfg.WeekList[i].Icon = utils.S3Url(cfgItem.Icon, userInfo.AppID)
		v2Cfg.WeekList[i].Img = utils.S3Url(cfgItem.Img, userInfo.AppID)
	}
	for i, cfgItem := range v2Cfg.MonthList {
		v2Cfg.MonthList[i].Icon = utils.S3Url(cfgItem.Icon, userInfo.AppID)
		v2Cfg.MonthList[i].Img = utils.S3Url(cfgItem.Img, userInfo.AppID)
	}
	for i, cfgItem := range v2Cfg.YearList {
		v2Cfg.YearList[i].Icon = utils.S3Url(cfgItem.Icon, userInfo.AppID)
		v2Cfg.YearList[i].Img = utils.S3Url(cfgItem.Img, userInfo.AppID)
	}
	res.TipV2 = v2Cfg
	if reqData.Position != 0 && !userInfo.IsAudit() {
		res.HalfDialog = true
		for i, bannerItem := range v2Cfg.WeekList {
			if bannerItem.Position == reqData.Position {
				res.TipV2Index = i
				break
			}
		}
	}

	return
}
