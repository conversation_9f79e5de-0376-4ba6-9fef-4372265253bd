package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"aimsg-server/utils"
	"context"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"math/rand"
	"sort"
	"time"
)

type AiRoleService struct{}

func (m *AiRoleService) Recent3DayNewAiRole(num int) (aiRoleList []*model.AiRole, err error) {
	db := global.DB.Model(&model.AiRole{})
	dayStart := utils.StartOfDay()
	dayStart = dayStart.AddDate(0, 0, -3)
	err = db.Where("created_at >= ? AND role_type != ?", dayStart, model.AiRoleTypePrivate).Order("created_at DESC").Limit(num).Find(&aiRoleList).Error
	return
}

func (m *AiRoleService) SortNew() {
	type TmpOne struct {
		AiId      uint             `json:"ai_id"`
		RoleId    string           `json:"role_id"`
		RoleType  model.AiRoleType `json:"role_type"`
		Nickname  string           `json:"nickname"`
		Gender    model.GenderType `json:"gender"`
		Avatar    string           `json:"avatar"`
		PayAmount decimal.Decimal  `json:"pay_amount"`
		ChatCount int64            `json:"chat_count"`
		Quotient  decimal.Decimal  `json:"quotient"`
		// QuotientFloat float64          `json:"quotient_float"`
	}
	type TmpTwo struct {
		ReceiverId         string `json:"receiver_id"        bson:"receiver_id"`
		UniqueSendersCount int64  `json:"uniqueSendersCount" bson:"uniqueSendersCount"`
	}
	var (
		err       error
		resList   []*TmpOne
		chatList  []TmpTwo
		aiSortSer AiSortStatisticsService
		countMap  = map[string]int64{}
		ctx       = context.Background()
		dayStart  = utils.StartOfDay()
	)
	startTime := dayStart.AddDate(0, 0, -7)
	endTime := dayStart
	// err = global.DB.Model(&model.Order{}).Raw("SELECT o.ai_role_id as ai_id, a.role_id, a.role_type, a.nickname, a.gender, a.avatar, SUM(o.pay_amount) AS pay_amount FROM orders o LEFT JOIN ai_role a ON o.ai_role_id = a.id WHERE o.deleted_at IS NULL AND a.role_type != 2 AND a.examine = 1 AND (o.created_at BETWEEN ? AND ?) AND o.order_status = 201 AND o.ai_role_id != 0 GROUP BY o.ai_role_id, a.role_id ORDER BY pay_amount DESC", startTime, endTime).Find(&resList).Error
	// 1_11 日 修改为 flower_consume_record 表查询数据 然后再计算
	err = global.DB.Model(&model.Order{}).Raw("select a.id as ai_id, a.role_id, a.role_type, a.nickname, a.gender, a.avatar, ABS(SUM(r.consume_amount)) AS pay_amount from flower_consume_record r left join digital_clone.ai_role a on a.role_id = r.ai_role_id where  r.deleted_at is null and a.role_type != 2  and a.examine = 1 and (r.created_at BETWEEN ? AND ?) and r.ai_role_id != 0 group by  r.ai_role_id,a.role_id order by pay_amount desc;", startTime, endTime).Find(&resList).Error

	if err != nil {
		global.LOG.Error("Find error", zap.Error(err))
		return
	}
	pipeline := mongo.Pipeline{
		{{"$match", bson.D{
			{"send_time", bson.D{
				{"$gte", startTime.UnixMilli()},
				{"$lt", endTime.UnixMilli()},
			}},
		}}},
		{{"$group", bson.D{
			{"_id", "$receiver_id"},
			{"uniqueSenders", bson.D{{"$addToSet", "$sender_id"}}},
		}}},
		{{"$project", bson.D{
			{"_id", 0},
			{"receiver_id", "$_id"},
			{"uniqueSendersCount", bson.D{{"$size", "$uniqueSenders"}}},
		}}},
	}
	if err = global.MONGO.Aggregate(ctx, pipeline).All(&chatList); err != nil {
		global.LOG.Error("Aggregate error", zap.Error(err))
		return
	}
	for _, c := range chatList {
		countMap[c.ReceiverId] = c.UniqueSendersCount
	}
	for _, tOne := range resList {
		tOne.ChatCount = countMap[tOne.RoleId]
		if tOne.ChatCount > 0 {
			tOne.Quotient = tOne.PayAmount.Div(decimal.NewFromInt(tOne.ChatCount))
			// tOne.QuotientFloat = tOne.Quotient.Round(2).InexactFloat64()
		}
	}
	sort.Slice(resList, func(i, j int) bool {
		// Quotient 降序
		return resList[i].Quotient.LessThanOrEqual(resList[j].Quotient)
	})
	// 结果统计到表
	for _, t := range resList {
		tObj := &model.AiSortStatistics{
			StartTime:  startTime,
			EndTime:    endTime,
			AiId:       t.AiId,
			AiRoleId:   t.RoleId,
			RoleType:   t.RoleType,
			Nickname:   t.Nickname,
			Gender:     t.Gender,
			Avatar:     t.Avatar,
			RevenueUSD: t.PayAmount,
			ChatCount:  t.ChatCount,
			Quotient:   t.Quotient.Round(2),
		}
		if err = aiSortSer.Save(ctx, tObj); err != nil {
			global.LOG.Error("Save error", zap.Error(err))
			continue
		}
	}

	maxSort := 1000 + len(resList)
	for i, tOne := range resList {
		err = global.DB.Model(&model.AiRole{}).Where("id = ?", tOne.AiId).Update("sort", 1000+i+1).Error
		if err != nil {
			global.LOG.Error("Update error", zap.Error(err))
		}
	}
	// 查询最近三天内是否有上新的角色,按照时间倒序,查询前2个
	var newAiRoles []*model.AiRole
	newAiRoles, err = m.Recent3DayNewAiRole(2)
	if err != nil {
		return
	}
	for i, aiRole := range newAiRoles {
		err = global.DB.Model(&model.AiRole{}).Where("id = ?", aiRole.Id).Update("sort", maxSort+i+1).Error
		if err != nil {
			global.LOG.Error("Update error", zap.Error(err))
		}
	}
	return
}

func (m *AiRoleService) Create(c *gin.Context, reqData *model.AiRole) (err error) {
	err = global.DB.Model(&model.AiRole{}).Create(reqData).Error
	if err == nil {
		m.SetCache(c, reqData)
	}
	return
}

func (m *AiRoleService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.AiRole{}).Where("id = ?", id).Delete(&model.AiRole{}).Error
	return
}

func (m *AiRoleService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.AiRole{}).Where("id IN (?)", reqData.Ids).Delete(&model.AiRole{}).Error
	return
}

func (m *AiRoleService) Update(ctx context.Context, reqData *model.AiRole) (err error) {
	err = global.DB.Model(&model.AiRole{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	if err == nil {
		m.DelCache(ctx, reqData.RoleID)
	}
	return
}

func (m *AiRoleService) GetById(ctx context.Context, id uint) (res *model.AiRole, err error) {
	err = global.DB.Model(&model.AiRole{}).Where("id = ?", id).First(&res).Error
	if err == nil {
		m.SetCache(ctx, res)
	}
	return
}

func (m *AiRoleService) GetCountByUserId(ctx context.Context, userId uint) (genCount int64, err error) {
	err = global.DB.Model(&model.AiRole{}).Where("digital_user_id = ? AND role_type = ?", userId, model.AiRoleTypePrivate).Count(&genCount).Error
	return
}

func (m *AiRoleService) GetByRoleId(roleId string) (res *model.AiRole, err error) {
	ctx := context.Background()
	res, err = m.GetCache(ctx, roleId)
	if err != nil {
		err = global.DB.Model(&model.AiRole{}).Where("role_id = ?", roleId).First(&res).Error
		if err == nil {
			m.SetCache(ctx, res)
		}
	}
	return
}

func (m *AiRoleService) CacheKey(aiRoleId string) string {
	return fmt.Sprintf("ai_role_info:%s", aiRoleId)
}

func (m *AiRoleService) SetCache(ctx context.Context, record *model.AiRole) {
	var (
		err           error
		recordBytes   []byte
		cacheRedisKey = m.CacheKey(record.RoleID)
	)
	recordBytes, err = json.Marshal(record)
	if err != nil {
		global.LOG.Error("SetCache", zap.Error(err))
	}
	recordStr := string(recordBytes)
	if err = global.REDIS.SetEx(ctx, cacheRedisKey, recordStr, time.Hour).Err(); err != nil {
		global.LOG.Error("SetCache", zap.Error(err))
	}
	return
}

func (m *AiRoleService) DelCache(ctx context.Context, aiRoleId string) {
	var (
		cacheRedisKey = m.CacheKey(aiRoleId)
	)
	global.REDIS.Del(ctx, cacheRedisKey)
	return
}

func (m *AiRoleService) GetCache(ctx context.Context, aiRoleId string) (record *model.AiRole, err error) {
	var (
		cacheRedisStr string
		cacheRedisKey = m.CacheKey(aiRoleId)
	)
	if cacheRedisStr, err = global.REDIS.Get(ctx, cacheRedisKey).Result(); err != nil {
		return
	}
	if err = json.Unmarshal([]byte(cacheRedisStr), &record); err != nil {
		return
	}
	return
}

func (m *AiRoleService) UnscopedGetByRoleId(roleId string) (res *model.AiRole, err error) {
	err = global.DB.Model(&model.AiRole{}).Where("role_id = ?", roleId).Unscoped().First(&res).Error
	return
}

func (m *AiRoleService) UnscopedGetById(roleId uint) (res *model.AiRole, err error) {
	err = global.DB.Model(&model.AiRole{}).Where("id = ?", roleId).Unscoped().First(&res).Error
	return
}

func (m *AiRoleService) InitDefaultAiRole() {
	var (
		err          error
		maleAiRole   *model.AiRole
		femaleAiRole *model.AiRole
	)
	maleAiRole, err = m.UnscopedGetByRoleId("998")
	if err != nil {
		global.LOG.Error("InitDefaultAiRole error", zap.Error(err))
		return
	}
	femaleAiRole, err = m.UnscopedGetByRoleId("999")
	if err != nil {
		global.LOG.Error("InitDefaultAiRole error", zap.Error(err))
		return
	}
	global.DefaultMaleAiRole = maleAiRole
	global.DefaultFemaleAiRole = femaleAiRole
}

func (m *AiRoleService) SearchOne(reqData *model.AiRole) (res *model.AiRole, err error) {
	db := global.DB.Model(&model.AiRole{})
	if reqData.Id != 0 {
		db = db.Where("id = ?", reqData.Id)
	}
	if reqData.RoleID != "" {
		db = db.Where("role_id = ?", reqData.RoleID)
	}
	if reqData.RoleType != 0 {
		db = db.Where("role_type = ?", reqData.RoleType)
	}
	if reqData.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", reqData.DigitalUserId)
	}
	if reqData.Gender != 0 {
		db = db.Where("gender = ?", reqData.Gender)
	}
	err = db.First(&res).Error
	return
}

func (m *AiRoleService) GetAll() (resList []*model.AiRole, err error) {
	err = global.DB.Model(&model.AiRole{}).Find(&resList).Error
	return
}

func (m *AiRoleService) GetList(info req.AiRoleSearch, realAnchorGroup bool) (resList []*model.AiRole, total int64, err error) {
	var (
		db = global.DB.Model(&model.AiRole{})
	)
	//  2025-01-06 孙宏伟需求 list ai 新增判断
	if info.AppID == 13 {
		info.AppID = 0
		if realAnchorGroup {
			db = db.Where("real_anchor_id > 0")
		}
		if !realAnchorGroup {
			db = db.Where("real_anchor_id is null")
		}
	}

	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.RoleID != "" {
		db = db.Where("role_id LIKE ?", "%"+info.RoleID+"%")
	}
	if info.Nickname != "" {
		db = db.Where("nickname LIKE ?", "%"+info.Nickname+"%")
	}
	if info.Gender != 0 {
		db = db.Where("gender = ?", info.Gender)
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.Examine != 0 {
		db = db.Where("examine = ?", info.Examine)
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if info.RealAnchorId != 0 {
		db = db.Where("real_anchor_id = ?", info.RealAnchorId)
	}
	if info.RoleType != 0 {
		db = db.Where("role_type = ?", info.RoleType)
	}
	if info.Enable != 0 {
		db = db.Where("enable = ?", info.Enable)
	}
	if info.NotRoleType != 0 {
		db = db.Where("role_type != ?", info.NotRoleType)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.IsSortUser { // 新用户排序
		db = db.Order("sort_user desc, id desc")
	} else {
		if info.SortProp != "" {
			db = db.Order("sort DESC, id desc")
		} else {
			db = db.Order("id DESC")
		}
	}

	err = db.Find(&resList).Error
	// 设置缓存
	if len(resList) == 0 {
		return
	}
	return
}

// 随机改变AI角色页(Discover)的数据顺序
func (m *AiRoleService) ChangePublicRoleSort() {
	var (
		aiRoleList []model.AiRole
		err        error
	)
	var haveSceneAiRoleList []model.AiRole

	err = global.DB.Model(&model.AiRole{}).Where("role_type IN (?)", []model.AiRoleType{model.AiRoleTypePublic, model.AiRoleTypeHot}).Find(&aiRoleList).Error
	if err != nil {
		global.LOG.Error("ChangePublicRoleSort select 失败", zap.Error(err))
		return
	}
	// 更新数据库
	for _, airole := range aiRoleList {
		// 查询AI角色是否有场景信息,有的话,加到有场景信息的AI角色列表里
		var aiScenesInfoCount int64
		if err = global.DB.Model(&model.AiScenes{}).Where("ai_role_id = ?", airole.Id).Count(&aiScenesInfoCount).Error; err != nil {
			global.LOG.Error("ChangePublicRoleSort select 失败", zap.Error(err))
			continue
		}
		if aiScenesInfoCount > 0 && airole.RolePlay == model.SwitchStatusNormal {
			haveSceneAiRoleList = append(haveSceneAiRoleList, airole)
		}
		err = global.DB.Model(&model.AiRole{}).Where("id = ?", airole.Id).Update("sort", rand.Intn(len(aiRoleList))).Error
		if err != nil {
			global.LOG.Error("ChangePublicRoleSort update 失败", zap.Error(err))
		}
	}
	// 有场景信息的角色,随机排序
	randMin := len(aiRoleList)
	randMax := randMin + len(haveSceneAiRoleList)*10
	for _, role := range haveSceneAiRoleList {
		randSort := utils.RandomInt(randMin, randMax)
		err = global.DB.Model(&model.AiRole{}).Where("id = ?", role.Id).Update("sort", randSort).Error
		if err != nil {
			global.LOG.Error("ChangePublicRoleSort update 失败", zap.Error(err))
		}
	}
}
