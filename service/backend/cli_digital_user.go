package backend

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/model/cli/td"
	"aimsg-server/service/backend/call"
	"aimsg-server/utils"
	"aimsg-server/utils/agora"
	"aimsg-server/utils/ilivedata"
	"aimsg-server/utils/notify"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"path/filepath"
	"strings"
	"time"

	"firebase.google.com/go/v4/auth"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"git.costnovel.com/center/middleware-sdk/fake"
	"git.costnovel.com/center/middleware-sdk/risk"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

func (m *DigitalUserService) decodeToken(deviceToken string) (tokenObj *response.DeviceToken, err error) {
	// 解析token
	token, err := jwt.ParseWithClaims(deviceToken, &jwt.MapClaims{}, func(token *jwt.Token) (i interface{}, e error) {
		return []byte(global.CONFIG.JWT.SigningKey), nil
	})
	if err != nil {
		return
	}
	mapCliPoint, mapCliPointOk := token.Claims.(*jwt.MapClaims)
	if !mapCliPointOk {
		err = fmt.Errorf("token.Claims.(*jwt.MapClaims) error")
		return
	}
	mapCli := *mapCliPoint
	deviceIdAny, deviceIdAnyExist := mapCli["device_id"]
	if !deviceIdAnyExist {
		err = fmt.Errorf("device_id not exist")
		return
	}
	deviceId, deviceIdOk := deviceIdAny.(string)
	if !deviceIdOk {
		err = fmt.Errorf("device_id not string")
		return
	}
	tokenObj = &response.DeviceToken{
		DeviceId: deviceId,
	}
	return
}

func (m *DigitalUserService) CreateUserToken(userInfo *model.DigitalUser) (token string, err error) {
	cliClaim := utils.JwtCliObj.CreateCliClaims(userInfo)
	token, err = utils.JwtCliObj.CreateToken(&cliClaim)
	return
}

func (m *DigitalUserService) usernameReg(c *gin.Context, req request.RegisterReq) (loginRes response.SocialLoginRes, err error) {
	var (
		agoraToken     string
		loginToken     string
		usernameCount  int64
		profileService DigitalUserProfileService
		userInfo       *model.DigitalUser
		userProfile    *model.DigitalUserProfile
		appVersion     = c.GetHeader("App-Version")
		distinctID     = c.GetHeader("Distinct-ID")
		headerIP       = c.GetHeader("User-Ip")
		appId          = c.GetUint(global.GinContextAppId)
		loginIp        = c.ClientIP()
		nowTime        = time.Now()
	)
	if req.Username == "" {
		err = fmt.Errorf("username is empty")
		return
	}
	if req.Password == "" {
		err = fmt.Errorf("password is empty")
		return
	}
	if headerIP != "" {
		loginIp = headerIP
	}
	if loginIp == "" {
		err = fmt.Errorf("login ip is empty")
		return
	}
	// 获取App信息
	appInfo, appInfoExist := global.AppMap[appId]
	if !appInfoExist {
		err = fmt.Errorf("app(%d) not found", appId)
		return
	}
	regFlo := appInfo.DetailConfig.Data().RegFlo
	// 判断用户名是否存在
	if usernameCount, err = m.UsernameCount(req.Username); err != nil {
		return
	}
	if usernameCount > 0 {
		err = fmt.Errorf("username is exist")
		return
	}
	countryCode := utils.GetCountryCode(c)
	userGroup := m.randUserGroup()
	// 创建用户
	userInfo = &model.DigitalUser{
		AppID:        appId,
		UserGroup:    userGroup,
		Avatar:       "",
		Gender:       0,
		Nickname:     utils.RandomLetter(1),
		LoginType:    model.LoginTypeUsername,
		Birthday:     datatypes.Date{},
		Username:     req.Username,
		Password:     req.Password,
		DeviceID:     "",
		LoginIP:      loginIp,
		CountryCode:  countryCode,
		Version:      appVersion,
		FcmToken:     "",
		Language:     "",
		LoginAt:      nowTime,
		ActiveAt:     nowTime,
		UserStatus:   model.UserStatusNormal,
		SubStatus:    model.SubStatusNo,
		SVipStatus:   model.SubStatusNo,
		SVipExpire:   time.Time{},
		SubType:      0,
		SubExpire:    time.Time{},
		AdjustIdData: datatypes.JSONType[model.AdjustIdData]{},
	}
	if distinctID != "" {
		userInfo.DistinctID = distinctID
	}

	// 2.从现在开始所有在这个app下注册的新账号都是审核模式
	if userInfo.AppID == 10 {
		userInfo.UserStatus = model.UserStatusAudit
	}

	if err = m.Create(c, userInfo); err != nil {
		return
	}
	m.TdSetUserGroup(userInfo)
	userProfile = &model.DigitalUserProfile{
		DigitalUserId: userInfo.Id,
		FlowerAmount:  regFlo,
		OtherInfo:     datatypes.NewJSONType(model.UserOtherInfo{}),
	}
	// 创建用户profile
	if err = profileService.Create(userProfile); err != nil {
		return
	}

	// 生成声网token
	if agoraToken, err = agora.GenerateAgoraToken(appInfo.AgoraData.Data().AppId, appInfo.AgoraData.Data().AppCert, fmt.Sprintf("%d", userInfo.Id), global.RtmExpireSec); err != nil {
		return
	}
	loginToken, err = m.CreateUserToken(userInfo)
	// 返回登录信息
	loginRes = response.SocialLoginRes{
		Token: loginToken,
		Rtm: response.SocialLoginRtm{
			Token:         agoraToken,
			ExpireSeconds: global.RtmExpireSec,
		},
		User: m.GetUserInfo(c, userInfo, userProfile, false),
	}
	return
}

func (m *DigitalUserService) randUserGroup() int {
	// 生成一个随机小数
	randFloat := utils.RandFloat64()
	if randFloat >= 0.5 {
		return 1
	} else {
		return 2
	}
}

func (m *DigitalUserService) TdSetUserGroup(userInfo *model.DigitalUser) {
	if userInfo.UserGroup != 0 && (userInfo.DistinctID != "" || userInfo.Id > 0) {
		go TdPointerUserSet(userInfo, map[string]interface{}{
			// 2025-06-27 暂时不统计用户组
			// "user_group": userInfo.UserGroup,
		})
	}
}

func (m *DigitalUserService) SocialLogin(c *gin.Context, req request.SocialLoginReq) (loginRes response.SocialLoginRes, err error) {
	var (
		agoraToken       string
		loginToken       string
		googleIDToken    *auth.Token
		googleUserRecord *auth.UserRecord
		profileService   DigitalUserProfileService
		deviceTokenObj   *response.DeviceToken
		userInfo         *model.DigitalUser
		userSearchReq    *model.DigitalUser
		userProfile      *model.DigitalUserProfile
		appParams        *request.AppParamsHeader
		appVersion       = c.GetHeader("App-Version")
		appParamsStr     = c.GetHeader("App-Params")
		distinctID       = c.GetHeader("Distinct-ID")
		headerDeviceID   = c.GetHeader("Device-Id")
		headerIP         = c.GetHeader("User-Ip")
		appId            = c.GetUint(global.GinContextAppId)
		loginIp          = c.ClientIP()
		nowTime          = time.Now()
	)

	if appId == 129 {
		global.LOG.Info("SocialLogin param start", zap.Any("req", req))
	}

	if headerIP != "" {
		loginIp = headerIP
	}
	if loginIp == "" {
		err = fmt.Errorf("login ip is empty")
		return
	}
	global.LOG.Info("startLogin", zap.Any("IP", loginIp), zap.Any("appId", appId), zap.Any("deviceId", req.DeviceId))
	ipInfo, ipGeoErr := utils.GetIpInfoByIpGeoApi(loginIp)
	if ipGeoErr != nil {
		global.LOG.Error("GetIpInfoWithCache error", zap.Error(ipGeoErr))
		ddNotifyMsg := fmt.Sprintf(`
## 中台查询IP失败
- IP地址: %s
- [中台查询: %s](https://game-geo.51payment.live/geo/ip/query?ip=%s)
- [IP-API查询: %s](https://ip-api.com/#%s)
- [ipinfo.io查询: %s](https://ipinfo.io/widget/demo/%s)
- [ipinfo.info查询: %s](https://ipinfo.info/ip_api.php?ip=%s)
- [iplocation.net查询: %s](https://api.iplocation.net/?ip=%s)
- 失败原因: %v
`,
			loginIp,
			loginIp, loginIp,
			loginIp, loginIp,
			loginIp, loginIp,
			loginIp, loginIp,
			loginIp, loginIp,
			ipGeoErr,
		)
		SendDDNotice(ddNotifyMsg)
	}
	if appParamsStr != "" {
		paramsBytes, b64Err := base64.StdEncoding.DecodeString(appParamsStr)
		if b64Err == nil {
			if jsonErr := json.Unmarshal(paramsBytes, &appParams); jsonErr != nil {
				global.LOG.Error("SocialLogin json unmarshal error", zap.Error(jsonErr))
			}
		}
	}
	// 获取App信息
	appInfo, _ := global.AppMap[appId]
	regFlo := appInfo.DetailConfig.Data().RegFlo
	switch req.LoginType {
	case model.LoginTypeVisitor:
		// 游客登录
		if req.DeviceToken == "" {
			err = fmt.Errorf("device token is empty")
			return
		}
		// 解析device token
		if deviceTokenObj, err = m.decodeToken(req.DeviceToken); err != nil {
			return
		}
		tokenDeviceId := deviceTokenObj.DeviceId
		if tokenDeviceId != req.DeviceId {
			err = fmt.Errorf("device_id is not match")
			return
		}
		userSearchReq = &model.DigitalUser{AppID: appId, DeviceID: req.DeviceId}
		if userInfo, err = m.FindOne(userSearchReq); err != nil {
			if !errors.Is(err, errs.UserNotFoundErr) {
				return
			}
		}
	case model.LoginTypeUsername:
		// 用户名登录
		if req.Username == "" {
			err = fmt.Errorf("username is empty")
			return
		}
		if req.Password == "" {
			err = fmt.Errorf("password is empty")
			return
		}
		userSearchReq = &model.DigitalUser{AppID: appId, Username: req.Username}
		if userInfo, err = m.FindOne(userSearchReq); err != nil {
			if !errors.Is(err, errs.UserNotFoundErr) {
				return
			}
			// 用户不存在,然后根据设备ID查询用户
			userSearchReq = &model.DigitalUser{AppID: appId, DeviceID: req.DeviceId}
			if userInfo, err = m.FindOne(userSearchReq); err != nil {
				if !errors.Is(err, errs.UserNotFoundErr) {
					return
				}
			}
		}
	case model.LoginTypeApple:
		fallthrough
	case model.LoginTypeGoogle:
		// Google登录
		if req.ThirdToken == "" {
			err = fmt.Errorf("third token is empty")
			return
		}
		authClient, authClientExist := global.AuthClientMap[appId]
		if !authClientExist {
			err = fmt.Errorf("auth client not exist")
			return
		}
		googleIDToken, err = authClient.VerifyIDToken(c, req.ThirdToken)
		if err != nil {
			return
		}
		googleUserRecord, err = authClient.GetUser(c, googleIDToken.UID)
		if err != nil {
			return
		}
		userSearchReq = &model.DigitalUser{AppID: appId, ThirdUserId: googleIDToken.UID}
		if userInfo, err = m.FindOne(userSearchReq); err != nil {
			if !errors.Is(err, errs.UserNotFoundErr) {
				return
			}
		}
	default:
		err = fmt.Errorf("login_type is error")
		return
	}
	isNew := false
	if userInfo == nil || userInfo.Id == 0 {
		isNew = true
		// 创建用户
		userGroup := m.randUserGroup()
		// 用户不存在
		userInfo = &model.DigitalUser{
			AppID:       appId,
			UserGroup:   userGroup,
			LoginType:   req.LoginType,
			Nickname:    utils.RandomLetter(1),
			LoginIP:     loginIp,
			CountryCode: ipInfo.Data.Areacode,
		}
		if distinctID != "" {
			userInfo.DistinctID = distinctID
		}
		switch req.LoginType {
		case model.LoginTypeVisitor:
			userInfo.DeviceID = req.DeviceId
		case model.LoginTypeUsername:
			userInfo.Username = req.Username
			userInfo.Password = req.Password
			userInfo.DeviceID = headerDeviceID
		case model.LoginTypeApple:
			fallthrough
		case model.LoginTypeGoogle:
			if googleUserRecord != nil {
				userInfo.ThirdUserId = googleUserRecord.UID
				if googleUserRecord.UserInfo != nil {
					userInfo.Email = googleUserRecord.UserInfo.Email
					userInfo.Avatar = googleUserRecord.UserInfo.PhotoURL
				}
				if googleUserRecord.UserInfo.DisplayName != "" {
					userInfo.Nickname = googleUserRecord.UserInfo.DisplayName
				}
				userInfo.DeviceID = headerDeviceID
			}
		default:
		}
		// 创建用户
		if err = m.Create(c, userInfo); err != nil {
			return
		}
		m.TdSetUserGroup(userInfo)
		userProfile = &model.DigitalUserProfile{
			DigitalUserId: userInfo.Id,
			FlowerAmount:  regFlo,
			OtherInfo:     datatypes.NewJSONType(model.UserOtherInfo{}),
		}
		// 创建用户profile
		if err = profileService.Create(userProfile); err != nil {
			return
		}
		if userInfo.AppID == 16 {
			// 保存消费记录 用户费用记录要加回去
			var msgService MsgService
			_ = msgService.SaveVideoSimplifyConsumeRecord(userInfo.Id, regFlo, model.VideoSimplifyConsumeTypeRegister, 0, 0)
		}
		if userInfo.DistinctID != "" {
			go TdPointer(userInfo, userProfile, td.ServerEvent, map[string]interface{}{
				"event_tag":         "coins_consume",
				"coins_consume_num": regFlo,
				"source":            "register_get",
			})
		}
		if userInfo.AppID == 7 && utils.CompareVersions(appVersion, "3.7.9") == 1 {
			expiresDate := time.Now().AddDate(20, 0, 0)
			userInfo.ContentVipStatus = model.SubStatusIng
			userInfo.ContentVipExpire = expiresDate
			userInfo.ChatVipStatus = model.SubStatusIng
			userInfo.ChatVipExpire = expiresDate
			userInfo.ChatVipType = model.SubTypeYear
			editMap := map[string]interface{}{
				"content_vip_status": model.SubStatusIng,
				"content_vip_expire": expiresDate,
				"chat_vip_status":    model.SubStatusIng,
				"chat_vip_expire":    expiresDate,
				"chat_vip_type":      model.SubTypeYear,
			}
			service := DigitalUserService{}
			_ = service.UpdateByMap(c, userInfo.Id, editMap)
		}
		userInfo.CreatedAt = time.Now()
	} else {
		userIdStr := fmt.Sprintf("%d", userInfo.Id)
		if userInfo.UserGroup == 0 {
			userGroup := m.randUserGroup()
			userInfo.UserGroup = userGroup
		}
		if distinctID != "" {
			userInfo.DistinctID = distinctID
		}
		m.TdSetUserGroup(userInfo)
		if req.LoginType == model.LoginTypeUsername {
			userInfo.DeviceID = headerDeviceID
		}
		if req.LoginType == model.LoginTypeUsername && userInfo.Username != userIdStr && userInfo.Password != req.Password {
			err = errs.ErrPassword
			return
		}
		switch req.LoginType {
		case model.LoginTypeApple:
			fallthrough
		case model.LoginTypeGoogle:
			if googleUserRecord != nil && googleUserRecord.UserInfo != nil {
				userInfo.Email = googleUserRecord.UserInfo.Email
				// userInfo.Nickname = googleUserRecord.UserInfo.DisplayName
				userInfo.Avatar = googleUserRecord.UserInfo.PhotoURL
			}
		default:

		}
		// 用户存在
		if userProfile, err = profileService.GetByUserId(userInfo.Id); err != nil {
			return
		}
	}
	if appId == 13 && userInfo.Avatar == "" {
		userInfo.Avatar = "public/image/default13.png"
	}

	if req.LoginType == model.LoginTypeUsername {
		// 用户名登录,但是用户名不一致,更新用户名
		if userInfo.Username != req.Username {
			userInfo.Username = req.Username
			userInfo.Password = req.Password
		}
	}
	if utils.CheckIsEmail(userInfo.Username) {
		userInfo.Email = userInfo.Username
	}
	if !userInfo.SubExpire.IsZero() && userInfo.SubExpire.Before(nowTime) {
		userInfo.SubStatus = model.SubStatusExpire
		userProfile.Nsfw = false
	}

	if !userInfo.SVipExpire.IsZero() && userInfo.SVipExpire.Before(nowTime) {
		userInfo.SVipStatus = model.SubStatusExpire
	}
	if !userInfo.ChatVipExpire.IsZero() && userInfo.ChatVipExpire.Before(nowTime) {
		userInfo.ChatVipStatus = model.SubStatusExpire
	}
	if !userInfo.ContentVipExpire.IsZero() && userInfo.ContentVipExpire.Before(nowTime) {
		userInfo.ContentVipStatus = model.SubStatusExpire
	}
	if userInfo.AppID == 5 {
		userInfo.SubStatus = model.SubStatusIng
		userInfo.SubType = model.SubTypeYear
		userInfo.SubExpire = time.Date(2025, 10, 1, 0, 0, 0, 0, time.Local)
		userInfo.SVipStatus = model.SubStatusIng
		userInfo.SVipType = model.SubTypeYear
		userInfo.SVipExpire = time.Date(2025, 10, 1, 0, 0, 0, 0, time.Local)
	}
	// 设置用户的用户名为用户ID
	if userInfo.Username == "" {
		userInfo.Username = fmt.Sprintf("%d", userInfo.Id)
	}
	userInfo.Version = appVersion
	userInfo.LoginIP = loginIp
	userInfo.LoginAt = nowTime
	userInfo.ActiveAt = nowTime
	if userInfo.DeviceID == "" && headerDeviceID != "" {
		userInfo.DeviceID = headerDeviceID
	}
	// 更新用户国家码
	userCountryCode := ipInfo.Data.Areacode
	if userCountryCode != "" && (userInfo.CountryCode == "" || userInfo.CountryCode != userCountryCode) {
		userInfo.CountryCode = userCountryCode
	}
	// 检查是否是谷歌审核ip
	ipAudit := utils.CheckIpAudit(loginIp, ipInfo)
	// 使用中台的审核人员服务
	auditReq := risk.ReviewerDataReq{
		FromKey:   "4b2f5g9dA8lK",
		ClientIP:  loginIp,
		DeviceID:  userInfo.DeviceID,
		AndroidID: userInfo.DeviceID,
	}
	if appParams != nil {
		auditReq.GoogleID = appParams.GpsId
	}
	// ipAudit := utils.CheckIpAuditMiddleware(c, auditReq)
	// 如果是iOS应用,Isp或者Owner包含google,那么就不是审核用户
	ispHaveGoogle := strings.Contains(strings.ToLower(ipInfo.Data.Isp), "google")
	ownerHaveGoogle := strings.Contains(strings.ToLower(ipInfo.Data.Owner), "google")
	if appInfo.AppType == model.AppTypeIOS && (ispHaveGoogle || ownerHaveGoogle) {
		ipAudit = false
	}
	if ipAudit && !userInfo.IsAudit() {
		userInfo.UserStatus = model.UserStatusAudit
		// 钉钉通知
		go m.SendDDNotifyUserAudit(c, appInfo, userInfo, 2)
		go utils.SyncRiskDataMiddleware(c, auditReq)
	}
	// 如果是审核版本进来的,也设置为审核用户
	if appVersion == appInfo.AuditVersion && !userInfo.IsAudit() {
		userInfo.UserStatus = model.UserStatusAudit
		// 钉钉通知
		go m.SendDDNotifyUserAudit(c, appInfo, userInfo, 1)
		go utils.SyncRiskDataMiddleware(c, auditReq)
	}
	// 更新用户信息
	if err = m.Update(c, userInfo); err != nil {
		return
	}
	// 更新用户profile信息
	if err = profileService.Update(userProfile); err != nil {
		return
	}
	// 生成声网token
	if agoraToken, err = agora.GenerateAgoraToken(appInfo.AgoraData.Data().AppId, appInfo.AgoraData.Data().AppCert, fmt.Sprintf("%d", userInfo.Id), global.RtmExpireSec); err != nil {
		return
	}
	loginToken, err = m.CreateUserToken(userInfo)
	// 返回登录信息
	loginRes = response.SocialLoginRes{
		Token: loginToken,
		Rtm: response.SocialLoginRtm{
			Token:         agoraToken,
			ExpireSeconds: global.RtmExpireSec,
		},
		User: m.GetUserInfo(c, userInfo, userProfile, isNew),
	}
	endTime := time.Now()
	global.LOG.Info("SocialLoginResp ", zap.Any("loginResp", loginRes), zap.Any("timeConsuming(ms)", endTime.Sub(nowTime).Milliseconds()))
	return
}

func (m *DigitalUserService) Register(c *gin.Context, req request.RegisterReq) (regRes response.SocialLoginRes, err error) {
	switch req.RegType {
	case model.LoginTypeUsername:
		if regRes, err = m.usernameReg(c, req); err != nil {
			return
		}
	default:
		err = fmt.Errorf("reg_type is error")
		return
	}
	return
}

func (m *DigitalUserService) UserInfo(c *gin.Context) (userInfoMap response.DigitalUserInfo, err error) {
	var (
		appParams      *request.AppParamsHeader
		profileService DigitalUserProfileService
		userInfo       *model.DigitalUser
		userProfile    *model.DigitalUserProfile
		appVersion     = c.GetHeader("App-Version")
		distinctID     = c.GetHeader("Distinct-ID")
		userId         = utils.GetDigitalUserID(c)
		nowTime        = time.Now()
		clientLanguage = c.GetHeader("Client-Language") // 客户端语言
		appParamsStr   = c.GetHeader("App-Params")
		headerDeviceID = c.GetHeader("Device-Id")
		userEditMap    = map[string]interface{}{} // 用户信息更新map
		userUpdate     = false                    // 用户信息更新标志
		profileUpdate  = false
	)
	// 获取用户信息
	if userInfo, err = m.GetById(c, userId); err != nil {
		return
	}
	// 更新用户分组
	if userInfo.UserGroup == 0 {
		userUpdate = true
		userGroup := m.randUserGroup()
		userInfo.UserGroup = userGroup
		userEditMap["user_group"] = userGroup
	}

	if userInfo.AppID == 13 && userInfo.Avatar == "" {
		userUpdate = true
		userInfo.Avatar = "public/image/default13.png"
		userEditMap["avatar"] = "public/image/default13.png"
	}
	m.TdSetUserGroup(userInfo)
	if distinctID != "" && userInfo.DistinctID != distinctID {
		userUpdate = true
		userInfo.DistinctID = distinctID
		userEditMap["distinct_id"] = distinctID
	}
	if userInfo.DeviceID == "" && headerDeviceID != "" {
		userUpdate = true
		userInfo.DeviceID = headerDeviceID
		userEditMap["device_id"] = headerDeviceID
	}
	// 保存用户版本号
	if userInfo.Version != appVersion && appVersion != "" {
		userUpdate = true
		userInfo.Version = appVersion
		userEditMap["version"] = appVersion
	}
	verCom := utils.CompareVersions(appVersion, "1.17.0")
	if appParamsStr != "" {
		paramsBytes, b64Err := base64.StdEncoding.DecodeString(appParamsStr)
		if b64Err != nil {
			global.LOG.Error("base64 decode error", zap.Uint("userId", userId), zap.Error(b64Err), zap.String("app_params_str", appParamsStr))
		} else {
			if jsonErr := json.Unmarshal(paramsBytes, &appParams); jsonErr != nil {
				global.LOG.Error("json unmarshal error", zap.Error(jsonErr))
			}
		}
	}
	// 更新用户的network_name
	if appParams != nil && appParams.NetworkName != "" {
		userUpdate = true
		userInfo.NetworkName = appParams.NetworkName
		userEditMap["network_name"] = appParams.NetworkName
	}
	if verCom >= 0 || userInfo.AppID >= 21 {
		adIdStr := c.GetHeader("Adid")
		userAdData := userInfo.AdjustIdData
		if userAdData.Data().Adid == "" && appParams != nil {
			upAdData := model.AdjustIdData{
				Adid:    adIdStr,
				Idfa:    appParams.Idfa,
				Idfv:    appParams.Idfv,
				GpsAdid: appParams.GpsId,
			}
			userUpdate = true
			jsonAdData := datatypes.NewJSONType(upAdData)
			userInfo.AdjustIdData = jsonAdData
			userEditMap["adjust_id_data"] = jsonAdData
		}
	}

	// 保存用户语言
	if userInfo.Language != clientLanguage && clientLanguage != "" {
		userUpdate = true
		userInfo.Language = clientLanguage
		userEditMap["language"] = clientLanguage
	}

	// 获取用户profile信息
	if userProfile, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	otherInfo := userProfile.OtherInfo.Data()
	// 检查订阅是否过期
	if !userInfo.SubExpire.IsZero() && userInfo.SubExpire.Before(nowTime) && userInfo.SubStatus != model.SubStatusExpire {
		userUpdate = true
		userInfo.SubStatus = model.SubStatusExpire
		userEditMap["sub_status"] = model.SubStatusExpire
	}
	// 检查SVip订阅是否过期
	if !userInfo.SVipExpire.IsZero() && userInfo.SVipExpire.Before(nowTime) && userInfo.SVipStatus != model.SubStatusExpire {
		userUpdate = true
		userInfo.SVipStatus = model.SubStatusExpire
		userEditMap["svip_status"] = model.SubStatusExpire
	}
	if !userInfo.ChatVipExpire.IsZero() && userInfo.ChatVipExpire.Before(nowTime) && userInfo.ChatVipStatus != model.SubStatusExpire {
		userInfo.ChatVipStatus = model.SubStatusExpire
		userUpdate = true
		userEditMap["chat_vip_status"] = model.SubStatusExpire
	}
	if !userInfo.ContentVipExpire.IsZero() && userInfo.ContentVipExpire.Before(nowTime) && userInfo.ContentVipStatus != model.SubStatusExpire {
		userInfo.ContentVipStatus = model.SubStatusExpire
		userUpdate = true
		userEditMap["content_vip_status"] = model.SubStatusExpire
	}
	if utils.CheckIsEmail(userInfo.Username) && userInfo.Email != userInfo.Username {
		userInfo.Email = userInfo.Username
		userUpdate = true
		userEditMap["email"] = userInfo.Username
	}
	if userUpdate {
		// 更新用户信息
		if err = m.UpdateByMap(c, userInfo.Id, userEditMap); err != nil {
			return
		}
	}

	if otherInfo.LastName == "" || otherInfo.FirstName == "" || len(otherInfo.LastName) == 1 || len(otherInfo.FirstName) == 1 {
		profileUpdate = true
		if len(userInfo.Nickname) > 1 {
			otherInfo.FirstName = userInfo.Nickname
			otherInfo.LastName = userInfo.Nickname
		} else {
			otherInfo.FirstName = fake.GenerateFirstName()
			otherInfo.LastName = fake.GenerateLastName(userInfo.CountryCode)
		}
	}
	if otherInfo.Birthday == "" {
		profileUpdate = true
		otherInfo.Birthday = fake.GenerateBirthday(userInfo.CountryCode)
	}
	if otherInfo.Phone == "" {
		profileUpdate = true
		otherInfo.Phone = fake.GeneratePhoneNumber(userInfo.CountryCode)
	}
	if utils.CheckIsEmail(userInfo.Username) {
		// 用户名是邮箱
		if otherInfo.Email == "" {
			profileUpdate = true
			otherInfo.Email = userInfo.Username
		} else {
			if otherInfo.Email != userInfo.Username {
				profileUpdate = true
				otherInfo.Email = userInfo.Username
			}
		}
	} else {
		if otherInfo.Email == "" {
			profileUpdate = true
			otherInfo.Email = fake.GenerateEmail(userInfo.CountryCode)
		}
	}
	if otherInfo.Country != userInfo.CountryCode {
		profileUpdate = true
		otherInfo.Country = userInfo.CountryCode
	}
	if profileUpdate {
		userProfile.OtherInfo = datatypes.NewJSONType(otherInfo)
		// 更新用户信息
		if err = profileService.Update(userProfile); err != nil {
			return
		}
	}

	// 组装用户信息结构
	userInfoMap = m.GetUserInfo(c, userInfo, userProfile, false)
	return
}

func (m *DigitalUserService) RtmToken(c *gin.Context) (res gin.H, err error) {
	var (
		agoraToken string
		userInfo   *model.DigitalUser
		userId     = utils.GetDigitalUserID(c)
		appId      = utils.GetAppID(c)
	)
	// 获取用户信息
	if userInfo, err = m.GetById(c, userId); err != nil {
		return
	}
	// 获取App信息
	appInfo, _ := global.AppMap[appId]
	// 生成声网token
	if agoraToken, err = agora.GenerateAgoraToken(appInfo.AgoraData.Data().AppId, appInfo.AgoraData.Data().AppCert, fmt.Sprintf("%d", userInfo.Id), global.RtmExpireSec); err != nil {
		return
	}
	res = gin.H{
		"token":          agoraToken,
		"expire_seconds": global.RtmExpireSec,
	}
	return
}

func (m *DigitalUserService) GetUserInfo(c *gin.Context, user *model.DigitalUser, userProfile *model.DigitalUserProfile, isNew bool) (res response.DigitalUserInfo) {
	var (
		orderSer OrdersService
		appInfo  = global.AppMap[user.AppID]
	)
	// isAudit := user.IsAudit()
	isSVip := user.IsSVip()
	isSub := user.IsSub()
	isChatVip := user.IsChatVip()
	isContentVip := user.IsContentVip()
	// 语音通话每分钟价格
	minPrice := appInfo.DetailConfig.Data().MinPrice
	if IsVipSVipUser(user) {
		if isSVip {
			minPrice = appInfo.DetailConfig.Data().MinPriceSVip
		} else if isSub {
			minPrice = appInfo.DetailConfig.Data().MinPriceSub
		}
	} else {
		if isChatVip {
			minPrice = appInfo.DetailConfig.Data().ChatVipMinPrice
		}
	}

	adCfg := GetSplashAdCfg(c, user.AppID)
	chatAds := ShowMsgActivityAds(c, user, userProfile)
	splashAd := ShowAppSplashAd(c, user, userProfile)
	userStatus := user.UserStatus
	if user.IsAudit() {
		userStatus = model.UserStatusAudit
	}
	app4IsOld := 2
	t2 := time.Date(2025, 4, 9, 0, 0, 0, 0, time.UTC)
	if user.CreatedAt.Before(t2) {
		app4IsOld = 1
	}
	isNew = false
	t3 := time.Date(2025, 4, 27, 0, 0, 0, 0, time.UTC)
	if utils.CompareVersions(user.Version, "3.7.9") == 1 && user.AppID == 7 && user.CreatedAt.After(t3) {
		isNew = true
	}
	res = response.DigitalUserInfo{
		Id:               user.Id,
		AppId:            user.AppID,
		UserGroup:        user.UserGroup,
		Nickname:         utils.Pointer(user.Nickname),
		Avatar:           utils.S3Url(user.Avatar, user.AppID),
		Username:         user.Username,
		LoginType:        user.LoginType,
		Gender:           user.Gender,
		LikeGender:       user.LikeGender,
		AgeRange:         user.AgeRange,
		SubStatus:        user.SubStatus,
		SubType:          user.SubType,
		UserStatus:       userStatus,
		SubExpire:        user.SubExpire.Format(time.DateTime),
		SVipStatus:       user.SVipStatus,
		SVipExpire:       user.SVipExpire.Format(time.DateTime),
		ChatVipStatus:    user.ChatVipStatus,
		ChatVipExpire:    user.ChatVipExpire.Format(time.DateTime),
		ChatVipType:      user.ChatVipType,
		ContentVipStatus: user.ContentVipStatus,
		ContentVipExpire: user.ContentVipExpire.Format(time.DateTime),
		ContentVipType:   user.ContentVipType,
		CreatedAt:        user.CreatedAt.Format(time.DateTime),
		FlowerAmount:     userProfile.FlowerAmount,
		Nsfw:             userProfile.Nsfw,
		ChatAds:          chatAds,
		SplashAd:         splashAd,
		OldUser:          IsVipSVipUser(user),
		SplashSecond:     adCfg.StepSecond,
		WebOne:           userProfile.WebOne,
		WebTwo:           userProfile.WebTwo,
		WebThree:         userProfile.WebThree,
		MinPrice:         minPrice,
		ResetQuickReply:  appInfo.DetailConfig.Data().UserResetQuickReplyCoin,
		IsNew:            isNew,
		App4IsOld:        app4IsOld,
	}
	// btc专属
	if appInfo.Id == 19 || appInfo.Id == 23 || appInfo.Id == 126 || appInfo.Id == 127 || appInfo.Id == 128 ||
		appInfo.Id == 129 || appInfo.Id == 130 || appInfo.Id == 131 {
		btcServer := BtcService{}
		res.BtcData, _ = btcServer.UserInfo(c)
	}

	callFreeTime, _ := call.GetFreeTime(c, user.Id)
	res.CallAssets = response.CallAssets{FreeTime: callFreeTime}

	if user.Password != "" {
		res.Password = "******"
	}
	if IsVipSVipUser(user) {
		// 弹窗逻辑
		if !isSVip {
			if isSub {
				// 查询用户是否购买过金币的商品
				isBuyCoin, _ := orderSer.UserIsBuyCoin(user.Id)
				if !isBuyCoin {
					if user.UserGroup == 1 {
						res.ShowDialogType = response.DialogTypeSVIP
					} else {
						res.ShowDialogType = response.DialogTypeLowPriceCoin
					}
				} else {
					res.ShowDialogType = response.DialogTypeSVIP
				}
			} else {
				res.ShowDialogType = response.DialogTypeVIP
			}
		}
	} else {
		// if isChatVip && !isContentVip {
		// 	res.ShowDialogType = response.DialogTypeContentVIP
		// }
		// if !isChatVip && isContentVip {
		// 	res.ShowDialogType = response.DialogTypeChatVIP
		// }
		if !isChatVip && !isContentVip {
			res.ShowDialogType = response.DialogTypeChatVIP
		}
	}
	birthdayTime := time.Time(user.Birthday)
	if !birthdayTime.IsZero() {
		res.Birthday = birthdayTime.Format(time.DateOnly)
	}
	if user.Avatar != "" {
		res.Avatar = utils.S3Url(user.Avatar, user.AppID)
	}
	return
}

func (m *DigitalUserService) ReceiveFlowersDialog(c *gin.Context) (res response.ReceiveFlowersDialogRes, err error) {
	var (
		floCount     int
		rKeyExistInt int64
		title        string
		userInfo     *model.DigitalUser
		appVersion   = c.GetHeader("App-Version")
		userId       = utils.GetDigitalUserID(c)
		nowTime      = time.Now()
		dayStr       = nowTime.Format(time.DateOnly)
		rKey         = fmt.Sprintf(global.FLOWER_SHOW_KEY, dayStr, userId)
	)
	// 获取用户信息
	if userInfo, err = m.GetById(c, userId); err != nil {
		return
	}
	// 获取App信息
	appId := utils.GetAppID(c)
	appInfo, exist := global.AppMap[appId]
	if !exist {
		err = fmt.Errorf("app(%d) not found", appId)
		return
	}
	// 领取鲜花数量
	if userInfo.IsSub() {
		floCount = appInfo.DetailConfig.Data().DaySubReceiveFlo
	} else {
		floCount = appInfo.DetailConfig.Data().DayFreeReceiveFlo
	}
	rKeyExistInt = global.REDIS.Exists(c, rKey).Val()
	title = "Daily Gold coin claimable"
	if appInfo.AppType == model.AppTypeIOS {
		if appVersion < "1.7.0" {
			title = ""
			rKeyExistInt = 1
		}
	}
	global.REDIS.SetEx(c, rKey, 1, time.Hour*24)
	res = response.ReceiveFlowersDialogRes{
		Title:      title,
		FloCount:   floCount,
		Exist:      rKeyExistInt,
		Content:    "New daily Gold coin arrive in 1 hours.\nRemember to open the app to get them",
		BtnContent: "Get Free Gold coin",
	}
	return
}

func (m *DigitalUserService) ReceiveFlowersDialogV2(c *gin.Context) (res response.ReceiveFlowersDialogV2Res, err error) {
	var (
		floCount     int
		rKeyExistInt int64
		userInfo     *model.DigitalUser
		userId       = utils.GetDigitalUserID(c)
		nowTime      = time.Now()
		nowWeekInt   = int(nowTime.Weekday())
		dayStr       = nowTime.Format(time.DateOnly)
		rKey         = fmt.Sprintf(global.FLOWER_SHOW_KEY, dayStr, userId)
	)
	// 获取用户信息
	if userInfo, err = m.GetById(c, userId); err != nil {
		return
	}
	// 领取鲜花数量
	rewardFlowersConfigIndex := global.WeekdayToRewardFlowersKeyMap[nowWeekInt]
	if userInfo.IsSub() {
		floCount = global.RewardFlowersVipUserConfig[rewardFlowersConfigIndex]
	} else {
		floCount = global.RewardFlowersFreeUserConfig[rewardFlowersConfigIndex]
	}
	rKeyExistInt = global.REDIS.Exists(c, rKey).Val()
	// 设置redis,已经显示过
	if err = global.REDIS.SetEx(c, rKey, 1, time.Hour*24).Err(); err != nil {
		return
	}
	res = response.ReceiveFlowersDialogV2Res{
		Title:      "Daily Flowers claimable",
		FloCount:   floCount,
		Exist:      rKeyExistInt,
		Content:    "Remember to open the app to get them🎉\nSurprises for Continuous Claims.",
		BtnContent: "Get Free Flowers",
	}
	return
}

func (m *DigitalUserService) SignFloRdsKey(userId uint) string {
	weekStart, weekEnd := utils.CurrentWeekRange(true)
	weekStartStr := weekStart.Format(time.DateOnly)
	weekEndStr := weekEnd.Format(time.DateOnly)
	return fmt.Sprintf(global.FLOWER_SHOW_KEY_V2, weekStartStr, weekEndStr, userId)
}

func (m *DigitalUserService) ReceiveFlowersDialogV3(c *gin.Context) (res response.ReceiveFlowersDialogV3Res, err error) {
	var (
		todayIsSigin   int
		signRewardList []response.SiginRewardData
		userId         = utils.GetDigitalUserID(c)
		nowTime        = time.Now()
		dayStr         = nowTime.Format(time.DateOnly)
	)
	rKey := m.SignFloRdsKey(userId)
	weekRangeTime := utils.CurrentWeekRangeTime(true)
	signMembers := global.REDIS.SMembers(c, rKey).Val()
	// 把记录处理成一个map
	signMap := map[string]bool{}
	for _, signDay := range signMembers {
		signMap[signDay] = true
	}
	for i, weekDayTime := range weekRangeTime {
		weekDay := weekDayTime.Format(time.DateOnly)
		// 判断是否签到
		isSign := 3
		if _, exists := signMap[weekDay]; exists {
			isSign = 1 // 已签到
		} else if weekDayTime.Before(nowTime) {
			isSign = 2 // 过期不可签
		}
		isToday := false
		if weekDay == dayStr {
			isToday = true
			todayIsSigin = isSign
		}
		signRewardList = append(signRewardList, response.SiginRewardData{
			FlowerNum:    global.RewardFlowersFreeUserConfig[i],
			FlowerNumSub: global.RewardFlowersVipUserConfig[i],
			Date:         weekDayTime.Format("Mon"),
			IsSigin:      isSign,
			IsToday:      isToday,
		})
	}
	res = response.ReceiveFlowersDialogV3Res{
		TodayIsSigin: todayIsSigin,
		List:         signRewardList,
	}
	return
}

func (m *DigitalUserService) sendNotifyChargeQueue(ctx context.Context, userInfo *model.DigitalUser, aiRoleInfo *model.AiRole) {
	score := time.Now().Add(time.Second * global.NotifyChargeSec).Unix()
	err := global.REDIS.ZAdd(ctx, global.NOTIFY_CHARGE_QUEUE_KEY, redis.Z{Score: float64(score), Member: fmt.Sprintf("%d_%s", userInfo.Id, aiRoleInfo.RoleID)}).Err()
	if err != nil {
		global.LOG.Error("sendNotifyChargeQueue error", zap.Error(err))
	}
}

// UserQuickReplyCharging 12-31 增加快捷回复消息下发付费
func (m *DigitalUserService) UserQuickReplyCharging(c *gin.Context, reqData *request.UserQuickReplyChargingReq) (err error) {

	var (
		userInfo        *model.DigitalUser
		aiRoleInfo      *model.AiRole
		userProfileInfo *model.DigitalUserProfile
		profileService  DigitalUserProfileService
		msgService      MsgService
		userId          = utils.GetDigitalUserID(c)
		aiSer           AiRoleService
	)
	// 获取用户信息
	if userInfo, err = m.GetById(c, userId); err != nil {
		err = fmt.Errorf("user find error, %v", err.Error())
		return
	}

	if userProfileInfo, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	// 获取AI角色信息
	if aiRoleInfo, err = aiSer.GetByRoleId(reqData.AiRoleId); err != nil {
		err = fmt.Errorf("ai role find error, %v", err.Error())
		return
	}
	// 重新生成需要扣费
	// 根据app_id 获取对应的配置金额 如果0的话就给一个默认的值
	val := global.REDIS.Get(c, fmt.Sprintf(global.USER_QUICK_REPLY_COST_RDS_KEY, reqData.QuickReplyType, userInfo.AppID)).Val()
	if val == "" {
		val = "10"
	}
	consumeAmount := utils.StrToInt(val)
	consumeType := model.ConsumeTypeResetUserQuickReplyCharge
	if userProfileInfo.FlowerAmount < consumeAmount {
		err = errs.FloShopErr
		return
	}
	// 扣费
	if err = msgService.SaveConsumeRecord(userId, aiRoleInfo, consumeAmount, consumeType); err != nil {
		return
	}
	go TdPointer(userInfo, userProfileInfo, td.ServerEvent, map[string]interface{}{
		"event_tag":         "coins_consume",
		"coins_consume_num": -consumeAmount,
		"ai_role_id":        aiRoleInfo.RoleID,
		"ai_role_type":      aiRoleInfo.RoleType,
		"source":            "quick_options_consume",
	})

	return
}

func (m *DigitalUserService) UserQuickReply(c *gin.Context, reqData *request.UserQuickReplyReq) (err error) {
	var (
		sendMsgInfo     *model.MsgInfo
		userInfo        *model.DigitalUser
		aiRoleInfo      *model.AiRole
		userProfileInfo *model.DigitalUserProfile
		profileService  DigitalUserProfileService
		msgService      MsgService
		aiSer           AiRoleService
		userId          = utils.GetDigitalUserID(c)
		userIdStr       = fmt.Sprintf("%d", userId)
	)
	// 获取用户信息
	if userInfo, err = m.GetById(c, userId); err != nil {
		err = fmt.Errorf("user find error, %v", err.Error())
		return
	}
	appInfo := global.AppMap[userInfo.AppID]
	if userProfileInfo, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	// 查询聊天记录
	msgQuery := bson.M{"message_id": reqData.MessageId, "receiver_id": userIdStr}
	if err = global.MONGO.Find(c, msgQuery).One(&sendMsgInfo); err != nil {
		err = fmt.Errorf("message find error, %v", err.Error())
		return
	}
	// 获取AI角色信息
	if aiRoleInfo, err = aiSer.GetByRoleId(sendMsgInfo.SenderID); err != nil {
		err = fmt.Errorf("ai role find error, %v", err.Error())
		return
	}
	redisMsgReq := &request.RedisMsgReq{
		UrlType:     msgService.GetMsgUrlType(userInfo, userProfileInfo),
		SendType:    request.SendTypeUserQuickReply,
		UserId:      userInfo.Id,
		AiRoleId:    aiRoleInfo.RoleID,
		RequireCost: 0, // 不需要扣费
		GiftId:      0,
	}
	if reqData.IsCancel {
		// 取消快捷回复
		go msgService.DelSendAiMsg(c, redisMsgReq)
		return
	}
	if reqData.IsReset {
		// 重新生成需要扣费
		consumeAmount := appInfo.DetailConfig.Data().UserResetQuickReplyCoin
		consumeType := model.ConsumeTypeResetUserQuickReply
		if userProfileInfo.FlowerAmount < consumeAmount {
			err = errs.FloShopErr
			return
		}
		// 扣费
		if err = msgService.SaveConsumeRecord(userId, aiRoleInfo, consumeAmount, consumeType); err != nil {
			return
		}
		go TdPointer(userInfo, userProfileInfo, td.ServerEvent, map[string]interface{}{
			"event_tag":         "coins_consume",
			"coins_consume_num": -consumeAmount,
			"ai_role_id":        aiRoleInfo.RoleID,
			"ai_role_type":      aiRoleInfo.RoleType,
			"source":            "reset_reply",
		})
	}
	// 保存MessageID
	_ = msgService.SetUserAiQuickReplyMsgId(c, userId, aiRoleInfo.RoleID, sendMsgInfo.MessageID)
	go msgService.SendAiMsg(c, redisMsgReq)
	return
}

func (m *DigitalUserService) SendMsg(c *gin.Context, req request.SendMsgReq) (res gin.H, err error) {
	var (
		profileService  DigitalUserProfileService
		msgService      MsgService
		aiRoleService   AiRoleService
		userInfo        *model.DigitalUser
		userProfileInfo *model.DigitalUserProfile
		aiRoleInfo      *model.AiRole
		appVersion      = c.GetHeader("App-Version")
		userId          = utils.GetDigitalUserID(c)
		requireCost     = 1
		nowTime         = time.Now()
		nowTimestamp    = nowTime.UnixMilli()
		dayStr          = nowTime.Format("2006-01-02")
		aiRoleId        = req.ReceiverId
		userMsg         = req.MessageContent
		clientLanguage  = c.GetHeader("Client-Language") // 客户端语言
		userEditMap     = map[string]interface{}{}       // 用户信息更新map
		userUpdate      = false                          // 用户信息更新标志
	)
	res = gin.H{}
	if req.GiftId != 0 {
		requireCost = 0
	}
	// 获取用户信息
	if userInfo, err = m.GetById(c, userId); err != nil {
		return
	}
	isSub := userInfo.IsSub()
	isAudit := userInfo.IsAudit()
	isChatVip := userInfo.IsChatVip()
	isContentVip := userInfo.IsContentVip()

	appAiFuncSwitchCfg := global.AllAiFuncCfgMap[userInfo.AppID]

	// 保存用户版本号
	if userInfo.Version != appVersion && appVersion != "" {
		userInfo.Version = appVersion
		userUpdate = true
		userEditMap["version"] = appVersion
	}
	// 保存用户语言
	if userInfo.Language != clientLanguage && clientLanguage != "" {
		userUpdate = true
		userInfo.Language = clientLanguage
		userEditMap["language"] = clientLanguage
	}
	// 更新用户信息
	if userUpdate {
		if err = m.UpdateByMap(c, userInfo.Id, userEditMap); err != nil {
			return
		}
	}
	if userInfo.Nickname == "" {
		err = fmt.Errorf("please set username")
		return
	}
	if userProfileInfo, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	// 获取App信息
	appInfo, _ := global.AppMap[userInfo.AppID]

	// 获取airole信息
	if aiRoleInfo, err = aiRoleService.GetByRoleId(aiRoleId); err != nil {
		return
	}

	sendTotal := msgService.GetUserSendMsgTotal(c, userInfo.Id, aiRoleInfo.RoleID)
	// 13号包,首张照片免费 张婷12月27号去掉
	// if sendTotal == 0 {
	// 	// 解锁图片1次/角色
	// 	_ = msgService.RewardUnlockImg(userInfo.Id, aiRoleInfo.Id, 1)
	// }
	// 用户可用消息次数
	freeReplyCount := appInfo.DetailConfig.Data().FreeMsgCount
	paidReplyCount := appInfo.DetailConfig.Data().PaidMsgCount
	userAdMsgCount := msgService.GetWatchAdMsgCountToday(c, userId)

	freeReplyCount += userAdMsgCount
	paidReplyCount += userAdMsgCount
	// AI回复消息消耗的鲜花
	replyPerFlower := appInfo.DetailConfig.Data().AiReplyPerFlower
	// 用户AI聊天角色限制数
	freeChatRoleLimitCount := appInfo.DetailConfig.Data().AiChatRoleLimitCount
	paidChatRoleLimitCount := appInfo.DetailConfig.Data().AiChatRoleLimitCountV2
	// 用户购买的畅聊包人数量

	userChatAmount := userProfileInfo.ChatsAmount
	freeChatRoleLimitCount += userChatAmount
	paidChatRoleLimitCount += userChatAmount
	userIdStr := fmt.Sprintf("%d", userId)
	// 读取Redis中的用户AI聊天角色限制数
	rKey := fmt.Sprintf(global.AI_CHAT_ROLE_LIMIT_KEY, dayStr, userId)
	chatRoleCountInt64, _ := global.REDIS.SCard(c, rKey).Result()
	chatRoleCount := int(chatRoleCountInt64)

	// 查询当日总共对话条数
	replyCount := msgService.GetAiReplyUserCountToday(c, userId)

	// 保存用户发送的消息到MongoDB
	userSendMessageId := uuid.NewString()
	userSendMsg := &model.MsgInfo{
		MessageID:      userSendMessageId,
		Channel:        nil,
		SendTime:       nowTimestamp,
		DeletedAt:      nil,
		MessageType:    model.MsgTypeTxt,
		SenderID:       userIdStr,
		SenderName:     userInfo.Nickname,
		ReceiverID:     aiRoleId,
		ReceiverName:   aiRoleInfo.Nickname,
		MessageContent: model.MessageContent{Text: userMsg},
	}
	if err = msgService.CreateMsgRecord(c, userSendMsg); err != nil {
		return
	}
	// 从空闲队列中删除
	global.REDIS.ZRem(c, global.IDLE_MSG_QUEUE_KEY, fmt.Sprintf("%d_%s", userId, aiRoleId))
	// 订阅用户超过10人返回错误信息

	// 判断角色是否已经产生过对话
	exist := global.REDIS.SIsMember(c, rKey, aiRoleId).Val()
	roleLimitCount := freeChatRoleLimitCount
	replyLimitCount := freeReplyCount
	if IsVipSVipUser(userInfo) {
		if isSub {
			// 订阅用户的限制数量
			roleLimitCount = paidChatRoleLimitCount
			replyLimitCount = paidReplyCount
		}
	} else {
		if isChatVip {
			roleLimitCount = appInfo.DetailConfig.Data().ChatVipRoleLimit
			replyLimitCount = appInfo.DetailConfig.Data().ChatVipMsgCount
		} else if isContentVip {
			roleLimitCount = appInfo.DetailConfig.Data().ContentVipRoleLimit
			replyLimitCount = appInfo.DetailConfig.Data().ContentVipMsgCount
		}
	}
	// 角色的限制
	if !exist && chatRoleCount >= roleLimitCount {
		// 催单消息
		m.sendNotifyChargeQueue(c, userInfo, aiRoleInfo)
		if IsVipSVipUser(userInfo) {
			// 人数达到限制弹订阅，不再弹出畅聊包弹窗 -继续刺激订阅
			verCompare := utils.CompareVersions(appVersion, "2.1.0")
			if verCompare >= 0 && !isSub {
				res["position"] = model.PositionAiRoleNotEnough
				err = errs.RequireSubErr
			} else {
				// 超出了每日限制的聊天角色数
				vCompare := utils.CompareVersions(appVersion, "3.0.0")
				if vCompare >= 0 {
					res["position"] = model.PositionAiRoleNotEnough
					err = errs.RequireSubErr
					return
				}
				res["position"] = model.PositionAiRoleNotEnough
				err = errs.RequireBuyChatErr
			}
			return
		} else {
			if !isChatVip {
				err = errs.ChatVipErr
				return
			}
		}
	}
	// 判断回复数是否大于每日限制回复数
	if replyCount > replyLimitCount {
		if IsVipSVipUser(userInfo) {
			if userInfo.AppID == 13 && !isSub {
				go msgService.SendAiSubMsg(c, userInfo, aiRoleInfo, appInfo)
				err = errs.RequireSubErr
				return
			}
			// 判断鲜花是否足够
			if userProfileInfo.FlowerAmount < replyPerFlower {
				// 鲜花不足,判断用户是否订阅,未订阅就提示订阅,订阅了就提示购买鲜花
				if isSub {
					go msgService.SendCloseFlowerMsg(c, userInfo, aiRoleInfo, appInfo)
					vCompare := utils.CompareVersions(appVersion, "3.0.0")
					if vCompare >= 0 {
						err = errs.FloShopErr
						return
					}
					err = errs.RequireBuyFloErr
				} else {
					go msgService.SendAiSubMsg(c, userInfo, aiRoleInfo, appInfo)
					res["position"] = model.PositionMsgNotEnough
					err = errs.RequireSubErr
				}
				// 催单消息
				m.sendNotifyChargeQueue(c, userInfo, aiRoleInfo)
				return
			}
		} else {
			if !isChatVip {
				err = errs.ChatVipErr
				return
			}
		}
	}

	// 发送到消息队列
	if isAudit {
		// 审核用户
		go msgService.SendAuditAiMsg(c, userInfo, aiRoleInfo, appInfo, userSendMsg)
	} else {
		redisMsgReq := &request.RedisMsgReq{
			UrlType:     msgService.GetMsgUrlType(userInfo, userProfileInfo),
			SendType:    request.SendTypeMsg,
			UserId:      userInfo.Id,
			AiRoleId:    aiRoleInfo.RoleID,
			RequireCost: requireCost,
			GiftId:      req.GiftId,
		}
		if req.GiftId > 0 {
			redisMsgReq.SendType = request.SendTypeGift
		}
		go msgService.SendAiMsg(c, redisMsgReq)
	}
	showReviewDialog := false
	if !userProfileInfo.ReviewState {
		randomFloat := rand.Float64()
		if isSub && randomFloat <= global.MSG_REVIEW_RATE && (replyCount == 50 || replyCount == 80 || replyCount == 100) {
			showReviewDialog = true
		}
		if !isSub && randomFloat <= global.MSG_REVIEW_RATE && (replyCount == 5 || replyCount == 7 || replyCount == 9) {
			showReviewDialog = true
		}
	}
	if !showReviewDialog {
		// 判断是否是评价弹窗测试用户
		showReviewDialog = global.REDIS.SIsMember(c, global.REVIEW_TEST_KEY, userIdStr).Val()
	}
	versionCompare := utils.CompareVersions(appVersion, "1.8.0")

	if versionCompare >= 0 {
		res["show_review_dialog"] = showReviewDialog
	}
	// 增加用户和该AI角色的消息发送总数--->用户发送的数量
	_ = msgService.UserSendMsgTotalIncr(c, userInfo.Id, aiRoleInfo.RoleID)
	// 增加用户聊过的AI角色数量
	_ = msgService.UserTotalAiRoleIncr(c, userInfo.Id, aiRoleInfo.RoleID)
	aiFuncCompare := utils.CompareVersions(appVersion, "2.0.1")
	if aiFuncCompare >= 0 {
		// 用户和该AI角色的消息发送总数--->用户发送的数量
		reloadAiFuncSwitch := sendTotal == appAiFuncSwitchCfg.TouchMsgCount || sendTotal == appAiFuncSwitchCfg.OrdersMsgCount || sendTotal == appAiFuncSwitchCfg.RolePlayMsgCount || sendTotal == appAiFuncSwitchCfg.NSFWShowMsgCount || sendTotal == appAiFuncSwitchCfg.GiftMsgCount || sendTotal == appAiFuncSwitchCfg.AsmrMsgCount || sendTotal >= appAiFuncSwitchCfg.DiyMsgCount || sendTotal >= appAiFuncSwitchCfg.PrivateCount
		res["reload_ai_func_switch"] = reloadAiFuncSwitch
	}
	res["message_id"] = uuid.New().String()
	return
}

func (m *DigitalUserService) GetAiFuncSwitch(c *gin.Context, req request.GetAiFuncSwitchReq) (res gin.H, err error) {
	var (
		userInfo        *model.DigitalUser
		userProfileInfo *model.DigitalUserProfile
		aiRoleInfo      *model.AiRole
		intimateInfo    *model.UsersIntimate
		aiRoleService   AiRoleService
		profileService  DigitalUserProfileService
		privateSpaceSer PrivateSpaceService
		userIntimateSer UsersIntimateService
		msgService      MsgService
		appVersion      = c.GetHeader("App-Version")
		userId          = utils.GetDigitalUserID(c)
		appId           = utils.GetAppID(c)
	)
	// 为什么这个接口这么乱,因为需求一直在变.时间还不够,任何接口都可以看提交历史记录,寻找它为什么这么改.
	// 获取用户信息
	if userInfo, err = m.GetById(c, userId); err != nil {
		return
	}
	isSub := userInfo.IsSub()
	isSVip := userInfo.IsSVip()
	isChatVip := userInfo.IsChatVip()
	isContentVip := userInfo.IsContentVip()
	isAudit := userInfo.IsAudit()
	verCompare340 := utils.CompareVersions(appVersion, "3.4.0")
	verCompare350 := utils.CompareVersions(appVersion, "3.5.0")
	// verCompare360 := utils.CompareVersions(appVersion, "3.6.0")
	verCompare364 := utils.CompareVersions(appVersion, "3.6.4")
	verCompare363 := utils.CompareVersions(appVersion, "3.6.3")
	verCompare365 := utils.CompareVersions(appVersion, "3.6.5")
	verCompare370 := utils.CompareVersions(appVersion, "3.7.0")

	if userProfileInfo, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	appAiFuncSwitchCfg := global.AllAiFuncCfgMap[userInfo.AppID]
	// 获取airole信息
	if aiRoleInfo, err = aiRoleService.GetByRoleId(req.AiRoleId); err != nil {
		return
	}
	// 获取亲密值记录
	if intimateInfo, err = userIntimateSer.GetByUidAndRoleid(userId, aiRoleInfo.Id); err != nil {
		return
	}
	// 用户和该AI角色的消息发送总数--->用户发送的数量
	sendTotal := msgService.GetUserSendMsgTotal(c, userInfo.Id, aiRoleInfo.RoleID)

	// 获取App信息
	appInfo, _ := global.AppMap[userInfo.AppID]
	auditAsmrShow := appInfo.DetailConfig.Data().AuditASMRShow
	auditSeeuShow := appInfo.DetailConfig.Data().AuditSeeUShow
	normalAsmrShow := appInfo.DetailConfig.Data().NormalASMRShow
	normalSeeuShow := appInfo.DetailConfig.Data().NormalSeeUShow
	asmrMsg := "Babe, could you please make me one of your magical ASMR videos to help me relax tonight?"
	seeuMsg := "Let me see u"
	asmrHave := len(aiRoleInfo.AsmrList) > 0
	seeuHave := len(aiRoleInfo.ImgList) > 0
	asmrShow := asmrHave && normalAsmrShow
	seeuShow := seeuHave && normalSeeuShow
	touchShow := true
	ordersShow := true
	giftShow := true
	nsfwShow := false
	voiceCallShow := appInfo.DetailConfig.Data().VoiceCall
	privateShow := false
	diyShow := appInfo.DetailConfig.Data().NormalDiyPhotoShow
	bodyImgShow := false
	realCallShow := false
	if isAudit {
		asmrShow = asmrHave && auditAsmrShow
		seeuShow = seeuHave && auditSeeuShow
		touchShow = false
		ordersShow = false
		giftShow = false
		voiceCallShow = appInfo.DetailConfig.Data().AuditVoiceCall
		diyShow = appInfo.DetailConfig.Data().AuditDiyPhotoShow
	}

	realCallShow = intimateInfo.Level >= 0 && verCompare370 >= 0 && appId == 13

	verCompare320 := utils.CompareVersions(appVersion, "3.2.0")
	if verCompare320 >= 0 {
		if !isSub || isAudit {
			diyShow = false
		} else {
			diyShow = true
		}
	}
	if verCompare320 >= 0 && userInfo.AppID == 4 {
		diyShow = appInfo.DetailConfig.Data().NormalDiyPhotoShow
		if isAudit {
			diyShow = appInfo.DetailConfig.Data().AuditDiyPhotoShow
		}
	}
	aiFuncCompare := utils.CompareVersions(appVersion, "2.0.1")
	if aiFuncCompare >= 0 {
		touchShow = touchShow && sendTotal >= appAiFuncSwitchCfg.TouchMsgCount
		ordersShow = ordersShow && sendTotal >= appAiFuncSwitchCfg.OrdersMsgCount
		giftShow = giftShow && sendTotal >= appAiFuncSwitchCfg.GiftMsgCount && isSub && userProfileInfo.Nsfw
		asmrShow = asmrShow && sendTotal >= appAiFuncSwitchCfg.AsmrMsgCount && isSub && userProfileInfo.Nsfw
		diyShow = diyShow && sendTotal >= 0
		nsfwShow = sendTotal >= appAiFuncSwitchCfg.NSFWShowMsgCount && !isAudit
	}
	// 需求地址 https://shimo.im/docs/B1Awd4W7m8FKN3m8/ 《persona.AI双订阅权益优化2.0》，可复制链接后用石墨文档 App 打开
	// 免费用户阶段，私人订制可见, appid1,>=3.3.1
	verCompare331 := utils.CompareVersions(appVersion, "3.3.1")
	if userInfo.AppID == 1 && verCompare331 >= 0 && !isAudit {
		diyShow = true
	}
	privateShow = isSub && userProfileInfo.Nsfw && !isAudit && sendTotal >= appAiFuncSwitchCfg.PrivateCount
	if !isAudit && isSVip && userProfileInfo.Nsfw {
		privateShow = true
	}
	var resList []response.AiFuncItem
	// 用户版本>=3.4.0
	if verCompare340 >= 0 && appId != 10 {
		asmrShow = asmrHave && intimateInfo.Level >= 4 && (isSub || isSVip) && !isAudit && userProfileInfo.Nsfw
		giftShow = intimateInfo.Level >= 3 && !isAudit
		voiceCallShow = intimateInfo.Level >= 4
		diyShow = intimateInfo.Level >= 5 && (isSub || isSVip) && !isAudit && userProfileInfo.Nsfw
		privateShow = intimateInfo.Level >= 6 && isSub && !isAudit && userProfileInfo.Nsfw
	}
	// 用户版本>=3.5.0
	if verCompare350 >= 0 {
		bodyImgShow = !isAudit && intimateInfo.Level >= 1 && aiRoleInfo.Gender == model.GenderTypeFemale
	}
	// 孙宏伟需求 2024-10-31 17:39 钉钉消息需求
	// 只有11号包的3.5.0这一个版本，后续亲密度功能上线之后就按照亲密度走
	if verCompare350 == 0 && appId == 11 {
		touchShow = sendTotal >= appAiFuncSwitchCfg.TouchMsgCount && !isAudit && userProfileInfo.Nsfw
		ordersShow = sendTotal >= appAiFuncSwitchCfg.OrdersMsgCount && !isAudit && userProfileInfo.Nsfw
		giftShow = sendTotal >= appAiFuncSwitchCfg.GiftMsgCount && isSub && !isAudit && userProfileInfo.Nsfw
		asmrShow = asmrHave && sendTotal >= appAiFuncSwitchCfg.AsmrMsgCount && isSub && !isAudit && userProfileInfo.Nsfw
		diyShow = sendTotal >= 0 && !isAudit && userProfileInfo.Nsfw
		nsfwShow = sendTotal >= appAiFuncSwitchCfg.NSFWShowMsgCount && !isAudit
		bodyImgShow = sendTotal >= 1 && !isAudit
		voiceCallShow = sendTotal >= 1 && !isAudit
		privateShow = sendTotal >= appAiFuncSwitchCfg.PrivateCount && !isAudit && userProfileInfo.Nsfw
	}
	if appId == 4 || appId == 6 || appId == 7 || appId == 10 {
		if IsVipSVipUser(userInfo) {
			if appId == 4 || appId == 6 || appId == 7 {
				touchShow = sendTotal >= appAiFuncSwitchCfg.TouchMsgCount && !isAudit && userProfileInfo.Nsfw
				ordersShow = sendTotal >= appAiFuncSwitchCfg.OrdersMsgCount && !isAudit && userProfileInfo.Nsfw
				giftShow = sendTotal >= appAiFuncSwitchCfg.GiftMsgCount && isSub && !isAudit && userProfileInfo.Nsfw
				asmrShow = asmrHave && sendTotal >= appAiFuncSwitchCfg.AsmrMsgCount && isSub && !isAudit && userProfileInfo.Nsfw
				diyShow = sendTotal >= 0 && !isAudit
				nsfwShow = sendTotal >= appAiFuncSwitchCfg.NSFWShowMsgCount && !isAudit
				bodyImgShow = sendTotal >= 1 && !isAudit
				voiceCallShow = sendTotal >= 1 && !isAudit
				privateShow = sendTotal >= appAiFuncSwitchCfg.PrivateCount && !isAudit && userProfileInfo.Nsfw
			}
			if appId == 6 {
				if verCompare320 <= 0 {
					bodyImgShow = false
					touchShow = !isAudit && sendTotal >= appAiFuncSwitchCfg.TouchMsgCount
				}
				if !isSub || isAudit {
					diyShow = false
				} else {
					diyShow = true
				}
			}
			if appId == 7 {
				if verCompare320 <= 0 {
					bodyImgShow = false
					touchShow = !isAudit && sendTotal >= appAiFuncSwitchCfg.TouchMsgCount
				}
				if !isSub || isAudit {
					diyShow = false
				} else {
					diyShow = true
				}
			}
		} else {
			asmrShow = asmrHave
			seeuShow = seeuHave
			touchShow = true
			ordersShow = true
			giftShow = !isAudit
			diyShow = true
			voiceCallShow = !isAudit
			privateShow = true
			nsfwShow = !isAudit
			bodyImgShow = aiRoleInfo.Gender == model.GenderTypeFemale && !isAudit
		}
	}

	// 孙宏伟需求 2024-12-24 新增app_id 4号包数据 亲密度功能
	if (appId == 4 && verCompare364 > 0) || appId == 25 {
		asmrShow = false
		voiceCallShow = false
		diyShow = false
		privateShow = false
		giftShow = false
		bodyImgShow = false
		if intimateInfo.Level >= 3 && !isAudit {
			giftShow = true
		}
		if intimateInfo.Level >= 1 {
			bodyImgShow = aiRoleInfo.Gender == model.GenderTypeFemale && !isAudit
		}
		// 根据用户身份来控制用户的权益
		if intimateInfo.Level >= 4 && !isAudit && asmrHave {
			asmrShow = true
		}
		if intimateInfo.Level >= 4 && !isAudit {
			voiceCallShow = true
		}

		if intimateInfo.Level >= 5 && !isAudit {
			diyShow = true
		}

		if intimateInfo.Level >= 6 && !isAudit {
			privateShow = true
		}
	}

	// 孙宏伟需求 2024-12-24 新增app_id 10号包数据 亲密度 功能 增加新17
	if (appId == 10 && verCompare363 > 0) || (appId == 6 && verCompare365 > 0) || (appId == 7 && verCompare365 > 0) || (appId == 17) {
		seeuShow = false
		ordersShow = false
		if isAudit {
			touchShow = false
		}
		asmrShow = false
		voiceCallShow = false
		diyShow = false
		privateShow = false
		giftShow = false
		bodyImgShow = false
		if intimateInfo.Level >= 3 && !isAudit {
			giftShow = true
		}
		if intimateInfo.Level >= 1 {
			bodyImgShow = aiRoleInfo.Gender == model.GenderTypeFemale && !isAudit
		}
		// 根据用户身份来控制用户的权益
		if intimateInfo.Level >= 4 && !isAudit && asmrHave {
			asmrShow = true
			// voiceCallShow = true
		}

		if intimateInfo.Level >= 4 && !isAudit {
			voiceCallShow = true
		}

		if intimateInfo.Level >= 5 && !isAudit {
			diyShow = true
		}

		if intimateInfo.Level >= 6 && !isAudit {
			privateShow = true
		}
	}

	if appId == 6 {
		seeuShow = false
		ordersShow = false
		if isAudit {
			touchShow = false
		}
	}
	if appId == 7 {
		seeuShow = false
		ordersShow = false
		if isAudit {
			touchShow = false
		}
	}
	if appId == 13 {
		touchShow = !isAudit
		ordersShow = false
		giftShow = !isAudit
		asmrShow = false
		diyShow = !isAudit && intimateInfo.Level >= 5
		nsfwShow = !isAudit
		bodyImgShow = false
		voiceCallShow = false
		privateShow = verCompare370 >= 0 && intimateInfo.Level >= 6 && !isAudit
	}
	// 0220日 张婷需求 关闭1号包order show 功能
	if appId == 1 {
		ordersShow = false
	}

	if asmrShow {
		iconList := global.AppImgMap[appId][model.ImgConfigTypeAsmr].NormalList
		if isAudit {
			iconList = global.AppImgMap[appId][model.ImgConfigTypeAsmr].AuditList
		}
		iconUrl := utils.S3Url("public/icon/ic_asmr.png", appId)
		if len(iconList) > 0 {
			iconUrl = iconList[0]
		}
		resList = append(resList, response.AiFuncItem{
			FuncType: model.FuncTypeAsmr,
			Icon:     iconUrl,
			Msg:      asmrMsg,
		})
	}
	if seeuShow {
		iconList := global.AppImgMap[appId][model.ImgConfigTypeSeeu].NormalList
		if isAudit {
			iconList = global.AppImgMap[appId][model.ImgConfigTypeSeeu].AuditList
		}
		iconUrl := utils.S3Url("public/icon/ic_seeu.png", appId)
		if len(iconList) > 0 {
			iconUrl = iconList[0]
		}
		resList = append(resList, response.AiFuncItem{
			FuncType: model.FuncTypeSeeu,
			Icon:     iconUrl,
			Msg:      seeuMsg,
		})
	}
	versionCompare := utils.CompareVersions(appVersion, "1.14.0")
	if touchShow && versionCompare >= 0 {
		iconList := global.AppImgMap[appId][model.ImgConfigTypeTouch].NormalList
		if isAudit {
			iconList = global.AppImgMap[appId][model.ImgConfigTypeTouch].AuditList
		}
		iconUrl := utils.S3Url("public/icon/ic_touch.png", appId)
		if len(iconList) > 0 {
			iconUrl = iconList[0]
		}
		resList = append(resList, response.AiFuncItem{
			FuncType:     model.FuncTypeTouch,
			Icon:         iconUrl,
			Msg:          "",
			TouchOptions: global.TouchOptionList,
		})
	}
	ordersOptionCompare := utils.CompareVersions(appVersion, "1.18.0")
	if ordersOptionCompare >= 0 && ordersShow {
		iconList := global.AppImgMap[appId][model.ImgConfigTypeOrders].NormalList
		if isAudit {
			iconList = global.AppImgMap[appId][model.ImgConfigTypeOrders].AuditList
		}
		iconUrl := utils.S3Url("public/icon/ic_orders.png", appId)
		if len(iconList) > 0 {
			iconUrl = iconList[0]
		}
		resList = append(resList, response.AiFuncItem{
			FuncType:     model.FuncTypeOrders,
			Icon:         iconUrl,
			Msg:          "",
			TouchOptions: global.OrdersOptionList,
		})
	}
	giftOptionCompare := utils.CompareVersions(appVersion, "1.19.0")
	if (giftOptionCompare >= 0 && giftShow) || (appId == 24 && giftShow) {
		iconList := global.AppImgMap[appId][model.ImgConfigTypeGift].NormalList
		if isAudit {
			iconList = global.AppImgMap[appId][model.ImgConfigTypeGift].AuditList
		}
		iconUrl := utils.S3Url("public/icon/45a90a650d06b0f5badb6a4461ce20ea.png", appId)
		if len(iconList) > 0 {
			iconUrl = iconList[0]
		}
		var newGiftList []*response.GiftRes
		var newMaleGiftList []*response.GiftRes
		var haveFreeGift = false
		for _, gRes := range global.AllGiftList {
			// 获取该礼物的免费数量
			freeCount, _ := msgService.GetRewardGiftCount(userId, gRes.Id)
			if freeCount > 0 {
				haveFreeGift = true
			}
			newGiftList = append(newGiftList, &response.GiftRes{
				Id:        gRes.Id,
				FreeCount: freeCount,
				Name:      gRes.Name,
				Icon:      gRes.Icon,
				Consume:   gRes.Consume,
				Msg:       gRes.Msg,
			})
		}
		for _, gRes := range global.MaleGiftList {
			// 获取该礼物的免费数量
			freeCount, _ := msgService.GetRewardGiftCount(userId, gRes.Id)
			if freeCount > 0 {
				haveFreeGift = true
			}
			newMaleGiftList = append(newMaleGiftList, &response.GiftRes{
				Id:        gRes.Id,
				FreeCount: freeCount,
				Name:      gRes.Name,
				Icon:      gRes.Icon,
				Consume:   gRes.Consume,
				Msg:       gRes.Msg,
			})
		}

		giftFuncItem := response.AiFuncItem{
			FuncType: model.FuncTypeGift,
			Icon:     iconUrl,
			Msg:      "",
			GiftList: newGiftList,
			Dot:      haveFreeGift,
		}
		if aiRoleInfo.Gender == model.GenderTypeMale {
			giftFuncItem.GiftList = newMaleGiftList
		}
		resList = append(resList, giftFuncItem)
	}
	diyOptionCompare := utils.CompareVersions(appVersion, "2.0.1")
	if aiRoleInfo.RoleType == 400 {
		diyShow = false
	}
	if diyOptionCompare > 0 && diyShow {
		iconList := global.AppImgMap[appId][model.ImgConfigTypeDiy].NormalList
		if isAudit {
			iconList = global.AppImgMap[appId][model.ImgConfigTypeDiy].AuditList
		}
		iconUrl := utils.S3Url("public/icon/0ae4b373679293131771a400da528a59.png", appId)
		if len(iconList) > 0 {
			iconUrl = iconList[0]
		}
		resList = append(resList, response.AiFuncItem{
			FuncType: model.FuncTypeDiy,
			Icon:     iconUrl,
			Msg:      "",
		})
	}

	voiceCallCompare := utils.CompareVersions(appVersion, "2.3.0")
	// 语音屏蔽打开 1_03军锋需求修改文字转语音
	// voiceCallShow = false
	if voiceCallCompare >= 0 && voiceCallShow {
		minPrice := appInfo.DetailConfig.Data().MinPrice
		if IsVipSVipUser(userInfo) {
			if isSVip {
				minPrice = appInfo.DetailConfig.Data().MinPriceSVip
			} else if isSub {
				minPrice = appInfo.DetailConfig.Data().MinPriceSub
			}
		} else {
			minPrice = appInfo.DetailConfig.Data().ChatVipMinPrice
		}
		iconList := global.AppImgMap[appId][model.ImgConfigTypeVoiceCall].NormalList
		if isAudit {
			iconList = global.AppImgMap[appId][model.ImgConfigTypeVoiceCall].AuditList
		}
		iconUrl := utils.S3Url("public/image/a7fb20a5-649b-422f-9660-53b200550395.png", appId)
		if len(iconList) > 0 {
			iconUrl = iconList[0]
		}
		resList = append(resList, response.AiFuncItem{
			FuncType: model.FuncTypeVoiceCall,
			Icon:     iconUrl,
			Msg:      "",
			MinPrice: minPrice,
		})
	}

	privateCompare := utils.CompareVersions(appVersion, "3.1.0")
	if privateCompare >= 0 && privateShow {
		// 查询该AI角色的私人空间是否有内容
		var (
			privateSpaceCount int64
			privateSearchReq  = &model.PrivateSpace{AiId: aiRoleInfo.Id}
		)
		privateSpaceCount, err = privateSpaceSer.SearchCount(c, privateSearchReq)
		if err != nil {
			return
		}
		if privateSpaceCount > 0 {
			iconList := global.AppImgMap[appId][model.ImgConfigTypePrivateSpace].NormalList
			if isAudit {
				iconList = global.AppImgMap[appId][model.ImgConfigTypePrivateSpace].AuditList
			}
			iconUrl := utils.S3Url("public/image/b2d1b3f4-7213-4bd0-b3b6-f8ae0b7b7c7f.png", appId)
			if len(iconList) > 0 {
				iconUrl = iconList[0]
			}
			resList = append(resList, response.AiFuncItem{
				FuncType: model.FuncTypePrivateSpace,
				Icon:     iconUrl,
			})
		}
	}
	// 索要图片功能
	if aiRoleInfo.RoleType == 400 {
		bodyImgShow = false
	}
	if intimateInfo.Level < 3 {
		bodyImgShow = false
	}
	if bodyImgShow {
		var diyConfigType model.DiyConfigType
		if aiRoleInfo.RoleType == model.AiRoleTypeAnime {
			diyConfigType = model.DiyConfigTypeAnime
		} else {
			if aiRoleInfo.Gender == model.GenderTypeMale {
				diyConfigType = model.DiyConfigTypeMale
			} else {
				diyConfigType = model.DiyConfigTypeFemale
			}
		}

		appBodyImgArr, appBodyImgArrExist := global.AllAppAiBodyImg[appId][diyConfigType]
		if appBodyImgArrExist && len(appBodyImgArr) > 0 {
			var newBodyImgArr []response.AiBodyImgSwitchRes
			for _, bodyImg := range appBodyImgArr {
				newItem := response.AiBodyImgSwitchRes{
					ID:       bodyImg.Id,
					BodyName: bodyImg.BodyName,
					Icon:     utils.S3Url(bodyImg.Icon, userInfo.AppID),
					Sort:     bodyImg.Sort,
				}
				newBodyImgArr = append(newBodyImgArr, newItem)
			}
			iconList := global.AppImgMap[appId][model.ImgConfigTypeBodyImg].NormalList
			if isAudit {
				iconList = global.AppImgMap[appId][model.ImgConfigTypeBodyImg].AuditList
			}
			iconUrl := utils.S3Url("public/image/2b87a03b-cb06-4663-b66b-c029d0505799.png", appId)
			if len(iconList) > 0 {
				iconUrl = iconList[0]
			}
			resList = append(resList, response.AiFuncItem{
				FuncType: model.FuncTypeBodyImg,
				Icon:     iconUrl,
				BodyList: newBodyImgArr,
			})
		}
	}

	if realCallShow {
		appDetailConfig := appInfo.DetailConfig.Data()
		var minPrice = appDetailConfig.VideoCallMinPriceFree
		if isSVip {
			minPrice = appDetailConfig.VideoCallMinPriceSVip
		} else if isSub {
			minPrice = appDetailConfig.VideoCallMinPriceSub
		} else if isContentVip {
			minPrice = appDetailConfig.VideoCallMinPriceContentVip
		} else if isChatVip {
			minPrice = appDetailConfig.VideoCallMinPriceChatVip
		}
		resList = append(resList, response.AiFuncItem{
			FuncType: model.FuncTypeRealCall,
			Icon:     "",
			Msg:      "",
			MinPrice: minPrice,
		})
	}

	res = gin.H{
		"list": resList,
	}
	t2 := time.Date(2025, 4, 9, 0, 0, 0, 0, time.UTC)
	if (userInfo.AppID == 4 && userInfo.CreatedAt.After(t2)) || appId == 25 {
		nsfwShow = false
		aiFuncCompare = 1
	}
	if aiFuncCompare >= 0 {
		res["nsfw"] = nsfwShow
	}
	return
}

func (m *DigitalUserService) GetAiFuncContent(c *gin.Context, req request.GetAiFuncContentReq) (err error) {
	var (
		userInfo          *model.DigitalUser
		aiRoleInfo        *model.AiRole
		aiRoleService     AiRoleService
		msgCountSer       UserAiMsgCountService
		msgService        MsgService
		userId            = utils.GetDigitalUserID(c)
		userIntimateSer   UsersIntimateService
		usersIntimateInfo *model.UsersIntimate
	)
	// 获取用户信息
	if userInfo, err = m.GetById(c, userId); err != nil {
		return
	}
	// 获取airole信息
	if aiRoleInfo, err = aiRoleService.GetByRoleId(req.AiRoleId); err != nil {
		return
	}
	if usersIntimateInfo, err = userIntimateSer.GetByUidAndRoleid(userId, aiRoleInfo.Id); err != nil {
		return
	}
	if usersIntimateInfo.Level < 3 && req.FuncType == model.FuncTypeBodyImg {
		return
	}
	switch req.FuncType {
	case model.FuncTypeAsmr:
		if err = msgService.SendAsmrMsg(userInfo.Id, aiRoleInfo.Id); err != nil {
			return
		}
		// 更新UserAiMsgCount
		if err = msgCountSer.FindUpdate(c, userId, aiRoleInfo, map[string]interface{}{
			"unlock_asmr_count": gorm.Expr("unlock_asmr_count + 1"),
		}); err != nil {
			return
		}
	case model.FuncTypeSeeu:
		err = msgService.SendSeeuMsg(c, userInfo, aiRoleInfo)
	case model.FuncTypeBodyImg:
		// 给用户发身体位置图片
		err = msgService.SendBodyImgMsgNew(c, userInfo, aiRoleInfo, req)
	default:
		err = fmt.Errorf("func_type is not allowed")
	}
	// 从空闲队列中删除
	global.REDIS.ZRem(c, global.IDLE_MSG_QUEUE_KEY, fmt.Sprintf("%d_%s", userId, req.AiRoleId))

	return
}

func (m *DigitalUserService) ImgBlindBox(c *gin.Context, req *request.ImgBlindBoxReq) (res response.ImgBlindBoxRes, err error) {
	var (
		userInfo      *model.DigitalUser
		aiRoleInfo    *model.AiRole
		aiRoleService AiRoleService
		userId        = utils.GetDigitalUserID(c)
	)
	// 获取用户信息
	if userInfo, err = m.GetById(c, userId); err != nil {
		return
	}
	// 获取airole信息
	if aiRoleInfo, err = aiRoleService.GetByRoleId(req.AiRoleId); err != nil {
		return
	}
	// 查询索要照片姿势图列表
	var diyConfigType model.DiyConfigType
	if aiRoleInfo.RoleType == model.AiRoleTypeAnime {
		diyConfigType = model.DiyConfigTypeAnime
	} else {
		if aiRoleInfo.Gender == model.GenderTypeMale {
			diyConfigType = model.DiyConfigTypeMale
		} else {
			diyConfigType = model.DiyConfigTypeFemale
		}
	}
	appBodyImgArr, appBodyImgArrExist := global.AllAppAiBodyImg[userInfo.AppID][diyConfigType]
	if appBodyImgArrExist && len(appBodyImgArr) > 0 {
		var newBodyImgArr []response.AiBodyImgSwitchRes
		for _, bodyImg := range appBodyImgArr {
			newItem := response.AiBodyImgSwitchRes{
				ID:       bodyImg.Id,
				BodyName: bodyImg.BodyName,
				Icon:     utils.S3Url(bodyImg.Icon, userInfo.AppID),
				Sort:     bodyImg.Sort,
			}
			newBodyImgArr = append(newBodyImgArr, newItem)
		}
		res.BodyImgArr = newBodyImgArr
	}
	imgBoxCfg := GetRequireImgBoxCfg(c, userInfo.AppID)
	res.MainTitle = imgBoxCfg.MainTitle
	res.Subtitle = imgBoxCfg.Subtitle
	return
}

func (m *DigitalUserService) GetAiFirstMsg(c *gin.Context, aiRoleId string) (agoData agora.AgoSendRes, err error) {
	var (
		transMsg      string
		userInfo      *model.DigitalUser
		aiRoleService AiRoleService
		transSer      TranslateService
		aiRole        *model.AiRole
		userId        = utils.GetDigitalUserID(c)
		msgService    MsgService
	)

	// 获取用户信息
	if userInfo, err = m.GetById(c, userId); err != nil {
		return
	}
	if userInfo.Nickname == "" {
		err = errors.New("please set username")
		return
	}
	// 查询airole
	if aiRole, err = aiRoleService.GetByRoleId(aiRoleId); err != nil {
		return
	}
	msgType, msgContent, err1 := msgService.GetMsgContent(c, userInfo, aiRole, model.SceneFirstMsg, "", 0)
	if err1 != nil {
		err = err1
		return
	}
	canVoice := false
	if aiRole.FirstVoice != "" {
		canVoice = true
		if msgType == model.MsgTypeTxtImg {
			msgType = model.MsgTypeTxtImgVoice
		} else if msgType == model.MsgTypeTxtVideo {
			msgType = model.MsgTypeTxtVideoVoice
		}
	}
	nowTime := time.Now()
	receiverID := fmt.Sprintf("%d", userId)

	sendMsgText := aiRole.FirstMsg
	appInfo := global.AppMap[userInfo.AppID]
	if transMsg, err = transSer.MsgTranslate(c, appInfo, userInfo, sendMsgText); err != nil {
		global.LOG.Error("TranslateText error", zap.Any("err", err))
	}
	msgContent.FirstMsgTyoe = msgType
	msgContent.Text = transMsg
	msgContent.OriginText = sendMsgText
	msgContent.CanVoice = canVoice
	// 语音屏蔽打开 1_03军锋需求修改文字转语音
	// msgContent.CanVoice = false

	firstMsg := model.MsgInfo{
		MessageID:      uuid.NewString(),
		SendTime:       nowTime.UnixMilli(),
		MessageType:    model.MsgTypeAIHelp,
		SenderID:       aiRoleId,
		SenderName:     aiRole.Nickname,
		ReceiverID:     receiverID,
		ReceiverName:   userInfo.Nickname,
		MessageContent: msgContent,
	}
	// 发送消息到声网
	if err = msgService.SendMsgToAgora(&firstMsg, appInfo); err != nil {
		global.LOG.Error("SendMsgToAgora error", zap.Any("err", err))
		return
	}

	// 保存发送过的记录
	aiSendMediaSer := AiSendMediaService{}
	aiSendMediaRecord := model.AiSendMedia{
		DigitalUserId: userInfo.Id,
		AiRoleId:      aiRole.Id,
		MediaType:     0,
		MediaURL:      "",
		ThumbnailURL:  "",
		AsmrName:      "",
		SendTime:      nowTime,
		UnlockState:   model.UnlockStateNo,
		UnlockTime:    nil,
	}
	if msgType == model.MsgTypeTxtImg || msgType == model.MsgTypeTxtImgVoice {
		// 发送的是图片
		aiSendMediaRecord.MediaType = model.MediaTypeImg
		aiSendMediaRecord.MediaURL = msgContent.ImgURL
		aiSendMediaRecord.ThumbnailURL = msgContent.ImgURL
		if !userInfo.IsAudit() {
			aiSendMediaRecord.CanUndress = msgContent.CanUndress
		}
		// if userInfo.IsSub() {
		// 	aiSendMediaRecord.UnlockState = model.UnlockStateYes
		// 	aiSendMediaRecord.UnlockTime = &now
		// }
		aiSendMediaSer.SaveByMsg(&aiSendMediaRecord)
	} else if msgType == model.MsgTypeTxtVideo || msgType == model.MsgTypeTxtVideoVoice {
		// 发送的是视频
		aiSendMediaRecord.MediaType = model.MediaTypeVideo
		aiSendMediaRecord.MediaURL = msgContent.FileURL
		aiSendMediaRecord.ThumbnailURL = msgContent.ImgURL
		aiSendMediaSer.SaveByMsg(&aiSendMediaRecord)
	}

	// 非审核模式加入队列,增加到1分钟没聊天的补发消息队列
	if !userInfo.IsAudit() {
		member := fmt.Sprintf("%d_%s", userId, aiRoleId)
		global.REDIS.ZAdd(c, global.IDLE_MSG_QUEUE_KEY, redis.Z{
			Score:  float64(time.Now().Unix()),
			Member: member,
		})
	}
	// 测试环境加上调试信息
	if global.CONFIG.System.Env == "local" {
		agoData.Debug = firstMsg
	}
	return
}

func (m *DigitalUserService) GetAiFirstVoice(c *gin.Context, data request.GetAiFirstVoiceReq) (res gin.H, err error) {
	var (
		aiSer  AiRoleService
		aiRole *model.AiRole
		appId  = utils.GetAppID(c)
	)
	if aiRole, err = aiSer.GetByRoleId(data.AiRoleId); err != nil {
		return
	}
	res = gin.H{
		"first_voice_url": utils.S3Url(aiRole.FirstVoice, appId),
	}
	return
}

func (m *DigitalUserService) UnlockData(c *gin.Context, data request.UnlockDataReq) (res gin.H, err error) {
	var (
		appVersion      = c.GetHeader("App-Version")
		userInfo        *model.DigitalUser
		userProfileInfo *model.DigitalUserProfile
		aiRoleInfo      *model.AiRole
		aiRoleService   AiRoleService
		profileService  DigitalUserProfileService
		msgCountSer     UserAiMsgCountService
		msgService      MsgService
		subType         model.IntimateSubType
		consumeAmount   int
		aiSendMediaSer  AiSendMediaService
		sendMsgInfo     *model.MsgInfo
		userId          = utils.GetDigitalUserID(c)
		userIdStr       = fmt.Sprintf("%d", userId)
	)
	// 获取用户信息
	if userInfo, err = m.GetById(c, userId); err != nil {
		return
	}
	userFreeUnlockCountToday := GetUserUnlockLv1ImgFreeCount(userInfo)
	unlockCountToday := GetUserUnlockLv1ImgCountToday(userInfo)
	isSub := userInfo.IsSub()
	isSvip := userInfo.IsSVip()
	isChatVip := userInfo.IsChatVip()
	isContentVip := userInfo.IsContentVip()
	if userProfileInfo, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	// 获取App信息
	appInfo, _ := global.AppMap[userInfo.AppID]

	// 获取airole信息
	if aiRoleInfo, err = aiRoleService.GetByRoleId(data.AiRoleId); err != nil {
		return
	}
	msgQuery := bson.M{"message_id": data.RelationId, "sender_id": aiRoleInfo.RoleID, "receiver_id": userIdStr}
	if err = global.MONGO.Find(c, msgQuery).One(&sendMsgInfo); err != nil {
		return
	}

	tdPointerMap := map[string]interface{}{
		"event_tag":         "coins_consume",
		"coins_consume_num": 0,
		"ai_role_id":        aiRoleInfo.RoleID,
		"ai_role_type":      aiRoleInfo.RoleType,
		"source":            "pic_consume",
	}
	switch data.ConsumeType {
	case model.ConsumeTypeMaskMsg:
		consumeAmount = appInfo.DetailConfig.Data().MaskMsgPerFlower
		if IsVipSVipUser(userInfo) {
			if isSvip {
				consumeAmount = appInfo.DetailConfig.Data().MaskMsgPerFlowerSVip
			} else if isSub {
				consumeAmount = appInfo.DetailConfig.Data().MaskMsgPerFlowerSub
			}
		} else {
			if !isChatVip {
				err = errs.ChatVipErr
				return
			} else {
				consumeAmount = 0
			}
		}
		subType = model.SubTypeMaskMsg
		tdPointerMap["source"] = "mask_msg"
	case model.ConsumeTypePrivatePhoto:
		consumeAmount = appInfo.DetailConfig.Data().PrivatePhotoFlower
		subType = model.SubTypePrivatePhoto
	case model.ConsumeTypeMaskPhoto, 6:
		consumeAmount = appInfo.DetailConfig.Data().MaskPhotoFlower
		if IsVipSVipUser(userInfo) {
			if isSvip {
				consumeAmount = appInfo.DetailConfig.Data().MaskPhotoFlowerSVIP
			} else if isSub {
				consumeAmount = appInfo.DetailConfig.Data().MaskPhotoFlowerSub
				// 孙宏伟需求:2024-10-31 20:28
				// AI Persona Android和AI Persona IOS包
				// 10.31前是老用户,之后是新用户,老用户开通VIP后解锁L1图片不消耗金币
				// 新用户按照后台配置消耗金币
				if userInfo.AppID == 1 || userInfo.AppID == 2 {
					if userInfo.CreatedAt.Before(global.OldSubTime) {
						consumeAmount = 0
					}
				}
			}
			if unlockCountToday < userFreeUnlockCountToday {
				// 免费获取
				consumeAmount = 0
			}
		} else {
			if !isContentVip {
				// 判断是否有免费次数
				if unlockCountToday >= userFreeUnlockCountToday {
					err = errs.ContentVipErr
					return
				} else {
					// 免费获取
					consumeAmount = 0
				}
			} else {
				consumeAmount = appInfo.DetailConfig.Data().MaskPhotoContentVip
			}
		}
		subType = model.SubTypeMaskPhoto
	default:
		err = fmt.Errorf("consume_type is not allowed")
		return
	}

	if data.ConsumeType == 6 {
		// 判断是否解锁过,解锁过就不扣费
		searchReq := &model.AiSendMedia{
			DigitalUserId: userInfo.Id,
			AiRoleId:      aiRoleInfo.Id,
			MediaType:     model.MediaTypeImg,
			MediaURL:      sendMsgInfo.MessageContent.ImgURL,
		}
		var findItem *model.AiSendMedia
		if findItem, err = aiSendMediaSer.SearchOne(searchReq); err == nil {
			if findItem.UnlockState == model.UnlockStateYes {
				consumeAmount = 0
			}
		}
	}

	// 旧版本SVip不消耗金币
	if userInfo.OldSVip && isSvip {
		consumeAmount = 0
	}
	if sendMsgInfo.MessageContent.Free {
		consumeAmount = 0
	}
	if appInfo.Id == 7 && (utils.CompareVersions(appVersion, "3.8.0") == 1 || utils.CompareVersions(appVersion, "3.8.0") == 0) {
		res = gin.H{}
		res["status"] = 0
		if data.Type == 1 && appInfo.Id == 7 && (utils.CompareVersions(appVersion, "3.8.0") == 1 || utils.CompareVersions(appVersion, "3.8.0") == 0) && userProfileInfo.FlowerAmount < consumeAmount {
			consumeAmount = 0
			res["status"] = 1
		}
	}
	tdPointerMap["coins_consume_num"] = -consumeAmount
	if userProfileInfo.FlowerAmount < consumeAmount {
		if IsVipSVipUser(userInfo) {
			versionCompare := utils.CompareVersions(appVersion, "1.15.0")
			if !isSub && versionCompare >= 0 {
				err = errs.RequireSubErr
				return
			}
			vCompare := utils.CompareVersions(appVersion, "3.0.0")
			if vCompare >= 0 {
				err = errs.FloShopErr
				return
			}
			err = errs.RequireBuyFloErr
			return
		} else {
			if !isContentVip {
				err = errs.ContentVipErr
				return
			} else {
				err = errs.FloShopErr
				return
			}
		}
	}
	go TdPointer(userInfo, userProfileInfo, td.ServerEvent, tdPointerMap)
	if err = msgService.SaveConsumeRecord(userId, aiRoleInfo, consumeAmount, data.ConsumeType); err != nil {
		return
	}
	// 保存亲密度
	if err = msgService.SaveIntimateRecord(c, userId, aiRoleInfo, model.IntimateTypeAdd, subType); err != nil {
		return
	}
	if data.ConsumeType == model.ConsumeTypeMaskPhoto || data.ConsumeType == 6 {
		sendRecord := &model.AiSendMedia{
			DigitalUserId: userInfo.Id,
			AiRoleId:      aiRoleInfo.Id,
			MediaType:     model.MediaTypeImg,
			MediaURL:      sendMsgInfo.MessageContent.ImgURL,
		}
		if err = aiSendMediaSer.UpdateUnlock(sendRecord); err != nil {
			return
		}
	}
	switch subType {
	case model.SubTypeMaskPhoto, 6:
		// 更新解锁lv1图片次数
		_ = UserUnlockLv1ImgCountTodayIncr(userInfo)
		// 更新UserAiMsgCount
		if err = msgCountSer.FindUpdate(c, userId, aiRoleInfo, map[string]interface{}{
			"unlock_lv1_img_count": gorm.Expr("unlock_lv1_img_count + 1"),
		}); err != nil {
			return
		}
		var fbSer FireBaseUtilSer
		_ = fbSer.SetFirebaseReportEvent(c, userId, response.FirebaseReportItem{
			EventID:   uuid.NewString(),
			EventName: "unlock_photo",
			PropertyMap: map[string]interface{}{
				"user_id":        userId,
				"ai_role_id":     aiRoleInfo.RoleID,
				"consume_amount": consumeAmount,
			},
		})
	}

	return
}

func (m *DigitalUserService) GenMsgVoice(c *gin.Context, data request.GenMsgVoiceReq) (res gin.H, err error) {
	var (
		appVersion      = c.GetHeader("App-Version")
		userInfo        *model.DigitalUser
		userProfileInfo *model.DigitalUserProfile
		aiRoleInfo      *model.AiRole
		aiRoleService   AiRoleService
		profileService  DigitalUserProfileService
		msgCountSer     UserAiMsgCountService
		sendMsgInfo     *model.MsgInfo
		msgService      MsgService
		voiceKey        string
		userId          = utils.GetDigitalUserID(c)
		appId           = utils.GetAppID(c)
		userIdStr       = fmt.Sprintf("%d", userId)
	)
	// 不生成语音  生成内容违规，账号被禁止了
	// 语音屏蔽打开 1_03军锋需求修改文字转语音
	// res = gin.H{
	// 	"voice_second": 0,
	// 	"voice_url":    utils.S3Url("public/other/silence.mp3", appId),
	// }
	// return
	// 获取用户信息
	if userInfo, err = m.GetById(c, userId); err != nil {
		return
	}
	appInfo := global.AppMap[userInfo.AppID]
	isSub := userInfo.IsSub()
	isSVip := userInfo.IsSVip()
	isChatVip := userInfo.IsChatVip()
	verCompare320 := utils.CompareVersions(appVersion, "3.2.0")
	if IsVipSVipUser(userInfo) {
		if verCompare320 >= 0 && !isSub {
			err = errs.RequireSubErr
			return
		}
	} else {
		if !isChatVip {
			err = errs.ChatVipErr
			return
		}
	}
	if userProfileInfo, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	genCount := appInfo.DetailConfig.Data().FreeGenVoice
	genFlower := appInfo.DetailConfig.Data().FreeGenVoicePerFlower

	if IsVipSVipUser(userInfo) {
		if isSVip {
			genCount = appInfo.DetailConfig.Data().SVipGenVoice
			genFlower = appInfo.DetailConfig.Data().SVipGenVoicePerFlower
		} else if isSub {
			genCount = appInfo.DetailConfig.Data().SubGenVoice
			genFlower = appInfo.DetailConfig.Data().SubGenVoicePerFlower
		}
	} else {
		genCount = 0
		genFlower = appInfo.DetailConfig.Data().ChatVipGenVoiceCoin
	}

	// 获取airole信息
	if aiRoleInfo, err = aiRoleService.GetByRoleId(data.SenderId); err != nil {
		return
	}
	// 查询用户今天生成语音的数量
	dayStr := time.Now().Format(time.DateOnly)
	rKey := fmt.Sprintf(global.AI_VOICE_GEN_COUNT_KEY, dayStr, userIdStr, aiRoleInfo.RoleID)

	voiceGenCount, _ := global.REDIS.Get(c, rKey).Int()
	// 检查是否超过数量
	isExceeds := voiceGenCount > genCount || genCount == 0

	// 已超出数量,检查鲜花数量是否足够
	if isExceeds && userProfileInfo.FlowerAmount < genFlower {
		if IsVipSVipUser(userInfo) {
			versionCompare := utils.CompareVersions(appVersion, "1.15.0")
			if !isSub && versionCompare >= 0 {
				err = errs.RequireSubErr
				return
			}
			vCompare := utils.CompareVersions(appVersion, "3.0.0")
			if vCompare >= 0 {
				err = errs.FloShopErr
				return
			}
			err = errs.RequireBuyFloErr
			return
		} else {
			err = errs.FloShopErr
			return
		}
	}
	// 查询聊天记录
	msgQuery := bson.M{"message_id": data.MessageId, "sender_id": aiRoleInfo.RoleID, "receiver_id": userIdStr}
	if err = global.MONGO.Find(c, msgQuery).One(&sendMsgInfo); err != nil {
		return
	}
	canVoice := sendMsgInfo.MessageContent.CanVoice
	if !canVoice {
		err = fmt.Errorf("message can't voice")
		return
	}
	msgText := sendMsgInfo.MessageContent.Text
	voiceText := msgService.ExtractVoiceTextV3(msgText)
	// 生成语音
	if voiceKey, err = msgService.ElevenLabsTts(data.MessageId, voiceText, aiRoleInfo); err != nil {
		return
	}
	if isExceeds {
		go TdPointer(userInfo, userProfileInfo, td.ServerEvent, map[string]interface{}{
			"event_tag":         "coins_consume",
			"coins_consume_num": -genFlower,
			"ai_role_id":        aiRoleInfo.RoleID,
			"ai_role_type":      aiRoleInfo.RoleType,
			"source":            "voice_consume",
		})
		if err = msgService.SaveConsumeRecord(userId, aiRoleInfo, genFlower, model.ConsumeTypeGenVoice); err != nil {
			return
		}
		// 保存亲密度
		if err = msgService.SaveIntimateRecord(c, userId, aiRoleInfo, model.IntimateTypeAdd, model.SubTypeGenVoice); err != nil {
			return
		}
	}
	// 更新UserAiMsgCount
	if err = msgCountSer.FindUpdate(c, userId, aiRoleInfo, map[string]interface{}{
		"unlock_voice_count": gorm.Expr("unlock_voice_count + 1"),
	}); err != nil {
		return
	}
	// 用户今天生成语音的数量+1
	global.REDIS.Incr(c, rKey)
	global.REDIS.Expire(c, rKey, time.Hour*24)
	res = gin.H{
		"voice_second": 0,
		"voice_url":    utils.S3Url(voiceKey, appId),
	}
	return
}

func (m *DigitalUserService) GetAsmrVoice(c *gin.Context, data request.GetAsmrVoiceReq) (res gin.H, err error) {
	var (
		appVersion      = c.GetHeader("App-Version")
		userInfo        *model.DigitalUser
		userProfileInfo *model.DigitalUserProfile
		aiRoleInfo      *model.AiRole
		aiRoleService   AiRoleService
		profileService  DigitalUserProfileService
		aiSendMediaSer  AiSendMediaService
		sendMsgInfo     *model.MsgInfo
		msgService      MsgService
		userId          = utils.GetDigitalUserID(c)
		userIdStr       = fmt.Sprintf("%d", userId)
	)
	// 获取用户信息
	if userInfo, err = m.GetById(c, userId); err != nil {
		return
	}
	// 获取App信息
	appInfo, _ := global.AppMap[userInfo.AppID]
	if userProfileInfo, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	// 查询聊天记录
	msgQuery := bson.M{"message_id": data.MessageId, "receiver_id": userIdStr}
	if err = global.MONGO.Find(c, msgQuery).One(&sendMsgInfo); err != nil {
		return
	}
	// 获取airole信息
	if aiRoleInfo, err = aiRoleService.GetByRoleId(sendMsgInfo.SenderID); err != nil {
		return
	}
	// 已超出数量,检查鲜花数量是否足够
	isSub := userInfo.IsSub()
	isSVip := userInfo.IsSVip()
	isChatVip := userInfo.IsChatVip()
	if IsVipSVipUser(userInfo) {
		if !isSub {
			err = errs.RequireSubErr
			return
		}
		if !isSub && !sendMsgInfo.MessageContent.Free {
			err = errs.RequireSubErr
			return
		}
	} else {
		if !isChatVip && !sendMsgInfo.MessageContent.Free {
			err = errs.ChatVipErr
			return
		}
	}
	asmrFlo := appInfo.DetailConfig.Data().FreeAsmrPerFlower
	if IsVipSVipUser(userInfo) {
		if isSVip {
			asmrFlo = appInfo.DetailConfig.Data().SVipAsmrPerFlower
		} else if isSub {
			asmrFlo = appInfo.DetailConfig.Data().SubAsmrPerFlower
		}
	} else {
		asmrFlo = appInfo.DetailConfig.Data().ChatVipAsmr
	}
	// 如果是免费的,那就不需要消耗鲜花
	if sendMsgInfo.MessageContent.Free {
		asmrFlo = 0
	}
	if userProfileInfo.FlowerAmount < asmrFlo {
		if IsVipSVipUser(userInfo) {
			versionCompare := utils.CompareVersions(appVersion, "1.15.0")
			if !isSub && versionCompare >= 0 {
				err = errs.RequireSubErr
				return
			}
			vCompare := utils.CompareVersions(appVersion, "3.0.0")
			if vCompare >= 0 {
				err = errs.FloShopErr
				return
			}
			err = errs.RequireBuyFloErr
			return
		} else {
			err = errs.FloShopErr
			return
		}
	}

	go TdPointer(userInfo, userProfileInfo, td.ServerEvent, map[string]interface{}{
		"event_tag":         "coins_consume",
		"coins_consume_num": -asmrFlo,
		"ai_role_id":        aiRoleInfo.RoleID,
		"ai_role_type":      aiRoleInfo.RoleType,
		"source":            "asmr_consume",
	})
	if err = msgService.SaveConsumeRecord(userId, aiRoleInfo, asmrFlo, model.ConsumeTypeAsmr); err != nil {
		return
	}
	// 保存亲密度
	if err = msgService.SaveIntimateRecord(c, userId, aiRoleInfo, model.IntimateTypeAdd, model.SubTypeAsmr); err != nil {
		return
	}
	// 从空闲队列中删除
	global.REDIS.ZRem(c, global.IDLE_MSG_QUEUE_KEY, fmt.Sprintf("%d_%s", userId, aiRoleInfo.RoleID))
	res = gin.H{
		"voice_second": 0,
		"voice_url":    sendMsgInfo.MessageContent.FileURL,
	}
	sendRecord := &model.AiSendMedia{
		DigitalUserId: userInfo.Id,
		AiRoleId:      aiRoleInfo.Id,
		MediaType:     model.MediaTypeAsmr,
		MediaURL:      sendMsgInfo.MessageContent.FileURL,
	}
	if err = aiSendMediaSer.UpdateUnlock(sendRecord); err != nil {
		return
	}
	return
}

func (m *DigitalUserService) UnlockVideo(c *gin.Context, data request.GenMsgVideoReq) (res gin.H, err error) {
	var (
		appVersion      = c.GetHeader("App-Version")
		userInfo        *model.DigitalUser
		userProfileInfo *model.DigitalUserProfile
		aiRoleInfo      *model.AiRole
		aiRoleService   AiRoleService
		profileService  DigitalUserProfileService
		aiSendMediaSer  AiSendMediaService
		msgCountSer     UserAiMsgCountService
		sendMsgInfo     *model.MsgInfo
		msgService      MsgService
		userId          = utils.GetDigitalUserID(c)
		userIdStr       = fmt.Sprintf("%d", userId)
	)
	// 获取用户信息
	if userInfo, err = m.GetById(c, userId); err != nil {
		return
	}
	isSub := userInfo.IsSub()
	isSVip := userInfo.IsSVip()
	isContentVip := userInfo.IsContentVip()
	appInfo := global.AppMap[userInfo.AppID]

	if IsVipSVipUser(userInfo) {
		verCompare350 := utils.CompareVersions(userInfo.Version, "3.5.0")
		if verCompare350 >= 0 && !isSub {
			err = errs.RequireSubErr
			return
		}
	} else {
		if !isContentVip {
			err = errs.ContentVipErr
			return
		}
	}

	if userProfileInfo, err = profileService.GetByUserId(userId); err != nil {
		return
	}

	// 获取airole信息
	if aiRoleInfo, err = aiRoleService.GetByRoleId(data.SenderId); err != nil {
		return
	}

	// 已经解锁过的资源不进行扣费
	msgQuery := bson.M{"message_id": data.MessageId, "sender_id": aiRoleInfo.RoleID, "receiver_id": userIdStr}
	if err = global.MONGO.Find(c, msgQuery).One(&sendMsgInfo); err != nil {
		return
	}
	mediaUrl := utils.ReplaceS3Domain(sendMsgInfo.MessageContent.FileURL, appInfo.S3ImgPrefix)
	isCostKey := fmt.Sprintf(global.MEDIA_IS_COST_FLOWER, userId, aiRoleInfo.Id, mediaUrl)
	isCostKeyInt := global.REDIS.Exists(c, isCostKey).Val()
	// TODO 从数据库查询
	if isCostKeyInt > 0 {
		res = gin.H{
			"status":    0,
			"video_url": sendMsgInfo.MessageContent.FileURL,
		}
		return
	}
	watchCount := appInfo.DetailConfig.Data().FreeWatchVideoNum
	genFlower := appInfo.DetailConfig.Data().FreeGenVideoPerFlower

	if IsVipSVipUser(userInfo) {
		if isSVip {
			watchCount = appInfo.DetailConfig.Data().SVIPWatchVideoNum
			genFlower = appInfo.DetailConfig.Data().SVIPGenVideoPerFlower
		} else if isSub {
			watchCount = appInfo.DetailConfig.Data().SubWatchVideoNum
			genFlower = appInfo.DetailConfig.Data().SubGenVideoPerFlower
		}
	} else {
		watchCount = 0
		genFlower = appInfo.DetailConfig.Data().ContentVipGenVideo
	}

	// 旧版本SVip不消耗金币
	if userInfo.OldSVip && isSVip {
		genFlower = 0
	}
	// 如果信息是free的,消耗金币数也是0
	if sendMsgInfo.MessageContent.Free {
		genFlower = 0
	}
	res = gin.H{}
	if appInfo.Id == 7 && (utils.CompareVersions(appVersion, "3.8.0") == 1 || utils.CompareVersions(appVersion, "3.8.0") == 0) {
		res["status"] = 0
		if data.Type == 1 && appInfo.Id == 7 && (utils.CompareVersions(appVersion, "3.8.0") == 1 || utils.CompareVersions(appVersion, "3.8.0") == 0) && userProfileInfo.FlowerAmount < genFlower {
			genFlower = 0
			res["status"] = 1
		}
	}
	// 查询用户生成视频的数量
	rKey := fmt.Sprintf(global.AI_VIDEO_WATCH_COUNT_KEY, userIdStr, aiRoleInfo.RoleID)

	voiceGenCount, _ := global.REDIS.Get(c, rKey).Int()
	// 检查是否超过数量
	isExceeds := voiceGenCount > watchCount || watchCount == 0

	// 已超出数量,检查鲜花数量是否足够
	if isExceeds && userProfileInfo.FlowerAmount < genFlower {
		if IsVipSVipUser(userInfo) {
			versionCompare := utils.CompareVersions(appVersion, "1.15.0")
			if !isSub && versionCompare >= 0 {
				err = errs.RequireSubErr
				return
			}
			vCompare := utils.CompareVersions(appVersion, "3.0.0")
			if vCompare >= 0 {
				err = errs.FloShopErr
				return
			}
			err = errs.RequireBuyFloErr
			return
		} else {
			err = errs.FloShopErr
			return
		}
	}

	if isExceeds {
		go TdPointer(userInfo, userProfileInfo, td.ServerEvent, map[string]interface{}{
			"event_tag":         "coins_consume",
			"coins_consume_num": -genFlower,
			"ai_role_id":        aiRoleInfo.RoleID,
			"ai_role_type":      aiRoleInfo.RoleType,
			"source":            "video_consume",
		})
		if err = msgService.SaveConsumeRecord(userId, aiRoleInfo, genFlower, model.ConsumeTypeGetVideo); err != nil {
			return
		}
		// 保存亲密度
		if err = msgService.SaveIntimateRecord(c, userId, aiRoleInfo, model.IntimateTypeAdd, model.SubTypeGetVideo); err != nil {
			return
		}
		// 更新UserAiMsgCount
		if err = msgCountSer.FindUpdate(c, userId, aiRoleInfo, map[string]interface{}{
			"unlock_video_count": gorm.Expr("unlock_video_count + 1"),
		}); err != nil {
			return
		}
	}
	// 用户今天生成语音的数量+1
	global.REDIS.Incr(c, rKey)
	global.REDIS.Expire(c, rKey, time.Hour*24)
	// 解锁过的资源存储起来
	global.REDIS.Set(c, isCostKey, 1, global.MEDIA_IS_COST_FLOWER_KEY_EXPIRE*time.Second)
	res["video_url"] = sendMsgInfo.MessageContent.FileURL
	// 更新解锁的视频
	sendRecord := &model.AiSendMedia{
		DigitalUserId: userInfo.Id,
		AiRoleId:      aiRoleInfo.Id,
		MediaType:     model.MediaTypeVideo,
		MediaURL:      sendMsgInfo.MessageContent.FileURL,
		ThumbnailURL:  sendMsgInfo.MessageContent.ImgURL,
	}
	if err = aiSendMediaSer.UpdateUnlock(sendRecord); err != nil {
		return
	}
	return
}

func (m *DigitalUserService) ResetChat(c *gin.Context, data request.ResetChatReq) (err error) {
	var (
		userInfo      *model.DigitalUser
		aiRoleInfo    *model.AiRole
		aiRoleService AiRoleService
		msgService    MsgService
		userId        = utils.GetDigitalUserID(c)
		userIdStr     = fmt.Sprintf("%d", userId)
	)
	// 获取用户信息
	if userInfo, err = m.GetById(c, userId); err != nil {
		return
	}
	// 获取app信息
	appInfo, _ := global.AppMap[userInfo.AppID]

	// 获取airole信息
	if aiRoleInfo, err = aiRoleService.GetByRoleId(data.RoleId); err != nil {
		return
	}
	if err = msgService.ResetChat(c, userIdStr, data.RoleId); err != nil {
		return
	}
	// 更新亲密值
	if err = msgService.SaveIntimateRecord(c, userId, aiRoleInfo, model.IntimateTypeReduce, model.SubTypeResetChat); err != nil {
		return
	}
	// 是否发送消息
	if appInfo.DetailConfig.Data().ResetMsgSwitch {
		go msgService.SendResetChatMsg(c, userInfo, aiRoleInfo, appInfo)
	}
	return
}

func (m *DigitalUserService) UpdateFcmToken(c *gin.Context, data *request.FcmTokenReq) {
	var (
		deviceId = c.GetHeader("Device-Id")
	)
	err := m.UpdateByDeviceIdMap(c, deviceId, map[string]interface{}{"fcm_token": data.FcmToken})
	if err != nil {
		global.LOG.Error("UpdateFcmToken error", zap.Any("err", err))
	}
	return
}

func (m *DigitalUserService) UpdateAdjustIdData(c *gin.Context, data *model.AdjustIdData) (err error) {
	var (
		userId     = utils.GetDigitalUserID(c)
		appId      = utils.GetAppID(c)
		appVersion = c.GetHeader("App-Version")
	)
	appInfo := global.AppMap[appId]
	// verCom := utils.CompareVersions(appVersion, "1.17.0")
	// if verCom >= 0 && appInfo.AppType == model.AppTypeIOS {
	// 	// reqHeaderBytes, _ := json.Marshal(c.Request.Header)
	// 	// reqHeaderStr := string(reqHeaderBytes)
	// 	// global.LOG.Info("UpdateAdjustIdData", zap.Any("reqHeaderStr", reqHeaderStr))
	// }
	if appInfo.AppType == model.AppTypeAndroid && appVersion == "1.2.2" {
		// Android 1.2.2版本的错误.
		dataAdid := data.Adid
		data.Adid = data.GpsAdid
		data.GpsAdid = dataAdid
	}
	err = m.UpdateByMap(c, userId, map[string]interface{}{"adjust_id_data": datatypes.NewJSONType(data)})
	return
}

func (m *DigitalUserService) SetNickname(c *gin.Context, data *request.SetNicknameReq) (err error) {
	var (
		userId = utils.GetDigitalUserID(c)
	)
	err = m.UpdateByMap(c, userId, map[string]interface{}{"nickname": data.Nickname})
	return
}

func (m *DigitalUserService) ChangePassword(c *gin.Context, data *request.ChangePasswordReq) (err error) {
	var (
		userId = utils.GetDigitalUserID(c)
	)
	err = m.UpdateByMap(c, userId, map[string]interface{}{"password": data.Password})
	return
}

func (m *DigitalUserService) SetUsername(c *gin.Context, data *request.SetUsernameReq) (err error) {
	var (
		usernameCount int64
		userId        = utils.GetDigitalUserID(c)
	)
	// 判断用户名是否存在
	if usernameCount, err = m.UsernameCount(data.Username); err != nil {
		return
	}
	if usernameCount > 0 {
		err = fmt.Errorf("username is exist")
		return
	}
	err = m.UpdateByMap(c, userId, map[string]interface{}{"username": data.Username})
	return
}

func (m *DigitalUserService) WebUnlockGame(c *gin.Context, data *request.WebUnlockGameReq) (err error) {
	var (
		userProfile    *model.DigitalUserProfile
		profileService DigitalUserProfileService
		userId         = utils.GetDigitalUserID(c)
		gameCoinMap    = map[uint]int{
			1: 100,
			2: 200,
			3: 300,
		}
	)
	gameCoin, gameCoinExist := gameCoinMap[data.GameId]
	if !gameCoinExist {
		err = fmt.Errorf("game id is not exist")
		return
	}
	// 获取用户profile信息
	if userProfile, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	// 检查金币是否足够
	if userProfile.FlowerAmount < gameCoin {
		err = errs.FloShopErr
		return
	}
	// 扣除金币
	updateMap := map[string]interface{}{
		"flower_amount": gorm.Expr("flower_amount - ?", gameCoin),
	}
	if data.GameId == 1 {
		updateMap["web_one"] = 1
	} else if data.GameId == 2 {
		updateMap["web_two"] = 1
	} else if data.GameId == 3 {
		updateMap["web_three"] = 1
	}
	if err = profileService.UpdateByMap(c, userId, updateMap); err != nil {
		return
	}
	return
}

func (m *DigitalUserService) CompleteUserInfo(c *gin.Context, data *request.CompleteUserInfoReq) (err error) {
	var (
		userId = utils.GetDigitalUserID(c)
	)
	editMap := map[string]interface{}{
		"nickname": data.Nickname,
		"gender":   data.Gender,
	}
	if data.Avatar != "" {
		editMap["avatar"] = data.Avatar
	}
	if data.Birthday != "" {
		editMap["birthday"] = data.Birthday
	}
	if len(data.LikeGender) > 0 {
		editMap["like_gender"] = data.LikeGender
	}
	if data.AgeRange != 0 {
		editMap["age_range"] = data.AgeRange
	}
	err = m.UpdateByMap(c, userId, editMap)
	if err != nil {
		global.LOG.Error("CompleteUserInfo error", zap.Any("err", err))
	}
	return
}

func (m *DigitalUserService) UserDel(c *gin.Context) (err error) {
	var (
		userId = utils.GetDigitalUserID(c)
	)
	return m.DeleteById(c, userId)
}

func (m *DigitalUserService) UserSubExpire(c *gin.Context, userInfo *model.DigitalUser, userProfile *model.DigitalUserProfile, productInfo *model.Product) (err error) {
	// 用户订阅过期
	var (
		profileService DigitalUserProfileService
		userEditMap    = make(map[string]interface{})
		profileEditMap = make(map[string]interface{})
	)
	if productInfo.ProductType == model.PTSVip || productInfo.ProductType == model.PTSVipWeek || productInfo.ProductType == model.PTSVipMonth {
		userInfo.SVipStatus = model.SubStatusExpire
		userEditMap["svip_status"] = model.SubStatusExpire
	} else {
		userInfo.SubStatus = model.SubStatusExpire
		userEditMap["sub_status"] = model.SubStatusExpire
	}
	userProfile.Nsfw = false
	profileEditMap["nsfw"] = false

	if err = m.UpdateByMap(c, userInfo.Id, userEditMap); err != nil {
		return
	}
	if err = profileService.UpdateByMap(c, userInfo.Id, profileEditMap); err != nil {
		return
	}
	return
}

func (m *DigitalUserService) UserSubRefund(c *gin.Context, userInfo *model.DigitalUser, userProfile *model.DigitalUserProfile, productInfo *model.Product) (err error) {
	// 用户订阅退款
	var (
		profileService DigitalUserProfileService
		userEditMap    = make(map[string]interface{})
		profileEditMap = make(map[string]interface{})
	)
	appInfo := global.AppMap[userInfo.AppID]
	userProfile.Nsfw = false
	profileEditMap["nsfw"] = false
	if productInfo.ProductType == model.PTSVip || productInfo.ProductType == model.PTSVipWeek || productInfo.ProductType == model.PTSVipMonth {
		userInfo.SVipStatus = model.SubStatusExpire
		userEditMap["svip_status"] = model.SubStatusExpire
		floCount := 0
		if productInfo.ProductType == model.PTSVipWeek {
			floCount = appInfo.DetailConfig.Data().SVipWeekSendFlo
		} else if productInfo.ProductType == model.PTSVipMonth {
			floCount = appInfo.DetailConfig.Data().SVipMonthSendFlo
		} else if productInfo.ProductType == model.PTSVip {
			floCount = appInfo.DetailConfig.Data().SVipYearSendFlo
		}
		userProfile.FlowerAmount -= floCount
		if userProfile.FlowerAmount < 0 {
			userProfile.FlowerAmount = 0
		}
		profileEditMap["flower_amount"] = userProfile.FlowerAmount
	} else {
		userInfo.SubStatus = model.SubStatusExpire
		userEditMap["sub_status"] = model.SubStatusExpire
	}
	if err = m.UpdateByMap(c, userInfo.Id, userEditMap); err != nil {
		return
	}
	if err = profileService.UpdateByMap(c, userInfo.Id, profileEditMap); err != nil {
		return
	}
	go TdPointer(userInfo, userProfile, td.ServerEvent, map[string]interface{}{
		"event_tag":  "cancel_sub",
		"sub_type":   productInfo.ProductType,
		"amount":     productInfo.USDPrice,
		"product_id": productInfo.Id,
	})
	return
}

func (m *DigitalUserService) AiRoleTypeList(c *gin.Context) (res gin.H, err error) {
	var (
		userInfo         *model.DigitalUser
		aiRoleTypeList   []gin.H
		userBadgeService UserBadgeService
		appVersion       = c.GetHeader("App-Version")
		userId           = utils.GetDigitalUserID(c)
		appId            = utils.GetAppID(c)
	)
	// 获取用户信息
	if userInfo, err = m.GetById(c, userId); err != nil {
		return
	}
	isAudit := userInfo.IsAudit()
	for _, l := range model.AiRoleTypeList {
		label := model.AiRoleTypeMap[l]
		if isAudit && (l == model.AiRoleTypeHot || l == model.AiRoleTypeAnime) && !lo.Contains([]uint{24, 21}, appId) {
			continue
		}
		if lo.Contains([]uint{24}, appId) && (l == model.AiRoleTypeFans || l == model.AiRoleTypePrivate) {
			continue
		}
		if lo.Contains([]uint{21}, appId) && l == model.AiRoleTypeFans {
			continue
		}
		aiRoleTypeList = append(aiRoleTypeList, gin.H{"label": label, "value": l})
	}
	res = gin.H{
		"list": aiRoleTypeList,
	}
	diyOptionCompare := utils.CompareVersions(appVersion, "2.0.0")
	if diyOptionCompare >= 0 {
		badgeList := userBadgeService.CheckLoginBadge(c, userId)
		if len(badgeList) > 0 {
			res["badge_list"] = badgeList
		}
	}
	return
}

func (m *DigitalUserService) AiRoleTypeListPub(c *gin.Context) (res gin.H, err error) {
	var (
		isAudit        bool
		aiRoleTypeList []gin.H
		userId         = utils.FromTokenGetUserID(c)
		clientIp       = c.ClientIP()
	)
	if userId == 0 {
		ipInfo, ipGeoErr := utils.GetIpInfoByIpGeoApi(clientIp)
		if ipGeoErr != nil {
			global.LOG.Error("GetIpInfoByIpGeoApi error", zap.Error(ipGeoErr))
		}
		// 检查是否是谷歌审核ip
		isAudit = utils.CheckIpAudit(clientIp, ipInfo)
	} else {
		// 获取用户信息
		userInfo, userErr := m.GetById(c, userId)
		if userErr != nil {
			err = userErr
			return
		}
		isAudit = userInfo.IsAudit()
	}
	for _, l := range model.AiRoleTypeList {
		label := model.AiRoleTypeMap[l]
		if isAudit && (l == model.AiRoleTypeHot || l == model.AiRoleTypeAnime) {
			continue
		}
		if l == model.AiRoleTypePrivate {
			continue
		}
		aiRoleTypeList = append(aiRoleTypeList, gin.H{"label": label, "value": l})
	}
	res = gin.H{
		"list": aiRoleTypeList,
	}
	return
}

func (m *DigitalUserService) FansTypeList(c *gin.Context) (res gin.H, err error) {
	var (
		userInfo       *model.DigitalUser
		aiRoleTypeList []gin.H
		userId         = utils.GetDigitalUserID(c)
	)
	// 获取用户信息
	if userInfo, err = m.GetById(c, userId); err != nil {
		return
	}
	verCompare330 := utils.CompareVersions(userInfo.Version, "3.3.0")
	isAudit := userInfo.IsAudit()
	if userInfo.AppID == 4 {
		isAudit = false
	}
	t2 := time.Date(2025, 4, 9, 0, 0, 0, 0, time.UTC)
	for _, l := range model.FansTypeList {
		if userInfo.AppID == 4 && userInfo.CreatedAt.After(t2) && l != model.AiRoleTypeFans {
			continue
		}
		label := model.AiRoleTypeMap[l]
		if isAudit && (l == model.AiRoleTypePublic || l == model.AiRoleTypeHot || l == model.AiRoleTypeAnime) {
			continue
		}
		if userInfo.AppID == 25 && (l == model.AiRoleTypePublic || l == model.AiRoleTypeHot || l == model.AiRoleTypeAnime) {
			continue
		}
		if isAudit && l == model.AiRoleTypePrivate && userInfo.AppID != 25 {
			continue
		}
		if verCompare330 < 0 && l == model.AiRoleTypePrivate && userInfo.AppID != 25 {
			continue
		}
		aiRoleTypeList = append(aiRoleTypeList, gin.H{"label": label, "value": l})
	}
	res = gin.H{
		"list": aiRoleTypeList,
	}
	return
}

func (m *DigitalUserService) CloseFlowerDialog(c *gin.Context, data *request.RoleIdRequest) (err error) {
	var (
		userInfo      *model.DigitalUser
		aiRoleInfo    *model.AiRole
		aiRoleService AiRoleService
		msgService    MsgService
		userId        = utils.GetDigitalUserID(c)
	)
	if userInfo, err = m.GetById(c, userId); err != nil {
		return
	}
	if aiRoleInfo, err = aiRoleService.GetByRoleId(data.RoleId); err != nil {
		return
	}
	appInfo := global.AppMap[userInfo.AppID]
	go msgService.SendCloseFlowerMsg(c, userInfo, aiRoleInfo, appInfo)
	return
}

func (m *DigitalUserService) CloseChatDialog(c *gin.Context, data *request.RoleIdRequest) (err error) {
	var (
		userInfo      *model.DigitalUser
		aiRoleInfo    *model.AiRole
		aiRoleService AiRoleService
		msgService    MsgService
		userId        = utils.GetDigitalUserID(c)
	)
	if userInfo, err = m.GetById(c, userId); err != nil {
		return
	}
	if aiRoleInfo, err = aiRoleService.GetByRoleId(data.RoleId); err != nil {
		return
	}
	appInfo := global.AppMap[userInfo.AppID]
	go msgService.SendCloseChatMsg(c, userInfo, aiRoleInfo, appInfo)
	return
}

func (m *DigitalUserService) LowFlowersDialog(c *gin.Context) (res gin.H, err error) {
	var (
		orderService OrdersService
		productInfo  *model.Product
		userInfo     *model.DigitalUser
		userId       = utils.GetDigitalUserID(c)
		appId        = utils.GetAppID(c)
	)
	if userInfo, err = m.GetById(c, userId); err != nil {
		return
	}
	// 查询用户是否购买过金币的商品
	isBuyCoin, _ := orderService.UserIsBuyCoin(userId)
	if isBuyCoin {
		return
	}
	productInfoType := model.ProductInfoTypeLowFlowerNoSub
	if userInfo.IsSub() {
		switch userInfo.SubType {
		case model.SubTypeWeek:
			productInfoType = model.ProductInfoTypeLowFlowerWeekSub
		case model.SubTypeMonth:
			productInfoType = model.ProductInfoTypeLowFlowerMonthSub
		case model.SubTypeYear:
			productInfoType = model.ProductInfoTypeLowFlowerYearSub
		default:
		}
	}
	// 查询商品
	productList := global.AppInfoTypeListMap[appId][productInfoType].ProductList
	if len(productList) == 0 {
		err = fmt.Errorf("product is not exist")
		return
	}
	productInfo = productList[0]
	if userInfo.AppID == 1 && userInfo.UserGroup == 2 {
		productInfoNew, productInfoNewExist := global.AllProductMap[11]
		if productInfoNewExist {
			productInfo = productInfoNew
		}
	}

	res = gin.H{
		"title":         "Special Starter Offer",
		"content":       "Special bundle to get your started!",
		"flo_count":     productInfo.Amount,
		"flo_count_del": productInfo.Amount / 2,
		"origin_price":  fmt.Sprintf("$%s", productInfo.OriginPrice),
		"price":         fmt.Sprintf("$%s", productInfo.USDPrice),
		"countdown_sec": 60 * 10,
		"id":            productInfo.Id,
		"goo_id":        productInfo.GooID,
		"ios_id":        productInfo.IOSID,
	}
	return
}

func (m *DigitalUserService) GenAiRolePreInfo(c *gin.Context) (res gin.H, err error) {
	var (
		roleImgInfoRes []gin.H
		appId          = utils.GetAppID(c)
	)
	for _, info := range global.AiRoleImgInfoList {
		roleImgInfoRes = append(roleImgInfoRes, gin.H{
			"id":          info.Id,
			"chat_bg_url": utils.S3Url(info.ChatBG, appId),
		})
	}
	genAiPreCfg := GetGenAiPreCfg(c, appId)
	res = gin.H{
		"label_list":     genAiPreCfg.LabelList,
		"first_msg_list": genAiPreCfg.FirstMsgList,
		"img_list":       roleImgInfoRes,
	}
	return
}

func (m *DigitalUserService) addGenImgRedisTask(ctx context.Context, taskId uint) (err error) {
	taskRedisReq := model.ImgToImgReq{
		TaskType: 1,
		TaskId:   taskId,
	}
	taskRedisReqBytes, _ := json.Marshal(taskRedisReq)
	err = global.REDIS.ZAdd(ctx, global.IMG_TO_IMG_TRAIN_KEY, redis.Z{Score: float64(time.Now().Unix()), Member: string(taskRedisReqBytes)}).Err()
	return
}

func (m *DigitalUserService) GenAiRole(c *gin.Context, reqData *request.GenAiRoleReq) (res gin.H, err error) {
	var (
		chatBg          string
		firstImg        string
		imgList         datatypes.JSONSlice[string]
		undressList     datatypes.JSONSlice[string]
		videoList       datatypes.JSONSlice[model.AiRoleVideoItem]
		defaultAiRole   *model.AiRole
		voiceInfoSer    VoiceInfoService
		aiRoleSer       AiRoleService
		aiRoleMediaSer  AiRoleMediaService
		userService     DigitalUserService
		imgGenRecordSer ImgGenRecordService
		userInfo        *model.DigitalUser
		nowTime         = time.Now()
		nowTimestamp    = nowTime.Unix()
		userId          = utils.GetDigitalUserID(c)
		appId           = utils.GetAppID(c)
		roleId          = fmt.Sprintf("%d%08d", nowTimestamp, userId)
	)
	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	isAudit := userInfo.IsAudit()
	isSub := userInfo.IsSub()
	isSvip := userInfo.IsSVip()
	appInfo := global.AppMap[userInfo.AppID]
	if IsVipSVipUser(userInfo) {
		var genRoleCount int64
		// 查询用户生成AI角色的数量
		if genRoleCount, err = aiRoleSer.GetCountByUserId(c, userId); err != nil {
			return
		}
		if !isSvip && !isSub {
			if int(genRoleCount) >= appInfo.DetailConfig.Data().GenAICountFree {
				err = errs.RequireSubErr
				return
			}
		}
	} else {
		// 不限制
	}

	if reqData.AvatarType == 1 {
		// 系统预置头像,根据id获取写真集列表
		aiRoleImgInfo, aiRoleImgInfoExist := global.AiRoleImgInfoIdMap[reqData.ImgId]
		if !aiRoleImgInfoExist {
			err = fmt.Errorf("img_id not exist")
			return
		}
		reqData.Avatar = aiRoleImgInfo.Avatar
		chatBg = aiRoleImgInfo.ChatBG
		firstImg = aiRoleImgInfo.FirstImg
		imgList = aiRoleImgInfo.ImgList
		undressList = aiRoleImgInfo.UndressList
		videoList = aiRoleImgInfo.VideoList
	} else {
		chatBg = reqData.Avatar
	}

	if reqData.Gender == model.GenderTypeMale {
		// 男性角色
		defaultAiRole = global.DefaultMaleAiRole
	} else {
		// 女性角色
		defaultAiRole = global.DefaultFemaleAiRole
	}
	if reqData.FirstMsg == "" {
		reqData.FirstMsg = strings.ReplaceAll(defaultAiRole.FirstMsg, "{{char}}", reqData.Nickname)
	}
	if reqData.Describe == "" {
		reqData.Describe = strings.ReplaceAll(defaultAiRole.Describe, "{{char}}", reqData.Nickname)
	}
	aiRoleInfo := &model.AiRole{
		AppID:         userInfo.AppID,
		RoleID:        roleId,
		RoleType:      model.AiRoleTypePrivate,
		DigitalUserId: userId,
		RoleLabels:    reqData.RoleLabels,
		Nickname:      reqData.Nickname,
		Describe:      reqData.Describe,
		Gender:        reqData.Gender,
		Avatar:        reqData.Avatar,
		ChatBG:        chatBg,
		FirstMsg:      reqData.FirstMsg,
		FirstImg:      firstImg,
		ReqData:       defaultAiRole.ReqData,
		ImgList:       imgList,
		UndressList:   undressList,
		Examine:       model.ExamineTypeNormal,
		Enable:        model.SwitchStatusNormal,
	}
	if reqData.VoiceInfoId > 0 {
		aiRoleInfo.EleVoiceReq = reqData.EleVoiceReq
		// 使用数增加
		_ = voiceInfoSer.IncrUseCount(c, reqData.VoiceInfoId, 1)
	}
	if isAudit {
		aiRoleInfo.Examine = model.ExamineTypeAudit
	}
	if err = aiRoleSer.Create(c, aiRoleInfo); err != nil {
		return
	}
	var aiRoleMediaList []*model.AiRoleMedia
	if len(videoList) > 0 {
		for _, videoItem := range videoList {
			aiRoleMediaRecord := &model.AiRoleMedia{
				AiRoleId:     aiRoleInfo.Id,
				MediaType:    model.MediaTypeVideo,
				MediaURL:     videoItem.VideoUrl,
				ThumbnailURL: videoItem.ImageUrl,
				UndressUrl:   "",
				MediaName:    "",
				MediaLevel:   videoItem.Level,
				Sort:         0,
			}
			aiRoleMediaList = append(aiRoleMediaList, aiRoleMediaRecord)
		}
	}
	if len(aiRoleMediaList) > 0 {
		if err = aiRoleMediaSer.CreateMany(aiRoleMediaList); err != nil {
			return
		}
	}
	if reqData.AvatarType != 1 && reqData.Gender == model.GenderTypeFemale {
		// 女性才生成图片
		if err = m.addGenImgRedisTask(c, aiRoleInfo.Id); err != nil {
			return
		}
	}
	if reqData.ImgTaskId != 0 {
		imgGenUpdateMap := map[string]interface{}{
			"ai_id":      aiRoleInfo.Id,
			"ai_role_id": aiRoleInfo.RoleID,
		}
		if err = imgGenRecordSer.UpdateMap(c, reqData.ImgTaskId, imgGenUpdateMap); err != nil {
			return
		}
	}
	res = gin.H{
		"id":            aiRoleInfo.Id,
		"role_id":       aiRoleInfo.RoleID,
		"role_type":     aiRoleInfo.RoleType,
		"digital_user":  aiRoleInfo.DigitalUserId,
		"nickname":      aiRoleInfo.Nickname,
		"describe":      aiRoleInfo.Describe,
		"yarm_path":     nil,
		"gender":        aiRoleInfo.Gender,
		"avatar":        utils.S3Url(aiRoleInfo.Avatar, appId),
		"chat_bg":       utils.S3Url(aiRoleInfo.ChatBG, appId),
		"ele_voice_req": aiRoleInfo.EleVoiceReq,
		"goo_voice_req": aiRoleInfo.GooVoiceReq,
		"first_msg":     aiRoleInfo.FirstMsg,
		"first_voice":   aiRoleInfo.FirstVoice,
		"first_img":     aiRoleInfo.FirstImg,
		"img_list":      aiRoleInfo.ImgList,
		"undress_list":  aiRoleInfo.UndressList,
		"asmr_list":     aiRoleInfo.AsmrList,
		"examine":       aiRoleInfo.Examine,
		"sort":          aiRoleInfo.Sort,
	}
	return
}

func (m *DigitalUserService) AiRoleFaceImgCheck(c *gin.Context, reqData *request.AiRoleFaceImgCheckReq) (err error) {
	var checkSuccess bool
	checkSuccess, err = m.FaceImgCheck(c, reqData.FileKey)
	if err != nil {
		return
	}
	if !checkSuccess {
		err = fmt.Errorf("isn't face")
	}
	return
}

func (m *DigitalUserService) FaceImgCheck(ctx context.Context, fileKey string) (checkSuccess bool, err error) {
	var (
		imgCheckRes       ilivedata.ImgCheckRes
		fileUrl           = utils.S3Url(fileKey, 0)
		s3ClientV2        = GetS3ClientV2(ctx)
		imgCheckILiveData = GetILiveData()
	)
	apiReqMap := map[string]interface{}{
		"type":       1,
		"strategyId": "face_occlusion",
		"image":      fileUrl,
	}
	if imgCheckRes, err = imgCheckILiveData.Check(apiReqMap); err != nil {
		return
	}
	if imgCheckRes.ErrorCode != 0 || imgCheckRes.Code != 0 || imgCheckRes.Result != 0 {
		checkSuccess = false
	} else {
		checkSuccess = true
	}
	if err = s3ClientV2.PutObjectTag(fileKey, map[string]string{"face_check": fmt.Sprintf("%v", checkSuccess)}); err != nil {
		return
	}
	return
}

func (m *DigitalUserService) ReviewDialogInfo(c *gin.Context) (res gin.H, err error) {
	var (
		appId = utils.GetAppID(c)
	)
	appInfo, _ := global.AppMap[appId]
	if appInfo.Id == 16 {
		// 修改名称
		res = gin.H{
			"title":           "Enjoy Creating with Vidmagic？",
			"review_send_flo": appInfo.DetailConfig.Data().ReviewSendFlo,
			"wait_seconds":    appInfo.DetailConfig.Data().ReviewWaitSec,
			"content":         "Enjoy turning your photos into captivating videos with Vidmagic？If you're loving the experience, please rate us and share your feedback to help us improve and bring even more creativity to your fingertips!",
		}
		return
	}
	res = gin.H{
		"title":           "Enjoying AI Girl Chat?",
		"review_send_flo": appInfo.DetailConfig.Data().ReviewSendFlo,
		"wait_seconds":    appInfo.DetailConfig.Data().ReviewWaitSec,
		"content":         fmt.Sprintf("If you are satisfied our APP, Please take %ds rate us a good score and make a comment on Appstore, %d coins will top up to your account as a gift.", appInfo.DetailConfig.Data().ReviewWaitSec, appInfo.DetailConfig.Data().ReviewSendFlo),
	}
	return
}

func (m *DigitalUserService) UserFlowerAmount(c *gin.Context) (res gin.H, err error) {
	var (
		profileService DigitalUserProfileService
		userProfile    *model.DigitalUserProfile
		userId         = utils.GetDigitalUserID(c)
	)
	// 获取用户profile信息
	if userProfile, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	res = gin.H{
		"flower_amount": userProfile.FlowerAmount,
	}
	return
}

func (m *DigitalUserService) UploadFileUrl(c *gin.Context, data *request.UploadFileUrlReq) (res gin.H, err error) {
	var (
		userId    = utils.GetDigitalUserID(c)
		userIdStr = fmt.Sprintf("%d", userId)
	)
	userId = ********
	if !utils.IsImageFile(data.FileName) {
		err = fmt.Errorf("file type is not allowed")
		return
	}
	imgPathList := []string{"public", "img", userIdStr}
	switch data.FileType {
	case 1:
		imgPathList = append(imgPathList, "face")
	case 2:
		imgPathList = append(imgPathList, "normal")
	case 3:
		imgPathList = append(imgPathList, "ai_avatar")
	default:
		err = fmt.Errorf("file type is not allowed")
		return
	}
	fileExt := filepath.Ext(data.FileName)
	uuidStr := uuid.NewString()
	uuidStr = strings.ReplaceAll(uuidStr, "-", "")
	uuidFileName := fmt.Sprintf("%s%s", uuid.NewString(), fileExt)
	imgPathList = append(imgPathList, uuidFileName)
	fileKey := strings.Join(imgPathList, "/")
	fmt.Println("fileKey", fileKey)
	res = gin.H{"fields": fileExt, "url": fileKey}
	return
}

func (m *DigitalUserService) SaveFeedbackRecord(c *gin.Context, data *request.SaveFeedbackRecordReq) (res gin.H, err error) {
	var (
		tranSer     TranslateService
		feedBackSer FeedbackRecordService
		userId      = utils.GetDigitalUserID(c)
		appId       = utils.GetAppID(c)
	)
	feedback := model.FeedbackRecord{
		DigitalUserId: userId,
		Content:       data.Content,
		Star:          data.Star,
	}
	appInfo := global.AppMap[appId]
	if data.Content != "" {
		transTxt, transTxtErr := tranSer.TranslateText2(c, "zh", data.Content)
		if transTxtErr == nil {
			feedback.TranslateContent = transTxt
		}
		ddNotifyMsg := fmt.Sprintf(`## 用户反馈
- [应用名称: %s](https://aiadmin.aigirlchat.net/#/layout/backend_app/app)
- [应用ID: %d](https://aiadmin.aigirlchat.net/#/layout/backend_app/app)
- [用户ID: %d](https://aiadmin.aigirlchat.net/#/layout/backendUser/digitalUser?id=%d)
- 评分: %d
- 评价内容: %s
- 翻译: %s
`,
			appInfo.Name,
			appId,
			userId, userId,
			data.Star,
			data.Content,
			transTxt,
		)
		SendDDNotice(ddNotifyMsg)
	}
	if err = feedBackSer.Create(&feedback); err != nil {
		return
	}
	res = gin.H{
		"title":   "Thank You",
		"content": "Your input helps us make your experience better. Thanks again for your feedback.",
		"button":  "Okey",
	}
	return
}

func (m *DigitalUserService) SendDDNotifyUserAudit(c *gin.Context, appInfo model.App, userInfo *model.DigitalUser, notifyType int) {
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Error("SendDDNotifyUserAudit panic", zap.Any("err", err))
		}
	}()
	var (
		err     error
		resBody string
		data    = notify.Message{Msgtype: ""}
		token   = global.CONFIG.DDnotify.Token
		secret  = global.CONFIG.DDnotify.Secret
	)
	if token == "" || secret == "" {
		return
	}
	var sendText = fmt.Sprintf(`
** 进入审核状态 **
- [应用名称: %s](https://aiadmin.aigirlchat.net/#/layout/backend_app/app)
- 应用审核版本: %s
- 用户版本: %s
- [用户ID: %d](https://aiadmin.aigirlchat.net/#/layout/backendUser/digitalUser?id=%d)
- 注册时间: %s
- 用户国家: %s
- 设备ID: %s
- IP地址: %s
- [中台查询: %s](https://game-geo.51payment.live/geo/ip/query?ip=%s)
- [IP-API查询: %s](https://ip-api.com/#%s)
- [ipinfo.io查询: %s](https://ipinfo.io/widget/demo/%s)
- [ipinfo.info查询: %s](https://ipinfo.info/ip_api.php?ip=%s)
- [iplocation.net查询: %s](https://api.iplocation.net/?ip=%s)
`,
		appInfo.Name,
		appInfo.AuditVersion,
		userInfo.Version,
		userInfo.Id, userInfo.Id,
		userInfo.CreatedAt.Format(time.DateTime),
		userInfo.CountryCode,
		userInfo.DeviceID,
		userInfo.LoginIP,
		userInfo.LoginIP, userInfo.LoginIP,
		userInfo.LoginIP, userInfo.LoginIP,
		userInfo.LoginIP, userInfo.LoginIP,
		userInfo.LoginIP, userInfo.LoginIP,
		userInfo.LoginIP, userInfo.LoginIP,
	)
	if notifyType == 1 {
		sendText += "- **用户由审核版本进入审核模式**"
	} else {
		sendText += "- **用户因IP进入审核模式**"
	}
	data.Markdown.Title = "进入审核状态"
	data.Msgtype = "markdown"
	data.Markdown.Text = sendText
	resBody, err = notify.SendDingTalk(data, token, secret)
	if err != nil {
		global.LOG.Error("发送钉钉通知 error", zap.Any("err", err), zap.String("resBody", resBody))
		return
	}
}

// userDayLoginKey 用户每天活跃的redisKey
func (m *DigitalUserService) userDayActiveKey(userId uint, dayStr string) string {
	return fmt.Sprintf(global.UserDayActiveKey, dayStr, userId)
}

// userContinuousLoginKey 用户连续登录的redisKey
func (m *DigitalUserService) userContinuousActiveKey(userId uint) string {
	return fmt.Sprintf(global.USER_CONTINUOUS_LOGIN_DAY_KEY, userId)
}

// GetUserContinuousActiveDays 获取用户连续登录天数
func (m *DigitalUserService) GetUserContinuousActiveDays(ctx context.Context, userId uint) (days int) {
	continuousLoginKey := m.userContinuousActiveKey(userId)
	days, _ = global.REDIS.Get(ctx, continuousLoginKey).Int()
	return
}

// SetDayUserActive 设置用户每天活跃到redis
func (m *DigitalUserService) SetDayUserActive(ctx context.Context, userId uint) {
	var (
		nowTime    = time.Now()
		todayStr   = nowTime.Format(time.DateOnly)
		lastDay    = nowTime.AddDate(0, 0, -1)
		lastDayStr = lastDay.Format(time.DateOnly)
	)
	todayKey := m.userDayActiveKey(userId, todayStr)
	lastDayKey := m.userDayActiveKey(userId, lastDayStr)
	continuousLoginKey := m.userContinuousActiveKey(userId)
	// 设置今天登录
	setSucc := global.REDIS.SetNX(ctx, todayKey, 1, global.UserDayActiveExpire).Val()
	if setSucc {
		// 判断昨天是否登录过,如果登录过,那么就设置连续登录天数+1
		if global.REDIS.Exists(ctx, lastDayKey).Val() > 0 {
			// 设置连续登录
			global.REDIS.Incr(ctx, continuousLoginKey)
		} else {
			// 未登录过,连续登录天数置1
			global.REDIS.Set(ctx, continuousLoginKey, 1, 0)
		}
		// 更新用户活跃时间
		global.DB.Model(&model.DigitalUser{}).Where("id = ?", userId).Update("active_at", time.Now())
		m.DayActiveUsersIncr()
	}

}

func (m *DigitalUserService) VideoTxtList(c *gin.Context) (res gin.H, err error) {
	var (
		videoTxtList []response.VideoTxtListItem
		appId        = utils.GetAppID(c)
	)
	if len(global.AllVideoTxtList) == 0 {
		return
	}
	for _, item := range global.AllVideoTxtList {
		videoTxtList = append(videoTxtList, response.VideoTxtListItem{
			Id:          item.Id,
			Txt:         item.Txt,
			FileFullUrl: utils.S3Url(item.FileKey, appId),
			Source:      item.Source,
			Price:       item.Price,
		})
	}
	res = gin.H{
		"list": videoTxtList,
	}
	return
}

func (m *DigitalUserService) GenVideoTask(c *gin.Context, reqData *request.GenVideoTaskReq) (res gin.H, err error) {
	var (
		consume        int
		userInfo       *model.DigitalUser
		userService    DigitalUserService
		userProfile    *model.DigitalUserProfile
		profileService DigitalUserProfileService
		imgToVideoSer  ImgToVideoService
		appId          = utils.GetAppID(c)
		userId         = utils.GetDigitalUserID(c)
		appVersion     = c.GetHeader("App-Version")
	)
	verCompare := utils.CompareVersions(appVersion, "2.2.0")
	// 获取使用的mp3文件地址
	videoTxtItem, videoTxtItemExist := global.AllVideoTxtMap[reqData.TxtId]
	if !videoTxtItemExist {
		err = fmt.Errorf("video_txt: %d not exist", reqData.TxtId)
		return
	}
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	if userProfile, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	isSub := userInfo.IsSub()

	if verCompare > 0 {
		consume = videoTxtItem.Price
		if isSub {
			// 如果是订阅用户,价格减半
			consume = consume / 2
		}
		if userProfile.FlowerAmount < consume {
			vCompare := utils.CompareVersions(appVersion, "3.0.0")
			if vCompare >= 0 {
				err = errs.FloShopErr
				return
			}
			err = errs.RequireBuyFloErr
			return
		}
	} else {
		appInfo := global.AppMap[appId]
		limitCount := appInfo.DetailConfig.Data().FreeImgToVideoNum
		if isSub {
			limitCount = appInfo.DetailConfig.Data().SubImgToVideoNum
		}
		// 获取用户今天生成的次数
		genCount := imgToVideoSer.UserDayGenCount(c, userId)
		if !isSub {
			if genCount >= limitCount {
				err = errs.RequireSubErr
				return
			}
		}
	}

	taskRecord := &model.ImgToVideo{
		DigitalUserId: userId,
		TxtId:         reqData.TxtId,
		Consume:       consume,
		GenStatus:     model.GenStatusGenerating,
		UserImgUrl:    reqData.UserImg,
	}
	var fRecord = &model.FlowerConsumeRecord{
		DigitalUserId: userId,
		AiId:          0,
		AiRoleId:      "",
		ConsumeType:   model.ConsumeTypeImgToVideo,
		RelationID:    fmt.Sprintf("%d", userId),
		OriginAmount:  userProfile.FlowerAmount,
		ConsumeAmount: consume,
	}
	if err = global.DB.Transaction(func(tx *gorm.DB) (tErr error) {
		if verCompare > 0 {
			// 创建FlowerConsumeRecord记录
			if tErr = tx.Model(&model.FlowerConsumeRecord{}).Create(fRecord).Error; tErr != nil {
				return
			}
			updateMap := map[string]interface{}{
				"flower_amount": gorm.Expr("flower_amount - ?", consume),
			}
			if tErr = tx.Model(&model.DigitalUserProfile{}).Where("id = ?", userProfile.Id).Updates(updateMap).Error; tErr != nil {
				return
			}
		}
		if tErr = tx.Model(&model.ImgToVideo{}).Create(taskRecord).Error; tErr != nil {
			return
		}
		if tErr = imgToVideoSer.AddRedisTask(c, taskRecord.Id); tErr != nil {
			return
		}
		if tErr = imgToVideoSer.UserDayGenIncr(c, userId); tErr != nil {
			return
		}
		return
	}); err != nil {
		return
	}
	res = gin.H{
		"id":                taskRecord.Id,
		"user_img_full_url": utils.S3Url(reqData.UserImg, appId),
	}
	return
}

func (m *DigitalUserService) GenVideoHistory(c *gin.Context, reqData *request.GenVideoHistoryReq) (listRes []response.GenVideoHistoryItem, total int64, err error) {
	var (
		imgToVideos   []*model.ImgToVideo
		imgToVideoSer ImgToVideoService
		appId         = utils.GetAppID(c)
		userId        = utils.GetDigitalUserID(c)
	)
	searchReq := req.ImgToVideoSearch{}
	searchReq.Page = reqData.Page
	searchReq.PageSize = reqData.PageSize
	searchReq.DigitalUserId = userId
	searchReq.Id = reqData.TaskId

	imgToVideos, total, err = imgToVideoSer.GetList(c, searchReq)
	if err != nil {
		return
	}
	if len(imgToVideos) == 0 {
		return
	}

	for _, item := range imgToVideos {
		itemRes := response.GenVideoHistoryItem{
			Id:             item.Id,
			GenStatus:      item.GenStatus,
			VideoFullUrl:   "",
			VideoUrl:       item.VideoUrl,
			UserImgFullUrl: utils.S3Url(item.UserImgUrl, appId),
			UserImgUrl:     item.UserImgUrl,
		}
		if item.VideoUrl != "" {
			itemRes.VideoFullUrl = utils.S3Url(item.VideoUrl, appId)
		}
		listRes = append(listRes, itemRes)

	}
	return
}
