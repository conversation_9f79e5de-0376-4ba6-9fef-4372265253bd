package call

import (
	"aimsg-server/global"
	"context"
	"fmt"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"time"
)

const (
	RedisKeyFreeTime = "ai:call:free_time:AddFreeTime:%d"
)

func AddFreeTime(c context.Context, userId uint, seconds int) (err error) {
	redisKey := fmt.Sprintf(RedisKeyFreeTime, userId)
	err = global.REDIS.IncrBy(c, redisKey, int64(seconds)).Err()
	if err != nil {
		global.LOG.Error(err.Error())
		return
	}
	err = global.REDIS.Expire(c, redisKey, time.Hour*24*30).Err()
	if err != nil {
		global.LOG.Error(err.Error())
		return
	}
	return
}

func GetFreeTime(c context.Context, userId uint) (seconds int, err error) {
	redisKey := fmt.Sprintf(RedisKeyFreeTime, userId)
	seconds, err = global.REDIS.Get(c, redisKey).Int()
	if err != nil {
		global.LOG.Error(err.Error())
		return
	}
	return
}

func DelFreeTime(c context.Context, userId uint, seconds int) (status bool, err error) {
	freeTime, err := GetFreeTime(c, userId)
	if errors.Is(err, redis.Nil) {
		return false, nil
	}
	if err != nil && !errors.Is(err, redis.Nil) {
		return
	}
	if freeTime <= 0 {
		return
	}
	redisKey := fmt.Sprintf(RedisKeyFreeTime, userId)
	allFreeTime, err := global.REDIS.DecrBy(c, redisKey, int64(seconds)).Result()
	if err != nil {
		return
	}
	status = true
	if allFreeTime <= 0 {
		global.REDIS.Del(c, redisKey)
	}
	return
}
