package backend

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/model/cli/td"
	"aimsg-server/service/backend/ai_role"
	"aimsg-server/utils"
	"errors"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"math/rand"
	"sort"
	"time"
)

// ByUnlockState implements sort.Interface for []*model.AiSendMedia based on unlock state and time
type ByUnlockState []*model.AiSendMedia

func (a ByUnlockState) Len() int      { return len(a) }
func (a ByUnlockState) Swap(i, j int) { a[i], a[j] = a[j], a[i] }

// Less function defines the sorting logic
func (a ByUnlockState) Less(i, j int) bool {
	if a[i].UnlockState != a[j].UnlockState {
		return a[i].UnlockState > a[j].UnlockState
	}
	if a[i].UnlockState == model.UnlockStateYes {
		// If both are unlocked, sort by UnlockTime in descending order
		return a[i].UnlockTime.After(*a[j].UnlockTime)
	} else {
		// If both are locked, sort by SendTime in descending order
		return a[i].SendTime.After(a[j].SendTime)
	}
}

func (m *AiRoleService) AiRoleInfo(c *gin.Context, reqData request.AiRoleIdReq) (res *response.AiRoleListV2Res, err error) {
	var (
		roleInfo *model.AiRole
		appId    = utils.GetAppID(c)
	)
	if roleInfo, err = m.GetByRoleId(reqData.AiRoleId); err != nil {
		return
	}
	res = &response.AiRoleListV2Res{
		Id:           roleInfo.Id,
		RoleID:       roleInfo.RoleID,
		RoleType:     roleInfo.RoleType,
		RoleLabels:   roleInfo.RoleLabels,
		Nickname:     roleInfo.Nickname,
		Describe:     roleInfo.Describe,
		Gender:       roleInfo.Gender,
		Avatar:       utils.S3Url(roleInfo.Avatar, appId),
		GifUrl:       utils.S3Url(roleInfo.GifUrl, appId),
		WebThumbnail: utils.S3Url(roleInfo.WebThumbnail, appId),
		ChatBG:       utils.S3Url(roleInfo.ChatBG, appId),
		FirstMsg:     roleInfo.FirstMsg,
		FirstVoice:   utils.S3Url(roleInfo.FirstVoice, appId),
		FirstImg:     utils.S3Url(roleInfo.FirstImg, appId),
		ChatCount:    roleInfo.ChatCount,
		RolePlay:     roleInfo.RolePlay,
		RealAnchorId: roleInfo.RealAnchorId,
		OnlineStatus: roleInfo.OnlineStatus,
		CountStr:     utils.FormatNumber(roleInfo.ChatCount),
	}
	return
}

func (m *AiRoleService) DelAiRole(c *gin.Context, reqData request.AiRoleIdReq) (err error) {
	var (
		roleInfo *model.AiRole
		userId   = utils.GetDigitalUserID(c)
	)
	if userId == 0 {
		err = fmt.Errorf("user id is empty")
		return
	}
	searchReq := model.AiRole{RoleID: reqData.AiRoleId, RoleType: model.AiRoleTypePrivate, DigitalUserId: userId}
	if roleInfo, err = m.SearchOne(&searchReq); err != nil {
		return
	}
	return m.DeleteById(roleInfo.Id)
}

func (m *AiRoleService) isNewUser(targetTime time.Time) bool {
	// 计算当前时间减去 3 天
	threeDaysAgo := time.Now().Add(-72 * time.Hour)
	// 判断 targetTime 是否大于 threeDaysAgo
	if targetTime.After(threeDaysAgo) {
		return true
	} else {
		return false
	}
}

func (m *AiRoleService) AiRoleListV2(c *gin.Context, reqData *request.AiRoleListV2Req, aiType int) (res []*response.AiRoleListV2Res, total int64, err error) {
	var (
		resArr      []*model.AiRole
		userInfo    *model.DigitalUser
		appId       = utils.GetAppID(c)
		userService DigitalUserService
		userId      = utils.GetDigitalUserID(c)
	)

	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	isAudit := userInfo.IsAudit()
	var h AiList

	h = &AIRoleIndex{ // 原有排序逻辑
		c:       c,
		reqData: reqData,
		aiType:  aiType,
	}

	resArr, total, err = h.Handler()
	if err != nil {
		return
	}
	res = []*response.AiRoleListV2Res{}
	for _, v2Res := range resArr {
		// 2025-01-06 孙宏伟需求 ai_label
		labelArr := make([]response.AiRoleLabelAndIcon, 0)
		if v2Res.RoleType != 4 && !isAudit {
			// 创建 AiRoleLabelAndIcon 实例并追加到 labelArr
			labelArr = append(labelArr, response.AiRoleLabelAndIcon{
				RoleLabel: "send photo",
				LabelIcon: "https://images.aipersona.cloud/public/image/b96512b1-c057-40f8-a55b-23c586036e54.webp",
			})
			labelArr = append(labelArr, response.AiRoleLabelAndIcon{
				RoleLabel: "send video",
				LabelIcon: "https://images.aipersona.cloud/public/image/8f05d34d-a81f-4d9a-918b-ed925add6068.webp",
			})
		}
		if v2Res.RealAnchorId != 0 && !isAudit {
			labelArr = append(labelArr, response.AiRoleLabelAndIcon{
				RoleLabel: "video calling",
				LabelIcon: "https://images.aipersona.cloud/public/image/85a31d1b-9823-4be1-bc95-0573de45b3a0.webp",
			})

		}
		if len(v2Res.RoleLabels) > 0 {
			for _, v := range v2Res.RoleLabels {
				labelArr = append(labelArr, response.AiRoleLabelAndIcon{
					RoleLabel: v,
					LabelIcon: global.REDIS.Get(c, fmt.Sprintf(global.AI_ROLE_LABEL_KEY, v)).Val(),
				})
			}
		}

		newObj := &response.AiRoleListV2Res{
			Id:           v2Res.Id,
			RoleID:       v2Res.RoleID,
			RoleType:     v2Res.RoleType,
			RoleLabels:   v2Res.RoleLabels,
			Nickname:     v2Res.Nickname,
			Describe:     v2Res.Describe,
			Gender:       v2Res.Gender,
			Avatar:       utils.S3Url(v2Res.Avatar, appId),
			GifUrl:       utils.S3Url(v2Res.GifUrl, appId),
			VideoUrl:     utils.S3Url(v2Res.VideoUrl, appId),
			WebThumbnail: utils.S3Url(v2Res.WebThumbnail, appId),
			ChatBG:       utils.S3Url(v2Res.ChatBG, appId),
			FirstMsg:     v2Res.FirstMsg,
			RoleDesc:     v2Res.RoleDesc,
			FirstVoice:   utils.S3Url(v2Res.FirstVoice, appId),
			FirstImg:     utils.S3Url(v2Res.FirstImg, appId),
			ChatCount:    v2Res.ChatCount,
			RolePlay:     v2Res.RolePlay,
			RealAnchorId: v2Res.RealAnchorId,
			OnlineStatus: v2Res.OnlineStatus,
			CountStr:     utils.FormatNumber(v2Res.ChatCount),
			LabelList:    labelArr, // 2025-01-06 孙宏伟需求 ai_label
		}
		if isAudit {
			newObj.RolePlay = model.SwitchStatusDisabled
		}
		res = append(res, newObj)
	}

	return res, total, nil
}

type AiList interface {
	Handler() ([]*model.AiRole, int64, error)
}

type AIRoleIndex struct {
	c       *gin.Context
	reqData *request.AiRoleListV2Req
	aiType  int
}

func (h *AIRoleIndex) Handler() (list []*model.AiRole, total int64, err error) {
	var (
		c           = h.c
		reqData     = h.reqData
		aiType      = h.aiType
		userService DigitalUserService
		userInfo    *model.DigitalUser
		// userId      = uint(145966)
		userId = utils.GetDigitalUserID(c)
		m      = &AiRoleService{}
	)

	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	isNewUser := m.isNewUser(userInfo.CreatedAt)
	var isSortUser bool
	if isNewUser {
		isSortUser = true
	} else {
		isSortUser = false
	}

	isAudit := userInfo.IsAudit()
	searchReq := req.AiRoleSearch{
		IsSortUser: isSortUser,
	}
	searchReq.Page = reqData.Page
	searchReq.PageSize = reqData.PageSize
	searchReq.RoleType = reqData.RoleType
	searchReq.Enable = model.SwitchStatusNormal
	if isAudit || userInfo.AppID == 15 {
		searchReq.Examine = model.ExamineTypeAudit
		searchReq.AppID = userInfo.AppID
	} else {
		searchReq.Examine = model.ExamineTypeNormal
	}
	if reqData.RoleType == model.AiRoleTypePrivate {
		searchReq.DigitalUserId = userId
	}
	if reqData.RoleType == model.AiRoleTypePublic {
		searchReq.RoleType = 0
		searchReq.NotRoleType = model.AiRoleTypePrivate
	}
	searchReq.SortProp = "sort"
	searchReq.SortDesc = true

	//  2025-01-06 孙宏伟需求 list ai
	realAnchorGroup := false
	if userInfo.AppID == 13 {
		searchReq.AppID = userInfo.AppID
		if aiType == 0 {
			realAnchorGroup = false
		} else {
			realAnchorGroup = true
		}
	}
	if list, total, err = m.GetList(searchReq, realAnchorGroup); err != nil {
		return
	}
	return
}

func (m *AiRoleService) AiRoleListPub(c *gin.Context, reqData *request.AiRoleListPubReq) (res []*response.AiRoleListV2Res, total int64, err error) {
	var (
		isAudit     bool
		userService DigitalUserService
		appId       = utils.GetAppID(c)
		clientIp    = c.ClientIP()
		userId      = utils.FromTokenGetUserID(c)
	)
	if clientIp == "" {
		err = fmt.Errorf("ip is empty")
		return
	}
	if userId == 0 {
		ipInfo, ipGeoErr := utils.GetIpInfoByIpGeoApi(clientIp)
		if ipGeoErr != nil {
			global.LOG.Error("GetIpInfoByIpGeoApi error", zap.Error(ipGeoErr))
		}
		// 检查是否是谷歌审核ip
		isAudit = utils.CheckIpAudit(clientIp, ipInfo)
	} else {
		userInfo, userErr := userService.GetById(c, userId)
		if userErr != nil {
			err = userErr
			return
		}
		isAudit = userInfo.IsAudit()
	}
	searchReq := req.AiRoleSearch{}
	searchReq.Page = reqData.Page
	searchReq.PageSize = reqData.PageSize
	searchReq.RoleType = reqData.RoleType
	db := global.DB.Model(&model.AiRole{})
	if reqData.RoleType == model.AiRoleTypePublic {
		db = db.Where("role_type != ?", model.AiRoleTypePrivate)
	} else {
		db = db.Where("role_type = ?", reqData.RoleType)
	}
	db = db.Where("enable = ?", int(model.SwitchStatusNormal))
	if isAudit || appId == 15 {
		db = db.Where("examine = ?", int(model.ExamineTypeAudit))
		db = db.Where("app_id = ?", appId)
	} else {
		db = db.Where("examine = ?", int(model.ExamineTypeNormal))
	}
	if reqData.RoleType == model.AiRoleTypePrivate {
		if userId == 0 {
			err = errs.RequireLoginErr
			return
		}
		db = db.Where("digital_user_id = ?", userId)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(reqData.PageSize).Offset((reqData.Page - 1) * reqData.PageSize)
	db = db.Order("sort DESC")
	err = db.Find(&res).Error
	if err != nil {
		return
	}
	var newRes []*response.AiRoleListV2Res
	if len(res) == 0 {
		return
	}
	for _, v2Res := range res {
		if v2Res.Avatar != "" {
			v2Res.Avatar = utils.S3Url(v2Res.Avatar, appId)
		}
		if v2Res.GifUrl != "" {
			v2Res.GifUrl = utils.S3Url(v2Res.GifUrl, appId)
		}
		if v2Res.WebThumbnail != "" {
			v2Res.WebThumbnail = utils.S3Url(v2Res.WebThumbnail, appId)
		}
		if v2Res.FirstImg != "" {
			v2Res.FirstImg = utils.S3Url(v2Res.FirstImg, appId)
		}
		if v2Res.ChatBG != "" {
			v2Res.ChatBG = utils.S3Url(v2Res.ChatBG, appId)
		}
		if v2Res.FirstVoice != "" {
			v2Res.FirstVoice = utils.S3Url(v2Res.FirstVoice, appId)
		}
		v2Res.CountStr = utils.FormatNumber(v2Res.ChatCount)
		if isAudit {
			v2Res.RolePlay = model.SwitchStatusDisabled
		}
		newRes = append(newRes, v2Res)
	}
	return newRes, total, nil
}

func (m *AiRoleService) AiFansList(c *gin.Context, reqData *request.AiFansListReq) (res []*response.AiRoleListV2Res, total int64, err error) {
	var (
		userService DigitalUserService
		userInfo    *model.DigitalUser
		userId      = utils.GetDigitalUserID(c)
		appId       = utils.GetAppID(c)
	)
	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	isAudit := userInfo.IsAudit()
	isNewUser := m.isNewUser(userInfo.CreatedAt)
	var isSortUser bool
	if isNewUser {
		isSortUser = true
	} else {
		isSortUser = false
	}
	if isAudit {
		searchReq := req.AiRoleSearch{}
		searchReq.Page = reqData.Page
		searchReq.PageSize = reqData.PageSize
		searchReq.RoleType = reqData.RoleType
		db := global.DB.Table("live_receive_fans AS l")
		db = db.Where("l.live_user_id = ?", userId)
		if reqData.RoleType == model.AiRoleTypePublic {
			db = db.Where("a.role_type != ?", model.AiRoleTypePrivate)
		} else {
			db = db.Where("a.role_type = ?", reqData.RoleType)
		}
		db = db.Where("a.examine = ?", int(model.ExamineTypeAudit))
		db = db.Where("a.enable = ?", int(model.SwitchStatusNormal))
		db = db.Where("a.app_id = ?", userInfo.AppID)
		if reqData.RoleType == model.AiRoleTypePrivate {
			db = db.Where("a.digital_user_id = ?", userId)
		}
		db = db.Joins("LEFT JOIN ai_role AS a ON l.ai_id = a.id")
		if err = db.Count(&total).Error; err != nil {
			return
		}
		if total == 0 {
			return
		}
		db = db.Select("a.*")
		db = db.Limit(reqData.PageSize).Offset((reqData.Page - 1) * reqData.PageSize)
		if isSortUser {
			db = db.Order("a.sort_user DESC")
		} else {
			db = db.Order("l.updated_at ASC")
		}
		err = db.Find(&res).Error
		if err != nil {
			return
		}
	} else {
		searchReq := req.AiRoleSearch{}
		searchReq.Page = reqData.Page
		searchReq.PageSize = reqData.PageSize
		searchReq.RoleType = reqData.RoleType
		db := global.DB.Model(&model.AiRole{})
		if reqData.RoleType == model.AiRoleTypePublic {
			db = db.Where("role_type != ?", model.AiRoleTypePrivate)
		} else {
			db = db.Where("role_type = ?", reqData.RoleType)
		}
		db = db.Where("examine = ?", int(model.ExamineTypeNormal))
		db = db.Where("enable = ?", int(model.SwitchStatusNormal))
		if reqData.RoleType == model.AiRoleTypePrivate {
			db = db.Where("digital_user_id = ?", userId)
		}
		if err = db.Count(&total).Error; err != nil {
			return
		}
		db = db.Limit(reqData.PageSize).Offset((reqData.Page - 1) * reqData.PageSize)
		if isSortUser {
			db = db.Order("sort_user DESC")
		} else {
			db = db.Order("sort DESC")
		}
		err = db.Find(&res).Error
		if err != nil {
			return
		}
	}
	var newRes []*response.AiRoleListV2Res
	if len(res) == 0 {
		return
	}
	appVersion := c.GetHeader("App-Version")
	versionCompare := utils.CompareVersions(appVersion, "3.9.1")
	for _, v2Res := range res {
		if v2Res.Avatar != "" {
			v2Res.Avatar = utils.S3Url(v2Res.Avatar, appId)
		}
		if v2Res.GifUrl != "" {
			v2Res.GifUrl = utils.S3Url(v2Res.GifUrl, appId)
		}
		if appId != 4 {
			if v2Res.VideoUrl != "" {
				v2Res.VideoUrl = utils.S3Url(v2Res.VideoUrl, appId)
			}
		} else {
			if versionCompare >= 0 {
				if v2Res.VideoUrl != "" {
					v2Res.VideoUrl = utils.S3Url(v2Res.VideoUrl, appId)
				}
			} else {
				v2Res.VideoUrl = ""
			}
		}
		if v2Res.WebThumbnail != "" {
			v2Res.WebThumbnail = utils.S3Url(v2Res.WebThumbnail, appId)
		}
		if v2Res.FirstImg != "" {
			v2Res.FirstImg = utils.S3Url(v2Res.FirstImg, appId)
		}
		if v2Res.ChatBG != "" {
			v2Res.ChatBG = utils.S3Url(v2Res.ChatBG, appId)
		}
		if v2Res.FirstVoice != "" {
			v2Res.FirstVoice = utils.S3Url(v2Res.FirstVoice, appId)
		}
		v2Res.CountStr = utils.FormatNumber(v2Res.ChatCount)
		if isAudit {
			v2Res.RolePlay = model.SwitchStatusDisabled
		}
		newRes = append(newRes, v2Res)
	}

	return newRes, total, nil
}

func (m *AiRoleService) GetProfileInfo(c *gin.Context, reqData *request.AiRoleIdReq) (res *response.AiRoleProfileInfo, err error) {
	var (
		aiSendDataAll      []*model.AiSendMedia
		aiContactInfoList  []*model.AiContactInfo
		contactList        []*response.AiContactInfoItem
		aiProfileInfo      *model.AiProfileInfo
		aiRoleInfo         *model.AiRole
		userInfo           *model.DigitalUser
		userService        DigitalUserService
		aiSendMediaService AiSendMediaService
		buyCount           int64
		aiProfileInfoSer   AiProfileInfoService
		aiContactInfoSer   AiContactInfoService
		fCRService         FlowerConsumeRecordService
		appVersion         = c.GetHeader("App-Version")
		userId             = utils.GetDigitalUserID(c)
		// userId = uint(145966)
		appId = utils.GetAppID(c)
	)
	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	isAudit := userInfo.IsAudit()
	// 获取AI角色信息
	if aiRoleInfo, err = m.GetByRoleId(reqData.AiRoleId); err != nil {
		return
	}
	canUndress := len(aiRoleInfo.UndressList) > 0
	if isAudit {
		canUndress = false
	}
	// 查询ai_send_media表数据
	if aiSendDataAll, err = aiSendMediaService.GetAiSendDataAll(userId, aiRoleInfo.Id); err != nil {
		return
	}
	var (
		photoList []*model.AiSendMedia
		asmrList  []*model.AiSendMedia
		videoList []*model.AiSendMedia
	)
	for _, sendMedia := range aiSendDataAll {
		if isAudit {
			sendMedia.CanUndress = false
		}
		if sendMedia.MediaType == model.MediaTypeImg {
			photoList = append(photoList, sendMedia)
		} else if sendMedia.MediaType == model.MediaTypeAsmr && !isAudit {
			asmrList = append(asmrList, sendMedia)
		} else if sendMedia.MediaType == model.MediaTypeVideo && !isAudit {
			videoList = append(videoList, sendMedia)
		}
	}
	// 排序
	if len(photoList) > 0 {
		sort.SliceStable(photoList, func(i, j int) bool {
			return ByUnlockState(photoList).Less(i, j)
		})
	}
	if len(asmrList) > 0 {
		sort.SliceStable(asmrList, func(i, j int) bool {
			return ByUnlockState(asmrList).Less(i, j)
		})
	}
	if len(videoList) > 0 {
		sort.SliceStable(videoList, func(i, j int) bool {
			return ByUnlockState(videoList).Less(i, j)
		})
	}
	profileInfoCompare := utils.CompareVersions(appVersion, "2.1.0")
	if profileInfoCompare >= 0 && aiRoleInfo.RoleType == model.AiRoleTypeHot {
		// 获取AI角色profileInfo记录
		if aiProfileInfo, err = aiProfileInfoSer.GetByAiRoleId(c, reqData.AiRoleId); err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			} else {
				err = nil
				aiProfileInfo = nil
			}
		} else {
			// 获取AI角色联系方式记录
			if aiContactInfoList, err = aiContactInfoSer.SearchAiRoleIdAll(c, reqData.AiRoleId); err != nil {
				return
			}
			// 查询用户是否有解锁过联系方式
			buyCount, err = fCRService.SearchCount(&model.FlowerConsumeRecord{
				DigitalUserId: userId,
				ConsumeType:   model.ConsumeTypeGetContactInfo,
				RelationID:    fmt.Sprintf("%d", aiProfileInfo.Id),
			})
			if err != nil {
				return
			}
			if len(aiContactInfoList) > 0 {
				for _, contactInfo := range aiContactInfoList {
					icon := model.ContactTypeIconMap[contactInfo.ContactType]
					if icon != "" {
						icon = utils.S3Url(icon, appId)
					}
					newItem := response.AiContactInfoItem{
						Content:        contactInfo.Content,
						ContactTypeStr: model.ContactTypeMap[contactInfo.ContactType],
						Icon:           icon,
					}
					if buyCount == 0 {
						newItem.Content = "********"
					}
					contactList = append(contactList, &newItem)
				}
			}
		}
	}
	// 2025-01-06 孙宏伟需求 ai_label
	labelArr := make([]response.AiRoleLabelAndIcon, 0)
	if aiRoleInfo.RoleType != 4 && !isAudit {
		// 创建 AiRoleLabelAndIcon 实例并追加到 labelArr
		labelArr = append(labelArr, response.AiRoleLabelAndIcon{
			RoleLabel: "send photo",
			LabelIcon: "https://images.aipersona.cloud/public/image/b96512b1-c057-40f8-a55b-23c586036e54.webp",
		})
		labelArr = append(labelArr, response.AiRoleLabelAndIcon{
			RoleLabel: "send video",
			LabelIcon: "https://images.aipersona.cloud/public/image/8f05d34d-a81f-4d9a-918b-ed925add6068.webp",
		})
	}
	if aiRoleInfo.RealAnchorId != 0 && !isAudit {
		labelArr = append(labelArr, response.AiRoleLabelAndIcon{
			RoleLabel: "video calling",
			LabelIcon: "https://images.aipersona.cloud/public/image/85a31d1b-9823-4be1-bc95-0573de45b3a0.webp",
		})
	}
	if len(aiRoleInfo.RoleLabels) > 0 {
		for _, v := range aiRoleInfo.RoleLabels {
			labelArr = append(labelArr, response.AiRoleLabelAndIcon{
				RoleLabel: v,
				LabelIcon: global.REDIS.Get(c, fmt.Sprintf(global.AI_ROLE_LABEL_KEY, v)).Val(),
			})
		}
	}

	res = &response.AiRoleProfileInfo{
		Id:          aiRoleInfo.Id,
		RoleID:      aiRoleInfo.RoleID,
		RoleType:    aiRoleInfo.RoleType,
		RoleLabels:  aiRoleInfo.RoleLabels,
		Nickname:    aiRoleInfo.Nickname,
		CanUndress:  canUndress,
		ChatCount:   aiRoleInfo.ChatCount,
		CountStr:    utils.FormatNumber(aiRoleInfo.ChatCount),
		Describe:    aiRoleInfo.Describe,
		Gender:      aiRoleInfo.Gender,
		Avatar:      utils.S3Url(aiRoleInfo.Avatar, appId),
		PhotoList:   photoList,
		AsmrList:    asmrList,
		VideoList:   videoList,
		ContactLock: buyCount == 0,
		ContactList: contactList,
		ProfileInfo: aiProfileInfo,
		LabelList:   labelArr, // 2025-01-06 孙宏伟需求 ai_label
	}
	return
}

func (m *AiRoleService) UndressImg(c *gin.Context, reqData *request.UndressImgReq) (res gin.H, err error) {
	var (
		appVersion           = c.GetHeader("App-Version")
		undressRecordCount   int64
		aiRoleInfo           *model.AiRole
		userInfo             *model.DigitalUser
		userProfileInfo      *model.DigitalUserProfile
		userService          DigitalUserService
		profileService       DigitalUserProfileService
		msgService           MsgService
		aiRoleMediaSer       AiRoleMediaService
		undressRecordService UndressRecordService
		msgCountSer          UserAiMsgCountService
		imgTrainSer          ImgToImgTrainService
		roleMedia            *model.AiRoleMedia
		userId               = utils.GetDigitalUserID(c)
		appId                = utils.GetAppID(c)
	)
	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	isSVip := userInfo.IsSVip()
	isSub := userInfo.IsSub()
	isChatVip := userInfo.IsChatVip()
	isContentVip := userInfo.IsContentVip()
	if userProfileInfo, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	// 获取AI角色信息
	if aiRoleInfo, err = m.GetByRoleId(reqData.AiRoleId); err != nil {
		return
	}

	appInfo := global.AppMap[userInfo.AppID]
	consumeAmount := appInfo.DetailConfig.Data().FreeUndressPerFlower
	limitCount := appInfo.DetailConfig.Data().FreeUndressCount
	verCompare320 := utils.CompareVersions(userInfo.Version, "3.2.0")
	verCompare355 := utils.CompareVersions(userInfo.Version, "3.5.5")
	verCompare360 := utils.CompareVersions(userInfo.Version, "3.6.0")

	if IsVipSVipUser(userInfo) {
		if userInfo.AppID == 13 {
			if !isSub {
				err = errs.RequireSubErr
				return
			}
		} else {
			if verCompare320 >= 0 && !isSVip && verCompare360 < 0 {
				err = errs.RequireSVipErr
				return
			}
			if verCompare360 >= 0 && userInfo.AppID != 2 && userInfo.AppID != 1 && userInfo.AppID != 11 && !isSVip {
				err = errs.RequireSVipErr
				return
			}
		}
		if isSVip {
			consumeAmount = appInfo.DetailConfig.Data().SVipUndressPerFlower
			limitCount = appInfo.DetailConfig.Data().SVipUndressCount
		} else if isSub {
			consumeAmount = appInfo.DetailConfig.Data().SubUndressPerFlower
			limitCount = appInfo.DetailConfig.Data().SubUndressCount
		}
		// 旧版本SVip不消耗金币
		if userInfo.OldSVip && isSVip {
			consumeAmount = 0
		}
	} else {
		// 非内容Vip不可以使用
		if verCompare355 == 0 {
			if !isContentVip {
				err = errs.ContentVipErr
				return
			}
			consumeAmount = appInfo.DetailConfig.Data().ContentVipUndress
			limitCount = 0
		}
		if verCompare360 >= 0 && (appId == 4 || appId == 6 || appId == 7 || appId == 10 || appId == 17) {
			if !isChatVip && !isContentVip {
				// 免费用户,直接谈内容会员订阅
				err = errs.ContentVipErr
				return
			}
			if isContentVip {
				consumeAmount = appInfo.DetailConfig.Data().ContentVipUndress
			} else if isChatVip {
				consumeAmount = appInfo.DetailConfig.Data().ChatVipUndress
			} else {
				consumeAmount = appInfo.DetailConfig.Data().FreeUndressPerFlower
			}
			limitCount = 0
		}
	}
	// 判断是否超出免费次数
	dayStr := time.Now().Format("2006-01-02")
	rKey := fmt.Sprintf(global.UNDRESS_COUNT_KEY, dayStr, userId, reqData.AiRoleId)
	dayUndressCount, _ := global.REDIS.Get(c, rKey).Int()

	imgUrl := "public/other/fe05418fd9bd0f8d21188090f45639aa.png"
	if reqData.ImgUrl == "" {
		if len(aiRoleInfo.UndressList) > 0 {
			imgUrl = aiRoleInfo.UndressList[rand.Intn(len(aiRoleInfo.UndressList))]
		}
	} else {
		pubUrl := utils.PubS3Url(reqData.ImgUrl)
		undressPubPathS3 := imgTrainSer.GetImgToUndress(c, aiRoleInfo, pubUrl)
		if undressPubPathS3 != "" {
			imgUrl = undressPubPathS3
			consumeAmount = appInfo.DetailConfig.Data().RequireUndress
		} else {
			// 从ai_role_media里查数据
			mediaUrl := utils.ReplaceS3Domain(reqData.ImgUrl, appInfo.S3ImgPrefix)
			mediaSearch := &model.AiRoleMedia{AiRoleId: aiRoleInfo.Id, MediaType: model.MediaTypeImg, MediaURL: mediaUrl}
			if roleMedia, err = aiRoleMediaSer.SearchOne(mediaSearch); err != nil {
				if !errors.Is(err, gorm.ErrRecordNotFound) {
					return
				}
			}
			if roleMedia == nil || roleMedia.UndressUrl == "" {
				if len(aiRoleInfo.UndressList) > 0 {
					imgUrl = aiRoleInfo.UndressList[rand.Intn(len(aiRoleInfo.UndressList))]
				}
			} else {
				imgUrl = roleMedia.UndressUrl
			}
		}
	}
	// 检测是否undress过
	undressRecordCount, err = undressRecordService.SearchCount(&model.UndressRecord{DigitalUserId: userId, AiRoleId: aiRoleInfo.Id, MediaURL: imgUrl})
	if err != nil {
		return
	}
	if undressRecordCount == 0 {
		isExceeds := dayUndressCount > limitCount || limitCount == 0
		if isExceeds {
			go TdPointer(userInfo, userProfileInfo, td.ServerEvent, map[string]interface{}{
				"event_tag":         "coins_consume",
				"coins_consume_num": -consumeAmount,
				"ai_role_id":        aiRoleInfo.RoleID,
				"ai_role_type":      aiRoleInfo.RoleType,
				"source":            "undress_consume",
			})
			// 检测是否有足够的花
			if userProfileInfo.FlowerAmount < consumeAmount {
				if IsVipSVipUser(userInfo) {
					versionCompare := utils.CompareVersions(appVersion, "1.15.0")
					if !userInfo.IsSub() && versionCompare >= 0 {
						err = errs.RequireSubErr
						return
					}
					vCompare := utils.CompareVersions(appVersion, "3.0.0")
					if vCompare >= 0 {
						err = errs.FloShopErr
						return
					}
					err = errs.RequireBuyFloErr
					return
				} else {
					if !isChatVip {
						err = errs.ChatVipErr
						return
					}
					err = errs.FloShopErr
					return
				}
			}
			// 保存消费记录
			if err = msgService.SaveConsumeRecord(userId, aiRoleInfo, consumeAmount, model.ConsumeTypeUndress); err != nil {
				return
			}
		}
		// 保存亲密度
		if err = msgService.SaveIntimateRecord(c, userId, aiRoleInfo, model.IntimateTypeAdd, model.SubTypeUndress); err != nil {
			return
		}
		// 保存undress记录
		undressRecord := &model.UndressRecord{
			DigitalUserId: userId,
			AiRoleId:      aiRoleInfo.Id,
			MediaURL:      imgUrl,
			UndressTime:   time.Now(),
		}
		if err = undressRecordService.Create(undressRecord); err != nil {
			return
		}
	}
	imgFullUrl := utils.S3Url(imgUrl, appId)
	// 更新UserAiMsgCount
	if err = msgCountSer.FindUpdate(c, userId, aiRoleInfo, map[string]interface{}{
		"unlock_lv2_img_count": gorm.Expr("unlock_lv2_img_count + 1"),
	}); err != nil {
		return
	}

	var fbSer FireBaseUtilSer
	_ = fbSer.SetFirebaseReportEvent(c, userId, response.FirebaseReportItem{
		EventID:   uuid.NewString(),
		EventName: "undress_img",
		PropertyMap: map[string]interface{}{
			"user_id":        userId,
			"ai_role_id":     aiRoleInfo.RoleID,
			"consume_amount": consumeAmount,
		},
	})
	res = gin.H{
		"img_url": imgFullUrl,
	}
	return
}

func (m *AiRoleService) UnlockProfileContent(c *gin.Context, reqData *request.UnlockProfileContentReq) (resp interface{}, err error) {
	var (
		appVersion         = c.GetHeader("App-Version")
		consumeAmount      int
		subType            model.IntimateSubType
		consumeType        model.ConsumeType
		aiSendDataRecord   *model.AiSendMedia
		aiRoleInfo         *model.AiRole
		userInfo           *model.DigitalUser
		userProfileInfo    *model.DigitalUserProfile
		userService        DigitalUserService
		profileService     DigitalUserProfileService
		aiSendMediaService AiSendMediaService
		msgService         MsgService
		userId             = utils.GetDigitalUserID(c)
		now                = time.Now()
	)
	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	appInfo := global.AppMap[userInfo.AppID]
	isSub := userInfo.IsSub()
	isSVip := userInfo.IsSVip()
	isContentVip := userInfo.IsContentVip()
	isChatVip := userInfo.IsChatVip()
	if userProfileInfo, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	// 获取AI角色信息
	if aiRoleInfo, err = m.GetById(c, reqData.AiRoleId); err != nil {
		return
	}
	// 查询ai_send_media表数据
	if aiSendDataRecord, err = aiSendMediaService.GetById(reqData.MediaID); err != nil {
		return
	}
	if aiSendDataRecord.DigitalUserId != userId {
		return
	}
	if aiSendDataRecord.UnlockState == model.UnlockStateYes {
		return
	}

	if IsVipSVipUser(userInfo) {
		switch aiSendDataRecord.MediaType {
		case model.MediaTypeImg:
			consumeAmount = appInfo.DetailConfig.Data().MaskPhotoFlower
			if isSVip {
				consumeAmount = appInfo.DetailConfig.Data().MaskPhotoFlowerSVIP
			} else if isSub {
				consumeAmount = appInfo.DetailConfig.Data().MaskPhotoFlowerSub
			}
			subType = model.SubTypeMaskPhoto
			consumeType = model.ConsumeTypeMaskPhoto
		case model.MediaTypeVideo:
			consumeAmount = appInfo.DetailConfig.Data().FreeGenVideoPerFlower
			if isSVip {
				consumeAmount = appInfo.DetailConfig.Data().SVIPGenVideoPerFlower
			} else if isSub {
				consumeAmount = appInfo.DetailConfig.Data().SubGenVideoPerFlower
			}
			// 旧版本SVip不消耗金币
			if userInfo.OldSVip && userInfo.IsSVip() {
				consumeAmount = 0
			}
			subType = model.SubTypeGetVideo
			consumeType = model.ConsumeTypeGetVideo
		case model.MediaTypeAsmr:
			consumeAmount = appInfo.DetailConfig.Data().FreeAsmrPerFlower
			if isSVip {
				consumeAmount = appInfo.DetailConfig.Data().SVipAsmrPerFlower
			} else if isSub {
				consumeAmount = appInfo.DetailConfig.Data().SubAsmrPerFlower
			}
			subType = model.SubTypeAsmr
			consumeType = model.ConsumeTypeAsmr
		default:
			err = fmt.Errorf("mediaType is error")
		}
	} else {
		switch aiSendDataRecord.MediaType {
		case model.MediaTypeImg:
			consumeAmount = appInfo.DetailConfig.Data().MaskPhotoContentVip
			userFreeUnlockCountToday := GetUserUnlockLv1ImgFreeCount(userInfo)
			if !isContentVip {
				// 判断是否有免费次数
				unlockCountToday := GetUserUnlockLv1ImgCountToday(userInfo)
				if unlockCountToday >= userFreeUnlockCountToday {
					err = errs.ContentVipErr
					return
				} else {
					// 免费获取
					consumeAmount = 0
				}
			}
			subType = model.SubTypeMaskPhoto
			consumeType = model.ConsumeTypeMaskPhoto
		case model.MediaTypeVideo:
			consumeAmount = appInfo.DetailConfig.Data().ContentVipGenVideo
			if !isContentVip {
				err = errs.ContentVipErr
				return
			}
			subType = model.SubTypeGetVideo
			consumeType = model.ConsumeTypeGetVideo
		case model.MediaTypeAsmr:
			consumeAmount = appInfo.DetailConfig.Data().ChatVipAsmr
			if !isChatVip {
				err = errs.ChatVipErr
				return
			}
			subType = model.SubTypeAsmr
			consumeType = model.ConsumeTypeAsmr
		default:
			err = fmt.Errorf("mediaType is error")
		}
	}

	// if userInfo.IsSVip() {
	// 	consumeAmount = 0
	// }
	var lockStatus = 0
	if reqData.Type == 1 && appInfo.Id == 7 && (utils.CompareVersions(appVersion, "3.8.0") == 1 || utils.CompareVersions(appVersion, "3.8.0") == 0) && userProfileInfo.FlowerAmount < consumeAmount {
		lockStatus = 1
		consumeAmount = 0
	}
	if userProfileInfo.FlowerAmount < consumeAmount {
		if IsVipSVipUser(userInfo) {
			versionCompare := utils.CompareVersions(appVersion, "1.15.0")
			if !isSub && versionCompare >= 0 {
				err = errs.RequireSubErr
				return
			}
			vCompare := utils.CompareVersions(appVersion, "3.0.0")
			if vCompare >= 0 {
				err = errs.FloShopErr
				return
			}
			err = errs.RequireBuyFloErr
			return
		} else {
			if !isContentVip {
				err = errs.ContentVipErr
				return
			} else {
				err = errs.FloShopErr
				return
			}
		}
	}
	if err = msgService.SaveConsumeRecord(userId, aiRoleInfo, consumeAmount, consumeType); err != nil {
		return
	}
	// 保存亲密度
	if err = msgService.SaveIntimateRecord(c, userId, aiRoleInfo, model.IntimateTypeAdd, subType); err != nil {
		return
	}
	aiSendDataRecord.UnlockState = model.UnlockStateYes
	aiSendDataRecord.UnlockTime = &now
	if err = aiSendMediaService.Update(aiSendDataRecord); err != nil {
		return
	}

	if aiSendDataRecord.MediaType == model.MediaTypeImg {
		// 更新解锁lv1图片次数
		_ = UserUnlockLv1ImgCountTodayIncr(userInfo)
	}
	if appInfo.Id == 7 && (utils.CompareVersions(appVersion, "3.8.0") == 1 || utils.CompareVersions(appVersion, "3.8.0") == 0) {
		resp = &response.UnlockProfileContentResp{Status: lockStatus}
	} else {
	}
	return
}

func (m *AiRoleService) RequireImgSubMsg(c *gin.Context, reqData *request.RequireImgSubMsgReq) (err error) {
	var (
		originMsgInfo *model.MsgInfo
		aiInfo        *model.AiRole
		userInfo      *model.DigitalUser
		userService   DigitalUserService
		msgSer        MsgService
		userId        = utils.GetDigitalUserID(c)
		userIdStr     = fmt.Sprintf("%d", userId)
		nowTime       = time.Now()
	)
	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	requireImgSubMsgCfg := GetRequireImgSubMsgCfg(c, userInfo.AppID)
	appInfo := global.AppMap[userInfo.AppID]
	isSub := userInfo.IsSub()
	isSVip := userInfo.IsSVip()
	// 用户开通VIP后不再弹出该弹窗
	if isSub || isSVip {
		return
	}
	// 获取AI角色信息
	if aiInfo, err = m.GetByRoleId(reqData.AiRoleId); err != nil {
		return
	}
	// 查询聊天记录
	msgQuery := bson.M{"message_id": reqData.MessageId, "receiver_id": userIdStr}
	if err = global.MONGO.Find(c, msgQuery).One(&originMsgInfo); err != nil {
		err = fmt.Errorf("message find error, %v", err.Error())
		return
	}
	msgText := utils.RandList(requireImgSubMsgCfg.MsgList)
	msgContent := model.MessageContent{
		Avatar:            utils.S3Url(aiInfo.Avatar, userInfo.AppID),
		Text:              msgText,
		OriginText:        msgText,
		CanVoice:          false,
		CanUndress:        false,
		ImgURL:            utils.S3Url(originMsgInfo.MessageContent.BodyImgKey, userInfo.AppID),
		BodyMaskImg:       utils.S3Url(requireImgSubMsgCfg.MaskImg, userInfo.AppID),
		BodyImgKey:        originMsgInfo.MessageContent.BodyImgKey,
		BodyUndressResKey: originMsgInfo.MessageContent.BodyUndressResKey,
	}
	if requireImgSubMsgCfg.ProductID != 0 {
		var (
			productSer ProductService
		)
		// 查询商品信息
		if productInfo, productErr := productSer.GetById(requireImgSubMsgCfg.ProductID); productErr == nil {
			msgContent.ProductID = productInfo.Id
			msgContent.IosProductID = productInfo.IOSID
		}
	}
	// 发送消息
	msgInfo := &model.MsgInfo{
		MessageID:      uuid.NewString(),
		Channel:        nil,
		SendTime:       nowTime.UnixMilli(),
		DeletedAt:      nil,
		MessageType:    model.MsgTypeRequireImgSubMsg,
		SenderID:       aiInfo.RoleID,
		SenderName:     aiInfo.Nickname,
		ReceiverID:     fmt.Sprintf("%d", userInfo.Id),
		ReceiverName:   userInfo.Nickname,
		MessageContent: msgContent,
	}
	// 发送消息到声网
	if err = msgSer.SendMsgToAgora(msgInfo, appInfo); err != nil {
		global.LOG.Error("SendMsgToAgora error", zap.Any("err", err))
		return
	}
	return
}

func (m *AiRoleService) IndexPage(c *gin.Context, req *request.IndexReq) (res []*response.AiRoleListV2Res, total int64, err error) {
	var (
		userService DigitalUserService
		userId      = utils.GetDigitalUserID(c)
		appId       = utils.GetAppID(c)
		userInfo    *model.DigitalUser
	)
	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}

	// ---------21,24,25号包逻辑start
	if (appId == 21 || appId == 24 || appId == 25) && req.RoleType != model.AiRoleTypePrivate {
		return ai_role.GetIndexPagePkg(c, req, userInfo.IsAudit())
	}
	// ---------21,24,25号包逻辑end

	// ---------4号包逻辑start
	t2 := time.Date(2025, 4, 9, 0, 0, 0, 0, time.UTC)
	if appId == 4 && userInfo.CreatedAt.After(t2) && req.RoleType != model.AiRoleTypePrivate && req.RoleType != model.AiRoleTypeFans {
		return ai_role.GetIndexPagePkg4(c, req)
	}
	if (appId == 4 && userInfo.CreatedAt.After(t2) && req.RoleType == model.AiRoleTypeFans) || appId == 25 {
		selectNum, _ := global.REDIS.Get(c, fmt.Sprintf("ai:UserDayLive:userid:%d", userId)).Int()
		if selectNum == 0 {
			return
		}
		s := AiRoleService{}
		return s.AiRoleListV2(c, &request.AiRoleListV2Req{
			RoleType: req.RoleType,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, req.AiType)
	}
	// ---------4号包逻辑end

	isNewUser := m.isNewUser(userInfo.CreatedAt)
	var isSortUser bool
	if isNewUser {
		isSortUser = true
	} else {
		isSortUser = false
	}
	if appId == 15 || req.RoleType == model.AiRoleTypePrivate || isSortUser || userInfo.UserGroup == 2 || userInfo.IsAudit() {
		s := AiRoleService{}
		return s.AiRoleListV2(c, &request.AiRoleListV2Req{
			RoleType: req.RoleType,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, req.AiType)
	} else {
		if appId == 13 {
			aiType := 2
			if req.AiType == 1 {
				aiType = 1
			}
			res, total, err = ai_role.IndexRecommendV2(c, &request.IndexReq{
				RoleType: req.RoleType,
				Page:     req.Page,
				PageSize: req.PageSize,
				AiType:   aiType,
			})
		} else {
			res, total, err = ai_role.IndexRecommendV2(c, req)
		}
		if err != nil {
			return
		}
	}
	if len(res) == 0 && req.Page == 1 {
		s := AiRoleService{}
		return s.AiRoleListV2(c, &request.AiRoleListV2Req{
			RoleType: req.RoleType,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, req.AiType)
	}
	return
}
