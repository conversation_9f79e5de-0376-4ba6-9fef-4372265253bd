package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

type VoiceInfoService struct{}

func (m *VoiceInfoService) CopyInfo(sourceAppId, targetAppId uint) (err error) {
	var (
		sourceInfos []*model.VoiceInfo
		newInfos    []*model.VoiceInfo
		nowTime     = time.Now()
	)
	sourceInfos, err = m.FindByAppId(sourceAppId)
	if err != nil {
		return
	}
	for _, sourceInfo := range sourceInfos {
		sourceInfo.Id = 0
		sourceInfo.CreatedAt = nowTime
		sourceInfo.UpdatedAt = nowTime
		sourceInfo.AppID = targetAppId
		newInfos = append(newInfos, sourceInfo)
	}
	if len(newInfos) == 0 {
		return
	}
	err = global.DB.Model(&model.VoiceInfo{}).Create(&newInfos).Error
	return
}

func (m *VoiceInfoService) FindByAppId(appId uint) (resList []*model.VoiceInfo, err error) {
	err = global.DB.Model(&model.VoiceInfo{}).Where("app_id = ?", appId).Find(&resList).Error
	return
}

func (m *VoiceInfoService) Create(ctx context.Context, reqData *model.VoiceInfo) (err error) {
	err = global.DB.Model(&model.VoiceInfo{}).Create(reqData).Error
	m.SyncAll()
	return
}

func (m *VoiceInfoService) DeleteById(ctx context.Context, id uint) (err error) {
	err = global.DB.Model(&model.VoiceInfo{}).Where("id = ?", id).Delete(&model.VoiceInfo{}).Error
	m.SyncAll()
	return
}

func (m *VoiceInfoService) DeleteByIds(ctx context.Context, reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.VoiceInfo{}).Where("id IN (?)", reqData.Ids).Delete(&model.VoiceInfo{}).Error
	m.SyncAll()
	return
}

func (m *VoiceInfoService) Update(ctx context.Context, reqData *model.VoiceInfo) (err error) {
	err = global.DB.Model(&model.VoiceInfo{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	m.SyncAll()
	return
}

func (m *VoiceInfoService) IncrUseCount(ctx context.Context, recId, useCount uint) (err error) {
	err = global.DB.Model(&model.VoiceInfo{}).Where("id = ?", recId).Updates(map[string]interface{}{
		"use_count": gorm.Expr("use_count + ?", useCount),
		"real_use":  gorm.Expr("real_use + 1"),
	}).Error
	return
}

func (m *VoiceInfoService) GetById(ctx context.Context, id uint) (res *model.VoiceInfo, err error) {
	err = global.DB.Model(&model.VoiceInfo{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *VoiceInfoService) GetAll() (resList []*model.VoiceInfo, err error) {
	err = global.DB.Model(&model.VoiceInfo{}).Order("sort DESC").Find(&resList).Error
	return
}

func (m *VoiceInfoService) SyncAll() {
	var (
		err     error
		resList []*model.VoiceInfo
		allMap  = map[uint]*model.VoiceInfo{}
	)
	if resList, err = m.GetAll(); err != nil {
		global.LOG.Error("SyncAll 失败", zap.Error(err))
		return
	}
	for _, item := range resList {
		allMap[item.Id] = item
	}
	global.AllVoiceInfoMap = allMap
	global.AllVoiceInfoList = resList
}

func (m *VoiceInfoService) GetList(ctx context.Context, info req.VoiceInfoSearch) (resList []*model.VoiceInfo, total int64, err error) {
	db := global.DB.Model(&model.VoiceInfo{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.Gender != 0 {
		db = db.Where("gender = ?", info.Gender)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
