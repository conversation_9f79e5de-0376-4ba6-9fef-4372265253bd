package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm/clause"
)

type UserAiMsgCountService struct{}

func (m *UserAiMsgCountService) Create(ctx context.Context, reqData *model.UserAiMsgCount) (err error) {
	err = global.DB.Model(&model.UserAiMsgCount{}).Create(reqData).Error
	return
}

func (m *UserAiMsgCountService) DeleteById(ctx context.Context, id uint) (err error) {
	err = global.DB.Model(&model.UserAiMsgCount{}).Where("id = ?", id).Delete(&model.UserAiMsgCount{}).Error
	return
}

func (m *UserAiMsgCountService) DeleteByIds(ctx context.Context, reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.UserAiMsgCount{}).Where("id IN (?)", reqData.Ids).Delete(&model.UserAiMsgCount{}).Error
	return
}

func (m *UserAiMsgCountService) Update(ctx context.Context, reqData *model.UserAiMsgCount) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return
	}
	err = global.DB.Model(&model.UserAiMsgCount{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	return
}

func (m *UserAiMsgCountService) GetById(ctx context.Context, id uint) (res *model.UserAiMsgCount, err error) {
	err = global.DB.Model(&model.UserAiMsgCount{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *UserAiMsgCountService) GetAll(ctx context.Context) (resList []*model.UserAiMsgCount, err error) {
	err = global.DB.Model(&model.UserAiMsgCount{}).Find(&resList).Error
	return
}

func (m *UserAiMsgCountService) SearchOne(ctx context.Context, info *model.UserAiMsgCount) (res *model.UserAiMsgCount, err error) {
	db := global.DB.Model(&model.UserAiMsgCount{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.RoleType != 0 {
		db = db.Where("role_type = ?", info.RoleType)
	}
	if info.Gender != 0 {
		db = db.Where("gender = ?", info.Gender)
	}
	if err = db.First(&res).Error; err != nil {
		return
	}
	return
}

func (m *UserAiMsgCountService) SearchCount(ctx context.Context, info *model.UserAiMsgCount) (total int64, err error) {
	db := global.DB.Model(&model.UserAiMsgCount{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.RoleType != 0 {
		db = db.Where("role_type = ?", info.RoleType)
	}
	if info.Gender != 0 {
		db = db.Where("gender = ?", info.Gender)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	return
}

func (m *UserAiMsgCountService) GetList(ctx context.Context, info req.UserAiMsgCountSearch) (resList []*model.UserAiMsgCount, total int64, err error) {
	db := global.DB.Model(&model.UserAiMsgCount{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.RoleType != 0 {
		db = db.Where("role_type = ?", info.RoleType)
	}
	if info.Gender != 0 {
		db = db.Where("gender = ?", info.Gender)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
