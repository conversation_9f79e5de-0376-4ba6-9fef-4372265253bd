package backend

import (
	"aimsg-server/global"
	reqBtc "aimsg-server/model/backend/request/btc"
	"aimsg-server/pkg/channel"
	"aimsg-server/pkg/payment_queue"
	"aimsg-server/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/big"
	"strconv"
	"strings"
	"time"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"git.costnovel.com/center/middleware-sdk/thinkdata"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

const (
	LimitMin                = 0.0000002
	LimitMax                = 0.000025
	GetCashoutBtcNumHistory = "ai:btc:GetCashoutBtcNumHistory2:userid:%d"
	CacheCashOut            = "ai:btc:CashOut2:userid:%d:day:%s"
	CacheWithdrawalHistory  = "ai:btc:WithdrawalHistory:userid2:%d"
)

// 获取当前用户的历史数据
func (m *BtcService) GetCashoutBtcNumHistory(ctx context.Context, userID uint) (resp float64, err error) {
	cache := fmt.Sprintf(GetCashoutBtcNumHistory, userID)
	data, err := global.REDIS.Get(ctx, cache).Result()
	if err != nil && err != redis.Nil {
		return
	}
	if err != redis.Nil {
		resp, _ = strconv.ParseFloat(data, 64)
		return
	}
	err = global.DB.Model(&model.BtcWithdrawalRecord{}).
		Where("user_id = ? and withdrawal_status in (1,2)", userID).Select("COALESCE(SUM(amount), 0)").
		Scan(&resp).Error
	if err != nil {
		return
	}
	jsonB, _ := json.Marshal(resp)
	global.REDIS.Set(ctx, cache, jsonB, time.Second*5)
	return
}

func (m *BtcService) CashOutBtcNum(c *gin.Context, userId uint) (resp float64, err error) {

	//总额度
	total, err := m.GetTotalByUserAll(c)
	if err != nil {
		return resp, err
	}
	//已提现额度
	histroyNum, err := m.GetCashoutBtcNumHistory(c, userId)
	if err != nil {
		return resp, err
	}
	totalNum, _ := strconv.ParseFloat(total.Total, 64)
	if histroyNum > totalNum {
		return 0, errors.New("提现系统错误")
	}

	a := new(big.Float).SetFloat64(totalNum)
	b := new(big.Float).SetFloat64(histroyNum)
	okBtcNum, _ := new(big.Float).Sub(a, b).Float64()
	return okBtcNum, nil

}

func (m *BtcService) CashOut(c *gin.Context, req *reqBtc.CashOutReq) (interface{}, error) {
	amount, err := strconv.ParseFloat(req.Amount, 64)
	if err != nil {
		return nil, err
	}
	if amount > LimitMax || amount < LimitMin {
		return nil, errors.New("amount is too big or not enough")
	}

	//加锁
	userId := utils.GetDigitalUserID(c)
	appId := utils.GetAppID(c)
	cacheKey := fmt.Sprintf(CacheCashOut, userId, time.Now().Format("2006-01-02"))
	isLock := global.REDIS.SetNX(c, cacheKey, 1, time.Hour*25).Val()
	if !isLock {
		return nil, errors.New("Withdrawals are too frequent.")
	}
	defer func() {
		global.REDIS.Del(c, fmt.Sprintf(GetCashoutBtcNumHistory, userId))
	}()

	//获取可以提现的金额
	cashoutBtc, err := m.CashOutBtcNum(c, userId)
	if err != nil {
		return nil, err
	}
	if cashoutBtc < amount {
		return nil, fmt.Errorf("The withdrawal amount of BTC is too large; the maximum allowed withdrawal is %.16f", cashoutBtc)
	}

	// 默认使用speed通道
	if string(req.ChannelCode) == "" {
		req.ChannelCode = channel.ChannelSpeed
	}
	if req.Method.Network == "" {
		req.Method.Network = string(model.BtcTradeNetworkLightning)
	}
	insertReq, _ := json.Marshal(req)

	records := model.BtcWithdrawalRecord{
		AppId:                 appId,
		UserID:                userId,
		Amount:                fmt.Sprintf("%g", amount),
		WithdrawalMethod:      req.Method.Id,
		WithdrawalTime:        time.Now(),
		WithdrawalStatus:      1,
		Req:                   string(insertReq),
		WithdrawalChannelCode: string(req.ChannelCode),
		WithdrawalNetwork:     req.Method.Network,
		WithdrawalAddress:     req.Method.Address,
	}
	err = global.DB.Create(&records).Error
	if err != nil {
		return nil, err
	}
	global.REDIS.Del(c, fmt.Sprintf(CacheWithdrawalHistory, userId))
	m.cashoutReport(c, req, "进行中")
	return nil, nil
}

func (m *BtcService) cashoutReport(c *gin.Context, req *reqBtc.CashOutReq, status string) {
	appId := utils.GetAppID(c)
	appInfo, appInfoExist := global.AppMap[appId]
	if !appInfoExist {
		return
	}
	// 获取用户信息
	var (
		err error
	)
	userId := utils.GetDigitalUserID(c)
	dataMap := make(map[string]interface{})

	//实时算力
	hashrateData, err := m.GetRateByUser(c, false)
	if err != nil {
		return
	}
	dataMap["active_mining_power"] = fmt.Sprintf("%s%s", hashrateData.MiningSpeed, hashrateData.MiningSpeedUnit)
	//实时btc
	data, err := m.GetTotalByUser(c)
	if err != nil {
		return
	}
	dataMap["taotal_assets_num"] = data.Total

	//提现金额
	dataMap["amount"] = req.Amount

	//账号类型
	if req.Method.Id == 1 {
		dataMap["account_type"] = "invoce"
	} else if req.Method.Id == 2 {
		dataMap["account_type"] = "lightning"
	}

	//账号
	dataMap["account"] = req.Method.Address

	dataMap["status"] = status

	//算力系数
	userHashrate, err := m.getFreePower(userId)
	if err != nil {
		return
	}
	dataMap["rate"] = userHashrate.Total

	sendTdData := thinkdata.SendThinkData{
		IsDebug:    global.CONFIG.System.Env != "public",
		ServerURL:  "https://ssdata.bitcoinmining.studio",
		AppId:      appInfo.ThinkData.Data().AppId,
		EventName:  "withdraw_event",
		AccountId:  fmt.Sprintf("%d", userId),
		DistinctId: "",
		Properties: dataMap,
	}
	if err := thinkdata.SendData(sendTdData); err != nil {
		global.LOG.Error("TdPointerTwo fail", zap.Error(err))
		return
	}
}

func (m *BtcService) Withdraw(c *gin.Context) (interface{}, error) {
	resp := reqBtc.WithdrawResp{}
	userId := utils.GetDigitalUserID(c)
	data, err := m.CashOutBtcNum(c, userId)
	if err != nil {
		return nil, err
	}
	resp.TotalAssets = fmt.Sprintf("%.16f", data)
	resp.Amount = reqBtc.AmountLimit{
		LimitMin: "0.0000002",
		LimitMax: "0.000025",
	}
	resp.WalletList = []reqBtc.WalletType{
		{
			Id:   1,
			Img:  "https://images.aipersona.cloud/public/image/3ee6a77d-0eea-4c74-8ae8-0bf656fe2c99.png",
			Name: "Speed",
			Method: []reqBtc.Method{
				{
					Id:      1,
					Network: "Invoice",
				},
				{
					Id:      2,
					Network: "Lightning Address",
				},
			},
		},
	}

	return resp, nil
}

func (m *BtcService) Wallet(c *gin.Context) (interface{}, error) {
	userId := utils.GetDigitalUserID(c)
	totalBtc, err := m.CashOutBtcNum(c, userId)
	if err != nil {
		return nil, err
	}
	totalAssets := fmt.Sprintf("%.16f", totalBtc)
	withdrawalHistory, err := m.WithdrawalHistory(c)
	if err != nil {
		return nil, err
	}
	resp := reqBtc.WalletResp{
		TotalAssets: totalAssets,
		List:        withdrawalHistory,
	}

	return resp, err
}

var speedErrMap = map[string]string{
	"Invalid lightning or bitcoin or ethereum address":        "Invalid lightning or bitcoin or ethereum address",
	"FAILURE_REASON_INCORRECT_PAYMENT_DETAILS":                "Invalid lightning or bitcoin or ethereum address",
	"Invalid lightning invoice. Please provide valid invoice": "Invalid lightning or bitcoin or ethereum address",
	"Insufficient funds!":                                     "You don't have enough funds to cover this payment and network fee",
	"ALREADY_EXISTS: invoice is already paid":                 "Payment already exists",
	"ALREADY_EXISTS: payment already exists":                  "Payment already exists",
	"FAILURE_REASON_NO_ROUTE":                                 "Check your network",
	"amount must not be blank":                                "You don't have enough funds to cover this payment and network fee",
	"Not valid Lnurl pay":                                     "Invalid lightning or bitcoin or ethereum address",
	"FAILURE_REASON_TIMEOUT":                                  "Please resubmit if timeout has expired",
	"Not a valid ln address":                                  "Invalid lightning or bitcoin or ethereum address",
}

func (m *BtcService) castWithdrawFailReason(str string) string {
	if str == "" {
		return ""
	}
	for k, v := range speedErrMap {
		if strings.Contains(str, k) {
			return v
		}
	}
	return ""
}

func (m *BtcService) WithdrawalHistory(c *gin.Context) (resp []*reqBtc.WalletList, err error) {
	userId := utils.GetDigitalUserID(c)
	cache := fmt.Sprintf(CacheWithdrawalHistory, userId)
	data, err := global.REDIS.Get(c, cache).Result()
	if err != nil && err != redis.Nil {
		return nil, err
	}
	if err != redis.Nil {
		json.Unmarshal([]byte(data), &resp)
		return
	}

	var list []*model.BtcWithdrawalRecord
	err = global.DB.Where("user_id = ?", userId).
		Find(&list).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	resp = []*reqBtc.WalletList{}
	for _, item := range list {
		resp = append(resp, &reqBtc.WalletList{
			Id:         item.ID,
			Income:     item.Amount,
			Date:       item.WithdrawalTime.Format("Jan 02 2006 15:04:05"),
			Status:     item.WithdrawalStatus,
			FailReason: m.castWithdrawFailReason(item.FailReason),
		})
	}
	jsonB, _ := json.Marshal(resp)
	global.REDIS.Set(c, cache, jsonB, time.Hour*24)
	return resp, nil
}

// 使用 ID 进行分页查询 BTC 提现记录
func (m *BtcService) GetBtcWithdrawalRecords(c *gin.Context, req *reqBtc.GetBtcWithdrawalRecordsReq) (*reqBtc.GetBtcWithdrawalRecordsResp, error) {
	var records []*reqBtc.GetBtcWithdrawalRecordsItem

	// 确保分页参数合理
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	var total int64
	// 构建查询
	query := global.DB.Table("btc_withdrawal_records").
		Where("withdrawal_status in ?", req.WithdrawalStatus).Count(&total).
		Limit(req.PageSize).Offset((req.Page - 1) * req.PageSize).
		Order("created_at desc")

	if len(req.CreatedAtRange) == 2 {
		query = query.Where("created_at BETWEEN ? AND ?", req.CreatedAtRange[0], req.CreatedAtRange[1])
	}

	// 执行查询
	err := query.Find(&records).Error
	if err != nil {
		return nil, err
	}

	var u DigitalUserService
	var app AppService
	uniqueUserIDs := lo.Uniq(lo.Map(records, func(u *reqBtc.GetBtcWithdrawalRecordsItem, _ int) uint {
		return u.UserID
	}))
	userMap, err := u.BatchById(uniqueUserIDs)
	if err != nil {
		return nil, err
	}
	appIds := make([]uint, 0, len(userMap))
	for _, user := range userMap {
		appIds = append(appIds, user.AppID)
	}
	appMap, err := app.BatchById(appIds)
	if err != nil {
		return nil, err
	}

	list := make([]*reqBtc.BtcWithdrawalRecordsItem, 0, len(records))
	for _, record := range records {
		if appMap[userMap[record.UserID].AppID].Name == "" {
			continue
		}
		var reqData *reqBtc.CashOutReq
		_ = json.Unmarshal(record.Req, &reqData)
		// 确定提现方式
		method := "Invoice"
		if reqData.Method.Id == 2 {
			method = "Lightning Address"
		}
		var amount float64
		amount, err = strconv.ParseFloat(record.Amount, 64)
		if err != nil {
			return nil, err
		}
		amount = amount * 1e8
		amountStr := strconv.FormatFloat(amount, 'f', -1, 64)
		list = append(list, &reqBtc.BtcWithdrawalRecordsItem{
			ID:               record.ID,
			UserID:           record.UserID,
			Amount:           amountStr,
			WithdrawalMethod: record.WithdrawalMethod,
			WithdrawalStatus: record.WithdrawalStatus,
			Req:              record.Req,
			WithdrawalTime:   record.WithdrawalTime.Format("2006-01-02 15:04:05"),
			CreatedAt:        record.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:        record.UpdatedAt.Format("2006-01-02 15:04:05"),
			Method:           method,
			AppName:          appMap[userMap[record.UserID].AppID].Name,
		})
	}

	resp := &reqBtc.GetBtcWithdrawalRecordsResp{
		List:  list,
		Total: total,
	}

	return resp, nil
}

// 根据 ID 修改 BTC 提现记录的 withdrawal_status
func (m *BtcService) UpdateBtcWithdrawalStatus(c *gin.Context, req *reqBtc.UpdateBtcWithdrawalStatus) error {
	// 使用 GORM 执行更新操作
	err := global.DB.Table("btc_withdrawal_records").
		Where("id in ?", req.IDS).
		Update("withdrawal_status", req.WithdrawalStatus).Error

	// 如果发生错误，返回错误信息
	if err != nil {
		return err
	}

	records := []*model.BtcWithdrawalRecord{}
	err = global.DB.Table("btc_withdrawal_records").
		Where("id in ?", req.IDS).Find(&records).Error
	if err != nil {
		return err
	}

	for _, record := range records {
		cache := fmt.Sprintf(GetCashoutBtcNumHistory, record.UserID)
		err = global.REDIS.Del(c, cache).Err()
		if err != nil {
			return err
		}

		cache = fmt.Sprintf(CacheWithdrawalHistory, record.UserID)
		err = global.REDIS.Del(c, cache).Err()
		if err != nil {
			return err
		}

		var reqData reqBtc.CashOutReq
		json.Unmarshal([]byte(record.Req), &reqData)
		////1未审核 2成功 3失败, 4人工审核通过 推送到转账队列
		var status string
		switch record.WithdrawalStatus {
		case 1:
			status = "进行中"
		case 2:
			status = "成功"
		case 3:
			status = "失败"
		case 4:
			status = "人工审核通过"
			err := payment_queue.PushToQueue(c.Request.Context(), record.ID)
			if err != nil {
				global.LOG.Error("push to queue failed", zap.Error(err))
			}

		case 5:
			status = "人工审核拒绝"
		}
		c.Set(global.GinContextUserId, record.UserID)
		m.cashoutReport(c, &reqData, status)
	}

	return nil
}
