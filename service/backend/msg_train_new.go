package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/model/cli/td"
	"aimsg-server/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"strings"
	"time"
)

func (m *MsgService) MsgTrainNewStart() (start bool) {
	return MsgNewStart()
}

func (m *MsgService) MsgTrainNew() {
	for i := 10; i < 20; i++ {
		tmpApi := request.MsgJobReq{
			UrlType: 2,
			ApiUrl:  "http://35.163.192.237:8901/v2/chat/completions",
		}
		go m.MsgApiTrainNew(tmpApi)
	}
}

func (m *MsgService) MsgApiTrainNew(msgApi request.MsgJobReq) {
	var (
		ctx      = context.Background()
		redisKey = global.MAIN_QUEUE_AIMSG
	)
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Error("MsgApiTrainNew panic", zap.Any("err", err), zap.Any("msgApi", msgApi))
		}
	}()
	for {
		time.Sleep(time.Millisecond * 100)
		// 从 Redis 有序集合中获取消息
		redisMsgList, err := global.REDIS.ZRange(ctx, redisKey, 0, -1).Result()
		if err != nil {
			global.LOG.Error("ZRevRange error", zap.Any("err", err))
			continue
		}
		redisMsgCount := len(redisMsgList)
		if redisMsgCount == 0 {
			continue
		}
		// 消息过多,发送钉钉通知
		m.CheckSendDDNotifyMsgToMany(redisMsgCount)
		var (
			sendMsgReq *request.RedisMsgReq
			taskStr    string
		)

		for _, v := range redisMsgList {
			md5Str := utils.MD5(v)
			isLock := global.REDIS.SetNX(ctx, fmt.Sprintf("ai:MsgApiTrainNew:msg:lock:%s", md5Str), v, time.Second*10).Val()
			if !isLock {
				continue
			}
			taskStr = v
			break
		}
		if taskStr == "" {
			return
		}
		if err = json.Unmarshal([]byte(taskStr), &sendMsgReq); err != nil {
			if _, err = global.REDIS.ZRem(ctx, redisKey, taskStr).Result(); err != nil {
				global.LOG.Error("删除消息key出错", zap.Any("err", err), zap.String("taskStr", taskStr))
			}
			continue
		}
		// 不是当前的消息类型,跳过
		if sendMsgReq.UrlType == 1 {
			continue
		}

		global.LOG.Info("1.取出消息", zap.Any("sendMsgReq", sendMsgReq))
		// 对该taskStr加锁
		lockRedisKey := m.GetMsgLockRedisKey(sendMsgReq)
		lockRes := global.REDIS.SetNX(ctx, lockRedisKey, "1", 10*time.Second).Val()
		if !lockRes {
			if _, err = global.REDIS.ZRem(ctx, redisKey, taskStr).Result(); err != nil {
				global.LOG.Error("删除消息key出错", zap.Any("err", err), zap.Any("sendMsgReq", sendMsgReq))
			}
			continue
		}
		global.LOG.Info("2.消息已加锁", zap.Any("sendMsgReq", sendMsgReq))
		// 删除掉该消息
		if _, err = global.REDIS.ZRem(ctx, redisKey, taskStr).Result(); err != nil {
			global.LOG.Error("删除消息key出错", zap.Any("err", err), zap.Any("sendMsgReq", sendMsgReq))
		}
		// 消息处理
		switch sendMsgReq.SendType {
		case request.SendTypeVoiceCall:
			// 语音通话
			err = m.execVoiceCallTrainNew(ctx, msgApi, sendMsgReq)
		default:
			err = m.execMsgTrainNew(ctx, msgApi, sendMsgReq)
		}
		if err != nil {
			// 消息解锁
			_ = global.REDIS.Del(ctx, lockRedisKey).Val()
			global.LOG.Error("MsgApiTrain error", zap.Any("err", err))
		}
	}
}

func (m *MsgService) getAiMsgNew(ctx context.Context, msgApi request.MsgJobReq, userSendMsg string, aiRoleInfo *model.AiRole, userInfo *model.DigitalUser, sendMsgReq *request.RedisMsgReq, userNsfw bool) (aiReplyMsg string, err error) {
	var (
		apiLog    string
		curlStr   string
		apiRes    response.AiMsgResNew
		msgHis    []request.MsgItemV2
		messages  []request.MsgItemV2
		firstMsg  = aiRoleInfo.FirstMsg
		userIdStr = fmt.Sprintf("%d", userInfo.Id)
	)
	gLog := global.LOG.With(zap.String("Func", "getAiMsgNew"), zap.String("business", "ai_req"), zap.Any("msgApi", msgApi), zap.String("用户发送内容", userSendMsg), zap.Any("RoleID", aiRoleInfo.RoleID), zap.Any("用户信息", userInfo), zap.Any("用户发送消息请求体", sendMsgReq))
	msgTimeoutConfig := GetMsgSendTimeOutConfig(ctx)
	gLog = gLog.With(zap.Any("超时设置", msgTimeoutConfig))
	if msgHis, err = m.GetMsgHistoryV2(ctx, userIdStr, aiRoleInfo.RoleID); err != nil {
		return
	}
	if len(msgHis) > 0 {
		messages = append(messages, msgHis...)
	}
	// 添加用户说的话
	messages = append(messages, request.MsgItemV2{Role: "user", Content: userSendMsg})
	reqData := map[string]interface{}{
		"model":       "abab5.5s-chat1",
		"temperature": 0.9,
		"max_tokens":  256,
		"top_p":       0.95,
		"messages":    messages,
		"close_nsfw":  userNsfw, // TODO 待确认
		"bot_name":    aiRoleInfo.Nickname,
		"user_name":   userInfo.Nickname,
		"context":     firstMsg,
		"greeting":    firstMsg,
	}
	gLog = gLog.With(zap.Any("请求数据", reqData))
	reqStart := time.Now()
	gLog = gLog.With(zap.Time("请求开始时间", reqStart))
	// 请求AI接口
	if apiLog, curlStr, err = utils.HttpPostJson(msgApi.ApiUrl, reqData, &apiRes, msgTimeoutConfig.Seconds); err != nil {
		gLog.Error("请求AI接口失败", zap.Any("err", err), zap.String("apiLog", apiLog), zap.String("curlStr", curlStr))
	}
	reqTime := time.Since(reqStart)
	gLog = gLog.With(zap.Float64("请求耗时", reqTime.Seconds()), zap.String("请求日志", apiLog))
	if apiRes.Code != 0 {
		gLog.Error("请求AI接口失败")
	}
	aiReplyMsg = apiRes.Data.Answer
	if aiReplyMsg == "" && len(global.ReplyList) > 0 {
		aiReplyMsg = utils.RandList(global.ReplyList)
	}
	// 如果结尾是\n,那么就不要结尾的\n
	aiReplyMsg = strings.TrimRight(aiReplyMsg, "\n")
	// 替换括号为*
	aiReplyMsg = utils.ReplaceBracketsWithStars(aiReplyMsg)
	// 随机输出成功日志
	gLog.Info("请求AI接口成功")
	return
}

func (m *MsgService) execMsgTrainNew(ctx context.Context, msgApi request.MsgJobReq, sendMsgReq *request.RedisMsgReq) (err error) {
	var (
		replyMsg        string
		transMsg        string
		lastMsgInfo     *model.MsgInfo
		aiRoleInfo      *model.AiRole
		userInfo        *model.DigitalUser
		userProfileInfo *model.DigitalUserProfile
		userService     DigitalUserService
		profileService  DigitalUserProfileService
		aiRoleService   AiRoleService
		transSer        TranslateService
		fcmSer          FcmService
		messageType     model.MsgType
		msgContent      model.MessageContent
		now             = time.Now()
		dayStr          = now.Format(time.DateOnly)
	)
	tStartTime := time.Now()
	gLog := global.LOG.With(zap.String("Func", "execMsgTrainNew"), zap.String("ApiUrl", msgApi.ApiUrl), zap.Any("sendMsgReq", sendMsgReq))
	uuidStr := uuid.NewString()
	userIdStr := fmt.Sprintf("%d", sendMsgReq.UserId)
	roleIdStr := sendMsgReq.AiRoleId
	requireCost := sendMsgReq.RequireCost
	userId := sendMsgReq.UserId

	sqlStart := time.Now()
	// 查询用户信息
	if userInfo, err = userService.GetById(ctx, userId); err != nil {
		gLog.Error("获取用户信息失败", zap.Error(err))
		return
	}
	// app信息
	appInfo := global.AppMap[userInfo.AppID]

	// App配置项
	freeReplyCount := appInfo.DetailConfig.Data().FreeMsgCount
	// replyImgRate := appInfo.DetailConfig.Data().AiReplyImgRate
	replyPerFlo := appInfo.DetailConfig.Data().AiReplyPerFlower
	if userInfo.IsSVip() {
		replyPerFlo = 0
	}
	paidReplyCount := appInfo.DetailConfig.Data().PaidMsgCount
	maskMsgFlo := appInfo.DetailConfig.Data().MaskMsgPerFlower

	userAdMsgCount := m.GetWatchAdMsgCountToday(ctx, userId)

	freeReplyCount += userAdMsgCount
	paidReplyCount += userAdMsgCount

	// 查询用户Profile信息
	if userProfileInfo, err = profileService.GetByUserId(userId); err != nil {
		gLog.Error("获取用户Profile信息出错", zap.Any("err", err))
		return
	}
	// 查询AI角色信息
	if aiRoleInfo, err = aiRoleService.GetByRoleId(roleIdStr); err != nil {
		gLog.Error("获取AI角色信息出错", zap.Any("err", err))
		return
	}
	sqlLatency := time.Since(sqlStart)
	gLog = gLog.With(zap.Any("sqlLatency", sqlLatency))
	// 查询当日总共对话条数
	replyCount := m.GetAiReplyUserCountToday(ctx, userId)

	startTime := time.Now()
	// 查询用户和该AI角色的最新一条聊天记录,按照send_time倒序
	if lastMsgInfo, err = m.LastMsg(ctx, userIdStr, roleIdStr); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			err = nil
			lastMsgInfo = &model.MsgInfo{
				MessageID:    uuidStr,
				Channel:      nil,
				SendTime:     now.UnixMilli(),
				DeletedAt:    nil,
				MessageType:  model.MsgTypeTxt,
				SenderID:     userIdStr,
				SenderName:   userInfo.Nickname,
				ReceiverID:   roleIdStr,
				ReceiverName: aiRoleInfo.Nickname,
				MessageContent: model.MessageContent{
					Text: "Hi",
				},
			}
		} else {
			gLog.Error("获取最后一条消息失败", zap.Any("err", err))
			return
		}
	}
	gLog.Info("3.获取最后一条消息成功", zap.Any("lastMsgInfo", lastMsgInfo))
	mongoLatency := time.Since(startTime)
	gLog = gLog.With(zap.Any("mongoLatency", mongoLatency))

	messageId := lastMsgInfo.MessageID
	userSendMsg := lastMsgInfo.MessageContent.Text
	// 获取回复消息
	aiReqStart := time.Now()
	if replyMsg, err = m.getAiMsgNew(ctx, msgApi, userSendMsg, aiRoleInfo, userInfo, sendMsgReq, userProfileInfo.Nsfw); err != nil {
		gLog.Error("获取大模型回复消息失败", zap.Any("err", err))
		return
	}
	aiReqLatency := time.Since(aiReqStart)
	gLog = gLog.With(zap.Any("aiReqLatency", aiReqLatency))
	latency := time.Since(startTime)
	latencySecond := int(latency.Seconds())
	go TdPointer(userInfo, userProfileInfo, td.ServerEvent, map[string]interface{}{
		"event_tag":    "msg",
		"user_msg":     userSendMsg,
		"ai_replay":    replyMsg,
		"ai_role_id":   aiRoleInfo.RoleID,
		"ai_role_type": aiRoleInfo.RoleType,
		"time_long":    latencySecond,
	})
	// 解锁
	lockRedisKey := m.GetMsgLockRedisKey(sendMsgReq)
	_ = global.REDIS.Del(ctx, lockRedisKey).Val()

	gLog.Info("4.获取回复消息成功&&消息已解锁", zap.String("userMsg", userSendMsg), zap.String("replyMsg", replyMsg))

	getMsgContentStart := time.Now()
	if requireCost == 0 {
		if sendMsgReq.GiftId != 0 {
			messageType, msgContent, err = m.GetMsgContent(ctx, userInfo, aiRoleInfo, model.SceneSendGift, userSendMsg, sendMsgReq.GiftId)
		} else {
			messageType, msgContent, err = m.GetMsgContent(ctx, userInfo, aiRoleInfo, model.SceneRemindMsg, userSendMsg, sendMsgReq.GiftId)
		}
	} else {
		messageType, msgContent, err = m.GetMsgContent(ctx, userInfo, aiRoleInfo, model.SceneChatReplyMsg, userSendMsg, sendMsgReq.GiftId)
	}
	getMsgContentLatency := time.Since(getMsgContentStart)
	gLog = gLog.With(zap.Any("getMsgContentLatency", getMsgContentLatency))

	// 检查是否可以生成语音
	msgContent.CanVoice = m.msgCanVoice(replyMsg)
	// 语音屏蔽打开 1_03军锋需求修改文字转语音
	// msgContent.CanVoice = false

	costStart := time.Now()
	msgIsFree := true
	hasSensitiveWord := false
	if requireCost == 1 {
		// 需要扣费
		if userInfo.IsSub() {
			// 已订阅用户
			// 当回复用户数量大于免费回复次数时,需要扣费
			if replyCount > paidReplyCount {
				if userProfileInfo.FlowerAmount < replyPerFlo {
					// 玫瑰花数量不足,发送蒙版消息
					messageType = model.MsgTypeTxtMask
				} else {
					// 玫瑰花数量足,扣玫瑰花,发正常的文本消息,记录消费记录
					if err = m.SaveConsumeRecord(userId, aiRoleInfo, replyPerFlo, model.ConsumeTypeMaskMsg); err != nil {
						gLog.Error("SaveConsumeRecord error", zap.Any("err", err))
						return
					}
					msgIsFree = false
				}
			}
		} else {
			// 未订阅用户
			// 当回复用户数量大于免费回复次数时,需要扣费
			if replyCount == freeReplyCount {
				if userProfileInfo.FlowerAmount < maskMsgFlo {
					// 玫瑰花数量不足,发送蒙版消息
					messageType = model.MsgTypeTxtMask
				} else {
					// 玫瑰花数量足,扣玫瑰花,发正常的文本消息,记录消费记录
					if err = m.SaveConsumeRecord(userId, aiRoleInfo, maskMsgFlo, model.ConsumeTypeMaskMsg); err != nil {
						gLog.Error("SaveConsumeRecord error", zap.Any("err", err))
						return
					}
					msgIsFree = false
				}
			} else if replyCount > freeReplyCount {
				if userProfileInfo.FlowerAmount < replyPerFlo {
					// 玫瑰花数量不足,发送蒙版消息
					messageType = model.MsgTypeTxtMask
				} else {
					// 玫瑰花数量足,扣玫瑰花,发正常的文本消息,记录消费记录
					if err = m.SaveConsumeRecord(userId, aiRoleInfo, replyPerFlo, model.ConsumeTypeMaskMsg); err != nil {
						gLog.Error("SaveConsumeRecord error", zap.Any("err", err))
						return
					}
					msgIsFree = false
				}
			}
		}

		// 1.12.0需求,非VIP权限用户在聊天页发敏感词 AI自动回复蒙版消息
		fantasyCompare := utils.CompareVersions(userInfo.Version, "3.1.0")
		// Fame Fantasy IOS 特殊处理
		if !(userInfo.AppID == 4 && fantasyCompare < 0) {
			if !userInfo.IsSub() && messageType != model.MsgTypeTxtMask {
				// 判断是否有敏感词
				if hasSensitiveWord = m.HasSensitiveWordTextMask(userSendMsg); hasSensitiveWord {
					messageType = model.MsgTypeTxtMask
				}
			}
		}
	}

	costLatency := time.Since(costStart)
	gLog = gLog.With(zap.Any("costLatency", costLatency))

	transStart := time.Now()
	if transMsg, err = transSer.MsgTranslate(ctx, appInfo, userInfo, replyMsg); err != nil {
		global.LOG.Error("TranslateText error", zap.Any("err", err))
	}
	transLatency := time.Since(transStart)
	gLog = gLog.With(zap.Any("transLatency", transLatency))
	msgContent.Text = transMsg
	msgContent.OriginText = replyMsg
	if sendMsgReq.GiftId != 0 {
		// 礼物回复消息不能undress
		msgContent.CanUndress = false
	}
	aiSendMsg := &model.MsgInfo{
		MessageID:      messageId,
		Channel:        nil,
		SendTime:       now.UnixMilli(),
		DeletedAt:      nil,
		MessageType:    messageType,
		SenderID:       roleIdStr,
		SenderName:     aiRoleInfo.Nickname,
		ReceiverID:     userIdStr,
		ReceiverName:   userInfo.Nickname,
		MessageContent: msgContent,
	}
	sendAgoraStart := time.Now()
	// 发送消息到声网
	if err = m.SendMsgToAgora(aiSendMsg, appInfo); err != nil {
		gLog.Error("SendMsgToAgora error", zap.Any("err", err))
		return
	}
	sendAgoraLatency := time.Since(sendAgoraStart)
	gLog = gLog.With(zap.Any("sendAgoraLatency", sendAgoraLatency))

	msgLatency := time.Since(tStartTime)
	gLog = gLog.With(zap.Any("msgLatency", msgLatency))

	gLog.Info("5.send agora success")

	// 更新UserAiMsgCount
	go func() {
		var (
			msgCountSer UserAiMsgCountService
		)
		if err = msgCountSer.FindUpdate(ctx, userId, aiRoleInfo, map[string]interface{}{
			"msg_round_count": gorm.Expr("msg_round_count + 1"),
		}); err != nil {
			return
		}
	}()

	// 发送IOS应用通知
	fcmSer.PushReplyMsg(ctx, userInfo, aiRoleInfo, aiSendMsg)

	saveStart := time.Now()
	// 保存发送过的记录
	aiSendMediaSer := AiSendMediaService{}
	aiSendMediaRecord := model.AiSendMedia{
		DigitalUserId: userInfo.Id,
		AiRoleId:      aiRoleInfo.Id,
		MediaType:     0,
		MediaURL:      "",
		ThumbnailURL:  "",
		AsmrName:      "",
		SendTime:      now,
		UnlockState:   model.UnlockStateNo,
		UnlockTime:    nil,
	}
	if messageType == model.MsgTypeTxtImg || messageType == model.MsgTypeTxtImgVoice {
		// 发送的是图片
		aiSendMediaRecord.MediaType = model.MediaTypeImg
		aiSendMediaRecord.MediaURL = msgContent.ImgURL
		aiSendMediaRecord.ThumbnailURL = msgContent.ImgURL
		aiSendMediaRecord.CanUndress = msgContent.CanUndress
		aiSendMediaSer.SaveByMsg(&aiSendMediaRecord)
	} else if messageType == model.MsgTypeTxtVideo || messageType == model.MsgTypeTxtVideoVoice {
		// 发送的是视频
		aiSendMediaRecord.MediaType = model.MediaTypeVideo
		aiSendMediaRecord.MediaURL = msgContent.FileURL
		aiSendMediaRecord.ThumbnailURL = msgContent.ImgURL
		aiSendMediaSer.SaveByMsg(&aiSendMediaRecord)
	}
	// 更新亲密值
	intimateSubType := model.SubTypePaidMsg
	if msgIsFree {
		intimateSubType = model.SubTypeFreeMsg
	}
	_ = m.SaveIntimateRecord(ctx, userInfo.Id, aiRoleInfo, model.IntimateTypeAdd, intimateSubType)

	saveLatency := time.Since(saveStart)
	gLog = gLog.With(zap.Any("saveLatency", saveLatency))

	// redis里增加回复用户的数量,增加已聊过的AI角色ID
	if requireCost == 1 {
		// 1.12.0 AI自动回复蒙版消息，不算在回消息数量限制内
		if !hasSensitiveWord {
			// 回复用户的数量
			m.IncrAiReplyUserCount(ctx, userId)
		}
		// 已聊过的AI角色ID
		aiRoleLimitKey := fmt.Sprintf(global.AI_CHAT_ROLE_LIMIT_KEY, dayStr, userId)
		// 自创建角色不加入限制
		if aiRoleInfo.RoleType != model.AiRoleTypePrivate {
			if err = global.REDIS.SAdd(ctx, aiRoleLimitKey, roleIdStr).Err(); err != nil {
				gLog.Error("SAdd aiRoleLimitKey error", zap.Any("err", err))
				return
			}
			// 设置过期时间
			if err = global.REDIS.Expire(ctx, aiRoleLimitKey, 48*time.Hour).Err(); err != nil {
				gLog.Error("Expire aiRoleLimitKey error", zap.Any("err", err))
				return
			}
		}
	}

	/*
		go func() {
				defer func() {
					if recoverErr := recover(); recoverErr != nil {
						global.LOG.Error("msgTimeRecordSer Insert panic", zap.Any("err", recoverErr))
					}
				}()
				msgTimeRec := &model.MsgTimeRecord{
					ID:                primitive.NewObjectID(),
					CreatedAt:         time.Now(),
					MessageID:         messageId,
					DigitalUserId:     userId,
					AiId:              aiRoleInfo.Id,
					AiRoleId:          aiRoleInfo.RoleID,
					JobName:           msgApi.Name,
					ReqUrl:            msgApi.ApiUrl,
					SqlLatency:        utils.FloatToDecimal128(sqlLatency.Seconds()),
					MongoLatency:      utils.FloatToDecimal128(mongoLatency.Seconds()),
					AiReqLatency:      utils.FloatToDecimal128(aiReqLatency.Seconds()),
					MsgContentLatency: utils.FloatToDecimal128(getMsgContentLatency.Seconds()),
					TranslateLatency:  utils.FloatToDecimal128(transLatency.Seconds()),
					AgoraLatency:      utils.FloatToDecimal128(sendAgoraLatency.Seconds()),
					MsgLatency:        utils.FloatToDecimal128(msgLatency.Seconds()),
					IntimateLatency:   utils.FloatToDecimal128(saveLatency.Seconds()),
				}
				var msgTimeRecordSer MsgTimeRecordService
				_, insertErr := msgTimeRecordSer.Insert(ctx, msgTimeRec)
				if insertErr != nil {
					global.LOG.Error("msgTimeRecordSer Insert error", zap.Any("err", insertErr))
				}
			}()
	*/
	gLog.Info("6.send msg end")
	return
}

// execVoiceCallTrain 语音通话逻辑
func (m *MsgService) execVoiceCallTrainNew(ctx context.Context, msgApi request.MsgJobReq, sendMsgReq *request.RedisMsgReq) (err error) {
	var (
		replyMsg         string
		lastVoiceMsgInfo *model.MsgInfo
		aiRoleInfo       *model.AiRole
		userInfo         *model.DigitalUser
		userProfileInfo  *model.DigitalUserProfile
		aiRoleSer        AiRoleService
		userService      DigitalUserService
		profileService   DigitalUserProfileService
		userIdStr        = fmt.Sprintf("%d", sendMsgReq.UserId)
		now              = time.Now()
	)
	gLog := global.LOG.With(zap.String("Func", "execVoiceCallTrainNew"), zap.String("ApiUrl", msgApi.ApiUrl), zap.Any("sendMsgReq", sendMsgReq))
	// 获取AI角色信息
	if aiRoleInfo, err = aiRoleSer.GetByRoleId(sendMsgReq.AiRoleId); err != nil {
		return
	}
	// 获取用户信息
	if userInfo, err = userService.GetById(ctx, sendMsgReq.UserId); err != nil {
		return
	}
	// 查询用户Profile信息
	if userProfileInfo, err = profileService.GetByUserId(sendMsgReq.UserId); err != nil {
		return
	}
	// 查询用户和该AI角色的最新一条聊天记录,按照send_time倒序
	if lastVoiceMsgInfo, err = m.LastVoiceCallMsg(ctx, userIdStr, sendMsgReq.AiRoleId); err != nil {
		gLog.Error("获取最后一条语言消息失败", zap.Any("err", err))
		return
	}
	gLog.Info("获取最后一条语音消息成功", zap.Any("lastVoiceMsgInfo", lastVoiceMsgInfo))
	userSendMsg := lastVoiceMsgInfo.MessageContent.Text
	// App信息
	appInfo := global.AppMap[userInfo.AppID]
	// 获取回复消息
	if replyMsg, err = m.getAiMsgNew(ctx, msgApi, userSendMsg, aiRoleInfo, userInfo, sendMsgReq, userProfileInfo.Nsfw); err != nil {
		gLog.Error("获取大模型回复消息失败", zap.Any("err", err))
		return
	}
	// 解锁
	lockRedisKey := m.GetMsgLockRedisKey(sendMsgReq)
	_ = global.REDIS.Del(ctx, lockRedisKey).Val()
	gLog.Info("4.获取回复消息成功&&消息已解锁", zap.String("userMsg", userSendMsg), zap.String("replyMsg", replyMsg))
	voiceText := m.ExtractVoiceTextV3(replyMsg)
	if voiceText == "" {
		// 如果没有提取到语音文本,则用模型回复的文本
		voiceText = replyMsg
	}
	msgVoiceKey, msgVoiceKeyExist := m.VoiceCache(ctx, voiceText, aiRoleInfo.RoleID)
	if !msgVoiceKeyExist {
		msgVoiceKey, _ = m.ElevenLabsTts(lastVoiceMsgInfo.MessageID, voiceText, aiRoleInfo)
	}
	gLog.Info("msg voice gen success", zap.String("voiceText", voiceText), zap.String("msgVoiceKey", utils.S3Url(msgVoiceKey, userInfo.AppID)))
	if msgVoiceKey == "" {
		// TODO 生成语音失败怎么办?
	}
	msgContent := model.MessageContent{
		Avatar:     utils.S3Url(aiRoleInfo.Avatar, userInfo.AppID),
		Text:       replyMsg,
		OriginText: replyMsg,
		CanUndress: false,
		AudioURL:   utils.S3Url(msgVoiceKey, userInfo.AppID),
	}
	aiSendMsg := &model.MsgInfo{
		MessageID:      lastVoiceMsgInfo.MessageID,
		Channel:        nil,
		SendTime:       now.UnixMilli(),
		DeletedAt:      nil,
		MessageType:    model.MsgTypeVoiceCall,
		SenderID:       sendMsgReq.AiRoleId,
		SenderName:     aiRoleInfo.Nickname,
		ReceiverID:     userIdStr,
		ReceiverName:   userInfo.Nickname,
		MessageContent: msgContent,
	}
	// 发送消息到声网
	if err = m.SendMsgToAgora(aiSendMsg, appInfo); err != nil {
		gLog.Error("SendMsgToAgora error", zap.Any("err", err))
		return
	}
	gLog.Info("发送消息到声网成功")
	return
}
