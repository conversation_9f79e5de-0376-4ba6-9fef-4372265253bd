package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	cliReq "aimsg-server/model/cli/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm/clause"
	"time"
)

type ImgToVideoService struct{}

func (m *ImgToVideoService) UserDayGenCountRdsKey(userId uint) (rdsKey string) {
	dayStr := time.Now().Format(time.DateOnly)
	rdsKey = fmt.Sprintf(global.UserDayGenCountRdsKey, dayStr, userId)
	return
}

func (m *ImgToVideoService) UserDayGenCount(ctx context.Context, userId uint) (dayCount int) {
	rdsKey := m.UserDayGenCountRdsKey(userId)
	dayCount, _ = global.REDIS.Get(ctx, rdsKey).Int()
	return
}

func (m *ImgToVideoService) UserDayGenIncr(ctx context.Context, userId uint) (err error) {
	rdsKey := m.UserDayGenCountRdsKey(userId)
	err = global.REDIS.Incr(ctx, rdsKey).Err()
	return
}

func (m *ImgToVideoService) UserDayGenReduce(ctx context.Context, userId uint) (err error) {
	rdsKey := m.UserDayGenCountRdsKey(userId)
	err = global.REDIS.IncrBy(ctx, rdsKey, -1).Err()
	return
}

func (m *ImgToVideoService) AddRedisTask(ctx context.Context, taskId uint) (err error) {
	taskRedisReq := cliReq.ImgToVideoRdsTask{
		TaskId: taskId,
	}
	taskRedisReqBytes, _ := json.Marshal(taskRedisReq)
	err = global.REDIS.ZAdd(ctx, global.IMG_TO_VIDEO_TRAIN_KEY, redis.Z{Score: float64(time.Now().Unix()), Member: string(taskRedisReqBytes)}).Err()
	return
}

func (m *ImgToVideoService) ReduceRedisTask(ctx context.Context, taskId uint) {
	taskRedisReq := cliReq.ImgToVideoRdsTask{
		TaskId: taskId,
	}
	taskRedisReqBytes, _ := json.Marshal(taskRedisReq)
	taskRedisReqStr := string(taskRedisReqBytes)
	global.REDIS.ZRem(ctx, global.IMG_TO_VIDEO_TRAIN_KEY, redis.Z{Member: taskRedisReqStr})
	return
}

func (m *ImgToVideoService) Create(ctx context.Context, reqData *model.ImgToVideo) (err error) {
	err = global.DB.Model(&model.ImgToVideo{}).Create(reqData).Error
	return
}

func (m *ImgToVideoService) DeleteById(ctx context.Context, id uint) (err error) {
	err = global.DB.Model(&model.ImgToVideo{}).Where("id = ?", id).Delete(&model.ImgToVideo{}).Error
	return
}

func (m *ImgToVideoService) DeleteByIds(ctx context.Context, reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.ImgToVideo{}).Where("id IN (?)", reqData.Ids).Delete(&model.ImgToVideo{}).Error
	return
}

func (m *ImgToVideoService) Update(ctx context.Context, reqData *model.ImgToVideo) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return
	}
	err = global.DB.Model(&model.ImgToVideo{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	return
}

func (m *ImgToVideoService) GetById(ctx context.Context, id uint) (res *model.ImgToVideo, err error) {
	err = global.DB.Model(&model.ImgToVideo{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *ImgToVideoService) GetAll(ctx context.Context) (resList []*model.ImgToVideo, err error) {
	err = global.DB.Model(&model.ImgToVideo{}).Find(&resList).Error
	return
}

func (m *ImgToVideoService) GetList(ctx context.Context, info req.ImgToVideoSearch) (resList []*model.ImgToVideo, total int64, err error) {
	db := global.DB.Model(&model.ImgToVideo{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if info.GenStatus != 0 {
		db = db.Where("gen_status = ?", info.GenStatus)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
