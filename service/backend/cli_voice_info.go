package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/response"
	"aimsg-server/utils"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
)

func (m *VoiceInfoService) VoiceList(c *gin.Context) (res gin.H, err error) {
	var (
		MaleList   []*response.VoiceListItem
		FemaleList []*response.VoiceListItem
		appId      = utils.GetAppID(c)
	)
	for _, v := range global.AllVoiceInfoList {
		if v.AppID != appId {
			continue
		}
		newVoiceInfo := &response.VoiceListItem{
			ID:       v.Id,
			AppID:    v.AppID,
			Gender:   v.Gender,
			Title:    v.Title,
			Example:  utils.S3Url(v.Example, v.AppID),
			UseCount: v.UseCount,
		}
		newVoiceInfo.VoiceReq = v.VoiceReq
		if v.Gender == model.GenderTypeMale {
			MaleList = append(MaleList, newVoiceInfo)
		} else if v.Gender == model.GenderTypeFemale {
			FemaleList = append(FemaleList, newVoiceInfo)
		}
	}
	res = gin.H{
		"male_list":   MaleList,
		"female_list": FemaleList,
	}
	return
}
