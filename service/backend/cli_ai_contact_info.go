package backend

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/utils"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func (m *AiContactInfoService) UnlockAiContactInfo(c *gin.Context, reqData *request.UnlockAiContactInfoReq) (err error) {
	var (
		buyCount         int64
		aiProfileInfo    *model.AiProfileInfo
		userProfile      *model.DigitalUserProfile
		aiProfileInfoSer AiProfileInfoService
		fCRService       FlowerConsumeRecordService
		profileSer       DigitalUserProfileService
		userId           = utils.GetDigitalUserID(c)
		appVersion       = c.GetHeader("App-Version")
	)
	// 查询用户是否有解锁过联系方式
	if buyCount, err = fCRService.SearchCount(&model.FlowerConsumeRecord{
		DigitalUserId: userId,
		ConsumeType:   model.ConsumeTypeGetContactInfo,
		RelationID:    fmt.Sprintf("%d", reqData.AiProfileId),
	}); err != nil {
		return
	}
	if buyCount > 0 {
		return
	}
	if userProfile, err = profileSer.GetByUserId(userId); err != nil {
		return
	}
	// 获取AI角色profileInfo记录
	if aiProfileInfo, err = aiProfileInfoSer.GetById(c, reqData.AiProfileId); err != nil {
		return
	}
	if userProfile.FlowerAmount < aiProfileInfo.ContactFlo {
		vCompare := utils.CompareVersions(appVersion, "3.0.0")
		if vCompare >= 0 {
			err = errs.FloShopErr
			return
		}
		err = errs.RequireBuyFloErr
		return
	}
	floCount := aiProfileInfo.ContactFlo
	err = global.DB.Transaction(func(tx *gorm.DB) (tErr error) {
		// 创建FlowerConsumeRecord记录
		var fRecord = &model.FlowerConsumeRecord{
			DigitalUserId: userId,
			AiId:          0,
			AiRoleId:      "",
			ConsumeType:   model.ConsumeTypeGetContactInfo,
			RelationID:    fmt.Sprintf("%d", aiProfileInfo.Id),
			OriginAmount:  userProfile.FlowerAmount,
			ConsumeAmount: floCount,
		}
		if tErr = tx.Model(&model.FlowerConsumeRecord{}).Create(fRecord).Error; tErr != nil {
			return
		}
		updateMap := map[string]interface{}{
			"flower_amount": gorm.Expr("flower_amount - ?", floCount),
		}
		if tErr = tx.Model(&model.DigitalUserProfile{}).Where("id = ?", userProfile.Id).Updates(updateMap).Error; tErr != nil {
			return
		}
		return
	})
	return
}
