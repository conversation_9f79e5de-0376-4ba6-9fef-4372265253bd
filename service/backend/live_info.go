package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm/clause"
)

type LiveInfoService struct{}

func (m *LiveInfoService) Create(ctx context.Context, reqData *model.LiveInfo) (err error) {
	err = global.DB.Model(&model.LiveInfo{}).Create(reqData).Error
	return
}

func (m *LiveInfoService) DeleteById(ctx context.Context, id uint) (err error) {
	err = global.DB.Model(&model.LiveInfo{}).Where("id = ?", id).Delete(&model.LiveInfo{}).Error
	return
}

func (m *LiveInfoService) DeleteByIds(ctx context.Context, reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.LiveInfo{}).Where("id IN (?)", reqData.Ids).Delete(&model.LiveInfo{}).Error
	return
}

func (m *LiveInfoService) Update(ctx context.Context, reqData *model.LiveInfo) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return
	}
	err = global.DB.Model(&model.LiveInfo{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	return
}

func (m *LiveInfoService) UpdateById(ctx context.Context, recId uint, exitMap map[string]interface{}) (err error) {
	err = global.DB.Model(&model.LiveInfo{}).Where("id = ?", recId).Updates(exitMap).Error
	return
}

func (m *LiveInfoService) GetById(ctx context.Context, id uint) (res *model.LiveInfo, err error) {
	err = global.DB.Model(&model.LiveInfo{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *LiveInfoService) GetAll(ctx context.Context) (resList []*model.LiveInfo, err error) {
	err = global.DB.Model(&model.LiveInfo{}).Find(&resList).Error
	return
}

func (m *LiveInfoService) SearchCount(ctx context.Context, reqData *model.LiveInfo) (count int64, err error) {
	db := global.DB.Model(&model.LiveInfo{})
	if reqData.Id != 0 {
		db = db.Where("id = ?", reqData.Id)
	}
	if reqData.AppID != 0 {
		db = db.Where("app_id = ?", reqData.AppID)
	}
	if reqData.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", reqData.DigitalUserId)
	}
	if err = db.Count(&count).Error; err != nil {
		return
	}
	return
}

func (m *LiveInfoService) GetList(ctx context.Context, info req.LiveInfoSearch) (resList []*model.LiveInfo, total int64, err error) {
	db := global.DB.Model(&model.LiveInfo{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
