package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm/clause"
)

type PayMethodsService struct{}

func (m *PayMethodsService) Create(reqData *model.PayMethods) (err error) {
	err = global.DB.Model(&model.PayMethods{}).Create(reqData).Error
	return
}

func (m *PayMethodsService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.PayMethods{}).Where("id = ?", id).Delete(&model.PayMethods{}).Error
	return
}

func (m *PayMethodsService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.PayMethods{}).Where("id IN (?)", reqData.Ids).Delete(&model.PayMethods{}).Error
	return
}

func (m *PayMethodsService) Update(reqData *model.PayMethods) (err error) {
	err = global.DB.Model(&model.PayMethods{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	return
}

func (m *PayMethodsService) GetById(id uint) (res *model.PayMethods, err error) {
	err = global.DB.Model(&model.PayMethods{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *PayMethodsService) GetAll() (resList []*model.PayMethods, err error) {
	err = global.DB.Model(&model.PayMethods{}).Find(&resList).Error
	return
}

func (m *PayMethodsService) GetList(info req.PayMethodsSearch) (resList []*model.PayMethods, total int64, err error) {
	db := global.DB.Model(&model.PayMethods{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.Name != "" {
		db = db.Where("name LIKE ?", "%"+info.Name+"%")
	}
	if info.InternalName != "" {
		db = db.Where("internal_name LIKE ?", "%"+info.InternalName+"%")
	}
	if info.OpenType != 0 {
		db = db.Where("open_type = ?", info.OpenType)
	}
	if info.Status != 0 {
		db = db.Where("status = ?", info.Status)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
