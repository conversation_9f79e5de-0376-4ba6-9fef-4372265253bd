package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"aimsg-server/utils"
	"errors"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type SysUserKocService struct{}

func (m *SysUserKocService) GetSysUserAiRoleId(c *gin.Context) (aiRoleId uint, err error) {
	var (
		aiRoleInfo  *model.AiRole
		resRecord   model.SysUserKoc
		kocUserInfo model.KocUserInfo
		aiRoleSer   AiRoleService
		sysUserId   = utils.GetUserID(c)
	)
	if err = global.DB.Model(&model.SysUserKoc{}).Where("sys_user_id = ?", sysUserId).First(&resRecord).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, nil
		}
		return
	}
	if err = global.DB.Model(&model.KocUserInfo{}).Where("id = ?", resRecord.KocUserId).First(&kocUserInfo).Error; err != nil {
		return
	}
	aiRoleInfo, err = aiRoleSer.GetByRoleId(kocUserInfo.AiRoleId)
	if err != nil {
		return
	}
	aiRoleId = aiRoleInfo.Id
	return
}

func (m *SysUserKocService) GetSysUserKocUserId(c *gin.Context) (kocUserId uint, err error) {
	var (
		resRecord model.SysUserKoc
		sysUserId = utils.GetUserID(c)
	)
	if err = global.DB.Model(&model.SysUserKoc{}).Where("sys_user_id = ?", sysUserId).First(&resRecord).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, nil
		}
		return
	}
	kocUserId = resRecord.KocUserId
	return
}

func (m *SysUserKocService) Create(reqData *model.SysUserKoc) (err error) {
	err = global.DB.Model(&model.SysUserKoc{}).Create(reqData).Error
	return
}

func (m *SysUserKocService) CreateOrUpdate(reqData *model.SysUserKoc) (err error) {
	var (
		sysUserKocInfo *model.SysUserKoc
	)
	err = global.DB.Model(&model.SysUserKoc{}).Where("sys_user_id = ?", reqData.SysUserId).First(&sysUserKocInfo).Error
	if err != nil {
		err = global.DB.Model(&model.SysUserKoc{}).Create(reqData).Error
	} else {
		err = global.DB.Model(&model.SysUserKoc{}).Where("id = ?", sysUserKocInfo.Id).Updates(reqData).Error
	}
	return
}

func (m *SysUserKocService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.SysUserKoc{}).Where("id = ?", id).Delete(&model.SysUserKoc{}).Error
	return
}

func (m *SysUserKocService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.SysUserKoc{}).Where("id IN (?)", reqData.Ids).Delete(&model.SysUserKoc{}).Error
	return
}

func (m *SysUserKocService) Update(reqData *model.SysUserKoc) (err error) {
	err = global.DB.Model(&model.SysUserKoc{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	return
}

func (m *SysUserKocService) GetById(id uint) (res *model.SysUserKoc, err error) {
	err = global.DB.Model(&model.SysUserKoc{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *SysUserKocService) GetAll() (resList []*model.SysUserKoc, err error) {
	err = global.DB.Model(&model.SysUserKoc{}).Find(&resList).Error
	return
}

func (m *SysUserKocService) GetList(info req.SysUserKocSearch) (resList []*model.SysUserKoc, total int64, err error) {
	db := global.DB.Model(&model.SysUserKoc{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.SysUserId != 0 {
		db = db.Where("sys_user_id = ?", info.SysUserId)
	}
	if info.KocUserId != 0 {
		db = db.Where("koc_user_id = ?", info.KocUserId)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
