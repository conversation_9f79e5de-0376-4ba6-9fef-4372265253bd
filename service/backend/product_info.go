package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
	"sort"
	"time"
)

type ProductInfoService struct{}

func (m *ProductInfoService) CopyInfo(sourceAppId, targetAppId uint) (err error) {
	var (
		sourceInfos []*model.ProductInfo
		newInfos    []*model.ProductInfo
		nowTime     = time.Now()
	)
	sourceInfos, err = m.FindByAppId(sourceAppId)
	if err != nil {
		return
	}
	for _, sourceInfo := range sourceInfos {
		sourceInfo.Id = 0
		sourceInfo.CreatedAt = nowTime
		sourceInfo.UpdatedAt = nowTime
		sourceInfo.AppID = targetAppId
		newInfos = append(newInfos, sourceInfo)
	}
	if len(newInfos) == 0 {
		return
	}
	err = global.DB.Model(&model.ProductInfo{}).Create(&newInfos).Error
	return
}

func (m *ProductInfoService) FindByAppId(appId uint) (resList []*model.ProductInfo, err error) {
	err = global.DB.Model(&model.ProductInfo{}).Where("app_id = ?", appId).Find(&resList).Error
	return
}

func (m *ProductInfoService) Create(reqData *model.ProductInfo) (err error) {
	err = global.DB.Model(&model.ProductInfo{}).Create(reqData).Error
	go m.SyncAll()
	return
}

func (m *ProductInfoService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.ProductInfo{}).Where("id = ?", id).Delete(&model.ProductInfo{}).Error
	go m.SyncAll()
	return
}

func (m *ProductInfoService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.ProductInfo{}).Where("id IN (?)", reqData.Ids).Delete(&model.ProductInfo{}).Error
	go m.SyncAll()
	return
}

func (m *ProductInfoService) Update(reqData *model.ProductInfo) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.ProductInfo{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	go m.SyncAll()
	return
}

func (m *ProductInfoService) GetById(id uint) (res *model.ProductInfo, err error) {
	err = global.DB.Model(&model.ProductInfo{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *ProductInfoService) GetAll() (resList []*model.ProductInfo, err error) {
	err = global.DB.Model(&model.ProductInfo{}).Find(&resList).Error
	return
}

func (m *ProductInfoService) SyncAll() {
	var (
		err                error
		productSer         ProductService
		dbResList          []*model.ProductInfo
		appInfoTypeListMap = map[uint]map[model.ProductInfoType]model.ProductInfoSyncItem{}
	)
	productSer.SyncAll()
	dbResList, err = m.GetAll()
	if err != nil {
		global.LOG.Error("SyncAll 失败", zap.Error(err))
		return
	}
	for _, productItem := range dbResList {
		appInfoTypeListItem := appInfoTypeListMap[productItem.AppID]
		if appInfoTypeListItem == nil {
			appInfoTypeListItem = map[model.ProductInfoType]model.ProductInfoSyncItem{}
		}
		productInfoSyncItem := appInfoTypeListItem[productItem.InfoType]
		// 增加
		productInfoSyncItem.ProductInfoList = append(productInfoSyncItem.ProductInfoList, productItem)
		// 排序productInfoTypeList,按照sort字段倒序
		sort.Slice(productInfoSyncItem.ProductInfoList, func(i, j int) bool {
			return productInfoSyncItem.ProductInfoList[i].Sort > productInfoSyncItem.ProductInfoList[j].Sort
		})
		appInfoTypeListItem[productItem.InfoType] = productInfoSyncItem
		appInfoTypeListMap[productItem.AppID] = appInfoTypeListItem
	}
	for appId, aObj := range appInfoTypeListMap {
		for infoType, productInfoSyncItem := range aObj {
			for _, productInfoItem := range productInfoSyncItem.ProductInfoList {
				productObj, productObjExist := global.AllProductMap[productInfoItem.ProductID]
				if productObjExist {
					productInfoSyncItem.ProductList = append(productInfoSyncItem.ProductList, productObj)
				}
			}
			aObj[infoType] = productInfoSyncItem
		}
		appInfoTypeListMap[appId] = aObj
	}
	global.AppInfoTypeListMap = appInfoTypeListMap
}

func (m *ProductInfoService) GetList(info req.ProductInfoSearch) (resList []*model.ProductInfo, total int64, err error) {
	db := global.DB.Model(&model.ProductInfo{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.InfoType != 0 {
		db = db.Where("info_type = ?", info.InfoType)
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.ProductID != 0 {
		db = db.Where("product_id = ?", info.ProductID)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
