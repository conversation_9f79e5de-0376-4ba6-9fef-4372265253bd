package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm/clause"
)

type PrivateSpaceService struct{}

func (m *PrivateSpaceService) Create(ctx context.Context, reqData *model.PrivateSpace) (err error) {
	err = global.DB.Model(&model.PrivateSpace{}).Create(reqData).Error
	return
}

func (m *PrivateSpaceService) DeleteById(ctx context.Context, id uint) (err error) {
	err = global.DB.Model(&model.PrivateSpace{}).Where("id = ?", id).Delete(&model.PrivateSpace{}).Error
	return
}

func (m *PrivateSpaceService) DeleteByIds(ctx context.Context, reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.PrivateSpace{}).Where("id IN (?)", reqData.Ids).Delete(&model.PrivateSpace{}).Error
	return
}

func (m *PrivateSpaceService) Update(ctx context.Context, reqData *model.PrivateSpace) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return
	}
	err = global.DB.Model(&model.PrivateSpace{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	return
}

func (m *PrivateSpaceService) GetById(ctx context.Context, id uint) (res *model.PrivateSpace, err error) {
	err = global.DB.Model(&model.PrivateSpace{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *PrivateSpaceService) GetAll(ctx context.Context) (resList []*model.PrivateSpace, err error) {
	err = global.DB.Model(&model.PrivateSpace{}).Find(&resList).Error
	return
}

func (m *PrivateSpaceService) SearchAll(ctx context.Context, info *model.PrivateSpace) (res []*model.PrivateSpace, err error) {
	db := global.DB.Model(&model.PrivateSpace{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.MediaType != 0 {
		db = db.Where("media_type = ?", info.MediaType)
	}
	err = db.Order("sort DESC").Find(&res).Error
	return
}

func (m *PrivateSpaceService) SearchOne(ctx context.Context, info *model.PrivateSpace) (res *model.PrivateSpace, err error) {
	db := global.DB.Model(&model.PrivateSpace{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.MediaType != 0 {
		db = db.Where("media_type = ?", info.MediaType)
	}
	if err = db.First(&res).Error; err != nil {
		return
	}
	return
}

func (m *PrivateSpaceService) SearchCount(ctx context.Context, info *model.PrivateSpace) (total int64, err error) {
	db := global.DB.Model(&model.PrivateSpace{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.MediaType != 0 {
		db = db.Where("media_type = ?", info.MediaType)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	return
}

func (m *PrivateSpaceService) GetList(ctx context.Context, info req.PrivateSpaceSearch) (resList []*model.PrivateSpace, total int64, err error) {
	db := global.DB.Model(&model.PrivateSpace{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.MediaType != 0 {
		db = db.Where("media_type = ?", info.MediaType)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
