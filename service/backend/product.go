package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"encoding/json"
	"go.uber.org/zap"
	"time"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm/clause"
)

type ProductService struct{}

func (m *ProductService) CopyInfo(sourceAppId, targetAppId uint) (err error) {
	var (
		sourceInfos []*model.Product
		newInfos    []*model.Product
		nowTime     = time.Now()
	)
	sourceInfos, err = m.FindByAppId(sourceAppId)
	if err != nil {
		return
	}
	for _, sourceInfo := range sourceInfos {
		sourceInfo.Id = 0
		sourceInfo.CreatedAt = nowTime
		sourceInfo.UpdatedAt = nowTime
		sourceInfo.AppID = targetAppId
		newInfos = append(newInfos, sourceInfo)
	}
	if len(newInfos) == 0 {
		return
	}
	err = global.DB.Model(&model.Product{}).Create(&newInfos).Error
	return
}

func (m *ProductService) FindByAppId(appId uint) (resList []*model.Product, err error) {
	err = global.DB.Model(&model.Product{}).Where("app_id = ?", appId).Find(&resList).Error
	return
}

func (m *ProductService) Create(reqData *model.Product) (err error) {
	err = global.DB.Model(&model.Product{}).Create(reqData).Error
	m.SyncAll()
	return
}

func (m *ProductService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.Product{}).Where("id = ?", id).Delete(&model.Product{}).Error
	m.SyncAll()
	return
}

func (m *ProductService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.Product{}).Where("id IN (?)", reqData.Ids).Delete(&model.Product{}).Error
	m.SyncAll()
	return
}

func (m *ProductService) Update(reqData *model.Product) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.Product{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	m.SyncAll()
	return
}

func (m *ProductService) GetById(id uint) (res *model.Product, err error) {
	err = global.DB.Model(&model.Product{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *ProductService) GetByIosId(iosId string) (res *model.Product, err error) {
	err = global.DB.Model(&model.Product{}).Where("ios_id = ?", iosId).First(&res).Error
	return
}

func (m *ProductService) GetByGooId(gooId string) (res *model.Product, err error) {
	err = global.DB.Model(&model.Product{}).Where("goo_id = ?", gooId).First(&res).Error
	return
}

func (m *ProductService) GetAll() (resList []*model.Product, err error) {
	err = global.DB.Model(&model.Product{}).Find(&resList).Error
	return
}

func (m *ProductService) SyncAll() {
	var (
		err               error
		dbResList         []*model.Product
		allProductMap     = map[uint]*model.Product{}
		appProductListMap = map[uint][]*model.Product{}
	)
	dbResList, err = m.GetAll()
	if err != nil {
		global.LOG.Error("SyncAll 失败", zap.Error(err))
		return
	}
	for _, productItem := range dbResList {
		// 所有商品的map
		allProductMap[productItem.Id] = productItem
		// 分APP的商品列表
		appProductList := appProductListMap[productItem.AppID]
		appProductList = append(appProductList, productItem)
		appProductListMap[productItem.AppID] = appProductList
	}
	global.AllProductMap = allProductMap
	global.AppProductListMap = appProductListMap
}

func (m *ProductService) GetOne(req *model.Product) (res *model.Product, err error) {
	db := global.DB.Model(&model.Product{})
	if req.Id != 0 {
		db = db.Where("id = ?", req.Id)
	}
	if req.GooID != "" {
		db = db.Where("goo_id = ?", req.GooID)
	}
	if req.GoodType != 0 {
		db = db.Where("good_type = ?", req.GoodType)
	}
	if req.ProductType != 0 {
		db = db.Where("product_type = ?", req.ProductType)
	}
	if req.AppID != 0 {
		db = db.Where("app_id = ?", req.AppID)
	}
	if req.IOSID != "" {
		db = db.Where("ios_id = ?", req.IOSID)
	}
	err = db.First(&res).Error
	return
}
func (m *ProductService) GetList(info req.ProductSearch) (resList []*model.Product, total int64, err error) {
	db := global.DB.Model(&model.Product{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.Name != "" {
		db = db.Where("name LIKE ?", "%"+info.Name+"%")
	}
	if info.GooID != "" {
		db = db.Where("goo_id LIKE ?", "%"+info.GooID+"%")
	}
	if info.GoodType != 0 {
		db = db.Where("good_type = ?", info.GoodType)
	}
	if info.ProductType != 0 {
		db = db.Where("product_type = ?", info.ProductType)
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.IOSID != "" {
		db = db.Where("ios_id LIKE ?", "%"+info.IOSID+"%")
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}

func (m *ProductService) SetProductInfoType() {

}

// 返回的数据模型定义  TODO
func (m *ProductService) GetSubList(pageName string) (resList []*model.Product, err error) {
	// 审核模式不走降价的逻辑

	return nil, nil
}
