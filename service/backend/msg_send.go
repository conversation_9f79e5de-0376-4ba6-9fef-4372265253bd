package backend

import (
	"aimsg-server/global"
	"aimsg-server/utils"
	"context"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"time"
)

func (m *MsgService) SendImgMsg(ctx context.Context, userInfo *model.DigitalUser, aiRoleInfo *model.AiRole) {
	defer func() {
		if rErr := recover(); rErr != nil {
			global.LOG.Error("SendImgMsg", zap.Any("err", rErr))
		}
	}()
	var (
		err        error
		msgType    model.MsgType
		msgContent model.MessageContent
		appInfo, _ = global.AppMap[userInfo.AppID]
		userIdStr  = fmt.Sprintf("%d", userInfo.Id)
		isAudit    = userInfo.IsAudit()
		nowTime    = time.Now()
	)
	if isAudit {
		return
	}
	if msgType, msgContent, err = m.GetMsgContent(ctx, userInfo, aiRoleInfo, model.SceneSeeUMsg, "", 0); err != nil {
		return
	}
	msgContent.Text = utils.RandList(global.RANDOM_SEEU_MSG_LIST)
	aiSendMsgInfo := &model.MsgInfo{
		MessageID:      uuid.NewString(),
		Channel:        nil,
		SendTime:       nowTime.UnixMilli(),
		DeletedAt:      nil,
		MessageType:    msgType,
		SenderID:       aiRoleInfo.RoleID,
		SenderName:     aiRoleInfo.Nickname,
		ReceiverID:     userIdStr,
		ReceiverName:   userInfo.Nickname,
		MessageContent: msgContent,
	}
	if err = m.SendMsgToAgora(aiSendMsgInfo, appInfo); err != nil {
		return
	}
	// 保存发送过的记录
	aiSendMediaSer := AiSendMediaService{}
	aiSendMediaRecord := model.AiSendMedia{
		DigitalUserId: userInfo.Id,
		AiRoleId:      aiRoleInfo.Id,
		MediaType:     model.MediaTypeImg,
		MediaURL:      msgContent.ImgURL,
		ThumbnailURL:  msgContent.ImgURL,
		SendTime:      nowTime,
		UnlockState:   model.UnlockStateNo,
		UnlockTime:    nil,
	}
	if msgType == model.MsgTypeTxtImg {
		if userInfo.IsSub() {
			aiSendMediaRecord.UnlockState = model.UnlockStateYes
			aiSendMediaRecord.UnlockTime = &nowTime
		}
	}
	aiSendMediaSer.SaveByMsg(&aiSendMediaRecord)
	return
}

func (m *MsgService) SendVideoMsg(ctx context.Context, userInfo *model.DigitalUser, aiRoleInfo *model.AiRole) {
	defer func() {
		if rErr := recover(); rErr != nil {
			global.LOG.Error("SendVideoMsg", zap.Any("err", rErr))
		}
	}()
	var (
		err        error
		msgType    model.MsgType
		msgContent model.MessageContent
		appInfo, _ = global.AppMap[userInfo.AppID]
		userIdStr  = fmt.Sprintf("%d", userInfo.Id)
		isAudit    = userInfo.IsAudit()
		nowTime    = time.Now()
	)
	if isAudit {
		return
	}
	if msgType, msgContent, err = m.GetMsgContent(ctx, userInfo, aiRoleInfo, model.SceneVideoMsg, "", 0); err != nil {
		return
	}
	msgContent.Text = utils.RandList(global.RANDOM_SEEU_MSG_LIST)
	aiSendMsgInfo := &model.MsgInfo{
		MessageID:      uuid.NewString(),
		Channel:        nil,
		SendTime:       nowTime.UnixMilli(),
		DeletedAt:      nil,
		MessageType:    msgType,
		SenderID:       aiRoleInfo.RoleID,
		SenderName:     aiRoleInfo.Nickname,
		ReceiverID:     userIdStr,
		ReceiverName:   userInfo.Nickname,
		MessageContent: msgContent,
	}
	if err = m.SendMsgToAgora(aiSendMsgInfo, appInfo); err != nil {
		return
	}
	// 保存发送过的记录
	aiSendMediaSer := AiSendMediaService{}
	aiSendMediaRecord := model.AiSendMedia{
		DigitalUserId: userInfo.Id,
		AiRoleId:      aiRoleInfo.Id,
		MediaType:     model.MediaTypeImg,
		MediaURL:      msgContent.ImgURL,
		ThumbnailURL:  msgContent.ImgURL,
		SendTime:      nowTime,
		UnlockState:   model.UnlockStateNo,
		UnlockTime:    nil,
	}
	if msgType == model.MsgTypeTxtImg {
		if userInfo.IsSub() {
			aiSendMediaRecord.UnlockState = model.UnlockStateYes
			aiSendMediaRecord.UnlockTime = &nowTime
		}
	} else if msgType == model.MsgTypeTxtVideo {
		aiSendMediaRecord.MediaType = model.MediaTypeVideo
		aiSendMediaRecord.MediaURL = msgContent.FileURL
		aiSendMediaSer.SaveByMsg(&aiSendMediaRecord)
	}
	aiSendMediaSer.SaveByMsg(&aiSendMediaRecord)
	return
}

func (m *MsgService) AISendUserGiftMsg(ctx context.Context, userId uint, aiRoleId string) {
	defer func() {
		if rErr := recover(); rErr != nil {
			global.LOG.Error("AISendUserGiftMsg", zap.Any("err", rErr))
		}
	}()
	var (
		err      error
		userInfo *model.DigitalUser
		userSer  DigitalUserService
	)
	// 查询用户
	if userInfo, err = userSer.GetByIdUnscoped(ctx, userId); err != nil {
		return
	}
	verCompare360 := utils.CompareVersions(userInfo.Version, "3.6.0")
	// 孙宏伟需求 2024-12-24 新增app_id 4号包数据 亲密度功能
	verCompare364 := utils.CompareVersions(userInfo.Version, "3.6.4")
	if verCompare364 > 0 && userInfo.AppID == 4 {
		verCompare360 = 1
	}
	// 孙宏伟需求 2024-12-24 新增app_id 10号包数据 亲密度功能
	verCompare363 := utils.CompareVersions(userInfo.Version, "3.6.3")
	if verCompare363 > 0 && userInfo.AppID == 10 {
		verCompare360 = 1
	}

	if userInfo.AppID != 17 {
		verCompare360 = 1

	}

	// // 孙宏伟需求 2024-12-24 新增app_id 6号包数据 亲密度功能 一直发 不用处理
	// verCompare365 := utils.CompareVersions(userInfo.Version, "3.6.5")
	// if verCompare365 > 0 && userInfo.AppID == 6 {
	// 	verCompare360 = 1
	// }

	if verCompare360 < 0 {
		return
	}
	if userInfo.AppID == 1 || userInfo.AppID == 2 || userInfo.AppID == 6 || userInfo.AppID == 11 || userInfo.AppID == 4 || userInfo.AppID == 10 || userInfo.AppID == 7 || userInfo.AppID == 17 {
		var (
			aiInfo *model.AiRole
			aiSer  AiRoleService
		)
		appInfo := global.AppMap[userInfo.AppID]
		isAudit := userInfo.IsAudit()
		if isAudit {
			return
		}
		// 查询AI
		if aiInfo, err = aiSer.GetByRoleId(aiRoleId); err != nil {
			return
		}
		// 查询AI送用户礼物图片配置
		imgCfg := GetAiSendUserGiftImgCfg(ctx, userInfo.AppID)

		if len(imgCfg.GiftList) == 0 {
			return
		}
		// 2024-12-26 新增礼物引导 孙宏伟需求
		// 照片处理处理
		giftInfoIcon := ""
		for _, v := range imgCfg.GiftList {
			content := global.REDIS.Get(ctx, fmt.Sprintf(global.GUIDED_GIFT_ICON_KEY, userInfo.Id, aiInfo.RoleID, userInfo.AppID, v.Icon)).Val()
			if content == "" {
				giftInfoIcon = v.Icon
				break
			}
		}
		if giftInfoIcon == "" {
			for _, v := range imgCfg.GiftList {
				global.REDIS.Del(ctx, fmt.Sprintf(global.GUIDED_GIFT_ICON_KEY, userInfo.Id, aiInfo.RoleID, userInfo.AppID, v.Icon)).Val()
			}
			// 重置redis
			giftInfoIcon = utils.RandList(imgCfg.GiftList).Icon
		}

		// 随机取一个礼物
		// giftInfo := utils.RandList(imgCfg.GiftList)
		sendMsgInfo := &model.MsgInfo{
			MessageID:    uuid.NewString(),
			SendTime:     time.Now().UnixMilli(),
			MessageType:  model.MsgTypeAISendUserGiftImg,
			SenderID:     aiInfo.RoleID,
			SenderName:   aiInfo.Nickname,
			ReceiverID:   fmt.Sprintf("%d", userInfo.Id),
			ReceiverName: userInfo.Nickname,
			MessageContent: model.MessageContent{
				Avatar: utils.S3Url(aiInfo.Avatar, userInfo.AppID),
				ImgURL: utils.S3Url(giftInfoIcon, userInfo.AppID),
			},
		}
		global.REDIS.Set(ctx, fmt.Sprintf(global.GUIDED_GIFT_ICON_KEY, userInfo.Id, aiInfo.RoleID, userInfo.AppID, giftInfoIcon), giftInfoIcon, 10*24*time.Hour)

		if err = m.SendMsgToAgora(sendMsgInfo, appInfo); err != nil {
			return
		}
		// 查询AI送用户礼物文案配置
		txtCfg := GetAiSendUserGiftTxtCfg(ctx, userInfo.AppID)
		// 2024-12-26 新增礼物引导 孙宏伟需求
		// 文案处理
		txtInfoContent := ""
		for _, v := range txtCfg.TxtList {
			content := global.REDIS.Get(ctx, fmt.Sprintf(global.GUIDED_GIFT_TXT_KEY, userInfo.Id, aiInfo.RoleID, userInfo.AppID, v.Content)).Val()
			if content == "" {
				txtInfoContent = v.Content
				break
			}
		}
		if txtInfoContent == "" {
			for _, v := range txtCfg.TxtList {
				global.REDIS.Del(ctx, fmt.Sprintf(global.GUIDED_GIFT_TXT_KEY, userInfo.Id, aiInfo.RoleID, userInfo.AppID, v.Content)).Val()
			}
			// 重置redis
			txtInfoContent = utils.RandList(txtCfg.TxtList).Content
		}
		// 随机取一个文案
		// txtInfo := utils.RandList(txtCfg.TxtList)
		txtMsgInfo := &model.MsgInfo{
			MessageID:    uuid.NewString(),
			SendTime:     time.Now().UnixMilli(),
			MessageType:  model.MsgTypeTxt,
			SenderID:     aiInfo.RoleID,
			SenderName:   aiInfo.Nickname,
			ReceiverID:   fmt.Sprintf("%d", userInfo.Id),
			ReceiverName: userInfo.Nickname,
			MessageContent: model.MessageContent{
				Avatar:     utils.S3Url(aiInfo.Avatar, userInfo.AppID),
				Text:       txtInfoContent,
				OriginText: txtInfoContent,
			},
		}
		if err = m.SendMsgToAgora(txtMsgInfo, appInfo); err != nil {
			return
		}
		// 将文案放到redis中 下次不抽取 如果没有文案了 那就重置这个redis
		global.REDIS.Set(ctx, fmt.Sprintf(global.GUIDED_GIFT_TXT_KEY, userInfo.Id, aiInfo.RoleID, userInfo.AppID, txtInfoContent), txtInfoContent, 10*24*time.Hour)
	}
	return
}
