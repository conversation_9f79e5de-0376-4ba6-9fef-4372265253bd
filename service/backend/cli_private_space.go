package backend

import (
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/utils"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
)

func (m *PrivateSpaceService) PrivateSpaceList(c *gin.Context, reqData *request.PrivateSpaceListReq) (res gin.H, err error) {
	var (
		privateSpaces       []*response.PrivateSpaceListItem
		privateSpacesList   []*model.PrivateSpace
		privateSpaceUsers   []*model.PrivateSpaceUser
		aiRoleInfo          *model.AiRole
		aiRoleSer           AiRoleService
		privateSpaceUserSer PrivateSpaceUserService
		userId              = utils.GetDigitalUserID(c)
		appId               = utils.GetAppID(c)
		userBuyIdMap        = map[uint]bool{}
		userBuyTimeMap      = map[uint]string{}
	)
	// 获取AI角色信息
	if aiRoleInfo, err = aiRoleSer.GetByRoleId(reqData.AiRoleId); err != nil {
		return
	}
	// 查询私人空间列表
	searchReq := &model.PrivateSpace{
		AiId: aiRoleInfo.Id,
	}
	if privateSpacesList, err = m.SearchAll(c, searchReq); err != nil {
		return
	}
	// 查询用户购买过的记录
	searchReqUser := &model.PrivateSpaceUser{
		DigitalUserId: userId,
		AiId:          aiRoleInfo.Id,
	}
	privateSpaceUsers, err = privateSpaceUserSer.SearchAll(c, searchReqUser)
	if err != nil {
		return
	}
	if len(privateSpaceUsers) > 0 {
		for _, privateSpaceUser := range privateSpaceUsers {
			userBuyIdMap[privateSpaceUser.PrivateSpaceId] = true
			userBuyTimeMap[privateSpaceUser.PrivateSpaceId] = privateSpaceUser.CreatedAt.Format("2006/1/2")
		}
	}
	// 组装返回数据
	for _, s := range privateSpacesList {
		tmpSpace := &response.PrivateSpaceListItem{
			Id:           s.Id,
			Name:         s.Name,
			MediaType:    s.MediaType,
			MediaSize:    s.MediaSize,
			ThumbnailURL: utils.S3Url(s.ThumbnailURL, appId),
			ImgCount:     s.ImgCount,
			VideoSecond:  s.VideoSecond,
			Price:        s.Price,
			BuyTime:      userBuyTimeMap[s.Id],
			IsBuy:        userBuyIdMap[s.Id],
		}
		privateSpaces = append(privateSpaces, tmpSpace)
	}
	res = gin.H{
		"list": privateSpaces,
	}
	return
}
