package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/utils"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm/clause"
)

func (m *AiScenesService) CliGetList(c *gin.Context, info req.CliAiScenesSearch) (resList []*response.CliAiScenesRes, total int64, err error) {
	var (
		scenesRes         []*model.AiScenes
		aiRoleInfo        *model.AiRole
		userUseScenesInfo *model.UserUseScenes
		aiRoleSer         AiRoleService
		userUseScenesSer  UserUseScenesService
		userId            = utils.GetDigitalUserID(c)
		appId             = utils.GetAppID(c)
	)
	aiRoleInfo, err = aiRoleSer.GetByRoleId(info.RoleID)
	if err != nil {
		return
	}
	scenes := model.AiScenes{}
	db := global.DB.Table(scenes.TableName())
	db = db.Where("ai_role_id = ?", aiRoleInfo.Id)

	if info.SceneType != 0 {
		db = db.Where("scene_type = ?", info.SceneType)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&scenesRes).Error
	if err != nil {
		return
	}

	// 查询用户绑定的场景信息
	userUseScenesInfo, _ = userUseScenesSer.SearchOne(c, &model.UserUseScenes{
		DigitalUserId: userId,
		AiRoleId:      aiRoleInfo.Id,
	})
	for _, scenesRe := range scenesRes {
		scRes := &response.CliAiScenesRes{
			Id:            scenesRe.Id,
			AiRoleId:      scenesRe.AiRoleId,
			SceneName:     scenesRe.SceneName,
			SceneType:     scenesRe.SceneType,
			SceneDescribe: scenesRe.SceneDescribe,
			ChatBG:        utils.S3Url(scenesRe.ChatBG, appId),
		}
		if userUseScenesInfo != nil && userUseScenesInfo.SceneId == scenesRe.Id {
			scRes.Checked = true
		}
		resList = append(resList, scRes)
	}
	return
}
