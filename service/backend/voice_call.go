package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm/clause"
)

type VoiceCallService struct{}

func (m *VoiceCallService) Create(ctx context.Context, reqData *model.VoiceCall) (err error) {
	err = global.DB.Model(&model.VoiceCall{}).Create(reqData).Error
	return
}

func (m *VoiceCallService) DeleteById(ctx context.Context, id uint) (err error) {
	err = global.DB.Model(&model.VoiceCall{}).Where("id = ?", id).Delete(&model.VoiceCall{}).Error
	return
}

func (m *VoiceCallService) DeleteByIds(ctx context.Context, reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.VoiceCall{}).Where("id IN (?)", reqData.Ids).Delete(&model.VoiceCall{}).Error
	return
}

func (m *VoiceCallService) Update(ctx context.Context, reqData *model.VoiceCall) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.VoiceCall{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	return
}

func (m *VoiceCallService) GetById(ctx context.Context, id uint) (res *model.VoiceCall, err error) {
	err = global.DB.Model(&model.VoiceCall{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *VoiceCallService) GetAll(ctx context.Context) (resList []*model.VoiceCall, err error) {
	err = global.DB.Model(&model.VoiceCall{}).Find(&resList).Error
	return
}

func (m *VoiceCallService) GetList(ctx context.Context, info req.VoiceCallSearch) (resList []*model.VoiceCall, total int64, err error) {
	db := global.DB.Model(&model.VoiceCall{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
