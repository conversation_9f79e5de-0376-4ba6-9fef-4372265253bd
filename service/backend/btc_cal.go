package backend

import (
	"aimsg-server/global"
	"aimsg-server/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/jinzhu/copier"
	"go.uber.org/zap"
	"math"
	"strconv"
	"strings"
	"time"
)

// btc 获取算力
// 网络总算力=矿机算力 * （1 - 矿池费%）*  区块奖励 * 区块数 /（收益 + 电费/汇率）
const (
	hashrateReq        = 200000000000000
	poolFeeRate        = 0     //矿池费
	blockReward        = 3.125 //区块奖励
	blockNum           = 144   //  区块数
	RedisBtcHashNetKey = "ai:btc:btchashnet"
)

func (m *BtcService) powerHash(value uint64, unit string) (string, string) {
	// 预定义单位（从 H/s 开始，确保 unitIndex 不会变成负数）
	var units = []string{"H/s", "KH/s", "MH/s", "GH/s", "TH/s", "PH/s", "EH/s"}

	// 规范输入单位（大小写不敏感）
	unit = strings.ToUpper(unit)

	// 确定输入单位的索引
	unitIndex := 0
	switch unit {
	case "H/S":
		unitIndex = 0
	case "KH/S":
		unitIndex = 1
	case "MH/S":
		unitIndex = 2
	case "GH/S":
		unitIndex = 3
	case "TH/S":
		unitIndex = 4
	case "PH/S":
		unitIndex = 5
	case "EH/S":
		unitIndex = 6
	default:
		unitIndex = 3 // 未知单位默认为 GH/s
	}

	// 先转换为 H/s
	fValue := float64(value) * math.Pow(1000, float64(unitIndex))

	unitIndex = 0
	// 进位转换，确保整数部分不超过 3 位
	for fValue >= 1000 && unitIndex < len(units)-1 {
		fValue /= 1000
		unitIndex++
	}

	for unitIndex < 3 {
		fValue /= 1000
		unitIndex++
	}

	// 保留 1 位小数
	formattedValue := fmt.Sprintf("%.1f", fValue)

	return formattedValue, units[unitIndex]
}

// 根据算力和时间得到 btc数量
func (m BtcService) getBtcBlock(value float64, seconds int64) (resp float64, err error) {
	btcHashNetStr := global.REDIS.Get(context.Background(), RedisBtcHashNetKey).Val()
	if btcHashNetStr == "" {
		err = errors.New("获取全网hash率失败")
		return
	}
	btcHashNet, err := strconv.ParseFloat(btcHashNetStr, 64)
	if err != nil {
		return
	}
	v, err := m.convertHashrate(value, "H/s")
	if err != nil {
		return
	}

	//收益BTC    =（矿机算力/网络总算力）× 区块奖励 × 挖矿周期内能挖多少个区块数 *（1 - 矿池费%）-电费/汇率
	btcRevenue := (v / btcHashNet) * blockNum * blockReward * (1 - poolFeeRate)
	resp = btcRevenue / 86400 * float64(seconds)
	return resp, nil
}

// ConvertHashrate 将给定的哈希率值和单位转换为 H/s
func (m BtcService) convertHashrate(value float64, unit string) (float64, error) {
	// 定义单位换算表
	unitMap := map[string]float64{
		"H/S":  1,
		"KH/S": 1e3,
		"MH/S": 1e6,
		"GH/S": 1e9,
		"TH/S": 1e12,
		"PH/S": 1e15,
		"EH/S": 1e18,
	}

	// 统一单位为大写
	unit = strings.ToUpper(unit)

	// 检查单位是否支持
	multiplier, exists := unitMap[unit]
	if !exists {
		return 0, errors.New("unsupported unit")
	}

	// 计算换算后的 H/s 值
	return value * multiplier, nil
}

func (m *BtcService) Crontab2() (record model.BtcBlockchainHashRate) {
	//请求api
	localKey := "ai:btc:crontab"
	isLock := global.REDIS.SetNX(context.Background(), localKey, time.Now().Format("2006-01-02 15:04:05"), time.Hour*24).Val()
	if !isLock {
		return
	}
	data, apiData, err := m.getBlockchainHashRate2()
	if err != nil {
		global.REDIS.Del(context.Background(), localKey)
		global.LOG.Error("Btc矿机计算api请求错误 error", zap.Any("err", err))
		return
	}
	_ = copier.Copy(&record, &data)
	record.JsonData = apiData
	record.Day = time.Now()
	//落库
	err = global.DB.Create(&record).Error
	if err != nil {
		global.REDIS.Del(context.Background(), localKey)
		global.LOG.Error("Btc矿机计算api请求错误 error", zap.Any("err", err))
		return
	}
	BtcRevenue24, err := strconv.ParseFloat(record.BtcRevenue24, 64)
	if err != nil {
		return
	}
	//btc网络哈希率
	btcHashNet := hashrateReq * (1 - poolFeeRate) * blockReward * blockNum / BtcRevenue24
	global.REDIS.Set(context.Background(), RedisBtcHashNetKey, btcHashNet, time.Hour*24*365)
	return
}

// 网络总算力=矿机算力 * （1 - 矿池费%）*  区块奖励 * 区块数 /（收益 + 电费/汇率）
func (BtcService) getBlockchainHashRate2() (resp CoinData, apiData string, err error) {
	req := map[string]interface{}{
		"cost": 0,
		"settings": []interface{}{
			map[string]interface{}{
				"algorithm": "sha256",
				"power":     0,
				"hashrate":  hashrateReq,
			},
		},
	}
	var data interface{}
	apiLog, curlStr, err := utils.HttpPostJson(fmt.Sprintf("%s?api_token=%s", whattomineAPI, token), req, &data, 3)
	if err != nil {
		err = errors.New(fmt.Sprintf("%s, %s, err:%s", apiLog, curlStr, err))
		return
	}
	josnB, _ := json.Marshal(data)
	var list []CoinData
	apiData = string(josnB)
	err = json.Unmarshal(josnB, &list)
	if err != nil {
		global.LOG.Error("Btc 矿机计算解析 error", zap.Any("err", err))
	}
	isSet := false
	for _, v := range list {
		if v.Tag == "BTC" {
			resp = v
			isSet = true
			break
		}
	}
	if !isSet {
		err = errors.New("btc 不存在")
		global.LOG.Error("Btc 矿机计算解析结果中不存在btc error")
		return
	}
	return
}
