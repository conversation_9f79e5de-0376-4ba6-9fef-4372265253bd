package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/utils"
	"context"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
	"time"
)

func (m *MsgService) UserAiQuickReplyRdsKey(userId uint, aiRoleId string) (rdsKey string) {
	return fmt.Sprintf(global.USER_AI_QUICK_REPLY_RDS_KEY, userId, aiRoleId)
}

func (m *MsgService) SetUserAiQuickReplyMsgId(ctx context.Context, userId uint, aiRoleId string, msgId string) (err error) {
	rdsKey := m.UserAiQuickReplyRdsKey(userId, aiRoleId)
	err = global.REDIS.Set(ctx, rdsKey, msgId, time.Hour).Err()
	return
}

func (m *MsgService) GetUserAiQuickReplyMsgId(ctx context.Context, userId uint, aiRoleId string) (msgId string, err error) {
	rdsKey := m.UserAiQuickReplyRdsKey(userId, aiRoleId)
	msgId, err = global.REDIS.Get(ctx, rdsKey).Result()
	return
}

func (m *MsgService) DelUserAiQuickReplyMsgId(ctx context.Context, userId uint, aiRoleId string) (err error) {
	rdsKey := m.UserAiQuickReplyRdsKey(userId, aiRoleId)
	err = global.REDIS.Del(ctx, rdsKey).Err()
	return
}

func (m *MsgService) execUserQuickReply(ctx context.Context, msgApi model.MsgJob, sendMsgReq *request.RedisMsgReq) (err error) {
	var (
		msgId       string
		lastMsgInfo *model.MsgInfo
		sendAiInfo  *model.AiRole
		userInfo    *model.DigitalUser
		profileInfo *model.DigitalUserProfile
		userService DigitalUserService
		profileSer  DigitalUserProfileService
		aiSer       AiRoleService
		userIdStr   = fmt.Sprintf("%d", sendMsgReq.UserId)
	)
	gLog := global.LOG.With(zap.String("Func", "execUserQuickReply"), zap.String("Name", msgApi.Name), zap.String("ApiUrl", msgApi.ApiUrl), zap.Any("sendMsgReq", sendMsgReq))
	// 获取用户信息
	if userInfo, err = userService.GetById(ctx, sendMsgReq.UserId); err != nil {
		return
	}
	// 获取用户资料
	if profileInfo, err = profileSer.GetByUserId(sendMsgReq.UserId); err != nil {
		return
	}
	if msgId, err = m.GetUserAiQuickReplyMsgId(ctx, sendMsgReq.UserId, sendMsgReq.AiRoleId); err != nil {
		err = fmt.Errorf("获取用户快捷回复消息ID失败, err: %v", err)
		return
	}
	// 查询聊天记录
	msgQuery := bson.M{"message_id": msgId, "receiver_id": userIdStr}
	if err = global.MONGO.Find(ctx, msgQuery).One(&lastMsgInfo); err != nil {
		err = fmt.Errorf("message find error, %v", err.Error())
		return
	}
	// 获取AI角色信息
	if sendAiInfo, err = aiSer.GetByRoleId(lastMsgInfo.SenderID); err != nil {
		return
	}
	aiSendMsgText := lastMsgInfo.MessageContent.Text

	err = m.UserQuickReplyMsgSend(ctx, msgApi, sendMsgReq, userInfo, profileInfo, sendAiInfo, aiSendMsgText, msgId)
	if err != nil {
		gLog.Error("UserQuickReplyMsgSend error", zap.Any("err", err))
		return
	}
	return
}

func (m *MsgService) UserQuickReplyMsgGetRandomMsg() (msgText string) {
	msgText = utils.RandList(global.UserQuickReplyArr)
	return
}

func (m *MsgService) UserQuickReplyMsgSend(ctx context.Context, msgApi model.MsgJob, sendMsgReq *request.RedisMsgReq, userInfo *model.DigitalUser, userProfileInfo *model.DigitalUserProfile, sendAiInfo *model.AiRole, aiSendText string, originMsgId string) (err error) {
	var (
		msgInfoArr []*model.MsgInfo
		aiSer      AiRoleService
		userIdStr  = fmt.Sprintf("%d", userInfo.Id)
		now        = time.Now()
	)
	maleRoleArr := []response.QuickReplyAiRoleType{
		{MsgType: model.MsgTypeUserQuickReplyNSFW, RoleID: 994},
		{MsgType: model.MsgTypeUserQuickReplyNormal, RoleID: 995},
	}
	femaleRoleArr := []response.QuickReplyAiRoleType{
		{MsgType: model.MsgTypeUserQuickReplyNSFW, RoleID: 996},
		{MsgType: model.MsgTypeUserQuickReplyNormal, RoleID: 997},
	}
	gLog := global.LOG.With(zap.String("Func", "GetUserQuickReplyMsgArr"), zap.String("Gender", model.GenderTypeMap[sendAiInfo.Gender]))

	if sendAiInfo.Gender == model.GenderTypeMale {
		// 男性AI角色
		// id:994: nsfw用户角色卡，对应app男性角色卡快捷回复
		// id:995: 普通用户角色卡，对应app男性角色卡快捷回复
		for _, mRoleTypeItem := range maleRoleArr {
			modelQuickReplyCoins := 10
			// 获取AI角色信息
			var (
				replyMsg   string
				aiRoleInfo *model.AiRole
			)
			if aiRoleInfo, err = aiSer.UnscopedGetById(mRoleTypeItem.RoleID); err != nil {
				gLog.Error("获取AI角色信息失败", zap.Any("err", err))
				continue
			}
			// 获取回复消息
			gLog.Info("获取AI角色信息成功", zap.Any("aiRoleInfo", aiRoleInfo))
			// 获取回复消息
			if replyMsg, err = m.getAiReplyMsg(ctx, msgApi, aiSendText, aiRoleInfo, userInfo, sendMsgReq, userProfileInfo.Nsfw); err != nil {
				gLog.Error("获取大模型回复消息失败", zap.Any("err", err))
				continue
			}
			gLog.Info("获取大模型回复消息成功", zap.String("replyMsg", replyMsg))
			if replyMsg == "" {
				replyMsg = m.UserQuickReplyMsgGetRandomMsg()
			}

			// 12-31 增加快捷回复消息下发付费 根据app_id 获取对应的配置金额 如果0的话就给一个默认的值
			val := global.REDIS.Get(ctx, fmt.Sprintf(global.USER_QUICK_REPLY_COST_RDS_KEY, mRoleTypeItem.MsgType, userInfo.AppID)).Val()
			if val == "" {
				val = "10"
			}
			modelQuickReplyCoins = utils.StrToInt(val)

			msgContent := model.MessageContent{
				Text:                 replyMsg,
				OriginText:           replyMsg,
				ModelQuickReplyCoins: modelQuickReplyCoins,
			}
			aiSendMsg := &model.MsgInfo{
				MessageID:      originMsgId,
				Channel:        nil,
				SendTime:       now.UnixMilli(),
				DeletedAt:      nil,
				MessageType:    mRoleTypeItem.MsgType,
				SenderID:       sendMsgReq.AiRoleId,
				SenderName:     aiRoleInfo.Nickname,
				ReceiverID:     userIdStr,
				ReceiverName:   userInfo.Nickname,
				MessageContent: msgContent,
			}
			msgInfoArr = append(msgInfoArr, aiSendMsg)
		}
	} else {
		// 女性AI角色
		// id:996  nsfw男性用户角色卡，对应app女性角色卡快捷回复
		// id:997  普通男性用户角色卡，对应app女性角色卡快捷回复
		for _, mRoleTypeItem := range femaleRoleArr {
			modelQuickReplyCoins := 10
			// 获取AI角色信息
			var (
				replyMsg   string
				aiRoleInfo *model.AiRole
			)
			if aiRoleInfo, err = aiSer.UnscopedGetById(mRoleTypeItem.RoleID); err != nil {
				gLog.Error("获取AI角色信息失败", zap.Any("err", err))
				continue
			}
			gLog.Info("获取AI角色信息成功", zap.Any("aiRoleInfo", aiRoleInfo))
			// 获取回复消息
			if replyMsg, err = m.getAiReplyMsg(ctx, msgApi, aiSendText, aiRoleInfo, userInfo, sendMsgReq, userProfileInfo.Nsfw); err != nil {
				gLog.Error("获取大模型回复消息失败", zap.Any("err", err))
				continue
			}
			gLog.Info("获取大模型回复消息成功", zap.String("replyMsg", replyMsg))
			if replyMsg == "" {
				replyMsg = m.UserQuickReplyMsgGetRandomMsg()
			}
			// 12-31 增加快捷回复消息下发付费 根据app_id 获取对应的配置金额 如果0的话就给一个默认的值
			val := global.REDIS.Get(ctx, fmt.Sprintf(global.USER_QUICK_REPLY_COST_RDS_KEY, mRoleTypeItem.MsgType, userInfo.AppID)).Val()
			if val == "" {
				val = "10"
			}
			modelQuickReplyCoins = utils.StrToInt(val)
			msgContent := model.MessageContent{
				Text:                 replyMsg,
				OriginText:           replyMsg,
				ModelQuickReplyCoins: modelQuickReplyCoins,
			}
			aiSendMsg := &model.MsgInfo{
				MessageID:      originMsgId,
				Channel:        nil,
				SendTime:       now.UnixMilli(),
				DeletedAt:      nil,
				MessageType:    mRoleTypeItem.MsgType,
				SenderID:       sendMsgReq.AiRoleId,
				SenderName:     aiRoleInfo.Nickname,
				ReceiverID:     userIdStr,
				ReceiverName:   userInfo.Nickname,
				MessageContent: msgContent,
			}
			msgInfoArr = append(msgInfoArr, aiSendMsg)
		}
	}
	if len(msgInfoArr) == 0 {
		gLog.Error("msgInfoArr is empty")
		return
	}
	gLog = gLog.With(zap.Any("msgInfoArr", msgInfoArr))
	// 解锁
	lockRedisKey := m.GetMsgLockRedisKey(sendMsgReq)
	_ = global.REDIS.Del(ctx, lockRedisKey).Val()
	appInfo := global.AppMap[userInfo.AppID]
	for _, msgInfo := range msgInfoArr {
		// 发送消息到声网
		if err = m.SendMsgToAgoraWithoutMongo(msgInfo, appInfo); err != nil {
			gLog.Error("SendMsgToAgoraWithoutMongo error", zap.Any("err", err))
			continue
		}
	}
	gLog.Info("发送消息到声网成功")
	return
}
