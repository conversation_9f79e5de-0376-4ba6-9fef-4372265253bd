package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/backend/response"
	"aimsg-server/model/common/request"
	"context"
	"errors"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

type DailyReportService struct{}

func (m *DailyReportService) Statistics(taskTime time.Time) {
	var (
		err           error
		statisticList []*model.DailyReport
		taskTimeStr   = taskTime.Format("2006-01-02")
	)
	if err = global.DB.Model(&model.Order{}).Select("DATE(pay_time) AS day, ai_role_id, SUM(usd_price) AS revenue_usd").Where("order_status = ? AND ai_role_id != 0 AND DATE(pay_time)= ?", model.OSPayGoodSuccess, taskTimeStr).Group("ai_role_id").Find(&statisticList).Error; err != nil {
		return
	}
	if len(statisticList) == 0 {
		global.LOG.Info("No data to statistics")
		return
	}
	for _, v := range statisticList {
		err = m.CreateOrUpdate(v)
		if err != nil {
			global.LOG.Error("CreateOrUpdate error", zap.Error(err))
		}
	}
	return
}

func (m *DailyReportService) CreateOrUpdate(reqData *model.DailyReport) (err error) {
	var (
		aiRoleInfo     *model.AiRole
		kocUserInfo    *model.KocUserInfo
		kocUserInfoErr error
		kocUserInfoSer KocUserInfoService
		aiRoleSer      AiRoleService
		searchOne      *model.DailyReport
		ctx            = context.Background()
	)
	// 查询是否存在
	if searchOne, err = m.SearchOne(reqData); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 不存在则创建
			if err = m.Create(reqData); err != nil {
				return
			}
			// 查询AI角色信息
			if aiRoleInfo, err = aiRoleSer.GetById(ctx, reqData.AiRoleId); err != nil {
				return
			}
			// 查询KOC用户信息, 更新总收入和待结算收入
			if kocUserInfo, kocUserInfoErr = kocUserInfoSer.SearchOne(&model.KocUserInfo{AiRoleId: aiRoleInfo.RoleID}); kocUserInfoErr == nil {
				// 更新总收入
				kocUserInfo.AllIncome = kocUserInfo.AllIncome.Add(reqData.RevenueUSD)
				// 待结算收入
				kocUserInfo.PendingIncome = kocUserInfo.PendingIncome.Add(reqData.RevenueUSD)
				// 更新KOC用户信息
				if err = kocUserInfoSer.Update(kocUserInfo); err != nil {
					return
				}
			}
			return
		} else {
			return
		}
	}
	// 存在则更新
	reqData.Id = searchOne.Id
	err = m.Update(reqData)
	return
}

func (m *DailyReportService) Create(reqData *model.DailyReport) (err error) {
	err = global.DB.Model(&model.DailyReport{}).Create(reqData).Error
	return
}

func (m *DailyReportService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.DailyReport{}).Where("id = ?", id).Delete(&model.DailyReport{}).Error
	return
}

func (m *DailyReportService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.DailyReport{}).Where("id IN (?)", reqData.Ids).Delete(&model.DailyReport{}).Error
	return
}

func (m *DailyReportService) Update(reqData *model.DailyReport) (err error) {
	err = global.DB.Model(&model.DailyReport{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	return
}

func (m *DailyReportService) GetById(id uint) (res *model.DailyReport, err error) {
	err = global.DB.Model(&model.DailyReport{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *DailyReportService) SearchOne(reqData *model.DailyReport) (res *model.DailyReport, err error) {
	db := global.DB.Model(&model.DailyReport{})
	if reqData.Id != 0 {
		db = db.Where("id = ?", reqData.Id)
	}
	db = db.Where("day = ?", reqData.Day)
	if reqData.AiRoleId != 0 {
		db = db.Where("ai_role_id = ?", reqData.AiRoleId)
	}
	err = db.First(&res).Error
	return
}
func (m *DailyReportService) GetAll() (resList []*model.DailyReport, err error) {
	err = global.DB.Model(&model.DailyReport{}).Find(&resList).Error
	return
}

func (m *DailyReportService) GetList(c *gin.Context, info req.DailyReportSearch) (resList []*response.DailyReportListRes, total int64, err error) {
	var (
		aiRoleId      uint
		sysUserKocSer SysUserKocService
		db            = global.DB.Table("daily_report AS d")
	)
	if aiRoleId, err = sysUserKocSer.GetSysUserAiRoleId(c); err != nil {
		return
	}
	if aiRoleId != 0 {
		info.AiRoleId = aiRoleId
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("d.created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("d.updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if len(info.DayRange) == 2 {
		db = db.Where("d.day BETWEEN ? AND ?", info.DayRange[0], info.DayRange[1])
	}
	if info.AiRoleId != 0 {
		db = db.Where("d.ai_role_id = ?", info.AiRoleId)
	}
	if info.RoleId != "" {
		db = db.Where("a.role_id = ?", info.RoleId)
	}
	db = db.Select("d.*, a.role_id")
	db = db.Joins("LEFT JOIN ai_role AS a ON d.ai_role_id = a.id")
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: "d." + info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("d.id DESC")
	}
	err = db.Find(&resList).Error
	return
}

func (m *DailyReportService) GetSum(c *gin.Context, info req.DailyReportSearch) (res *model.DailyReport, err error) {
	var (
		aiRoleId      uint
		sysUserKocSer SysUserKocService
		db            = global.DB.Model(&model.DailyReport{})
	)
	if aiRoleId, err = sysUserKocSer.GetSysUserAiRoleId(c); err != nil {
		return
	}
	if aiRoleId != 0 {
		info.AiRoleId = aiRoleId
	}
	db = db.Select("SUM(revenue_usd) AS revenue_usd")
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if len(info.DayRange) == 2 {
		db = db.Where("day BETWEEN ? AND ?", info.DayRange[0], info.DayRange[1])
	}
	if info.AiRoleId != 0 {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	err = db.Find(&res).Error
	return
}

func (m *DailyReportService) GetTotalIncomeByAiRoleId(aiRoleId uint, pendingIncomeDayRange []string) (revenueUsd decimal.Decimal, err error) {
	var (
		statisticList []*model.DailyReport
	)
	db := global.DB.Model(&model.DailyReport{}).Select("SUM(revenue_usd) AS revenue_usd").Where("ai_role_id = ?", aiRoleId)
	if len(pendingIncomeDayRange) == 2 {
		db = db.Where("day BETWEEN ? AND ?", pendingIncomeDayRange[0], pendingIncomeDayRange[1])
	}
	if err = db.Find(&statisticList).Error; err != nil {
		return
	}
	if len(statisticList) == 0 {
		global.LOG.Info("No data to statistics")
		return
	}
	revenueUsd = statisticList[0].RevenueUSD
	return
}
