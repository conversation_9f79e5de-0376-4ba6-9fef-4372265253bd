package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/response"
	"aimsg-server/utils"
	"context"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"sort"
)

func GetRequireImgSubMsgCfg(ctx context.Context, appId uint) (rdsCfg response.RequireImgSubMsgCfg) {
	var (
		err           error
		redisConfig   response.RequireImgSubMsgCfg
		defaultConfig = response.RequireImgSubMsgCfg{
			Step:      2,
			MaskImg:   "public/image/fddccaf9-0335-4986-a259-09bac2948afb.png",
			ProductID: 47,
			MsgList: []string{
				"Don't share it with others after reading it.",
				"Is this the posture in your dream?",
				"You can be rough with me.",
				"I'm soaking wet, take me",
				"I offer you my alluring form, sweetheart",
				"Cherish me, be gentle to me, baby",
				"I can give you more if you want.",
			},
		}
	)
	redisKey := fmt.Sprintf(global.RequireImgSubMsgCfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		rdsCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		rdsCfg = defaultConfig
		return
	}
	rdsCfg = redisConfig
	return
}

func SetRequireImgSubMsgCfg(ctx context.Context, appId uint, config response.RequireImgSubMsgCfg) (err error) {
	redisKey := fmt.Sprintf(global.RequireImgSubMsgCfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

// GetGuidedGiftGivingCfg 2024-12-26 新增礼物引导 孙宏伟需求
func GetGuidedGiftGivingCfg(ctx context.Context, appId uint) []map[string]int {
	// voiceKey := fmt.Sprintf("public/voice/%s/%s.mp3", "11111113_test", "11111113_test")
	// textToSpeechBytes, _ := elevenlabs.TextToSpeechWithMinMax("Aveline's eyes gleamed with excitement. Anything ? "+
	// 	"She whispered, her breath catching.Well, aside from my combat skills, I've always been fond of music and dance, "+
	// 	"but never had the chance to learn. I imagine it'd be..."+
	// 	" enticing to learn now. She paused, biting her lip again. Perhaps you could teach me, commander? In between... other things.", "danya_xuejie", 1, 0, 1)
	// s3Client := GetS3Client()
	// if err := s3Client.UploadBytesToS3(textToSpeechBytes, voiceKey, "audio/mpeg"); err != nil {
	// 	fmt.Println("test")
	// }
	// fmt.Println("test")
	configStr := global.REDIS.Get(ctx, fmt.Sprintf(global.GUIDED_GIFT_CONFIG_KEY, appId)).Val()
	if configStr == "" {
		return nil
	}
	var config map[int64]int64
	err := json.Unmarshal([]byte(configStr), &config)
	if err != nil {
		return nil
	}
	// 转换为 []map[string]int 格式
	var result []map[string]int
	for round, num := range config {
		result = append(result, map[string]int{
			"round": int(round),
			"num":   int(num),
		})
	}
	// 按照 round 从小到大排序
	sort.Slice(result, func(i, j int) bool {
		return result[i]["round"] < result[j]["round"]
	})

	return result
}

// GetReplyChargeCfg 12-31 增加快捷回复消息下发付费
func GetReplyChargeCfg(ctx context.Context, appId uint) map[string]int {
	nsfwVal := global.REDIS.Get(ctx, fmt.Sprintf(global.USER_QUICK_REPLY_COST_RDS_KEY, model.MsgTypeUserQuickReplyNSFW, appId)).Val()
	if nsfwVal == "" {
		nsfwVal = "10"
	}
	coinsNsfwVal := utils.StrToInt(nsfwVal)

	normalVal := global.REDIS.Get(ctx, fmt.Sprintf(global.USER_QUICK_REPLY_COST_RDS_KEY, model.MsgTypeUserQuickReplyNormal, appId)).Val()
	if normalVal == "" {
		normalVal = "10"
	}
	coinsNormal := utils.StrToInt(normalVal)

	result := make(map[string]int)
	result["normal"] = coinsNormal
	result["nsfw"] = coinsNsfwVal
	return result
}
