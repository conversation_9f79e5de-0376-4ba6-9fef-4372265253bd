package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm/clause"
)

type FlowerConsumeRecordService struct{}

func (m *FlowerConsumeRecordService) Create(reqData *model.FlowerConsumeRecord) (err error) {
	err = global.DB.Model(&model.FlowerConsumeRecord{}).Create(reqData).Error
	return
}

func (m *FlowerConsumeRecordService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.FlowerConsumeRecord{}).Where("id = ?", id).Delete(&model.FlowerConsumeRecord{}).Error
	return
}

func (m *FlowerConsumeRecordService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.FlowerConsumeRecord{}).Where("id IN (?)", reqData.Ids).Delete(&model.FlowerConsumeRecord{}).Error
	return
}

func (m *FlowerConsumeRecordService) Update(reqData *model.FlowerConsumeRecord) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.FlowerConsumeRecord{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	return
}

func (m *FlowerConsumeRecordService) GetById(id uint) (res *model.FlowerConsumeRecord, err error) {
	err = global.DB.Model(&model.FlowerConsumeRecord{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *FlowerConsumeRecordService) SearchCount(info *model.FlowerConsumeRecord) (total int64, err error) {
	db := global.DB.Model(&model.FlowerConsumeRecord{})
	if info.ConsumeType != 0 {
		db = db.Where("consume_type = ?", info.ConsumeType)
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.RelationID != "" {
		db = db.Where("relation_id = ?", info.RelationID)
	}
	err = db.Count(&total).Error
	return
}
func (m *FlowerConsumeRecordService) GetAll() (resList []*model.FlowerConsumeRecord, err error) {
	err = global.DB.Model(&model.FlowerConsumeRecord{}).Find(&resList).Error
	return
}

func (m *FlowerConsumeRecordService) GetList(info req.FlowerConsumeRecordSearch) (resList []*model.FlowerConsumeRecord, total int64, err error) {
	db := global.DB.Model(&model.FlowerConsumeRecord{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.ConsumeType != 0 {
		db = db.Where("consume_type = ?", info.ConsumeType)
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.RelationID != "" {
		db = db.Where("relation_id = ?", info.RelationID)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
