package user

import (
	"aimsg-server/model/cli/response"
	"github.com/gin-gonic/gin"
)

var RewardSer = &RewardService{}

type RewardService struct{}

func (m *RewardService) Info(c *gin.Context) (*response.UserRewardResp, error) {
	return &response.UserRewardResp{List: []response.UserRewardListItem{
		{
			Type: "content",
			Items: []string{
				"Unlock hot photos & videos",
				"Undress Your AI Girlfriend",
				"Unlock customized",
				"Unlock private space",
			},
		},
		{
			Type: "chat",
			Items: []string{
				"Unlimited character chat",
				"Unlock hot ASMR",
				"Unlock voice call",
				"Unlock NSFW filters",
			},
		},
	}}, nil
}
