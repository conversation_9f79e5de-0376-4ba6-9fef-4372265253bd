package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
	"time"
)

type AiContactInfoService struct{}

func (m *AiContactInfoService) Create(reqData *model.AiContactInfo) (err error) {
	err = global.DB.Model(&model.AiContactInfo{}).Create(reqData).Error
	return
}

func (m *AiContactInfoService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.AiContactInfo{}).Where("id = ?", id).Delete(&model.AiContactInfo{}).Error
	return
}

func (m *AiContactInfoService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.AiContactInfo{}).Where("id IN (?)", reqData.Ids).Delete(&model.AiContactInfo{}).Error
	return
}

func (m *AiContactInfoService) Update(reqData *model.AiContactInfo) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.AiContactInfo{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	return
}

func (m *AiContactInfoService) GetById(id uint) (res *model.AiContactInfo, err error) {
	err = global.DB.Model(&model.AiContactInfo{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *AiContactInfoService) GetAll() (resList []*model.AiContactInfo, err error) {
	err = global.DB.Model(&model.AiContactInfo{}).Find(&resList).Error
	return
}

func (m *AiContactInfoService) SearchAll(info *model.AiContactInfo) (resList []*model.AiContactInfo, err error) {
	db := global.DB.Model(&model.AiContactInfo{})
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.ContactType != 0 {
		db = db.Where("contact_type = ?", info.ContactType)
	}
	if info.ShowStatus != 0 {
		db = db.Where("show_status = ?", info.ShowStatus)
	}
	err = db.Find(&resList).Error
	return
}

func (m *AiContactInfoService) SearchAiRoleIdAll(ctx context.Context, aiRoleId string) (resList []*model.AiContactInfo, err error) {
	db := global.DB.Model(&model.AiContactInfo{})
	db = db.Where("ai_role_id = ?", aiRoleId)
	db = db.Where("show_status = ?", model.SwitchStatusNormal)
	err = db.Find(&resList).Error
	if err == nil {
		m.SetCache(ctx, aiRoleId, resList)
	}
	return
}

func (m AiContactInfoService) CacheKey(aiRoleId string) string {
	return fmt.Sprintf("ai_contact_info:%s", aiRoleId)
}

func (m *AiContactInfoService) SetCache(ctx context.Context, aiRoleId string, records []*model.AiContactInfo) {
	var (
		err           error
		recordBytes   []byte
		cacheRedisKey = m.CacheKey(aiRoleId)
	)
	recordBytes, err = json.Marshal(records)
	if err != nil {
		global.LOG.Error("SetCache", zap.Error(err))
	}
	recordStr := string(recordBytes)
	if err = global.REDIS.SetEx(ctx, cacheRedisKey, recordStr, time.Hour).Err(); err != nil {
		global.LOG.Error("SetCache", zap.Error(err))
	}
	return
}

func (m *AiContactInfoService) DelCache(ctx context.Context, aiRoleId string) {
	var (
		cacheRedisKey = m.CacheKey(aiRoleId)
	)
	global.REDIS.Del(ctx, cacheRedisKey)
	return
}

func (m *AiContactInfoService) GetCache(ctx context.Context, aiRoleId string) (record []*model.AiContactInfo, err error) {
	var (
		cacheRedisStr string
		cacheRedisKey = m.CacheKey(aiRoleId)
	)
	if cacheRedisStr, err = global.REDIS.Get(ctx, cacheRedisKey).Result(); err != nil {
		return
	}
	if err = json.Unmarshal([]byte(cacheRedisStr), &record); err != nil {
		return
	}
	return
}

func (m *AiContactInfoService) GetList(info req.AiContactInfoSearch) (resList []*model.AiContactInfo, total int64, err error) {
	db := global.DB.Model(&model.AiContactInfo{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.ContactType != 0 {
		db = db.Where("contact_type = ?", info.ContactType)
	}
	if info.ShowStatus != 0 {
		db = db.Where("show_status = ?", info.ShowStatus)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
