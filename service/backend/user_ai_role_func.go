package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"errors"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type UserAiRoleFuncService struct{}

func (m *UserAiRoleFuncService) Create(reqData *model.UserAiRoleFunc) (err error) {
	err = global.DB.Model(&model.UserAiRoleFunc{}).Create(reqData).Error
	return
}

func (m *UserAiRoleFuncService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.UserAiRoleFunc{}).Where("id = ?", id).Delete(&model.UserAiRoleFunc{}).Error
	return
}

func (m *UserAiRoleFuncService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.UserAiRoleFunc{}).Where("id IN (?)", reqData.Ids).Delete(&model.UserAiRoleFunc{}).Error
	return
}

func (m *UserAiRoleFuncService) Update(reqData *model.UserAiRoleFunc) (err error) {
	err = global.DB.Model(&model.UserAiRoleFunc{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	return
}

func (m *UserAiRoleFuncService) GetById(id uint) (res *model.UserAiRoleFunc, err error) {
	err = global.DB.Model(&model.UserAiRoleFunc{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *UserAiRoleFuncService) GetOne(userId, aiRoleId uint) (res *model.UserAiRoleFunc, err error) {
	err = global.DB.Model(&model.UserAiRoleFunc{}).Where("digital_user_id = ? AND ai_role_id = ?", userId, aiRoleId).First(&res).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 创建
			res = &model.UserAiRoleFunc{
				DigitalUserId: userId,
				AiRoleId:      aiRoleId,
				SendAsmrList:  []string{},
				SendSeeuList:  []string{},
			}
			err = m.Create(res)
			return
		} else {
			return
		}
	}
	return
}

func (m *UserAiRoleFuncService) GetAll() (resList []*model.UserAiRoleFunc, err error) {
	err = global.DB.Model(&model.UserAiRoleFunc{}).Find(&resList).Error
	return
}

func (m *UserAiRoleFuncService) GetList(info req.UserAiRoleFuncSearch) (resList []*model.UserAiRoleFunc, total int64, err error) {
	db := global.DB.Model(&model.UserAiRoleFunc{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.AiRoleId != 0 {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
