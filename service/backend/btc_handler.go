package backend

import (
	"aimsg-server/global"
	reqBtc "aimsg-server/model/backend/request/btc"
	"aimsg-server/utils"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"sort"
	"time"
)

const (
	ADMax = 30
)

type HomeHandler struct {
	ctx        *gin.Context
	Resp       *reqBtc.Home
	btcService *BtcService
}

func (m *BtcService) Index(c *gin.Context) (interface{}, error) {
	h := HomeHandler{
		ctx:        c,
		btcService: m,
		Resp:       &reqBtc.Home{},
	}
	var resp reqBtc.Home
	var err error
	resp.TotalAssets, err = h.TotalAssets()
	if err != nil {
		return nil, err
	}
	userId := utils.GetDigitalUserID(c)
	resp.FreeComputingPower, err = h.freeComputingPower(c, userId)
	if err != nil {
		return nil, err
	}
	resp.MoreComputingPower, err = h.MoreComputingPower()
	if err != nil {
		return nil, err
	}
	resp.FAQs = h.FAQs()
	return resp, nil
}

func (m *HomeHandler) FAQs() (resp *reqBtc.FAQs) {
	resp = &reqBtc.FAQs{
		Title: "FAQ",
		Img:   "https://images.aipersona.cloud/public/image/220ccb77-a75f-4ef4-bb06-8a38e172a337.png",
		Name:  "FAQs",
		List: []reqBtc.FAQDetail{
			{
				Img:     fmt.Sprintf("%s%s", global.CONFIG.AwsS3.BaseURL, "public/image/f7b0fd39-7f9f-44d1-9884-92061b248c1e.png"),
				Title:   "Are we mining real Bitcoin?",
				Content: "We are mining real BTC. All the actual computing power you own corresponds to real BTC miners in the cloud, which earn BTC for you.",
			},
			{
				Img:     fmt.Sprintf("%s%s", global.CONFIG.AwsS3.BaseURL, "public/image/f7b0fd39-7f9f-44d1-9884-92061b248c1e.png"),
				Title:   "What is Satoshi or sat?",
				Content: "It is the smallest unit of Bitcoin, equal to\n0.00000001 Bitcoin, expressed in scientific notation as 1.0 * 10^-8",
			},
			{
				Img:     fmt.Sprintf("%s%s", global.CONFIG.AwsS3.BaseURL, "public/image/f7b0fd39-7f9f-44d1-9884-92061b248c1e.png"),
				Title:   "How long does it take to mine ВТС?",
				Content: "When the BTC you mine reaches the withdrawal threshold, you can withdraw your BTC. Different withdrawal networks have different minimum withdrawal requirements, depending on the hash power you have. The more hash power, the faster BTC is generated.\nAdditionally, blockchain transactions take some time, and even after a transaction request is made, it requires some time to complete.",
			},
			{
				Img:     fmt.Sprintf("%s%s", global.CONFIG.AwsS3.BaseURL, "public/image/f7b0fd39-7f9f-44d1-9884-92061b248c1e.png"),
				Title:   "Can I earn BTC without spending any money?",
				Content: "Of course, you can get our free mining rig, and by watching ads, you can also earn a lot of free hash power that will continuously mine BTC for you.",
			},
			{
				Img:     fmt.Sprintf("%s%s", global.CONFIG.AwsS3.BaseURL, "public/image/f7b0fd39-7f9f-44d1-9884-92061b248c1e.png"),
				Title:   "What is a \"first-time offer\"?",
				Content: "\"First-time offer\" means that when you purchase products with this label, you can receive double the computing power and an additional bonus. This bonus allows you to claim more free computing power. Both the double power and the bonus are cumulative and valid indefinitely, but they can only be obtained on your first purchase.",
			},
			{
				Img:     fmt.Sprintf("%s%s", global.CONFIG.AwsS3.BaseURL, "public/image/f7b0fd39-7f9f-44d1-9884-92061b248c1e.png"),
				Title:   "How is the APR calculated?",
				Content: "APR is a comprehensive calculation result.\nOverall, it depends on your mining efficiency, the production efficiency of BTC blocks, the price of BTC, as well as maintenance costs, electricity costs, and other factors. All these will affect the APR.",
			},
			{
				Img:     fmt.Sprintf("%s%s", global.CONFIG.AwsS3.BaseURL, "public/image/f7b0fd39-7f9f-44d1-9884-92061b248c1e.png"),
				Title:   "How long is the validity period of the paid contract?",
				Content: "All paid contracts are valid indefinitely.",
			},
			{
				Img:     fmt.Sprintf("%s%s", global.CONFIG.AwsS3.BaseURL, "public/image/f7b0fd39-7f9f-44d1-9884-92061b248c1e.png"),
				Title:   "Do I need to renew the paid contract periodically?",
				Content: "The contracts currently offered are one-time purchases, and you do not need to renew them periodically.\nAbout the Lightning Network\nWe currently do not support invoice payments on the Lightning Network. Please enter your ZBD wallet's Gamertag & Lightning address in the withdrawal address field. At the moment, we only support the ZBD wallet, but we plan to support more wallets in the future.",
			},
			{
				Img:     fmt.Sprintf("%s%s", global.CONFIG.AwsS3.BaseURL, "public/image/f7b0fd39-7f9f-44d1-9884-92061b248c1e.png"),
				Title:   "Why did my total hash rate drop suddenly?",
				Content: "The hash rate might decrease because the free daily mining contracts and the ones obtained through ads each have an expiration of 8 hours and 24 hours, respectively. After they expire, the corresponding hash rate will decrease. The validity period starts from the moment each contract becomes active. For example, if you watch an ad now and receive a contract, it will expire at the same time tomorrow, lasting 24 hours. Apart from these cases, your hash rate shouldn't suddenly disappear.",
			},
		},
	}

	return resp
}

func (m *HomeHandler) MoreComputingPower() (resp *reqBtc.MoreComputingPower, err error) {
	resp = &reqBtc.MoreComputingPower{}
	resp.Title = "More computing power"
	appId := utils.GetAppID(m.ctx)
	list := m.btcService.getProductList(appId, nil)
	var goods []*ProductOther
	for _, v := range list {
		switch v.Category {
		case 0:
			if v.Onhome == 0 {
				continue
			}
			goods = append(goods, v)
		}
	}
	sort.Slice(goods, func(i, j int) bool {
		return goods[i].Onhome > goods[j].Onhome
	})

	for _, v := range goods {
		hashrate, unit := m.btcService.powerHash(v.Hashrate, "Gh/s")

		offer := ""
		if !v.Product.OriginPrice.IsZero() {
			// 定义现价和原价
			// 计算百分比变化: 1 - (现价 / 原价)
			percentageChange := decimal.NewFromInt(1).Sub(v.Product.USDPrice.Div(v.Product.OriginPrice)).Mul(decimal.NewFromInt(100))
			// 保留 1 位小数
			percentageChange = percentageChange.Round(1)
			offer = fmt.Sprintf("%.1f%%", percentageChange.InexactFloat64())
		}
		isFirstBuy, err := m.btcService.isFirstBuy(m.ctx, int(v.Product.Id))
		if err != nil {
			return nil, err
		}
		isBuy := !isFirstBuy
		buyDiscount := "Buy 1,Get 1 free"
		if isBuy {
			buyDiscount = ""
		}
		resp.ProductList = append(resp.ProductList, reqBtc.ProductDetail{
			GooID:               v.Product.GooID,
			IOSID:               v.Product.IOSID,
			Name:                v.Product.Name,
			MiningSpeed:         hashrate,
			MiningSpeedUnit:     unit,
			ID:                  int(v.Product.Id),
			Img:                 fmt.Sprintf("%s%s", global.CONFIG.AwsS3.BaseURL, v.Product.GetImg(0)),
			Description:         v.Product.Description,
			Bonus:               offer,
			APR:                 fmt.Sprintf("%s%%", v.Apr),
			OriginPrice:         v.Product.OriginPrice.String(),
			USDPrice:            v.Product.USDPrice.String(),
			IsBuy:               isBuy,
			FreeCoefficient:     fmt.Sprintf("%.1f%%", v.Ratio),
			FreeCoefficientDesc: "Free computing power",
			BuyDiscount:         buyDiscount,
		})
	}

	return
}

func (m *HomeHandler) freeComputingPower(c *gin.Context, userId uint) (resp *reqBtc.FreeComputingPower, err error) {
	curT := time.Now()
	appId := utils.GetAppID(c)
	resp = &reqBtc.FreeComputingPower{}
	var totalAssets reqBtc.FreeComputingPower
	totalAssets.Title = "Free computing power"
	power, err := m.btcService.getFreePower(userId)
	if err != nil {
		return nil, err
	}
	ratio := fmt.Sprintf("%.1f%%", power.Total)
	coefficientImg := "https://images.aipersona.cloud/public/image/64573bae-1d6e-44a7-9a59-9605750678ef.png"
	if power.Total == 0 {
		coefficientImg = "https://images.aipersona.cloud/public/image/89f0a832-f829-4324-b07d-f8e280ccdfd6.png"
		if appId == 126 {
			coefficientImg = "https://images.aipersona.cloud/public/image/bf37b75b-08c9-4a13-b65e-5faf30f8fdc5.png"
		}
		if appId == 128 {
			coefficientImg = "https://images.aipersona.cloud/public/image/2ff6448b-1198-43ff-ba96-9d74cd46bb2f.png"
		}
		if appId == 131 {
			coefficientImg = "https://images.aipersona.cloud/public/image/fc18e163-4099-42a8-8cd9-1c7207bfc3de.png"
		}
	}
	weekday := int(curT.Weekday())
	adInfo, err := m.btcService.ADDetail(m.ctx)
	if err != nil {
		return nil, err
	}
	img := "https://images.aipersona.cloud/public/image/d34e4060-1693-4627-ba19-4cd395598e2e.png"
	if appId == 126 {
		img = "https://images.aipersona.cloud/public/image/bf37b75b-08c9-4a13-b65e-5faf30f8fdc5.png"
	}
	if appId == 128 {
		img = "https://images.aipersona.cloud/public/image/2ff6448b-1198-43ff-ba96-9d74cd46bb2f.png"
	}

	if appId == 131 {
		img = "https://images.aipersona.cloud/public/image/fc18e163-4099-42a8-8cd9-1c7207bfc3de.png"
	}
	totalAssets.Header = []reqBtc.ComputingPowerHeader{
		*adInfo,
		{
			ID:             2,
			Name:           fmt.Sprintf("%sGh/s", m.btcService.getSingHashrate(weekday)),
			Img:            img,
			Desc:           "24 hours of validity",
			Coefficient:    ratio,
			CoefficientImg: coefficientImg,
			Used:           0,
			Total:          0,
			Type:           ProductTypeSign,
		},
	}

	signDetail, err := m.btcService.SignDetail(m.ctx)
	if err != nil {
		return nil, err
	}

	totalAssets.SignList = signDetail.SignList
	return &totalAssets, nil
}

func (m *HomeHandler) TotalAssets() (resp *reqBtc.TotalAssets, err error) {
	appId := utils.GetAppID(m.ctx)
	// 获取btc总收益
	resp = &reqBtc.TotalAssets{}
	totalBtc, err := m.btcService.CashOutBtcNum(m.ctx, utils.GetDigitalUserID(m.ctx))
	if err != nil {
		return resp, err
	}
	resp.Content = fmt.Sprintf("%.16f", totalBtc)
	btcYestorday, err := m.btcService.getBtcToYestoday(m.ctx)
	if err != nil {
		return
	}
	icon := fmt.Sprintf("%spublic/image/7db60ad7-357d-4141-81fe-01b630751208.png", global.CONFIG.AwsS3.BaseURL)
	if appId == 128 {
		icon = "https://images.aipersona.cloud/public/image/88d2c9e9-5235-4a8b-ac31-75fbf4632174.png"
	}
	resp.List = []reqBtc.AssetDetail{
		{
			Title:   "Yesterday you earned",
			Icon:    icon,
			Content: btcYestorday,
		},
	}

	return resp, nil
}
