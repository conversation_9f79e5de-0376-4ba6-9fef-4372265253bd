package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/backend/response"
	"aimsg-server/model/cli/request"
	cliResponse "aimsg-server/model/cli/response"
	"aimsg-server/utils"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"io"
	"math/rand"
	"net/http"
	"strings"
	"time"
)

type ImgToImgTrainService struct{}

func (m *ImgToImgTrainService) DelayImg2imgTrain() {
	var (
		ctx      = context.Background()
		redisKey = global.IMG_TO_IMG_DELAY_KEY
	)
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Panic("DelayImg2imgTrain panic", zap.Any("err", err))
		}
	}()
	for {
		time.Sleep(time.Millisecond * 500)
		now := time.Now()
		nowUnix := now.Unix()
		filter := &redis.ZRangeBy{
			Min:    "0",
			Max:    fmt.Sprintf("%d", nowUnix),
			Offset: 0,
			Count:  0,
		}
		redisMsgList, err := global.REDIS.ZRangeByScore(ctx, redisKey, filter).Result()
		if err != nil {
			global.LOG.Error("ZRevRange error", zap.Any("err", err))
			continue
		}
		if len(redisMsgList) == 0 {
			continue
		}
		var (
			imgReq  *model.ImgToImgReq
			taskStr = redisMsgList[0]
		)
		if _, err = global.REDIS.ZRem(ctx, redisKey, taskStr).Result(); err != nil {
			global.LOG.Error("删除消息key出错", zap.Any("err", err), zap.String("taskStr", taskStr))
		}
		if err = json.Unmarshal([]byte(taskStr), &imgReq); err != nil {
			continue
		}
		switch imgReq.TaskType {
		case 3:
			err = m.diyPhotoTaskTrain(ctx, imgReq)
		default:
			global.LOG.Error("未知任务类型", zap.Any("imgReq", imgReq))
		}
		if err != nil {
			global.LOG.Error("DelayImg2imgTrain error", zap.Any("err", err), zap.Any("imgReq", imgReq))
		}
	}
}

func (m *ImgToImgTrainService) ReceiveImg2imgTrain() {
	var (
		ctx      = context.Background()
		redisKey = global.IMG_TO_IMG_TRAIN_KEY
	)
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Panic("ReceiveImg2imgTrain panic", zap.Any("err", err))
		}
	}()
	for {
		time.Sleep(time.Millisecond * 500)
		redisMsgList, err := global.REDIS.ZRange(ctx, redisKey, 0, -1).Result()
		if err != nil {
			global.LOG.Error("ZRevRange error", zap.Any("err", err))
			continue
		}
		if len(redisMsgList) == 0 {
			continue
		}
		var (
			imgReq  *model.ImgToImgReq
			taskStr = redisMsgList[0]
		)
		if _, err = global.REDIS.ZRem(ctx, redisKey, taskStr).Result(); err != nil {
			global.LOG.Error("删除消息key出错", zap.Any("err", err), zap.String("taskStr", taskStr))
		}
		if err = json.Unmarshal([]byte(taskStr), &imgReq); err != nil {
			continue
		}
		switch imgReq.TaskType {
		case 1:
			err = m.userAvatarImg2Img(ctx, imgReq)
		case 2:
			err = m.imgGenRecordTrain(ctx, imgReq)
		case 3:
			err = m.diyPhotoTaskTrain(ctx, imgReq)
		default:
			global.LOG.Error("未知任务类型", zap.Any("imgReq", imgReq))
		}
		if err != nil {
			global.LOG.Error("ReceiveImg2imgTrain error", zap.Any("err", err), zap.Any("imgReq", imgReq))
		}
	}
}

func (m *ImgToImgTrainService) ReceiveImg2VideoTrain() {
	var (
		ctx      = context.Background()
		redisKey = global.IMG_TO_VIDEO_TRAIN_KEY
	)
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Panic("ReceiveImg2VideoTrain panic", zap.Any("err", err))
		}
	}()
	for {
		time.Sleep(time.Millisecond * 500)
		redisMsgList, err := global.REDIS.ZRange(ctx, redisKey, 0, -1).Result()
		if err != nil {
			global.LOG.Error("ZRevRange error", zap.Any("err", err))
			continue
		}
		if len(redisMsgList) == 0 {
			continue
		}
		var (
			taskReq *request.ImgToVideoRdsTask
			taskStr = redisMsgList[0]
		)
		if _, err = global.REDIS.ZRem(ctx, redisKey, taskStr).Result(); err != nil {
			global.LOG.Error("删除消息key出错", zap.Any("err", err), zap.String("taskStr", taskStr))
		}
		if err = json.Unmarshal([]byte(taskStr), &taskReq); err != nil {
			continue
		}
		err = m.imgToVideoTaskTrain(ctx, taskReq)
		if err != nil {
			// TODO 生成失败是否减每天生成的数量
			global.LOG.Error("ReceiveImg2VideoTrain error", zap.Any("err", err), zap.Any("taskReq", taskReq))
		}
	}
}

func (m *ImgToImgTrainService) userAvatarImg2Img(ctx context.Context, imgReq *model.ImgToImgReq) (err error) {
	var (
		checkFace   bool
		maskFiles   []string
		imgList     []string
		aiRoleInfo  *model.AiRole
		aiRoleSer   AiRoleService
		userSer     DigitalUserService
		imgTrainSer ImgToImgTrainService
		s3ClientV2  = GetS3ClientV2(ctx)
	)
	if aiRoleInfo, err = aiRoleSer.GetById(ctx, imgReq.TaskId); err != nil {
		return
	}
	// 调用云上曲率接口,判断是否是一张人脸
	avatarFullUrl := utils.S3Url(aiRoleInfo.Avatar, aiRoleInfo.AppID)
	if checkFace, err = userSer.FaceImgCheck(ctx, aiRoleInfo.Avatar); err != nil {
		return
	}
	if !checkFace {
		global.LOG.Info("FaceImgCheck 不是人脸照片", zap.Any("avatarFullUrl", avatarFullUrl))
		return
	}
	if maskFiles, err = s3ClientV2.ListFilesInS3Path("public/ai_role/private_ai/mask/"); err != nil {
		return
	}
	// 随机取蒙版
	maskFiles = utils.RandomStringList(maskFiles, 10)
	for maskIndex, maskFile := range maskFiles {
		var (
			imgPubPath string
		)
		if imgPubPath, err = imgTrainSer.Img2ImgReplaceFaceV2(aiRoleInfo.Avatar, maskFile); err != nil {
			global.LOG.Error("Img2ImgReplaceFaceV2 error", zap.Any("err", err))
			imgPubPath = maskFile
		}
		imgList = append(imgList, imgPubPath)
		if maskIndex == 0 {
			aiRoleInfo.FirstImg = imgPubPath
		}
		aiRoleInfo.ImgList = imgList
		if err = aiRoleSer.Update(ctx, aiRoleInfo); err != nil {
			global.LOG.Error("Update error", zap.Any("err", err))
			continue
		}
		uFullUrl := utils.S3Url(imgPubPath, aiRoleInfo.AppID)
		global.LOG.Info("Img2ImgTask success", zap.Int("maskIndex", maskIndex), zap.String("原图", avatarFullUrl), zap.String("写真图", uFullUrl))
	}
	return
}

func (m *ImgToImgTrainService) imgGenRecordTrain(ctx context.Context, imgReq *model.ImgToImgReq) (err error) {
	var (
		checkFace             bool
		imgPubPath            string
		imgGenRecordInfo      *model.ImgGenRecord
		imgTemplateRecordInfo *model.ImgTemplateRecord
		imgGenRecordSer       ImgGenRecordService
		imgTemplateRecordSer  ImgTemplateRecordService
		userSer               DigitalUserService
	)
	if imgGenRecordInfo, err = imgGenRecordSer.GetById(imgReq.TaskId); err != nil {
		return
	}
	if imgTemplateRecordInfo, err = imgTemplateRecordSer.GetById(imgGenRecordInfo.ImgRecordId); err != nil {
		return
	}
	// 调用云上曲率接口,判断是否是一张人脸
	avatarFullUrl := utils.S3Url(imgGenRecordInfo.UserImgUrl, 0)
	if checkFace, err = userSer.FaceImgCheck(ctx, imgGenRecordInfo.UserImgUrl); err != nil {
		return
	}
	if !checkFace {
		global.LOG.Info("FaceImgCheck 不是人脸照片", zap.Any("avatarFullUrl", avatarFullUrl))
		return
	}
	// 换脸
	imgPubPath, err = m.Img2ImgReplaceFaceV2(imgGenRecordInfo.UserImgUrl, imgTemplateRecordInfo.OriginalImage)
	uFullUrl := utils.S3Url(imgPubPath, 0)
	global.LOG.Info("imgGenRecordTrain success", zap.String("原图", avatarFullUrl), zap.String("结果", uFullUrl))
	// 修改img_gen_record记录
	imgGenRecordInfo.GenStatus = model.GenStatusSuccess
	imgGenRecordInfo.ResImgUrl = imgPubPath
	if err = imgGenRecordSer.Update(imgGenRecordInfo); err != nil {
		global.LOG.Error("Update error", zap.Any("err", err))
		return
	}
	return
}

func (m *ImgToImgTrainService) diyPhotoTaskTrain(ctx context.Context, imgReq *model.ImgToImgReq) (err error) {
	var (
		resImages        []string
		resFullImgs      []string
		faceImgKey       string
		diyPhotoTaskInfo *model.DiyPhotoTask
		diyPhotoTaskSer  DiyPhotoTaskService
		userInfo         *model.DigitalUser
		userSer          DigitalUserService
		aiRoleInfo       *model.AiRole
		aiRoleSer        AiRoleService
		fcmSer           FcmService
	)
	gLog := global.LOG.With(zap.String("func", "diyPhotoTaskTrain"), zap.String("business", "diy_photo_task"), zap.Uint("TaskId", imgReq.TaskId))
	gLog.Info("diyPhotoTaskTrain start")
	if diyPhotoTaskInfo, err = diyPhotoTaskSer.GetById(imgReq.TaskId); err != nil {
		return
	}
	gLog = gLog.With(zap.Any("diyPhotoTaskInfo", diyPhotoTaskInfo))
	// 获取AI角色信息
	if diyPhotoTaskInfo.AiRoleId != 0 {
		if aiRoleInfo, err = aiRoleSer.GetById(ctx, diyPhotoTaskInfo.AiRoleId); err != nil {
			gLog.Error("aiRoleSer.GetById error", zap.Any("err", err))
			return
		}
	}
	// 检查maskImgs是否为空
	if len(diyPhotoTaskInfo.MaskImgs) == 0 {
		gLog.Error("maskImgs is empty")
		err = fmt.Errorf("maskImgs is empty, diyPhotoTaskInfo.Id: %d", diyPhotoTaskInfo.Id)
		return
	}
	defer func() {
		if err != nil {
			// 钉钉通知
			sendDDMsg := fmt.Sprintf("私人订制任务失败,taskId: %d, error: %v", diyPhotoTaskInfo.Id, err)
			SendDDAtAll(sendDDMsg)
			diyPhotoTaskInfo.TaskState = model.DiyPhotoTaskStateFailed
			diyPhotoTaskInfo.ResultMsg = err.Error()
			if err = diyPhotoTaskSer.Update(ctx, diyPhotoTaskInfo); err != nil {
				gLog.Error("Update error", zap.Any("err", err))
			}
		}
	}()
	diyPhotoTaskInfo.TaskState = model.DiyPhotoTaskStateGenerating
	if err = diyPhotoTaskSer.Update(ctx, diyPhotoTaskInfo); err != nil {
		gLog.Error("Update error", zap.Any("err", err))
		return
	}
	faceImgKey = diyPhotoTaskInfo.FaceImg
	avatarFullUrl := utils.S3Url(faceImgKey, 0)
	gLog = gLog.With(zap.String("avatar", avatarFullUrl))
	for _, maskImgKey := range diyPhotoTaskInfo.MaskImgs {
		var (
			imgPubPath string
			faceErr    error
		)
		maskImgFullUrl := utils.S3Url(maskImgKey, 0)
		if aiRoleInfo != nil && aiRoleInfo.RoleType == model.AiRoleTypeAnime {
			imgPubPath = maskImgKey
		} else {
			if imgPubPath, faceErr = m.Img2ImgReplaceFaceV2(diyPhotoTaskInfo.FaceImg, maskImgKey); faceErr != nil {
				gLog.Error("Img2ImgReplaceFaceV2 error", zap.String("maskImgFullUrl", maskImgFullUrl), zap.Any("err", faceErr))
				sendDDMsg := fmt.Sprintf("私人订制任务失败,taskId: %d, error: %v", diyPhotoTaskInfo.Id, faceErr)
				SendDDNotice(sendDDMsg)
				imgPubPath = maskImgKey
			}
		}
		resImages = append(resImages, imgPubPath)
		uFullUrl := utils.S3Url(imgPubPath, 0)
		resFullImgs = append(resFullImgs, uFullUrl)
		global.LOG.Info("Img2ImgReplaceFaceV2 success", zap.String("func", "diyPhotoTaskTrain"), zap.String("business", "diy_photo_task"), zap.Uint("TaskId", imgReq.TaskId), zap.String("原图", avatarFullUrl), zap.String("结果图", uFullUrl))
	}

	// 修改diy_photo_task记录
	diyPhotoTaskInfo.TaskState = model.DiyPhotoTaskStateCompleted
	diyPhotoTaskInfo.CompleteTime = time.Now()
	diyPhotoTaskInfo.ResImgs = resImages
	if err = diyPhotoTaskSer.Update(ctx, diyPhotoTaskInfo); err != nil {
		gLog.Error("Update error", zap.Any("err", err))
		return
	}
	if userInfo, err = userSer.GetById(ctx, diyPhotoTaskInfo.DigitalUserId); err != nil {
		gLog.Error("GetById error", zap.Any("err", err))
		return
	}
	// 推送通知
	if userInfo.FcmToken != "" {
		if pushErr := fcmSer.PushDiySuccessMsg(ctx, diyPhotoTaskInfo); pushErr != nil {
			gLog.Error("PushDiySuccessMsg error", zap.Any("err", err))
		}
	}

	diyConfigItem, diyConfigItemExist := global.DiyConfigMap[diyPhotoTaskInfo.DiyConfigId]
	if !diyConfigItemExist {
		gLog.Error("diy_config not exist")
		err = fmt.Errorf("diy_config: %d not exist", diyPhotoTaskInfo.DiyConfigId)
		return
	}
	diyPhotoTaskSer.SendDDMsgTaskSuccess(diyPhotoTaskInfo, userInfo, diyConfigItem, diyPhotoTaskInfo.RemainMaskCount)
	if diyPhotoTaskInfo.Email != "" {
		_ = diyPhotoTaskSer.SendSuccessEmail(ctx, diyPhotoTaskInfo)
	}
	gLog.Info("success", zap.Any("resImgs", resFullImgs))
	return
}

func (m *ImgToImgTrainService) imgToVideoTaskTrain(ctx context.Context, taskReq *request.ImgToVideoRdsTask) (err error) {
	var (
		resObj         cliResponse.ImgToVideoRes
		apiResStr      string
		reqJson        string
		userImgFullUrl string
		mp3FullUrl     string
		apiResBytes    []byte
		apiResponse    *http.Response
		taskInfo       *model.ImgToVideo
		imgToVideoSer  ImgToVideoService
		genUrl         = "http://43.153.73.103:7861/generate/"
	)
	gLog := global.LOG.With(zap.String("func", "imgToVideoTaskTrain"), zap.String("business", "img_to_video"), zap.Uint("TaskId", taskReq.TaskId))
	gLog.Info("imgToVideoTaskTrain start")
	if taskInfo, err = imgToVideoSer.GetById(ctx, taskReq.TaskId); err != nil {
		return
	}
	gLog = gLog.With(zap.Any("taskInfo", taskInfo))
	defer func() {
		if err != nil {
			// 钉钉通知
			sendDDMsg := fmt.Sprintf("生成视频失败,taskId: %d, error: %v", taskInfo.Id, err)
			SendDDNotice(sendDDMsg)
			// 修改img_to_video记录
			taskInfo.GenStatus = model.GenStatusFail
			taskInfo.ResultMsg = err.Error()
			if err = imgToVideoSer.Update(ctx, taskInfo); err != nil {
				gLog.Error("Update error", zap.Any("err", err))
			}
		}
	}()
	userImgFullUrl = utils.S3Url(taskInfo.UserImgUrl, 0)
	gLog = gLog.With(zap.String("userImgFullUrl", userImgFullUrl))
	// 获取使用的mp3文件地址
	videoTxtItem, videoTxtItemExist := global.AllVideoTxtMap[taskInfo.TxtId]
	if !videoTxtItemExist {
		gLog.Error("video_txt not exist")
		err = fmt.Errorf("video_txt: %d not exist", taskInfo.TxtId)
		return
	}
	mp3FullUrl = utils.S3Url(videoTxtItem.FileKey, 0)
	reqJson = fmt.Sprintf(`{"image_link":"%s","audio_link":"%s"}`, userImgFullUrl, mp3FullUrl)
	gLog = gLog.With(zap.String("reqJson", reqJson))
	var httpReqClient = http.Client{
		Timeout: time.Minute * 20,
	}
	if apiResponse, err = httpReqClient.Post(genUrl, "application/json", strings.NewReader(reqJson)); err != nil {
		gLog.Error("Post error", zap.Any("err", err))
		err = fmt.Errorf("生成视频接口请求错误 error: %v", err.Error())
		return
	}
	defer apiResponse.Body.Close()
	gLog = gLog.With(zap.Int("StatusCode", apiResponse.StatusCode))
	if apiResponse.StatusCode != 200 {
		gLog.Error("StatusCode != 200", zap.Any("StatusCode", apiResponse.StatusCode))
		err = fmt.Errorf("生成视频接口请求错误 StatusCode != 200")
		return
	}
	if apiResBytes, err = io.ReadAll(apiResponse.Body); err != nil {
		gLog.Error("ReadAll error", zap.Any("err", err))
		err = fmt.Errorf("生成视频接口请求错误 ReadAll error: %v", err.Error())
		return
	}
	apiResStr = string(apiResBytes)
	gLog = gLog.With(zap.String("apiResStr", apiResStr))
	if err = json.Unmarshal(apiResBytes, &resObj); err != nil {
		gLog.Error("Unmarshal error", zap.Any("err", err))
		err = fmt.Errorf("生成视频接口请求错误 Unmarshal error: %v", err.Error())
		return
	}
	if resObj.Code != 200 {
		gLog.Error("resObj.Code != 200", zap.Any("resObj", resObj))
		err = fmt.Errorf("生成视频接口请求错误,错误信息:%s", resObj.Msg)
		return
	}
	taskInfo.GenStatus = model.GenStatusSuccess
	taskInfo.CompleteTime = time.Now()
	taskInfo.VideoUrl = resObj.Data
	if err = imgToVideoSer.Update(ctx, taskInfo); err != nil {
		gLog.Error("Update error", zap.Any("err", err))
		err = fmt.Errorf("更新img_to_video记录失败,Update error: %v", err.Error())
		return
	}
	gLog.Info("imgToVideoTaskTrain success")
	return
}

func (m *ImgToImgTrainService) Img2ImgReplaceFaceV2(avatarS3Url, maskS3Url string) (imgPubPath string, err error) {
	var (
		imgBytes   []byte
		apiLog     string
		curlStr    string
		img2imgRes response.Img2ImgTaskV2Res
		ctx        = context.Background()
		s3ClientV2 = GetS3ClientV2(ctx)
	)
	urls := []string{
		"https://u459706-a837-3a101a55.westx.seetacloud.com:8443/swap",
		"https://u459706-8961-4f1ad43d.westx.seetacloud.com:8443/swap",
	}

	// 初始化随机种子
	rand.Seed(time.Now().UnixNano())
	// 随机选择一个 URL
	imgReqUrl := urls[rand.Intn(len(urls))]
	img2imgReq := map[string]interface{}{
		"input_image":  fmt.Sprintf("%s/%s", global.CONFIG.AwsS3.BaseURL, maskS3Url),
		"source_image": fmt.Sprintf("%s%s", global.CONFIG.AwsS3.BaseURL, avatarS3Url),
	}
	startTime := time.Now()
	apiLog, curlStr, err = utils.HttpPostJson(imgReqUrl, img2imgReq, &img2imgRes, 120)
	reqDuration := time.Since(startTime)
	if err != nil {
		global.LOG.Error("Img2ImgReplaceFaceV2 error", zap.Any("err", err), zap.String("reqDuration", reqDuration.String()), zap.String("apiLog", apiLog), zap.String("curlStr", curlStr))
		return
	}
	global.LOG.Info("换脸耗时", zap.Any("time", reqDuration.Seconds()))
	if img2imgRes.Code != 0 {
		err = fmt.Errorf("img2imgRes.Code != 0, msg: %s", img2imgRes.Message)
		return
	}
	// base64 decode
	if imgBytes, err = base64.StdEncoding.DecodeString(img2imgRes.Data.Image); err != nil {
		global.LOG.Error("base64 decode error", zap.Any("err", err))
		return
	}
	uuidStr := uuid.NewString()
	uuidStr = strings.ReplaceAll(uuidStr, "-", "")
	imgFileName := fmt.Sprintf("%s.jpg", uuidStr)
	imgContentType := "image/jpeg"
	imgPubPath = fmt.Sprintf("public/img/img_replace_face/%s", imgFileName)
	// 上传到s3
	if err = s3ClientV2.UploadBytesToS3(imgBytes, imgPubPath, imgContentType); err != nil {
		global.LOG.Error("UploadBase64ToS3 error", zap.Any("err", err))
		return
	}
	return
}

func (m *ImgToImgTrainService) Img2ImgReplaceFaceRequireImg(avatarS3Url, maskS3Url string) (imgPubPath string, err error) {
	var (
		imgBytes   []byte
		apiLog     string
		curlStr    string
		img2imgRes response.Img2ImgTaskV2Res
		ctx        = context.Background()
		s3ClientV2 = GetS3ClientV2(ctx)
	)
	urls := []string{
		"https://u459706-a837-3a101a55.westx.seetacloud.com:8443/swap",
		"https://u459706-8961-4f1ad43d.westx.seetacloud.com:8443/swap",
	}
	// 初始化随机种子
	rand.Seed(time.Now().UnixNano())
	// 随机选择一个 URL
	imgReqUrl := urls[rand.Intn(len(urls))]
	img2imgReq := map[string]interface{}{
		"input_image":  fmt.Sprintf("%s/%s", global.CONFIG.AwsS3.BaseURL, maskS3Url),
		"source_image": fmt.Sprintf("%s%s", global.CONFIG.AwsS3.BaseURL, avatarS3Url),
	}
	startTime := time.Now()
	apiLog, curlStr, err = utils.HttpPostJson(imgReqUrl, img2imgReq, &img2imgRes, 120)
	reqDuration := time.Since(startTime)
	if err != nil {
		global.LOG.Error("Img2ImgReplaceFaceRequireImg error", zap.Any("err", err), zap.String("reqDuration", reqDuration.String()), zap.String("apiLog", apiLog), zap.String("curlStr", curlStr))
		return
	}
	if img2imgRes.Code != 0 {
		err = fmt.Errorf("img2imgRes.Code != 0, msg: %s", img2imgRes.Message)
		return
	}
	// base64 decode
	if imgBytes, err = base64.StdEncoding.DecodeString(img2imgRes.Data.Image); err != nil {
		global.LOG.Error("base64 decode error", zap.Any("err", err))
		return
	}
	uuidStr := uuid.NewString()
	uuidStr = strings.ReplaceAll(uuidStr, "-", "")
	imgFileName := fmt.Sprintf("%s.jpg", uuidStr)
	imgContentType := "image/jpeg"
	imgPubPath = fmt.Sprintf("public/img/img_replace_face/%s", imgFileName)
	// 上传到s3
	if err = s3ClientV2.UploadBytesToS3(imgBytes, imgPubPath, imgContentType); err != nil {
		global.LOG.Error("UploadBase64ToS3 error", zap.Any("err", err))
		return
	}
	return
}
