package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm/clause"
)

type FeedbackRecordService struct{}

func (m *FeedbackRecordService) Create(reqData *model.FeedbackRecord) (err error) {
	err = global.DB.Model(&model.FeedbackRecord{}).Create(reqData).Error
	return
}

func (m *FeedbackRecordService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.FeedbackRecord{}).Where("id = ?", id).Delete(&model.FeedbackRecord{}).Error
	return
}

func (m *FeedbackRecordService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.FeedbackRecord{}).Where("id IN (?)", reqData.Ids).Delete(&model.FeedbackRecord{}).Error
	return
}

func (m *FeedbackRecordService) Update(reqData *model.FeedbackRecord) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.FeedbackRecord{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	return
}

func (m *FeedbackRecordService) GetById(id uint) (res *model.FeedbackRecord, err error) {
	err = global.DB.Model(&model.FeedbackRecord{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *FeedbackRecordService) GetAll() (resList []*model.FeedbackRecord, err error) {
	err = global.DB.Model(&model.FeedbackRecord{}).Find(&resList).Error
	return
}

func (m *FeedbackRecordService) GetList(info req.FeedbackRecordSearch) (resList []*model.FeedbackRecord, total int64, err error) {
	db := global.DB.Model(&model.FeedbackRecord{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
