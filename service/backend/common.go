package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/backend/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/utils"
	"aimsg-server/utils/ilivedata"
	"aimsg-server/utils/notify"
	"aimsg-server/utils/upload"
	"context"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
	"math/rand"
	"sort"
	"time"
)

func GetS3Client() *upload.AwsS3 {
	return upload.NewAwsS3()
}

func GetS3ClientV2(ctx context.Context) *upload.AwsS3V2 {
	s3V2 := upload.NewAwsS3V2(ctx, global.CONFIG.AwsS3.SecretID, global.CONFIG.AwsS3.SecretKey, global.CONFIG.AwsS3.Bucket, global.CONFIG.AwsS3.Region)
	return s3V2
}

func GetILiveData() ilivedata.ILiveData {
	return ilivedata.ILiveData{
		ProjectID: global.ILiveDataProjectID,
		SecretKey: global.ILiveDataSecretKey,
	}
}

func SendDDNotice(msg string) {
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Error("SendDDNotice panic", zap.Any("err", err))
		}
	}()
	var (
		err     error
		resBody string
		data    = notify.Message{Msgtype: ""}
		token   = global.CONFIG.DDnotify.Token
		secret  = global.CONFIG.DDnotify.Secret
	)
	if token == "" || secret == "" {
		return
	}
	data.Markdown.Title = "通知"
	data.Msgtype = "markdown"
	data.Markdown.Text = msg
	resBody, err = notify.SendDingTalk(data, token, secret)
	if err != nil {
		global.LOG.Error("发送钉钉通知 error", zap.Any("err", err), zap.String("resBody", resBody))
		return
	}
}

func SendDDAtAll(msg string) {
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Error("SendDDNotice panic", zap.Any("err", err))
		}
	}()
	var (
		err     error
		resBody string
		data    = notify.Message{Msgtype: ""}
		token   = global.CONFIG.DDnotify.Token
		secret  = global.CONFIG.DDnotify.Secret
	)
	if token == "" || secret == "" {
		return
	}
	data.Markdown.Title = "通知"
	data.Msgtype = "markdown"
	data.Markdown.Text = msg
	data.At.IsAtAll = true
	resBody, err = notify.SendDingTalk(data, token, secret)
	if err != nil {
		global.LOG.Error("发送钉钉通知 error", zap.Any("err", err), zap.String("resBody", resBody))
		return
	}
}

func MsgNewStart() bool {
	return GetMsgNewStart() == "1"
}

func GetMsgNewStart() string {
	ctx := context.Background()
	return global.REDIS.Get(ctx, global.MSG_NEW_START_KEY).Val()
}

func SetMsgNewStart(startVal string) error {
	ctx := context.Background()
	return global.REDIS.Set(ctx, global.MSG_NEW_START_KEY, startVal, 0).Err()
}

func ShowMsgAds(ctx context.Context, userInfo *model.DigitalUser, userProfile *model.DigitalUserProfile, floConsume int) (showAds bool) {
	appInfo := global.AppMap[userInfo.AppID]
	now := time.Now()
	dayStr := now.Format(time.DateOnly)
	floDialogShowKey := fmt.Sprintf(global.FLO_DIALOG_SHOW_KEY, dayStr, userInfo.Id)
	msgAdsShowCountKey := fmt.Sprintf(global.MSG_ADS_SHOW_COUNT_KEY, dayStr, userInfo.Id)
	msgAdsPlanOne := appInfo.DetailConfig.Data().MsgAdsPlanOne
	days := utils.DaysBetween(userInfo.CreatedAt, now)
	// 获取方案1的配置信息
	msgAdsPlan := GetMsgAdsPlanOneConfig(ctx)
	// 需求: 用户首次登录 第 X 天起，在未付费VIP且玫瑰花不够扣的情况下，聊天页VIP弹窗降级为【LV1】，玫瑰花弹窗为【LV1】，用户关闭玫瑰花弹窗【LV1】 Y 次之后开始弹插屏广告
	if !userInfo.IsSub() && msgAdsPlanOne && userProfile.FlowerAmount < floConsume && days >= msgAdsPlan.Days {
		// 获取鲜花弹窗出现的次数
		floDialogShowCount, _ := global.REDIS.Get(ctx, floDialogShowKey).Int()
		// 判断弹窗显示次数是否大于等于配置的鲜花弹窗关闭次数
		if floDialogShowCount >= msgAdsPlan.FloClose {
			// 如果显示过了广告,就判断间隔次数. 因为使用的是取余算法,所以需要将步长+1
			step := msgAdsPlan.Step
			if step <= 0 {
				step = 1
			}
			msgAdsShowCount, _ := global.REDIS.Get(ctx, msgAdsShowCountKey).Int()
			if msgAdsShowCount > 0 {
				if (floDialogShowCount-msgAdsPlan.FloClose)%step == 0 {
					showAds = true
				}
			} else {
				showAds = true
			}
		}
	}
	if showAds {
		// 显示广告次数+1
		global.REDIS.Incr(ctx, msgAdsShowCountKey)
		global.REDIS.Expire(ctx, msgAdsShowCountKey, time.Hour*25)
	}
	return
}

func ShowMsgActivityAds(ctx context.Context, userInfo *model.DigitalUser, userProfile *model.DigitalUserProfile) (showAds bool) {
	appInfo := global.AppMap[userInfo.AppID]
	now := time.Now()
	dayStr := now.Format(time.DateOnly)
	msgAdsPlanTwo := appInfo.DetailConfig.Data().MsgAdsPlanTwo
	days := utils.DaysBetween(userInfo.CreatedAt, now)
	msgAdsShowCountKey := fmt.Sprintf(global.MSG_ADS_SHOW_COUNT_KEY, dayStr, userInfo.Id)
	// 获取方案1的配置信息
	msgAdsPlan := GetMsgAdsPlanTwoConfig(ctx)
	// 需求: 用户首次登录 第 A 天 起 未付费VIP且玫瑰花不够扣的的情况下，聊天页VIP弹窗降级为【LV1】，玫瑰花弹窗为【LV1】，用户进入聊天页先弹插屏广告
	if !userInfo.IsSub() && msgAdsPlanTwo && userProfile.FlowerAmount < msgAdsPlan.MinFlower && days >= msgAdsPlan.Days {
		showAds = true
	}
	if userInfo.IsChatVip() || userInfo.IsContentVip() {
		showAds = false
	}
	if showAds {
		// 显示广告次数+1
		global.REDIS.Incr(ctx, msgAdsShowCountKey)
		global.REDIS.Expire(ctx, msgAdsShowCountKey, time.Hour*25)
	}
	return
}

func ShowAppSplashAd(ctx context.Context, userInfo *model.DigitalUser, userProfile *model.DigitalUserProfile) (showAds bool) {
	now := time.Now()
	days := utils.DaysBetween(userInfo.CreatedAt, now)
	adCfg := GetSplashAdCfg(ctx, userInfo.AppID)
	// 注册第n天，没有订阅，开始弹出，步长为时间x秒
	if !userInfo.IsSub() && days >= adCfg.Days {
		showAds = true
	}
	if userInfo.IsChatVip() || userInfo.IsContentVip() {
		showAds = false
	}
	return
}

func GetMsgAdsPlanOneConfig(ctx context.Context) model.MsgPlanOneConfig {
	var (
		err           error
		redisConfig   model.MsgPlanOneConfig
		defaultConfig = model.MsgPlanOneConfig{Days: 1, FloClose: 3, FloReward: 0, Step: 2}
	)
	planConfigStr := global.REDIS.Get(ctx, global.MSG_ADS_PLAN_ONE_CONFIG_KEY).Val()
	if planConfigStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(planConfigStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func GetMsgAdsPlanTwoConfig(ctx context.Context) model.MsgPlanTwoConfig {
	var (
		err           error
		redisConfig   model.MsgPlanTwoConfig
		defaultConfig = model.MsgPlanTwoConfig{Days: 2, FloReward: 0}
	)
	planConfigStr := global.REDIS.Get(ctx, global.MSG_ADS_PLAN_TWO_CONFIG_KEY).Val()
	if planConfigStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(planConfigStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetMsgAdsPlanOneConfig(ctx context.Context, config model.MsgPlanOneConfig) (err error) {
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, global.MSG_ADS_PLAN_ONE_CONFIG_KEY, configStr, 0).Err()
	return
}

func SetMsgAdsPlanTwoConfig(ctx context.Context, config model.MsgPlanTwoConfig) (err error) {
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, global.MSG_ADS_PLAN_TWO_CONFIG_KEY, configStr, 0).Err()
	return
}

func GetMsgSendTimeOutConfig(ctx context.Context) model.MsgSendTimeOutConfig {
	var (
		err           error
		redisConfig   model.MsgSendTimeOutConfig
		defaultConfig = model.MsgSendTimeOutConfig{Status: true, Seconds: 30}
	)
	configStr := global.REDIS.Get(ctx, global.MSG_SEND_TIMEOUT_CONFIG).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetMsgSendTimeOutConfig(ctx context.Context, config model.MsgSendTimeOutConfig) (err error) {
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, global.MSG_SEND_TIMEOUT_CONFIG, configStr, 0).Err()
	return
}

func GetTextMaskMsgList(ctx context.Context) []string {
	var (
		defaultArr = global.TextMaskMsgList
	)
	configArr := global.REDIS.SMembers(ctx, global.TEXT_MASK_MSG_LIST_KEY).Val()
	if len(configArr) == 0 {
		return defaultArr
	}
	return configArr
}

func SetTextMaskMsgList(ctx context.Context, config []string) (err error) {
	err = global.REDIS.Del(ctx, global.TEXT_MASK_MSG_LIST_KEY).Err()
	if err != nil {
		return
	}
	if len(config) == 0 {
		return
	}
	err = global.REDIS.SAdd(ctx, global.TEXT_MASK_MSG_LIST_KEY, config).Err()
	return
}

func GetIPWhiteList(ctx context.Context) []string {
	var (
		defaultArr = []string{"***************", "*************"}
	)
	configArr := global.REDIS.SMembers(ctx, global.IP_WHITE_LIST).Val()
	if len(configArr) == 0 {
		return defaultArr
	}
	return configArr
}

func SetIPWhiteList(ctx context.Context, ips []string) (err error) {
	err = global.REDIS.Del(ctx, global.IP_WHITE_LIST).Err()
	if err != nil {
		return
	}
	if len(ips) == 0 {
		return
	}
	err = global.REDIS.SAdd(ctx, global.IP_WHITE_LIST, ips).Err()
	return
}

func GetFlowerDialogTipConfig(ctx context.Context) model.FlowerDialogTipConfig {
	var (
		err           error
		redisConfig   model.FlowerDialogTipConfig
		defaultConfig = model.FlowerDialogTipConfig{
			PlayVoice: "Unlock each voice message forever (VIP at 50% off）",
			PlayVideo: "Unlock each video (VIP at 60% off）",
			MaskMsg:   "Unlock each photo forever (Free For VIP）",
			SendMsg:   "Per-replay",
			Undress:   "Undress each photo (VIP at 60% off）",
			Asmr:      "listen each ASMR audio (VIP at 50% off）",
		}
	)
	configStr := global.REDIS.Get(ctx, global.FLOWER_DIALOG_TIP_CONFIG).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetFlowerDialogTipConfig(ctx context.Context, config model.FlowerDialogTipConfig) (err error) {
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, global.FLOWER_DIALOG_TIP_CONFIG, configStr, 0).Err()
	return
}

func GetReplyMsgRatioFree(ctx context.Context) model.ReplyMsgRatio {
	var (
		err           error
		redisConfig   model.ReplyMsgRatio
		defaultConfig = model.ReplyMsgRatio{
			TxtRatio:   []int{0, 20},
			ImgRatio:   []int{20, 50},
			VideoRatio: []int{50, 100},
		}
	)
	configStr := global.REDIS.Get(ctx, global.REPLY_MSG_RATIO_CONFIG_FREE).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetReplyMsgRatioFree(ctx context.Context, config model.ReplyMsgRatio) (err error) {
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, global.REPLY_MSG_RATIO_CONFIG_FREE, configStr, 0).Err()
	return
}

func GetReplyMsgRatioSub(ctx context.Context) model.ReplyMsgRatio {
	var (
		err           error
		redisConfig   model.ReplyMsgRatio
		defaultConfig = model.ReplyMsgRatio{
			TxtRatio:   []int{0, 20},
			ImgRatio:   []int{20, 50},
			VideoRatio: []int{50, 100},
		}
	)
	configStr := global.REDIS.Get(ctx, global.REPLY_MSG_RATIO_CONFIG_SUB).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetReplyMsgRatioSub(ctx context.Context, config model.ReplyMsgRatio) (err error) {
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, global.REPLY_MSG_RATIO_CONFIG_SUB, configStr, 0).Err()
	return
}

func GetSeeuRatioFree(ctx context.Context) model.ReplyMsgRatio {
	var (
		err           error
		redisConfig   model.ReplyMsgRatio
		defaultConfig = model.ReplyMsgRatio{
			ImgRatio:   []int{0, 60},
			VideoRatio: []int{60, 100},
		}
	)
	configStr := global.REDIS.Get(ctx, global.SEEU_RATIO_CONFIG_FREE).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetSeeuRatioFree(ctx context.Context, config model.ReplyMsgRatio) (err error) {
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, global.SEEU_RATIO_CONFIG_FREE, configStr, 0).Err()
	return
}

func GetSeeuRatioSub(ctx context.Context) model.ReplyMsgRatio {
	var (
		err           error
		redisConfig   model.ReplyMsgRatio
		defaultConfig = model.ReplyMsgRatio{
			ImgRatio:   []int{0, 60},
			VideoRatio: []int{60, 100},
		}
	)
	configStr := global.REDIS.Get(ctx, global.SEEU_RATIO_CONFIG_SUB).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetSeeuRatioSub(ctx context.Context, config model.ReplyMsgRatio) (err error) {
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, global.SEEU_RATIO_CONFIG_SUB, configStr, 0).Err()
	return
}

func GetVipDialogPlanConfig(ctx context.Context) model.VipDialogPlanConfig {
	var (
		err           error
		redisConfig   model.VipDialogPlanConfig
		defaultConfig = model.VipDialogPlanConfig{
			CloseWait:          3,
			PlanOneSwitch:      true,
			PlanOneRateRange:   []int{0, 33},
			PlanTwoSwitch:      true,
			PlanTwoRateRange:   []int{34, 66},
			PlanThreeSwitch:    true,
			PlanThreeRateRange: []int{67, 100},
		}
	)
	configStr := global.REDIS.Get(ctx, global.VIP_DIALOG_PLAN_CONFIG).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetVipDialogPlanConfig(ctx context.Context, config model.VipDialogPlanConfig) (err error) {
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, global.VIP_DIALOG_PLAN_CONFIG, configStr, 0).Err()
	return
}

func GetVipDialogPlanTwoTip(ctx context.Context, appId uint) response.VipDialogTip {
	var (
		err           error
		redisConfig   response.VipDialogTip
		defaultConfig = response.VipDialogTip{
			Title: "Enjoy The Best Chat Experience",
			Tips: []string{
				"Unlimited private chat",
				"Unlock NSFW filter",
				"Unlock All AI selfies,Roleplay scenes",
				"More discounts & No AD",
				"One-Time gift for you",
			},
		}
	)
	redisKey := fmt.Sprintf(global.VIP_DIALOG_TWO_TXT_CONFIG, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetVipDialogPlanTwoTip(ctx context.Context, appId uint, config response.VipDialogTip) (err error) {
	redisKey := fmt.Sprintf(global.VIP_DIALOG_TWO_TXT_CONFIG, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetVipDialogPlanTwoAuditTip(ctx context.Context, appId uint) response.VipDialogTip {
	var (
		err           error
		redisConfig   response.VipDialogTip
		defaultConfig = response.VipDialogTip{
			Title: "Enjoy The Best Chat Experience",
			Tips: []string{
				"Unlimited chat",
				"Get Closer to AI friend",
				"Remove AD",
			},
		}
	)
	redisKey := fmt.Sprintf(global.VIP_DIALOG_TWO_AUDIT_TXT_CONFIG, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetVipDialogPlanTwoAuditTip(ctx context.Context, appId uint, config response.VipDialogTip) (err error) {
	redisKey := fmt.Sprintf(global.VIP_DIALOG_TWO_AUDIT_TXT_CONFIG, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetSubLowCountry(ctx context.Context) map[string]bool {
	var (
		redisCountryCodes   []string
		defaultCountryCodes = []string{"IN", "BR", "PH"}
	)
	redisCountryCodes = global.REDIS.SMembers(ctx, global.SUB_LOW_COUNTRY_CONFIG).Val()
	if len(redisCountryCodes) == 0 {
		redisCountryCodes = defaultCountryCodes
	}
	res := make(map[string]bool)
	for _, code := range redisCountryCodes {
		res[code] = true
	}
	return res
}

func SetSubLowCountry(ctx context.Context, config []string) (err error) {
	err = global.REDIS.Del(ctx, global.SUB_LOW_COUNTRY_CONFIG).Err()
	if err != nil {
		return
	}
	if len(config) == 0 {
		return
	}
	err = global.REDIS.SAdd(ctx, global.SUB_LOW_COUNTRY_CONFIG, config).Err()
	return
}

func GetVipDialogDownConfig(ctx context.Context) model.VipDialogDownConfig {
	var (
		err           error
		redisConfig   model.VipDialogDownConfig
		defaultConfig = model.VipDialogDownConfig{
			PlanSwitch:     true,
			D0ShowCount:    4,
			AfterShowCount: 4,
		}
	)
	configStr := global.REDIS.Get(ctx, global.VIP_DIALOG_DOWN_CONFIG).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetVipDialogDownConfig(ctx context.Context, config model.VipDialogDownConfig) (err error) {
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, global.VIP_DIALOG_DOWN_CONFIG, configStr, 0).Err()
	return
}

func GetAppOtherConfig(ctx context.Context) model.AppOtherConfig {
	var (
		err           error
		redisConfig   model.AppOtherConfig
		defaultConfig = model.AppOtherConfig{
			StartShowSubDialog: true,
			IdleSecond:         10,
			WatchAdMsgCount:    5,
		}
	)
	configStr := global.REDIS.Get(ctx, global.APP_OTHER_CONFIG).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetAppOtherConfig(ctx context.Context, config model.AppOtherConfig) (err error) {
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, global.APP_OTHER_CONFIG, configStr, 0).Err()
	return
}

func GetSignInNoticeCfg(ctx context.Context) request.SignInNoticeCfg {
	var (
		err           error
		redisConfig   request.SignInNoticeCfg
		defaultConfig = request.SignInNoticeCfg{
			RegDay:      -3,
			NoActiveDay: -3,
		}
	)
	configStr := global.REDIS.Get(ctx, global.SignInNoticeCfg).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetSignInNoticeCfg(ctx context.Context, config request.SignInNoticeCfg) (err error) {
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, global.SignInNoticeCfg, configStr, 0).Err()
	return
}

func GetSubNoBuyFloCfg(ctx context.Context) request.SubNoBuyFloCfg {
	var (
		err           error
		redisConfig   request.SubNoBuyFloCfg
		defaultConfig = request.SubNoBuyFloCfg{
			Minute: 0,
			Flo:    0,
		}
	)
	configStr := global.REDIS.Get(ctx, global.SubNoBuyFloCfg).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetSubNoBuyFloCfg(ctx context.Context, config request.SubNoBuyFloCfg) (err error) {
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, global.SubNoBuyFloCfg, configStr, 0).Err()
	return
}

func getAiFuncSwitchCfgRedisKey(appId uint) string {
	return fmt.Sprintf(global.AiFuncSwitchCfg, appId)
}

func GetAiFuncSwitchCfg(ctx context.Context, appId uint) request.AiFuncSwitchCfg {
	var (
		err           error
		redisConfig   request.AiFuncSwitchCfg
		defaultConfig = request.AiFuncSwitchCfg{
			TouchMsgCount:    1,
			OrdersMsgCount:   5,
			RolePlayMsgCount: 10,
			NSFWShowMsgCount: 20,
			GiftMsgCount:     30,
			AsmrMsgCount:     30,
			DiyMsgCount:      100,
			PrivateCount:     100,
		}
	)
	redisKey := getAiFuncSwitchCfgRedisKey(appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func GetAllAiFuncSwitchCfg(ctx context.Context) (cfgMap map[uint]request.AiFuncSwitchCfg) {
	cfgMap = make(map[uint]request.AiFuncSwitchCfg)
	for appId, _ := range global.AppMap {
		cfgMap[appId] = GetAiFuncSwitchCfg(ctx, appId)
	}
	return
}

func SetAllAiFuncSwitchCfg(ctx context.Context, cfgMap map[uint]request.AiFuncSwitchCfg) (err error) {
	for appId, appCfg := range cfgMap {
		if err = SetAiFuncSwitchCfg(ctx, appCfg, appId); err != nil {
			return
		}
	}
	return
}

func SetAiFuncSwitchCfg(ctx context.Context, config request.AiFuncSwitchCfg, appId uint) (err error) {
	redisKey := getAiFuncSwitchCfgRedisKey(appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetDiyPhotoConfig(ctx context.Context, appId uint) request.DiyPhotoConfig {
	var (
		err           error
		redisConfig   request.DiyPhotoConfig
		defaultConfig = request.DiyPhotoConfig{}
	)
	redisKey := fmt.Sprintf(global.APP_DIY_PHOTO_CONFIG, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetDiyPhotoConfig(ctx context.Context, appId uint, config request.DiyPhotoConfig) (err error) {
	redisKey := fmt.Sprintf(global.APP_DIY_PHOTO_CONFIG, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetAppVoiceCallCfg(ctx context.Context, appId uint) response.AppVoiceCallCfg {
	var (
		err           error
		redisConfig   response.AppVoiceCallCfg
		defaultConfig = response.AppVoiceCallCfg{
			FirstMsgs: []string{
				"Hello! Has anything happy happened recently?",
				"Hi! How has your day been?",
				"Hello! Do you have any special hobbies or interests?",
				"Hi! Have you watched any good movies recently?",
				"Hello! Any good book recommendations recently?",
				"Hi! What's your favorite type of music?",
				"Hello! What's your favorite travel destination?",
				"Hi! Do you have any pets? What are their names?",
				"Hello! Anything interesting you want to share recently?",
				"Hi! What's your favorite season and why?",
				"Hello! What do you like to do to relax?",
				"Hi! Do you have a favorite dish?",
				"Hello! Have you learned any new skills recently?",
				"Hi! Do you have a dream job?",
				"Hello! What kind of sports do you like?",
				"Hi! Do you have any plans for the future?",
				"Hello! What's your favorite holiday?",
				"Hi! Which countries have you traveled to?",
				"Hello! What TV shows do you like to watch?",
				"Hi! What's your favorite animal?",
				"Hello! Want to share one of your most unforgettable secrets?",
				"Hi! Has anything made your heart race recently?",
				"Hello! What's your wildest dream?",
				"Hi! Do you have any daring plans?",
				"Hello! Do you have any irresistible hobbies?",
				"Hi! Would you share a deeply hidden wish with me?",
				"Hello! Has anything touched your heart recently?",
				"Hi! Have you ever done something surprising?",
				"Hello! Want to tell me about your greatest passion?",
				"Hi! Do you have any secret hobbies?",
				"Hello! What's your most desired adventure?",
				"Hi! Do you have any bold dreams?",
				"Hello! Do you have any unforgettable moments?",
				"Hi! Would you share a special memory with me?",
				"Hello! Do you have anything that excites you?",
				"Hi! What's your favorite secret date spot?",
				"Hello! Do you have any mysterious stories?",
				"Hi! Would you like to talk about your wildest dreams?",
				"Hello! Do you have any incredible experiences?",
				"Hi! What's your favorite romantic moment?",
			},
			IdleMsgs: []string{
				"Are you still there? Can you tell me more?",
				"Shall we continue talking?",
				"Do you have anything you'd like to share?",
				"It's nice to hear your voice.",
				"Please tell me your thoughts.",
				"I'm very interested in your story.",
				"Let's talk about your hobbies.",
				"I'd like to know more about you.",
				"Have you had any interesting experiences lately?",
				"We can talk about anything.",
				"Feel free to say anything.",
				"What's your favorite movie?",
				"Do you have any dreams?",
				"Let's talk about the music you like.",
				"What book have you read recently?",
				"Where is your hometown?",
				"Do you have any pets?",
				"Tell me about your day.",
				"What's your favorite travel destination?",
				"Do you have any special interests?",
			},
			FloNoEnoughMsgs: []string{
				"Our time is almost up. Can you continue?",
				"We're having a great chat. Would you like to continue?",
				"Would you like to extend our call time?",
				"We're running out of time. Do you want to continue?",
				"Do you have more to say? We can continue.",
				"I'm enjoying our conversation. Shall we continue?",
				"Our time is almost up. Shall we continue for a bit longer?",
				"Would you like to keep sharing? We can continue talking.",
				"Our time is almost up. Shall we keep talking?",
				"I like hearing your stories. Would you like to continue?",
				"You have many interesting things to share, right? Let's continue.",
				"We're having a great time. Want to chat a bit more?",
				"Time is running out. Would you like to extend the call?",
				"I'm enjoying our conversation. Would you like to continue?",
				"Our time is almost up. Shall we continue?",
				"Would you like to continue? We can talk about more interesting things.",
				"There are many topics we haven't finished. Shall we continue?",
				"I like hearing your thoughts. Want to chat a bit more?",
				"Time is running out. Shall we chat a bit more?",
				"Our conversation isn't over yet. Let's continue talking.",
			},
		}
	)
	redisKey := fmt.Sprintf(global.AppVoiceCallCfgKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	if len(redisConfig.FirstMsgs) == 0 {
		redisConfig.FirstMsgs = defaultConfig.FirstMsgs
	}
	if len(redisConfig.IdleMsgs) == 0 {
		redisConfig.IdleMsgs = defaultConfig.IdleMsgs
	}
	if len(redisConfig.FloNoEnoughMsgs) == 0 {
		redisConfig.FloNoEnoughMsgs = defaultConfig.FloNoEnoughMsgs
	}
	return redisConfig
}

func SetAppVoiceCallCfg(ctx context.Context, appId uint, config response.AppVoiceCallCfg) (err error) {
	redisKey := fmt.Sprintf(global.AppVoiceCallCfgKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetMsgReqMapCfg(ctx context.Context) response.MsgReqMapCfg {
	var (
		err           error
		redisConfig   response.MsgReqMapCfg
		defaultConfig = response.MsgReqMapCfg{
			MaxNewTokens:      60,
			Temperature:       0.7,
			TopP:              0.9,
			TopK:              20,
			RepetitionPenalty: 1.15,
		}
	)
	configStr := global.REDIS.Get(ctx, global.MsgReqMapConfigRdsKey).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetMsgReqMapConfig(ctx context.Context, config response.MsgReqMapCfg) (err error) {
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, global.MsgReqMapConfigRdsKey, configStr, 0).Err()
	return
}

func GetDiyPhotoLv2TacticsCfg(ctx context.Context, appId uint) response.DiyPhotoLv2TacticsCfg {
	var (
		err           error
		redisConfig   response.DiyPhotoLv2TacticsCfg
		defaultConfig = response.DiyPhotoLv2TacticsCfg{
			UserIsSub: true,
			RegDays:   2,
			DiyCount:  3,
			Nsfw:      true,
			ChatCount: 5,
		}
	)
	redisKey := fmt.Sprintf(global.DiyPhotoLv2TacticsCfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetDiyPhotoLv2TacticsCfg(ctx context.Context, appId uint, config response.DiyPhotoLv2TacticsCfg) (err error) {
	redisKey := fmt.Sprintf(global.DiyPhotoLv2TacticsCfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetFlowerShopSVipTextListCfg(ctx context.Context, appId uint) response.FlowerShopSVipTextListCfg {
	var (
		err           error
		redisConfig   response.FlowerShopSVipTextListCfg
		defaultConfig = response.FlowerShopSVipTextListCfg{
			Tips: []string{
				"Includes all Pro benefits",
				"Unlock all photo and video",
			},
		}
	)
	redisKey := fmt.Sprintf(global.FlowerShopSVipTextListCfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetFlowerShopSVipTextListCfg(ctx context.Context, appId uint, config response.FlowerShopSVipTextListCfg) (err error) {
	redisKey := fmt.Sprintf(global.FlowerShopSVipTextListCfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetFlowerShopSVipTextListAuditCfg(ctx context.Context, appId uint) response.FlowerShopSVipTextListCfg {
	var (
		err           error
		redisConfig   response.FlowerShopSVipTextListCfg
		defaultConfig = response.FlowerShopSVipTextListCfg{
			Tips: []string{
				"Includes all Pro benefits",
			},
		}
	)
	redisKey := fmt.Sprintf(global.FlowerShopSVipTextListAuditCfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetFlowerShopSVipTextListAuditCfg(ctx context.Context, appId uint, config response.FlowerShopSVipTextListCfg) (err error) {
	redisKey := fmt.Sprintf(global.FlowerShopSVipTextListAuditCfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetSVipDialogCfg(ctx context.Context, appId uint) response.SVipDialogCfg {
	var (
		err           error
		redisConfig   response.SVipDialogCfg
		defaultConfig = response.SVipDialogCfg{
			Title: "premier Benefits",
			TipList: []response.SVipDialogTipItem{
				{"Unlock NSFW filter", false},
				{"Unlock All AI selfies,Roleplay scenes", false},
				{"More discounts & No AD", false},
				{"Unlimitied private chat", false},
				{"50% discount for more spicy conten", false},
				{"Unlimited Photo and Video Access", true},
				{"One-Time gift for you 22000", true},
			},
		}
	)
	redisKey := fmt.Sprintf(global.SVipDialogCfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetSVipDialogCfg(ctx context.Context, appId uint, config response.SVipDialogCfg) (err error) {
	redisKey := fmt.Sprintf(global.SVipDialogCfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetSVipDialogAuditCfg(ctx context.Context, appId uint) response.SVipDialogCfg {
	var (
		err           error
		redisConfig   response.SVipDialogCfg
		defaultConfig = response.SVipDialogCfg{
			Title: "premier Benefits",
			TipList: []response.SVipDialogTipItem{
				{"Unlimited chat", false},
				{"Get Closer to AI friend", false},
				{"Remove AD", false},
				{"One-Time gift for you 22000", true},
			},
		}
	)
	redisKey := fmt.Sprintf(global.SVipDialogAuditCfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetSVipDialogAuditCfg(ctx context.Context, appId uint, config response.SVipDialogCfg) (err error) {
	redisKey := fmt.Sprintf(global.SVipDialogAuditCfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetSplashAdCfg(ctx context.Context, appId uint) response.SplashAdCfg {
	var (
		err           error
		redisConfig   response.SplashAdCfg
		defaultConfig = response.SplashAdCfg{
			Days:           2,
			StepSecond:     300,
			LowDialogCount: 5,
		}
	)
	redisKey := fmt.Sprintf(global.SplashAdCfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetSplashAdCfg(ctx context.Context, appId uint, config response.SplashAdCfg) (err error) {
	redisKey := fmt.Sprintf(global.SplashAdCfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetToPayCenterCountryGood(ctx context.Context, appId uint) response.CountryListCfg {
	var (
		err           error
		redisConfig   response.CountryListCfg
		defaultConfig = response.CountryListCfg{
			Countrys: []string{"US"},
		}
	)
	redisKey := fmt.Sprintf(global.ToPayCenterCountryGoodCfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetToPayCenterCountryGood(ctx context.Context, appId uint, config response.CountryListCfg) (err error) {
	redisKey := fmt.Sprintf(global.ToPayCenterCountryGoodCfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetToPayCenterCountrySub(ctx context.Context, appId uint) response.CountryListCfg {
	var (
		err           error
		redisConfig   response.CountryListCfg
		defaultConfig = response.CountryListCfg{
			Countrys: []string{"US"},
		}
	)
	redisKey := fmt.Sprintf(global.ToPayCenterCountrySubCfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		return defaultConfig
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		return defaultConfig
	}
	return redisConfig
}

func SetToPayCenterCountrySub(ctx context.Context, appId uint, config response.CountryListCfg) (err error) {
	redisKey := fmt.Sprintf(global.ToPayCenterCountrySubCfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetSVipDialogTip(ctx context.Context, appId uint) (rdsCfg response.SVipDialogTip) {
	var (
		err           error
		redisConfig   response.SVipDialogTip
		defaultConfig = response.SVipDialogTip{
			Title: "premier Benefits",
			TipWeek: []response.TipItem{
				{"", "Unlock all sexy features"},
				{"public/image/68995827-7b9f-4d5b-a17f-09c94b0bdff0.png", "Free View sexy pictures"},
				{"public/image/2f6f72b2-71e2-489c-bd8a-915865ef2e20.png", "Unlock private customization"},
				{"public/image/c6ae20b4-226c-41a4-995d-3aa6a6182988.png", "Unlock private space"},
				{"public/image/9dba4079-deec-49a2-8166-ca51121ac31f.png", "50% discount on all features"},
				{"public/image/ec597a11-8b09-4dfd-8c57-e107ca699f8f.png", "One-time gift of gold coins"},
			},
			TipMonth: []response.TipItem{
				{"", "Unlock all sexy features"},
				{"public/image/68995827-7b9f-4d5b-a17f-09c94b0bdff0.png", "Free View sexy pictures"},
				{"public/image/2f6f72b2-71e2-489c-bd8a-915865ef2e20.png", "Unlock private customization"},
				{"public/image/c6ae20b4-226c-41a4-995d-3aa6a6182988.png", "Unlock private space"},
				{"public/image/9dba4079-deec-49a2-8166-ca51121ac31f.png", "50% discount on all features"},
				{"public/image/ec597a11-8b09-4dfd-8c57-e107ca699f8f.png", "One-time gift of gold coins"},
			},
			TipYear: []response.TipItem{
				{"", "Unlock all sexy features"},
				{"public/image/68995827-7b9f-4d5b-a17f-09c94b0bdff0.png", "Free View sexy pictures"},
				{"public/image/2f6f72b2-71e2-489c-bd8a-915865ef2e20.png", "Unlock private customization"},
				{"public/image/c6ae20b4-226c-41a4-995d-3aa6a6182988.png", "Unlock private space"},
				{"public/image/9dba4079-deec-49a2-8166-ca51121ac31f.png", "50% discount on all features"},
				{"public/image/ec597a11-8b09-4dfd-8c57-e107ca699f8f.png", "One-time gift of gold coins"},
			},
		}
	)
	redisKey := fmt.Sprintf(global.SVipDialogTipCfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		rdsCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		rdsCfg = defaultConfig
		return
	}
	rdsCfg = redisConfig
	return
}

func SetSVipDialogTip(ctx context.Context, appId uint, config response.SVipDialogTip) (err error) {
	redisKey := fmt.Sprintf(global.SVipDialogTipCfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetSVipDialogTipAudit(ctx context.Context, appId uint) (rdsCfg response.SVipDialogTip) {
	var (
		err           error
		redisConfig   response.SVipDialogTip
		defaultConfig = response.SVipDialogTip{
			Title: "premier Benefits",
			TipWeek: []response.TipItem{
				{"", "Hot features"},
				{"", "Unlimited chat"},
				{"", "Get closer to AI friends"},
				{"", "Remove ads"},
				{"", "Give you a large amount of gold coins at one time"},
			},
			TipMonth: []response.TipItem{
				{"", "Hot features"},
				{"", "Unlimited chat"},
				{"", "Get closer to AI friends"},
				{"", "Remove ads"},
				{"", "Give you a large amount of gold coins at one time"},
			},
			TipYear: []response.TipItem{
				{"", "Hot features"},
				{"", "Unlimited chat"},
				{"", "Get closer to AI friends"},
				{"", "Remove ads"},
				{"", "Give you a large amount of gold coins at one time"},
			},
		}
	)
	redisKey := fmt.Sprintf(global.SVipDialogTipAuditCfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		rdsCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		rdsCfg = defaultConfig
		return
	}
	rdsCfg = redisConfig
	return
}

func SetSVipDialogTipAudit(ctx context.Context, appId uint, config response.SVipDialogTip) (err error) {
	redisKey := fmt.Sprintf(global.SVipDialogTipAuditCfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetSVipDialogV2Cfg(ctx context.Context, appId uint) (rdsCfg response.SVipDialogV2Cfg) {
	var (
		err           error
		redisConfig   response.SVipDialogV2Cfg
		defaultConfig = response.SVipDialogV2Cfg{
			BgIcon: "",
			WeekList: []response.DialogBannerCfgItem{
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "include all vip benefit", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private space", "do what you want do", model.PositionPrivateSpace, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private album customized", "private romantic conversatins", model.PositionDiyPhoto, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock all AI selfies for free", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Enjoy 90% discount", "Unlock more hot concent with best discount", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Subscription gift", "one-time gift 2000 cions for you", 0, ""},
			},
			MonthList: []response.DialogBannerCfgItem{
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "include all vip benefit", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private space", "do what you want do", model.PositionPrivateSpace, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private album customized", "private romantic conversatins", model.PositionDiyPhoto, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock all AI selfies for free", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Enjoy 90% discount", "Unlock more hot concent with best discount", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Subscription gift", "one-time gift 3000 cions for you", 0, ""},
			},
			YearList: []response.DialogBannerCfgItem{
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "include all vip benefit", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private space", "do what you want do", model.PositionPrivateSpace, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private album customized", "private romantic conversatins", model.PositionDiyPhoto, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock all AI selfies for free", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Enjoy 90% discount", "Unlock more hot concent with best discount", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Subscription gift", "one-time gift 5000 cions for you", 0, ""},
			},
		}
	)
	redisKey := fmt.Sprintf(global.SVipDialogV2CfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		rdsCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		rdsCfg = defaultConfig
		return
	}
	rdsCfg = redisConfig
	return
}

func SetSVipDialogV2Cfg(ctx context.Context, appId uint, config response.SVipDialogV2Cfg) (err error) {
	redisKey := fmt.Sprintf(global.SVipDialogV2CfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetSVipDialogV390Cfg(ctx context.Context, appId uint) (rdsCfg response.SVipDialogV2Cfg) {
	var (
		err           error
		redisConfig   response.SVipDialogV2Cfg
		defaultConfig = response.SVipDialogV2Cfg{
			BgIcon: "",
			WeekList: []response.DialogBannerCfgItem{
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "include all vip benefit", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private space", "do what you want do", model.PositionPrivateSpace, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private album customized", "private romantic conversatins", model.PositionDiyPhoto, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock all AI selfies for free", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Enjoy 90% discount", "Unlock more hot concent with best discount", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Subscription gift", "one-time gift 2000 cions for you", 0, ""},
			},
			MonthList: []response.DialogBannerCfgItem{
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "include all vip benefit", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private space", "do what you want do", model.PositionPrivateSpace, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private album customized", "private romantic conversatins", model.PositionDiyPhoto, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock all AI selfies for free", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Enjoy 90% discount", "Unlock more hot concent with best discount", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Subscription gift", "one-time gift 3000 cions for you", 0, ""},
			},
			YearList: []response.DialogBannerCfgItem{
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "include all vip benefit", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private space", "do what you want do", model.PositionPrivateSpace, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private album customized", "private romantic conversatins", model.PositionDiyPhoto, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock all AI selfies for free", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Enjoy 90% discount", "Unlock more hot concent with best discount", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Subscription gift", "one-time gift 5000 cions for you", 0, ""},
			},
		}
	)
	redisKey := fmt.Sprintf(global.SVipDialogV390CfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		rdsCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		rdsCfg = defaultConfig
		return
	}
	rdsCfg = redisConfig
	return
}

func SetSVipDialogV390Cfg(ctx context.Context, appId uint, config response.SVipDialogV2Cfg) (err error) {
	redisKey := fmt.Sprintf(global.SVipDialogV390CfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetVipDialogV2Cfg(ctx context.Context, appId uint) (rdsCfg response.VipDialogV2Cfg) {
	var (
		err           error
		redisConfig   response.VipDialogV2Cfg
		defaultConfig = response.VipDialogV2Cfg{
			BgIcon: "",
			WeekList: []response.DialogBannerCfgItem{
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock NSFW filter", "explore the secrets of the characters", model.PositionNSFWSwitch, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock ALL charcters", "All kind of hot characters to choose", model.PositionAiRoleNotEnough, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlimited chat", "private romantic conversatins", model.PositionMsgNotEnough, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Enjoy 50% discount", "Unlock more hot concent with better offers", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Subscription gift", "One-time gift of coins for you", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Remove AD", "Enjoy a pure experience", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlimited private customization", "Customize your own AI girlfriend ", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Premium models", "Smarter models and longer memories", 0, ""},
			},
			MonthList: []response.DialogBannerCfgItem{
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock NSFW filter", "explore the secrets of the characters", model.PositionNSFWSwitch, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock ALL charcters", "All kind of hot characters to choose", model.PositionAiRoleNotEnough, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlimited chat", "private romantic conversatins", model.PositionMsgNotEnough, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Enjoy 50% discount", "Unlock more hot concent with better offers", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Subscription gift", "one-time gift 150 cions for you", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Remove AD", "Enjoy a pure experience", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlimited private customization", "Customize your own AI girlfriend ", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Premium models", "Smarter models and longer memories", 0, ""},
			},
			YearList: []response.DialogBannerCfgItem{
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock NSFW filter", "explore the secrets of the characters", model.PositionNSFWSwitch, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock ALL charcters", "All kind of hot characters to choose", model.PositionAiRoleNotEnough, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlimited chat", "private romantic conversatins", model.PositionMsgNotEnough, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Enjoy 50% discount", "Unlock more hot concent with better offers", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Subscription gift", "one-time gift 300 cions for you", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Remove AD", "Enjoy a pure experience", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlimited private customization", "Customize your own AI girlfriend ", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Premium models", "Smarter models and longer memories", 0, ""},
			},
		}
	)
	redisKey := fmt.Sprintf(global.VipDialogV2CfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		rdsCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		rdsCfg = defaultConfig
		return
	}
	rdsCfg = redisConfig
	return
}

func SetVipDialogV2Cfg(ctx context.Context, appId uint, config response.VipDialogV2Cfg) (err error) {
	redisKey := fmt.Sprintf(global.VipDialogV2CfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetVipDialogV390Cfg(ctx context.Context, appId uint) (rdsCfg response.VipDialogV2Cfg) {
	var (
		err           error
		redisConfig   response.VipDialogV2Cfg
		defaultConfig = response.VipDialogV2Cfg{
			BgIcon: "",
			WeekList: []response.DialogBannerCfgItem{
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock NSFW filter", "explore the secrets of the characters", model.PositionNSFWSwitch, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock ALL charcters", "All kind of hot characters to choose", model.PositionAiRoleNotEnough, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlimited chat", "private romantic conversatins", model.PositionMsgNotEnough, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Enjoy 50% discount", "Unlock more hot concent with better offers", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Subscription gift", "One-time gift of coins for you", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Remove AD", "Enjoy a pure experience", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlimited private customization", "Customize your own AI girlfriend ", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Premium models", "Smarter models and longer memories", 0, ""},
			},
			MonthList: []response.DialogBannerCfgItem{
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock NSFW filter", "explore the secrets of the characters", model.PositionNSFWSwitch, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock ALL charcters", "All kind of hot characters to choose", model.PositionAiRoleNotEnough, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlimited chat", "private romantic conversatins", model.PositionMsgNotEnough, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Enjoy 50% discount", "Unlock more hot concent with better offers", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Subscription gift", "one-time gift 150 cions for you", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Remove AD", "Enjoy a pure experience", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlimited private customization", "Customize your own AI girlfriend ", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Premium models", "Smarter models and longer memories", 0, ""},
			},
			YearList: []response.DialogBannerCfgItem{
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock NSFW filter", "explore the secrets of the characters", model.PositionNSFWSwitch, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock ALL charcters", "All kind of hot characters to choose", model.PositionAiRoleNotEnough, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlimited chat", "private romantic conversatins", model.PositionMsgNotEnough, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Enjoy 50% discount", "Unlock more hot concent with better offers", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Subscription gift", "one-time gift 300 cions for you", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Remove AD", "Enjoy a pure experience", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlimited private customization", "Customize your own AI girlfriend ", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Premium models", "Smarter models and longer memories", 0, ""},
			},
		}
	)
	redisKey := fmt.Sprintf(global.VipDialogV390CfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		rdsCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		rdsCfg = defaultConfig
		return
	}
	rdsCfg = redisConfig
	return
}

func SetVipDialogV390Cfg(ctx context.Context, appId uint, config response.VipDialogV2Cfg) (err error) {
	redisKey := fmt.Sprintf(global.VipDialogV390CfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetIntimateLevelCfg(ctx context.Context, appId uint) (resCfg []response.IntimateLevelCfg) {
	var (
		err           error
		redisConfig   []response.IntimateLevelCfg
		defaultConfig = []response.IntimateLevelCfg{
			{10, 5111, 10000000},
			{9, 2551, 5111},
			{8, 1271, 2551},
			{7, 631, 1271},
			{6, 30, 631},
			{5, 22, 30},
			{4, 15, 22},
			{3, 8, 15},
			{2, 4, 8},
			{1, 2, 4},
			{0, 0, 2},
		}
	)
	redisKey := fmt.Sprintf(global.IntimateLevelAppIdCfg, appId)
	defer func() {
		if resCfg != nil && len(resCfg) > 0 {
			sort.Slice(resCfg, func(i, j int) bool {
				return resCfg[i].Level > resCfg[j].Level
			})
		}
	}()
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		resCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		resCfg = defaultConfig
		return
	}
	resCfg = redisConfig
	return
}

func SetIntimateLevelCfg(ctx context.Context, appId uint, config []response.IntimateLevelCfg) (err error) {
	redisKey := fmt.Sprintf(global.IntimateLevelAppIdCfg, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetIntimateUpgradeCfg(ctx context.Context) (resCfg map[model.IntimateSubType]response.IntimateUpgradeCfg) {
	var (
		err           error
		redisConfig   map[model.IntimateSubType]response.IntimateUpgradeCfg
		defaultConfig = map[model.IntimateSubType]response.IntimateUpgradeCfg{
			model.SubTypeFreeMsg:      {1, 2, 3, 2, 3},
			model.SubTypePaidMsg:      {1, 2, 3, 2, 3},
			model.SubTypeMaskMsg:      {2, 4, 6, 4, 6},
			model.SubTypeGenVoice:     {2, 4, 6, 4, 6},
			model.SubTypeUndress:      {2, 4, 6, 4, 6},
			model.SubTypeMaskPhoto:    {2, 4, 6, 4, 6},
			model.SubTypeAsmr:         {2, 4, 6, 4, 6},
			model.SubTypeGetVideo:     {2, 4, 6, 4, 6},
			model.SubTypeDiyPhoto:     {2, 4, 8, 4, 8},
			model.SubTypePrivateSpace: {4, 8, 12, 8, 12},
			model.SubTypeVoiceCall:    {2, 4, 6, 4, 6},
			model.SubTypeVideoCall:    {2, 4, 6, 4, 6},
		}
	)
	configStr := global.REDIS.Get(ctx, global.IntimateUpgradeCfg).Val()
	if configStr == "" {
		resCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		resCfg = defaultConfig
		return
	}
	resCfg = redisConfig
	return
}

func SetIntimateUpgradeCfg(ctx context.Context, resCfg map[model.IntimateSubType]response.IntimateUpgradeCfg) (err error) {
	configBytes, _ := json.Marshal(resCfg)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, global.IntimateUpgradeCfg, configStr, 0).Err()
	return
}

func GetIntimateDialogCfg(ctx context.Context, appId uint) (resCfg response.IntimateDialogCfg) {
	var (
		err           error
		redisConfig   response.IntimateDialogCfg
		defaultConfig = response.IntimateDialogCfg{
			Describe: "With each response from your Al Girlfriend, the level of intimacy between you two will increase, based on her current mood. Your Al Girlfriend will unlock more reply content as the intimacy level reaches certain milestones. When intimacy reaches its maximum, she may even fulfill some of your \"unreasonable\" requests. Therefore, using your charm to please your Al Girlfriend and unlock her heart is important!With each response from your Al Girlfriend, the level of intimacy between you two will increase, based on her current mood. Your Al Girlfriend will unlock more reply content as the intimacy level reaches certain milestones. When intimacy reaches its maximum, she may even fulfill some of your \"unreasonable\" requests. Therefore, using your charm to please your Al Girlfriend and unlock her heart is important!",
			LevelList: []response.IntimateLevelListItem{
				{
					Level:   1,
					Title:   "Encounter",
					Content: "Ready for my hot photos?",
					Icon:    "public/image/13daaeaa-9fc1-4949-a34e-23785682580a.png",
				},
				{
					Level:   2,
					Title:   "Connect",
					Content: "Ready for my hot videos?",
					Icon:    "public/image/13daaeaa-9fc1-4949-a34e-23785682580a.png",
				},
				{
					Level:   3,
					Title:   "Understand",
					Content: "Ready to do hot stuff with me?",
					Icon:    "public/image/13daaeaa-9fc1-4949-a34e-23785682580a.png",
				},
				{
					Level:   4,
					Title:   "Company",
					Content: "ready for my voice call and sexy AMSR？",
					Icon:    "public/image/13daaeaa-9fc1-4949-a34e-23785682580a.png",
				},
				{
					Level:   5,
					Title:   "Share",
					Content: "Undress and custom",
					Icon:    "public/image/13daaeaa-9fc1-4949-a34e-23785682580a.png",
				},
				{
					Level:   6,
					Title:   "Obsessed with you",
					Content: "Unlock my hot and private space",
					Icon:    "public/image/13daaeaa-9fc1-4949-a34e-23785682580a.png",
				},
				{
					Level:   7,
					Title:   "Obsessed with you",
					Content: "Unlock my hot and private space",
					Icon:    "public/image/13daaeaa-9fc1-4949-a34e-23785682580a.png",
				},
				{
					Level:   8,
					Title:   "Obsessed with you",
					Content: "Unlock my hot and private space",
					Icon:    "public/image/13daaeaa-9fc1-4949-a34e-23785682580a.png",
				},
				{
					Level:   9,
					Title:   "Obsessed with you",
					Content: "Unlock my hot and private space",
					Icon:    "public/image/13daaeaa-9fc1-4949-a34e-23785682580a.png",
				},
				{
					Level:   10,
					Title:   "Obsessed with you",
					Content: "Unlock my hot and private space",
					Icon:    "public/image/13daaeaa-9fc1-4949-a34e-23785682580a.png",
				},
			},
		}
	)
	redisKey := fmt.Sprintf(global.IntimateDialogAppIdCfg, appId)
	defer func() {
		if len(resCfg.LevelList) > 0 {
			var newListItem []response.IntimateLevelListItem
			for _, item := range resCfg.LevelList {
				if item.Icon != "" {
					item.Icon = utils.S3Url(item.Icon, 0)
				}
				newListItem = append(newListItem, item)
			}
			resCfg.LevelList = newListItem
		}
		if resCfg.LevelList != nil && len(resCfg.LevelList) > 0 {
			sort.Slice(resCfg.LevelList, func(i, j int) bool {
				return resCfg.LevelList[i].Level < resCfg.LevelList[j].Level
			})
		}
	}()
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		resCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		resCfg = defaultConfig
		return
	}
	resCfg = redisConfig
	return
}

func SetIntimateDialogCfg(ctx context.Context, appId uint, resCfg response.IntimateDialogCfg) (err error) {
	redisKey := fmt.Sprintf(global.IntimateDialogAppIdCfg, appId)
	configBytes, _ := json.Marshal(resCfg)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetIntimateUpgradeDialogCfg(ctx context.Context, appId uint) (resCfg map[uint]response.IntimateUpgradeDialogCfg) {
	var (
		err           error
		redisConfig   map[uint]response.IntimateUpgradeDialogCfg
		defaultConfig = map[uint]response.IntimateUpgradeDialogCfg{
			1:  {"Your intimacy reached the new level.", "Unlock hot photos rewards", "keep working to unlock more hot content!", "public/image/cba6b5b3-91a4-4dd7-b0b8-a405402a0cda.png", "public/image/f8a4368c-186d-4910-9a18-4915c45628f0.png", 1},
			2:  {"Your intimacy reached the new level.", "Unlock hot videos rewards", "keep working to unlock more hot content!", "public/image/cba6b5b3-91a4-4dd7-b0b8-a405402a0cda.png", "public/image/ce9de89d-e09b-49d3-a58c-95ae98989e40.png", 1},
			3:  {"Your intimacy reached the new level.", "{{ainame}} gives you a gift", "keep working to unlock more hot content!", "public/image/cba6b5b3-91a4-4dd7-b0b8-a405402a0cda.png", "public/image/ef05391d-ca7c-435f-9768-c6b248f409f1.png", 1},
			4:  {"Your intimacy reached the new level.", "You can have a voice call with {{ainame}} and enjoy hot ASMR", "", "public/image/cba6b5b3-91a4-4dd7-b0b8-a405402a0cda.png", "public/image/25f61da2-763c-41ab-8fcd-124917d3ca4e.png", 1},
			5:  {"Your intimacy reached the new level.", "Now you can customize and undress your {{ainame}}", "", "public/image/cba6b5b3-91a4-4dd7-b0b8-a405402a0cda.png", "public/image/57d02e3c-a758-4199-8ca5-18de13a2a68c.png", 2},
			6:  {"Your intimacy reached the new level.", "Explore {{ainame}}'s hottest secrets！！", "", "public/image/cba6b5b3-91a4-4dd7-b0b8-a405402a0cda.png", "public/image/8aeddeef-701d-4b12-98f0-bb02db736468.png", 2},
			7:  {"Your intimacy reached the new level.", "{{ainame}} love you even more", "", "public/image/cba6b5b3-91a4-4dd7-b0b8-a405402a0cda.png", "public/image/476554a9-8a21-4e18-9116-ec2990dff17a.png", 1},
			8:  {"Your intimacy reached the new level.", "{{ainame}} love you even more", "", "public/image/3649757f-1f27-48ff-bad4-5427e5fb09cf.png", "public/image/02b78784-2acf-41c8-9180-fa788b8c6c30.png", 1},
			9:  {"Your intimacy reached the new level.", "{{ainame}} love you even more", "", "public/image/3649757f-1f27-48ff-bad4-5427e5fb09cf.png", "public/image/36897821-3d92-4ff9-b73f-0f11c5d45b76.png", 1},
			10: {"Your intimacy reached the new level.", "{{ainame}} love you even more", "", "public/image/3649757f-1f27-48ff-bad4-5427e5fb09cf.png", "public/image/1f67d3aa-170b-421d-adbd-7ecd2ccbf3fd.png", 1},
		}
	)
	defer func() {
		if resCfg != nil {
			for u, cItem := range resCfg {
				if cItem.Icon != "" {
					cItem.Icon = utils.S3Url(cItem.Icon, 0)
				}
				if cItem.LevelIcon != "" {
					cItem.LevelIcon = utils.S3Url(cItem.LevelIcon, 0)
				}
				resCfg[u] = cItem
			}
		}
	}()
	redisKey := fmt.Sprintf(global.IntimateUpgradeDialogAppIdCfg, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		resCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		resCfg = defaultConfig
		return
	}
	resCfg = redisConfig
	return
}

func SetIntimateUpgradeDialogCfg(ctx context.Context, appId uint, resCfg map[uint]response.IntimateUpgradeDialogCfg) (err error) {
	redisKey := fmt.Sprintf(global.IntimateUpgradeDialogAppIdCfg, appId)
	configBytes, _ := json.Marshal(resCfg)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetIntimateUpgradeDialogAuditCfg(ctx context.Context, appId uint) (resCfg map[uint]response.IntimateUpgradeDialogCfg) {
	var (
		err           error
		redisConfig   map[uint]response.IntimateUpgradeDialogCfg
		defaultConfig = map[uint]response.IntimateUpgradeDialogCfg{
			1:  {"Your intimacy reached the new level.", "", "", "public/image/a012cd6b-94b1-45ab-9007-a8e8879df2a6.png", "public/image/f8a4368c-186d-4910-9a18-4915c45628f0.png", 1},
			2:  {"Your intimacy reached the new level.", "", "", "public/image/a012cd6b-94b1-45ab-9007-a8e8879df2a6.png", "public/image/ce9de89d-e09b-49d3-a58c-95ae98989e40.png", 1},
			3:  {"Your intimacy reached the new level.", "", "", "public/image/a012cd6b-94b1-45ab-9007-a8e8879df2a6.png", "public/image/ef05391d-ca7c-435f-9768-c6b248f409f1.png", 1},
			4:  {"Your intimacy reached the new level.", "", "", "public/image/a012cd6b-94b1-45ab-9007-a8e8879df2a6.png", "public/image/25f61da2-763c-41ab-8fcd-124917d3ca4e.png", 1},
			5:  {"Your intimacy reached the new level.", "", "", "public/image/a012cd6b-94b1-45ab-9007-a8e8879df2a6.png", "public/image/57d02e3c-a758-4199-8ca5-18de13a2a68c.png", 1},
			6:  {"Your intimacy reached the new level.", "", "", "public/image/a012cd6b-94b1-45ab-9007-a8e8879df2a6.png", "public/image/8aeddeef-701d-4b12-98f0-bb02db736468.png", 1},
			7:  {"Your intimacy reached the new level.", "", "", "public/image/a012cd6b-94b1-45ab-9007-a8e8879df2a6.png", "public/image/476554a9-8a21-4e18-9116-ec2990dff17a.png", 1},
			8:  {"Your intimacy reached the new level.", "", "", "public/image/a012cd6b-94b1-45ab-9007-a8e8879df2a6.png", "public/image/02b78784-2acf-41c8-9180-fa788b8c6c30.png", 1},
			9:  {"Your intimacy reached the new level.", "", "", "public/image/a012cd6b-94b1-45ab-9007-a8e8879df2a6.png", "public/image/36897821-3d92-4ff9-b73f-0f11c5d45b76.png", 1},
			10: {"Your intimacy reached the new level.", "", "", "public/image/a012cd6b-94b1-45ab-9007-a8e8879df2a6.png", "public/image/1f67d3aa-170b-421d-adbd-7ecd2ccbf3fd.png", 1},
		}
	)
	defer func() {
		if resCfg != nil {
			for u, cItem := range resCfg {
				if cItem.Icon != "" {
					cItem.Icon = utils.S3Url(cItem.Icon, 0)
				}
				resCfg[u] = cItem
			}
		}
	}()
	redisKey := fmt.Sprintf(global.IntimateUpgradeDialogAuditAppIdCfg, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		resCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		resCfg = defaultConfig
		return
	}
	resCfg = redisConfig
	return
}

func SetIntimateUpgradeDialogAuditCfg(ctx context.Context, appId uint, resCfg map[uint]response.IntimateUpgradeDialogCfg) (err error) {
	redisKey := fmt.Sprintf(global.IntimateUpgradeDialogAuditAppIdCfg, appId)
	configBytes, _ := json.Marshal(resCfg)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetDiyPhotoCountRangeCfg(ctx context.Context, appId uint) (rdsCfg response.DiyPhotoCountRangeCfg) {
	var (
		err           error
		redisConfig   response.DiyPhotoCountRangeCfg
		defaultConfig = response.DiyPhotoCountRangeCfg{
			CountRange: []int{20, 50, 100, 200},
		}
	)
	redisKey := fmt.Sprintf(global.DiyPhotoCountRangeCfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		rdsCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		rdsCfg = defaultConfig
		return
	}
	rdsCfg = redisConfig
	return
}

func SetDiyPhotoCountRangeCfg(ctx context.Context, appId uint, config response.DiyPhotoCountRangeCfg) (err error) {
	redisKey := fmt.Sprintf(global.DiyPhotoCountRangeCfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetAiSendUserGiftImgCfg(ctx context.Context, appId uint) (rdsCfg response.AiSendUserGiftImgCfg) {
	var (
		err           error
		redisConfig   response.AiSendUserGiftImgCfg
		defaultConfig = response.AiSendUserGiftImgCfg{
			GiftList: []response.AiSendUserGiftImgItem{
				{Icon: "public/image/6b0725d3-e925-42b8-8def-465ae6650d01.png"},
				{Icon: "public/image/d21d21ca-ee59-42f8-ae0a-8b50cc4c4cb6.png"},
				{Icon: "public/image/9591c39e-1c2d-43b4-aac6-ead877360317.png"},
				{Icon: "public/image/de4097b0-fb87-4bb7-8b6a-96909f0f2f2e.png"},
				{Icon: "public/image/1cfe152f-6fa0-4582-b097-3e474932fd1a.png"},
			},
		}
	)
	redisKey := fmt.Sprintf(global.AiSendUserGiftImgCfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		rdsCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		rdsCfg = defaultConfig
		return
	}
	rdsCfg = redisConfig
	return
}

func SetAiSendUserGiftImgCfg(ctx context.Context, appId uint, config response.AiSendUserGiftImgCfg) (err error) {
	redisKey := fmt.Sprintf(global.AiSendUserGiftImgCfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetAiSendUserGiftTxtCfg(ctx context.Context, appId uint) (rdsCfg response.AiSendUserGiftTxtCfg) {
	var (
		err           error
		redisConfig   response.AiSendUserGiftTxtCfg
		defaultConfig = response.AiSendUserGiftTxtCfg{
			TxtList: []response.AiSendUserGiftTxtItem{
				{Content: "This is the gift I gave you, and I also want to receive it from you. Can you please fulfill my request"},
			},
		}
	)
	redisKey := fmt.Sprintf(global.AiSendUserGiftTxtCfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		rdsCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		rdsCfg = defaultConfig
		return
	}
	rdsCfg = redisConfig
	return
}

func SetAiSendUserGiftTxtCfg(ctx context.Context, appId uint, config response.AiSendUserGiftTxtCfg) (err error) {
	redisKey := fmt.Sprintf(global.AiSendUserGiftTxtCfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetMsgModelStrageCfg(ctx context.Context) (resCfg response.MsgModelStrageCfg) {
	var (
		err           error
		redisConfig   response.MsgModelStrageCfg
		defaultConfig = response.MsgModelStrageCfg{
			Open: false,
		}
	)
	configStr := global.REDIS.Get(ctx, global.MsgModelStrageCfg).Val()
	if configStr == "" {
		resCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		resCfg = defaultConfig
		return
	}
	resCfg = redisConfig
	return
}

func SetMsgModelStrageCfg(ctx context.Context, resCfg response.MsgModelStrageCfg) (err error) {
	configBytes, _ := json.Marshal(resCfg)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, global.MsgModelStrageCfg, configStr, 0).Err()
	return
}

func GetReSubRewardCoinCfg(ctx context.Context, appId uint) (rdsCfg response.ReSubRewardCoinCfg) {
	var (
		err           error
		redisConfig   response.ReSubRewardCoinCfg
		defaultConfig = response.ReSubRewardCoinCfg{
			WeekVip:  100,
			MonthVip: 300,
			YearVip:  600,
		}
	)
	redisKey := fmt.Sprintf(global.ReSubRewardCoinCfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		rdsCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		rdsCfg = defaultConfig
		return
	}
	rdsCfg = redisConfig
	return
}

func SetReSubRewardCoinCfg(ctx context.Context, appId uint, config response.ReSubRewardCoinCfg) (err error) {
	redisKey := fmt.Sprintf(global.ReSubRewardCoinCfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetChatVipDialogTip(ctx context.Context, appId uint) (rdsCfg response.ChatVipDialogTip) {
	var (
		err           error
		redisConfig   response.ChatVipDialogTip
		defaultConfig = response.ChatVipDialogTip{
			Title: "premier Benefits",
			TipWeek: []response.TipItem{
				{"", "Unlock all sexy features"},
				{"public/image/68995827-7b9f-4d5b-a17f-09c94b0bdff0.png", "Free View sexy pictures"},
				{"public/image/2f6f72b2-71e2-489c-bd8a-915865ef2e20.png", "Unlock private customization"},
				{"public/image/c6ae20b4-226c-41a4-995d-3aa6a6182988.png", "Unlock private space"},
				{"public/image/9dba4079-deec-49a2-8166-ca51121ac31f.png", "50% discount on all features"},
				{"public/image/ec597a11-8b09-4dfd-8c57-e107ca699f8f.png", "One-time gift of gold coins"},
			},
			TipMonth: []response.TipItem{
				{"", "Unlock all sexy features"},
				{"public/image/68995827-7b9f-4d5b-a17f-09c94b0bdff0.png", "Free View sexy pictures"},
				{"public/image/2f6f72b2-71e2-489c-bd8a-915865ef2e20.png", "Unlock private customization"},
				{"public/image/c6ae20b4-226c-41a4-995d-3aa6a6182988.png", "Unlock private space"},
				{"public/image/9dba4079-deec-49a2-8166-ca51121ac31f.png", "50% discount on all features"},
				{"public/image/ec597a11-8b09-4dfd-8c57-e107ca699f8f.png", "One-time gift of gold coins"},
			},
			TipYear: []response.TipItem{
				{"", "Unlock all sexy features"},
				{"public/image/68995827-7b9f-4d5b-a17f-09c94b0bdff0.png", "Free View sexy pictures"},
				{"public/image/2f6f72b2-71e2-489c-bd8a-915865ef2e20.png", "Unlock private customization"},
				{"public/image/c6ae20b4-226c-41a4-995d-3aa6a6182988.png", "Unlock private space"},
				{"public/image/9dba4079-deec-49a2-8166-ca51121ac31f.png", "50% discount on all features"},
				{"public/image/ec597a11-8b09-4dfd-8c57-e107ca699f8f.png", "One-time gift of gold coins"},
			},
		}
	)
	defer func() {
		if len(rdsCfg.TipWeek) > 0 {
			var newTipWeek []response.TipItem
			for _, item := range rdsCfg.TipWeek {
				if item.Icon != "" {
					item.Icon = utils.S3Url(item.Icon, appId)
				}
				newTipWeek = append(newTipWeek, item)
			}
			rdsCfg.TipWeek = newTipWeek
		}
		if len(rdsCfg.TipMonth) > 0 {
			var newTipMonth []response.TipItem
			for _, item := range rdsCfg.TipMonth {
				if item.Icon != "" {
					item.Icon = utils.S3Url(item.Icon, appId)
				}
				newTipMonth = append(newTipMonth, item)
			}
			rdsCfg.TipMonth = newTipMonth
		}
		if len(rdsCfg.TipYear) > 0 {
			var newTipYear []response.TipItem
			for _, item := range rdsCfg.TipYear {
				if item.Icon != "" {
					item.Icon = utils.S3Url(item.Icon, appId)
				}
				newTipYear = append(newTipYear, item)
			}
			rdsCfg.TipYear = newTipYear
		}
	}()
	redisKey := fmt.Sprintf(global.ChatVipDialogTipCfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		rdsCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		rdsCfg = defaultConfig
		return
	}
	rdsCfg = redisConfig
	return
}

func SetChatVipDialogTip(ctx context.Context, appId uint, config response.ChatVipDialogTip) (err error) {
	redisKey := fmt.Sprintf(global.ChatVipDialogTipCfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetChatVipDialogTipAudit(ctx context.Context, appId uint) (rdsCfg response.ChatVipDialogTip) {
	var (
		err           error
		redisConfig   response.ChatVipDialogTip
		defaultConfig = response.ChatVipDialogTip{
			Title: "premier Benefits",
			TipWeek: []response.TipItem{
				{"", "Hot features"},
				{"", "Unlimited chat"},
				{"", "Get closer to AI friends"},
				{"", "Remove ads"},
				{"", "Give you a large amount of gold coins at one time"},
			},
			TipMonth: []response.TipItem{
				{"", "Hot features"},
				{"", "Unlimited chat"},
				{"", "Get closer to AI friends"},
				{"", "Remove ads"},
				{"", "Give you a large amount of gold coins at one time"},
			},
			TipYear: []response.TipItem{
				{"", "Hot features"},
				{"", "Unlimited chat"},
				{"", "Get closer to AI friends"},
				{"", "Remove ads"},
				{"", "Give you a large amount of gold coins at one time"},
			},
		}
	)
	defer func() {
		if len(rdsCfg.TipWeek) > 0 {
			var newTipWeek []response.TipItem
			for _, item := range rdsCfg.TipWeek {
				if item.Icon != "" {
					item.Icon = utils.S3Url(item.Icon, appId)
				}
				newTipWeek = append(newTipWeek, item)
			}
			rdsCfg.TipWeek = newTipWeek
		}
		if len(rdsCfg.TipMonth) > 0 {
			var newTipMonth []response.TipItem
			for _, item := range rdsCfg.TipMonth {
				if item.Icon != "" {
					item.Icon = utils.S3Url(item.Icon, appId)
				}
				newTipMonth = append(newTipMonth, item)
			}
			rdsCfg.TipMonth = newTipMonth
		}
		if len(rdsCfg.TipYear) > 0 {
			var newTipYear []response.TipItem
			for _, item := range rdsCfg.TipYear {
				if item.Icon != "" {
					item.Icon = utils.S3Url(item.Icon, appId)
				}
				newTipYear = append(newTipYear, item)
			}
			rdsCfg.TipYear = newTipYear
		}
	}()
	redisKey := fmt.Sprintf(global.ChatVipDialogTipAuditCfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		rdsCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		rdsCfg = defaultConfig
		return
	}
	rdsCfg = redisConfig
	return
}

func SetChatVipDialogTipAudit(ctx context.Context, appId uint, config response.ChatVipDialogTip) (err error) {
	redisKey := fmt.Sprintf(global.ChatVipDialogTipAuditCfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetChatVipDialogV2Cfg(ctx context.Context, appId uint) (rdsCfg response.ChatVipDialogV2Cfg) {
	var (
		err           error
		redisConfig   response.ChatVipDialogV2Cfg
		defaultConfig = response.ChatVipDialogV2Cfg{
			WeekList: []response.DialogBannerCfgItem{
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "include all vip benefit", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private space", "do what you want do", model.PositionPrivateSpace, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private album customized", "private romantic conversatins", model.PositionDiyPhoto, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock all AI selfies for free", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Enjoy 90% discount", "Unlock more hot concent with best discount", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Subscription gift", "one-time gift 2000 cions for you", 0, ""},
			},
			MonthList: []response.DialogBannerCfgItem{
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "include all vip benefit", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private space", "do what you want do", model.PositionPrivateSpace, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private album customized", "private romantic conversatins", model.PositionDiyPhoto, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock all AI selfies for free", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Enjoy 90% discount", "Unlock more hot concent with best discount", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Subscription gift", "one-time gift 3000 cions for you", 0, ""},
			},
			YearList: []response.DialogBannerCfgItem{
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "include all vip benefit", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private space", "do what you want do", model.PositionPrivateSpace, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private album customized", "private romantic conversatins", model.PositionDiyPhoto, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock all AI selfies for free", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Enjoy 90% discount", "Unlock more hot concent with best discount", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Subscription gift", "one-time gift 5000 cions for you", 0, ""},
			},
		}
	)
	defer func() {
		if len(rdsCfg.WeekList) > 0 {
			var newListItem []response.DialogBannerCfgItem
			for _, item := range rdsCfg.WeekList {
				if item.Img != "" {
					item.Img = utils.S3Url(item.Img, appId)
				}
				if item.Icon != "" {
					item.Icon = utils.S3Url(item.Icon, appId)
				}
				newListItem = append(newListItem, item)
			}
			rdsCfg.WeekList = newListItem
		}
		if len(rdsCfg.MonthList) > 0 {
			var newListItem []response.DialogBannerCfgItem
			for _, item := range rdsCfg.MonthList {
				if item.Img != "" {
					item.Img = utils.S3Url(item.Img, appId)
				}
				if item.Icon != "" {
					item.Icon = utils.S3Url(item.Icon, appId)
				}
				newListItem = append(newListItem, item)
			}
			rdsCfg.MonthList = newListItem
		}
		if len(rdsCfg.YearList) > 0 {
			var newListItem []response.DialogBannerCfgItem
			for _, item := range rdsCfg.YearList {
				if item.Img != "" {
					item.Img = utils.S3Url(item.Img, appId)
				}
				if item.Icon != "" {
					item.Icon = utils.S3Url(item.Icon, appId)
				}
				newListItem = append(newListItem, item)
			}
			rdsCfg.YearList = newListItem
		}
	}()
	redisKey := fmt.Sprintf(global.ChatVipDialogV2CfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		rdsCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		rdsCfg = defaultConfig
		return
	}
	rdsCfg = redisConfig
	return
}

func SetChatVipDialogV2Cfg(ctx context.Context, appId uint, config response.ChatVipDialogV2Cfg) (err error) {
	redisKey := fmt.Sprintf(global.ChatVipDialogV2CfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetContentVipDialogTip(ctx context.Context, appId uint) (rdsCfg response.ContentVipDialogTip) {
	var (
		err           error
		redisConfig   response.ContentVipDialogTip
		defaultConfig = response.ContentVipDialogTip{
			Title: "premier Benefits",
			TipWeek: []response.TipItem{
				{"", "Unlock all sexy features"},
				{"public/image/68995827-7b9f-4d5b-a17f-09c94b0bdff0.png", "Free View sexy pictures"},
				{"public/image/2f6f72b2-71e2-489c-bd8a-915865ef2e20.png", "Unlock private customization"},
				{"public/image/c6ae20b4-226c-41a4-995d-3aa6a6182988.png", "Unlock private space"},
				{"public/image/9dba4079-deec-49a2-8166-ca51121ac31f.png", "50% discount on all features"},
				{"public/image/ec597a11-8b09-4dfd-8c57-e107ca699f8f.png", "One-time gift of gold coins"},
			},
			TipMonth: []response.TipItem{
				{"", "Unlock all sexy features"},
				{"public/image/68995827-7b9f-4d5b-a17f-09c94b0bdff0.png", "Free View sexy pictures"},
				{"public/image/2f6f72b2-71e2-489c-bd8a-915865ef2e20.png", "Unlock private customization"},
				{"public/image/c6ae20b4-226c-41a4-995d-3aa6a6182988.png", "Unlock private space"},
				{"public/image/9dba4079-deec-49a2-8166-ca51121ac31f.png", "50% discount on all features"},
				{"public/image/ec597a11-8b09-4dfd-8c57-e107ca699f8f.png", "One-time gift of gold coins"},
			},
			TipYear: []response.TipItem{
				{"", "Unlock all sexy features"},
				{"public/image/68995827-7b9f-4d5b-a17f-09c94b0bdff0.png", "Free View sexy pictures"},
				{"public/image/2f6f72b2-71e2-489c-bd8a-915865ef2e20.png", "Unlock private customization"},
				{"public/image/c6ae20b4-226c-41a4-995d-3aa6a6182988.png", "Unlock private space"},
				{"public/image/9dba4079-deec-49a2-8166-ca51121ac31f.png", "50% discount on all features"},
				{"public/image/ec597a11-8b09-4dfd-8c57-e107ca699f8f.png", "One-time gift of gold coins"},
			},
		}
	)
	defer func() {
		if len(rdsCfg.TipWeek) > 0 {
			var newTipWeek []response.TipItem
			for _, item := range rdsCfg.TipWeek {
				if item.Icon != "" {
					item.Icon = utils.S3Url(item.Icon, appId)
				}
				newTipWeek = append(newTipWeek, item)
			}
			rdsCfg.TipWeek = newTipWeek
		}
		if len(rdsCfg.TipMonth) > 0 {
			var newTipMonth []response.TipItem
			for _, item := range rdsCfg.TipMonth {
				if item.Icon != "" {
					item.Icon = utils.S3Url(item.Icon, appId)
				}
				newTipMonth = append(newTipMonth, item)
			}
			rdsCfg.TipMonth = newTipMonth
		}
		if len(rdsCfg.TipYear) > 0 {
			var newTipYear []response.TipItem
			for _, item := range rdsCfg.TipYear {
				if item.Icon != "" {
					item.Icon = utils.S3Url(item.Icon, appId)
				}
				newTipYear = append(newTipYear, item)
			}
			rdsCfg.TipYear = newTipYear
		}
	}()
	redisKey := fmt.Sprintf(global.ContentVipDialogTipCfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		rdsCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		rdsCfg = defaultConfig
		return
	}
	rdsCfg = redisConfig
	return
}

func SetContentVipDialogTip(ctx context.Context, appId uint, config response.ContentVipDialogTip) (err error) {
	redisKey := fmt.Sprintf(global.ContentVipDialogTipCfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetContentVipDialogTipAudit(ctx context.Context, appId uint) (rdsCfg response.ContentVipDialogTip) {
	var (
		err           error
		redisConfig   response.ContentVipDialogTip
		defaultConfig = response.ContentVipDialogTip{
			Title: "premier Benefits",
			TipWeek: []response.TipItem{
				{"", "Hot features"},
				{"", "Unlimited chat"},
				{"", "Get closer to AI friends"},
				{"", "Remove ads"},
				{"", "Give you a large amount of gold coins at one time"},
			},
			TipMonth: []response.TipItem{
				{"", "Hot features"},
				{"", "Unlimited chat"},
				{"", "Get closer to AI friends"},
				{"", "Remove ads"},
				{"", "Give you a large amount of gold coins at one time"},
			},
			TipYear: []response.TipItem{
				{"", "Hot features"},
				{"", "Unlimited chat"},
				{"", "Get closer to AI friends"},
				{"", "Remove ads"},
				{"", "Give you a large amount of gold coins at one time"},
			},
		}
	)
	defer func() {
		if len(rdsCfg.TipWeek) > 0 {
			var newTipWeek []response.TipItem
			for _, item := range rdsCfg.TipWeek {
				if item.Icon != "" {
					item.Icon = utils.S3Url(item.Icon, appId)
				}
				newTipWeek = append(newTipWeek, item)
			}
			rdsCfg.TipWeek = newTipWeek
		}
		if len(rdsCfg.TipMonth) > 0 {
			var newTipMonth []response.TipItem
			for _, item := range rdsCfg.TipMonth {
				if item.Icon != "" {
					item.Icon = utils.S3Url(item.Icon, appId)
				}
				newTipMonth = append(newTipMonth, item)
			}
			rdsCfg.TipMonth = newTipMonth
		}
		if len(rdsCfg.TipYear) > 0 {
			var newTipYear []response.TipItem
			for _, item := range rdsCfg.TipYear {
				if item.Icon != "" {
					item.Icon = utils.S3Url(item.Icon, appId)
				}
				newTipYear = append(newTipYear, item)
			}
			rdsCfg.TipYear = newTipYear
		}
	}()
	redisKey := fmt.Sprintf(global.ContentVipDialogTipAuditCfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		rdsCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		rdsCfg = defaultConfig
		return
	}
	rdsCfg = redisConfig
	return
}

func SetContentVipDialogTipAudit(ctx context.Context, appId uint, config response.ContentVipDialogTip) (err error) {
	redisKey := fmt.Sprintf(global.ContentVipDialogTipAuditCfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetContentVipDialogV2Cfg(ctx context.Context, appId uint) (rdsCfg response.ContentVipDialogV2Cfg) {
	var (
		err           error
		redisConfig   response.ContentVipDialogV2Cfg
		defaultConfig = response.ContentVipDialogV2Cfg{
			WeekList: []response.DialogBannerCfgItem{
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "include all vip benefit", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private space", "do what you want do", model.PositionPrivateSpace, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private album customized", "private romantic conversatins", model.PositionDiyPhoto, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock all AI selfies for free", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Enjoy 90% discount", "Unlock more hot concent with best discount", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Subscription gift", "one-time gift 2000 cions for you", 0, ""},
			},
			MonthList: []response.DialogBannerCfgItem{
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "include all vip benefit", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private space", "do what you want do", model.PositionPrivateSpace, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private album customized", "private romantic conversatins", model.PositionDiyPhoto, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock all AI selfies for free", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Enjoy 90% discount", "Unlock more hot concent with best discount", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Subscription gift", "one-time gift 3000 cions for you", 0, ""},
			},
			YearList: []response.DialogBannerCfgItem{
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "include all vip benefit", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private space", "do what you want do", model.PositionPrivateSpace, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock private album customized", "private romantic conversatins", model.PositionDiyPhoto, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Unlock all AI selfies for free", "", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Enjoy 90% discount", "Unlock more hot concent with best discount", 0, ""},
				{"public/image/87f7e239-7622-4a4d-b89d-638dead02e40.png", "public/image/c786dab3-0bfa-4926-9700-775c6858fa1f.png", "Subscription gift", "one-time gift 5000 cions for you", 0, ""},
			},
		}
	)
	defer func() {
		if len(rdsCfg.WeekList) > 0 {
			var newListItem []response.DialogBannerCfgItem
			for _, item := range rdsCfg.WeekList {
				if item.Img != "" {
					item.Img = utils.S3Url(item.Img, appId)
				}
				if item.Icon != "" {
					item.Icon = utils.S3Url(item.Icon, appId)
				}
				newListItem = append(newListItem, item)
			}
			rdsCfg.WeekList = newListItem
		}
		if len(rdsCfg.MonthList) > 0 {
			var newListItem []response.DialogBannerCfgItem
			for _, item := range rdsCfg.MonthList {
				if item.Img != "" {
					item.Img = utils.S3Url(item.Img, appId)
				}
				if item.Icon != "" {
					item.Icon = utils.S3Url(item.Icon, appId)
				}
				newListItem = append(newListItem, item)
			}
			rdsCfg.MonthList = newListItem
		}
		if len(rdsCfg.YearList) > 0 {
			var newListItem []response.DialogBannerCfgItem
			for _, item := range rdsCfg.YearList {
				if item.Img != "" {
					item.Img = utils.S3Url(item.Img, appId)
				}
				if item.Icon != "" {
					item.Icon = utils.S3Url(item.Icon, appId)
				}
				newListItem = append(newListItem, item)
			}
			rdsCfg.YearList = newListItem
		}
	}()
	redisKey := fmt.Sprintf(global.ContentVipDialogV2CfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		rdsCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		rdsCfg = defaultConfig
		return
	}
	rdsCfg = redisConfig
	return
}

func SetContentVipDialogV2Cfg(ctx context.Context, appId uint, config response.ContentVipDialogV2Cfg) (err error) {
	redisKey := fmt.Sprintf(global.ContentVipDialogV2CfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetGenAiPreCfg(ctx context.Context, appId uint) (rdsCfg response.GenAiPreCfg) {
	var (
		err           error
		redisConfig   response.GenAiPreCfg
		defaultConfig = response.GenAiPreCfg{
			LabelList: []string{"Romantic", "Proactively", "Charming", "Shyness", "Amiable", "Obedient", "Flirty", "Funny", "Humorous", "Strong", "Lover", "Possessive", "Rude"},
			FirstMsgList: []response.FirstMsgItem{
				{
					Label: "Outgoing",
					Msg:   "Hey there, buddy! I'm an incredibly outgoing soul who lights up at the thought of meeting new friends like you!",
				},
				{
					Label: "Adventurous",
					Msg:   "Hola, amigo! I'm an adventurous spirit who jumps at the chance to explore the unknown with fantastic folks like yourself!",
				},
				{
					Label: "Creative",
					Msg:   "Hey there, creative soulmate! I'm a whirlwind of creativity, always sketching dreams and eager to share them with someone as imaginative as you!",
				},
				{
					Label: "Curious",
					Msg:   "Hi there, fellow explorer! I'm a curious cat with an insatiable thirst for knowledge, and I'm thrilled to embark on this journey of discovery with you!",
				},
				{
					Label: "Calm",
					Msg:   "Greetings, serene one! I'm a calm ocean in a stormy world, and I'm delighted to find a peaceful harbor in your presence!",
				},
				{
					Label: "Humorous",
					Msg:   "Hey-o, comedy enthusiast! I'm a walking joke book with a knack for turning the mundane into the hilarious, and I'm excited to make you laugh till you cry!",
				},
				{
					Label: "Determined",
					Msg:   "Yo, go-getter! I'm a mountain climber with a determined heart, always scaling new heights and eager to conquer goals alongside a fellow trailblazer like you!",
				},
				{
					Label: "Empathetic",
					Msg:   "Hello, compassionate soul! I'm a sponge for emotions, soaking up your feelings and offering a shoulder to lean on in return. It's a joy to meet you!",
				},
				{
					Label: "Enthusiastic",
					Msg:   "Woohoo, friend! I'm an enthusiastic cheerleader for life, always ready with a pom-pom and a smile to celebrate every little victory with you!",
				},
				{
					Label: "Thoughtful",
					Msg:   "Hey there, deep thinker! I'm a wanderer in the realm of ideas, always pondering life's mysteries and thrilled to share a thoughtful conversation with someone as introspective as you!",
				},
			},
		}
	)
	redisKey := fmt.Sprintf(global.GenAiPreCfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		rdsCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		rdsCfg = defaultConfig
		return
	}
	rdsCfg = redisConfig
	if rdsCfg.FirstMsgList == nil {
		rdsCfg.FirstMsgList = []response.FirstMsgItem{}
	}
	if rdsCfg.LabelList == nil {
		rdsCfg.LabelList = []string{}
	}
	return
}

func SetGenAiPreCfg(ctx context.Context, appId uint, config response.GenAiPreCfg) (err error) {
	redisKey := fmt.Sprintf(global.GenAiPreCfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetIntimateUpgradeMsgCfg(ctx context.Context, appId uint) (resCfg response.IntimateUpgradeMsgCfg) {
	var (
		err           error
		redisConfig   response.IntimateUpgradeMsgCfg
		defaultConfig = response.IntimateUpgradeMsgCfg{
			LevelOne:   []string{"You've been so good, let's have an appetizer first and enjoy me"},
			LevelTwo:   []string{"I'm very satisfied with your performance. Now I'm a little hot. Do you want to help me?"},
			LevelThree: []string{"Want to see me more vividly? Here are more of my hot"},
			LevelFour:  []string{"I am becoming more and more obsessed with you, let me have an intimate video ch"},
			LevelFive:  []string{"Now I will show you my secrets without reservation and become whatever you want"},
		}
	)
	redisKey := fmt.Sprintf(global.IntimateUpgradeMsgAppIdCfg, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		resCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		resCfg = defaultConfig
		return
	}
	resCfg = redisConfig
	return
}

func SetIntimateUpgradeMsgCfg(ctx context.Context, appId uint, resCfg response.IntimateUpgradeMsgCfg) (err error) {
	redisKey := fmt.Sprintf(global.IntimateUpgradeMsgAppIdCfg, appId)
	configBytes, _ := json.Marshal(resCfg)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

func GetRequireImgBoxCfg(ctx context.Context, appId uint) (rdsCfg response.RequireImgBoxCfg) {
	var (
		err           error
		redisConfig   response.RequireImgBoxCfg
		defaultConfig = response.RequireImgBoxCfg{
			MsgStep: 5,
			MsgList: []string{
				"Check out the random surprise I prepared for you",
			},
			MainTitle: "Let's have some fun, pick one",
			Subtitle:  "Keep contacting me and I will continue to surprise you",
			ImgUrl:    "public/image/c027b9b8-df2b-4123-b396-dfa3793e96e4.png",
		}
	)
	redisKey := fmt.Sprintf(global.RequireImgBoxCfgRdsKey, appId)
	configStr := global.REDIS.Get(ctx, redisKey).Val()
	if configStr == "" {
		rdsCfg = defaultConfig
		return
	}
	if err = json.Unmarshal([]byte(configStr), &redisConfig); err != nil {
		rdsCfg = defaultConfig
		return
	}
	rdsCfg = redisConfig
	return
}

func SetRequireImgBoxCfg(ctx context.Context, appId uint, config response.RequireImgBoxCfg) (err error) {
	redisKey := fmt.Sprintf(global.RequireImgBoxCfgRdsKey, appId)
	configBytes, _ := json.Marshal(config)
	configStr := string(configBytes)
	err = global.REDIS.Set(ctx, redisKey, configStr, 0).Err()
	return
}

// SetGuidedGiftGivingCfg 2024-12-26 新增礼物引导 孙宏伟需求
func SetGuidedGiftGivingCfg(ctx context.Context, appId uint, config response.GuidedGiftGivingCfgReq) (err error) {
	// 校验输入
	if config.Start > config.End {
		return fmt.Errorf("start (%d) cannot be greater than end (%d)", config.Start, config.End)
	}
	if config.Round <= 0 {
		return fmt.Errorf("round (%d) must be greater than 0", config.Round)
	}

	// 初始化随机数种子
	rand.Seed(time.Now().UnixNano())
	// 创建结果 map
	result := make(map[int]int)
	// 遍历回合数，生成随机值
	for i := 1; i <= config.Round; i++ {
		result[i] = rand.Intn(config.End-config.Start+1) + config.Start // 生成 start 到 end 之间的随机数
	}
	// 写入redis
	marshal, err := json.Marshal(result)
	if err != nil {
		return err
	}
	global.REDIS.Set(ctx, fmt.Sprintf(global.GUIDED_GIFT_CONFIG_KEY, appId), string(marshal), 0)
	return nil
}

// SetReplyChargeCfg 12-31 增加快捷回复消息下发付费
func SetReplyChargeCfg(ctx context.Context, appId uint, normal, nsfw int) (err error) {
	global.REDIS.Set(ctx, fmt.Sprintf(global.USER_QUICK_REPLY_COST_RDS_KEY, model.MsgTypeUserQuickReplyNormal, appId), normal, 0)
	global.REDIS.Set(ctx, fmt.Sprintf(global.USER_QUICK_REPLY_COST_RDS_KEY, model.MsgTypeUserQuickReplyNSFW, appId), nsfw, 0)
	return nil
}
