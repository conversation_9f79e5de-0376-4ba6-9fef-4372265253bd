package backend

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/model/cli/td"
	"aimsg-server/utils"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
	"github.com/golang-jwt/jwt/v4"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"io"
	"io/ioutil"
	"net/http"
	"os"
	"os/exec"
	"path"
	"strconv"
	"strings"
	"time"
)

type VideoSimplifyImgVideoService struct{}

func (v *VideoSimplifyImgVideoService) Test() error {
	return nil
}

func (v *VideoSimplifyImgVideoService) BannerList() (res []response.VideoSimplifyBannerResponse, err error) {
	err = global.DB.Model(&model.VideoSimplifyBanner{}).Find(&res).Error
	if err != nil {
		return
	}
	// 根据查出来的模版id 去查模版信息 增加字段
	for k, value := range res {
		if value.RedirectType == 1 {
			var template model.VideoSimplifyTemplate
			err = global.DB.Model(&model.VideoSimplifyTemplate{}).Where("id = ?", value.RedirectUrL).First(&template).Error
			if err != nil {
				continue
			}
			res[k].TemplateType = template.TemplateType
			res[k].Cost = template.Cost
		}
	}
	return
}

func (v *VideoSimplifyImgVideoService) UserMsgList(c *gin.Context) (res []*model.VideoSimplifyUserMsg, err error) {
	userId := utils.GetDigitalUserID(c)
	// userId := uint(145966)
	err = global.DB.Model(&model.VideoSimplifyUserMsg{}).Where("digital_user_id = ?", userId).Order("id DESC").Find(&res).Error
	if err != nil {
		return
	}
	for k, value := range res {
		formattedTime := value.CreatedAt.Format("2006/01/02 15:04")
		res[k].Date = formattedTime
	}
	// 将未读消息改为已读
	err = global.DB.Model(&model.VideoSimplifyUserMsg{}).Where("digital_user_id = ?", userId).Update("status", model.VideoSimplifyUserMsgRead).Error

	return
}

func (v *VideoSimplifyImgVideoService) UserMsgNotify(c *gin.Context) (isHave bool, err error) {
	userId := utils.GetDigitalUserID(c)
	// userId := uint(145966)
	var total int64
	isHave = false
	err = global.DB.Model(&model.VideoSimplifyUserMsg{}).Where("digital_user_id = ? and status = ?", userId, model.VideoSimplifyUserMsgUnRead).Count(&total).Error
	if err != nil {
		return
	}
	if total > 0 {
		isHave = true
	}
	return

}

func (v *VideoSimplifyImgVideoService) UserViewRecord(c *gin.Context, userVideoId uint) (isPop bool, err error) {

	userId := utils.GetDigitalUserID(c)
	// userId := uint(14556)
	// 将用户查看的作品放入redis 集合
	rdsKey := fmt.Sprintf("video_simplify:user_view_video_record_%d", userId)
	rdsKey2 := fmt.Sprintf("video_simplify:user_view_num_%d", userId)

	global.REDIS.ZAdd(c, rdsKey, redis.Z{Score: 1, Member: userVideoId})
	global.REDIS.Expire(c, rdsKey, time.Hour*24*60)
	// 查询用户生成的作品数量
	var total int64
	err = global.DB.Model(&model.VideoSimplifyUserVideo{}).Where("digital_user_id = ?", userId).Count(&total).Error
	if err != nil {
		return
	}
	recordTotal := global.REDIS.ZCard(c, rdsKey).Val()

	isPop = false
	if total >= 2 && recordTotal == 2 {
		isPop = true
	}
	num := int64(0)
	if isPop {
		num = global.REDIS.Incr(c, rdsKey2).Val()
		global.REDIS.Expire(c, rdsKey2, time.Hour*24*60)
	}
	if num > 1 {
		isPop = false
	}
	return

}

func (v *VideoSimplifyImgVideoService) DeleteUserVideo(userVideoId uint) (err error) {
	var userVideo model.VideoSimplifyUserVideo
	err = global.DB.Model(&model.VideoSimplifyUserVideo{}).Where("id = ?", userVideoId).First(&userVideo).Error
	if err != nil {
		return
	}
	if userVideo.Status == model.VideoSimplifyTaskProduct {
		// err = errs.VideoSimplifyTaskProductErr
		return
	}
	err = global.DB.Model(&model.VideoSimplifyUserVideo{}).Where("id = ?", userVideoId).Delete(&model.VideoSimplifyUserVideo{}).Error
	return
}

func (v *VideoSimplifyImgVideoService) AgainCreateVideo(c *gin.Context, req *request.UserCreateVideoReq2) (err error) {
	// 重新生成一个视频 先根据用户视频的id 获取到
	var userVideo model.VideoSimplifyUserVideo
	err = global.DB.Model(&model.VideoSimplifyUserVideo{}).Where("id = ?", req.VideoTemplateId).First(&userVideo).Error
	if err != nil {
		return
	}
	req.ImgUrl = userVideo.ImgUrL
	err = v.UserCreateVideo(c, req)
	if err != nil {
		return err
	}
	return
}

// VideoTemplateList 分页查询视频模板
func (v *VideoSimplifyImgVideoService) VideoTemplateList(page, pageSize int) (res []*model.VideoSimplifyTemplate, total int64, err error) {
	// 设置分页偏移量
	offset := (page - 1) * pageSize
	// 查询总记录数
	err = global.DB.Model(&model.VideoSimplifyTemplate{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	// 分页查询数据
	err = global.DB.Model(&model.VideoSimplifyTemplate{}).
		Order("sort ASC").
		Offset(offset).
		Where("klingai_type in (0,1,2)").
		Limit(pageSize).
		Find(&res).Error
	if err != nil {
		return nil, 0, err
	}
	for k, _ := range res {
		var tagList []string
		tagList = append(tagList, "5s")
		tagList = append(tagList, "HD")
		res[k].TagList = tagList
	}

	return res, total, nil
}
func (v *VideoSimplifyImgVideoService) VideoTemplateInfo(c *gin.Context, templateId uint) (res response.TemplateInfoRes, err error) {
	// v.downLoadVideoUrlAndUploadS3(c, "https://cdn.klingai.com/bs2/upload-kling-api/7264064477/image2video/ChGQ3Wd8q40AAAAAAPvhew-0_raw_video_1.mp4", 2, 1)
	userId := utils.GetDigitalUserID(c)
	// userId := uint(145966)
	// 查询用户信息
	var userSer DigitalUserService
	var userInfo *model.DigitalUser
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		return
	}
	isSVip := userInfo.IsSVip()

	err = global.DB.Model(&model.VideoSimplifyTemplate{}).Where("id = ?", templateId).First(&res).Error
	if err != nil {
		return
	}
	var duration []map[string]string

	if isSVip {
		duration = append(duration, map[string]string{"duration": "10"})
	}
	duration = append(duration, map[string]string{"duration": "5"})

	var modeType []map[string]string
	if isSVip {
		modeType = append(modeType, map[string]string{"mode": "pro"})
	}
	modeType = append(modeType, map[string]string{"mode": "std"})
	res.ModelType = modeType
	res.DurationList = duration
	res.TagList = []string{"5s", "HD"}
	return
}

func (v *VideoSimplifyImgVideoService) UserVideoInfo(c *gin.Context, userVideoId uint) (res model.VideoSimplifyUserVideo, err error) {
	err = global.DB.Model(&model.VideoSimplifyUserVideo{}).Where("id = ?", userVideoId).First(&res).Error
	if err != nil {
		return
	}
	res.TagList = []string{"5s", "HD"}
	return
}

// UserVideoList 根据用户分页查询视频
func (v *VideoSimplifyImgVideoService) UserVideoList(c *gin.Context, page, pageSize int) (res *response.UserVideoListResp, err error) {

	userId := utils.GetDigitalUserID(c)
	// userId := uint(145966)
	if userId == 0 {
		return
	}
	// 查询总记录数
	var total int64
	err = global.DB.Model(&model.VideoSimplifyUserVideo{}).Where("digital_user_id = ?", userId).Count(&total).Error
	if err != nil {
		return nil, err
	}

	// 设置分页偏移量
	offset := (page - 1) * pageSize
	// 分页查询数据
	var list []*model.VideoSimplifyUserVideo
	err = global.DB.Model(&model.VideoSimplifyUserVideo{}).Where("digital_user_id = ?", userId).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&list).Error
	if err != nil {
		return nil, err
	}
	templateIds := lo.Uniq(lo.Map(list, func(info *model.VideoSimplifyUserVideo, _ int) uint {
		return info.TemplateId
	}))

	var templateList []*model.VideoSimplifyTemplate
	err = global.DB.Model(&model.VideoSimplifyTemplate{}).Where("id in ?", templateIds).Find(&templateList).Error
	if err != nil {
		return nil, err
	}
	templateMap := lo.Associate(templateList, func(item *model.VideoSimplifyTemplate) (uint, string) {
		return item.Id, item.Title
	})
	var item []*response.UserVideoListItem
	for k, v2 := range list {
		var tagList []string
		tagList = append(tagList, "5s")
		tagList = append(tagList, "HD")
		list[k].TagList = tagList
		var ext model.Ext
		_ = json.Unmarshal(v2.Ext, &ext)
		title := templateMap[v2.TemplateId]
		if ext.KlingaiType == model.KlingaiTypeVideo2 && v2.Status == model.VideoSimplifyTaskComplete {
			title = ext.Title
		}
		item = append(item, &response.UserVideoListItem{
			Title:                  title,
			VideoSimplifyUserVideo: list[k],
		})
	}

	res = &response.UserVideoListResp{
		List:     item,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}
	return res, nil
}

// UserCreateVideo 用户图生视频
func (v *VideoSimplifyImgVideoService) UserCreateVideo(c *gin.Context, req *request.UserCreateVideoReq2) (err error) {
	imgUrl := req.ImgUrl
	videoTemplateId := req.VideoTemplateId
	duration := req.Duration
	modelType := req.ModelType

	createCount := 1
	userId := utils.GetDigitalUserID(c)
	imgList := make([]string, 2)
	// userId := uint(90001083)
	if userId == 0 {
		return errs.RequireLoginErr
	}

	// 1查询用户的身份  根据身份控制正在生成视频的数量 如果超过返回错误
	// 获取用户信息
	var userInfo *model.DigitalUser
	var userService DigitalUserService
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return errs.RequireLoginErr
	}
	if userInfo.IsSub() {
		createCount = 3
	}
	if userInfo.IsSVip() {
		createCount = 5
	}
	var total int64
	err = global.DB.Model(&model.VideoSimplifyUserVideo{}).Where("status = ? and digital_user_id = ?", 1, userId).Count(&total).Error
	if err != nil {
		return
	}
	global.LOG.Info("test_login", zap.Any("total", total), zap.Any("userInfo", userInfo))
	if userInfo.IsSVip() && total >= 5 {
		return errors.New("LIMIT")
	}

	if total >= int64(createCount) {
		return errs.RequireSVipErr
	}

	//  查询模版信息是否存在
	var videoTemplate model.VideoSimplifyTemplate
	if req.VideoTemplateId == 0 && req.Text != "" && req.ImgUrl != "" {
		err = global.DB.Model(&model.VideoSimplifyTemplate{}).Where("klingai_type = ? ", model.KlingaiTypeVideo2).Order("id desc").Limit(1).First(&videoTemplate).Error
		if err != nil {
			return errors.New("not found video template")
		}
		videoTemplateId = videoTemplate.Id
	} else {
		err = global.DB.Model(&model.VideoSimplifyTemplate{}).Where("id = ?", videoTemplateId).First(&videoTemplate).Error
		if err != nil {
			return errors.New("not found video template")
		}
	}

	var modelConfigData response.KLingModelConfigResponse
	err = json.Unmarshal([]byte(videoTemplate.ModelConfig), &modelConfigData)
	if err != nil {
		return errors.New("not found video template config")
	}

	var profileService DigitalUserProfileService
	var userProfileInfo *model.DigitalUserProfile
	if userProfileInfo, err = profileService.GetByUserId(userId); err != nil {
		return errors.New("not found user")
	}

	// 计算扣钱 并将其写入到一个记录表中记录 钱不够就返回错误让其充钱
	if userProfileInfo.FlowerAmount < videoTemplate.Cost {
		err = errs.FloShopErr
		return
	}
	// 逗号隔开 就切割成两个 传入到接口里 获得新的url
	// 根据逗号分割字符串
	if strings.Contains(imgUrl, ",") {
		parts := strings.Split(imgUrl, ",")
		// 赋值到 img_left 和 img_right
		imgLeft := parts[0]
		imgRight := parts[1]

		// 构造请求体
		requestBody := map[string]string{
			"img_left":  utils.S3Url(imgLeft, userInfo.AppID),
			"img_right": utils.S3Url(imgRight, userInfo.AppID),
		}

		global.LOG.Info("test_two_img_0", zap.Any("img", requestBody))

		requestBodyJSON, err := json.Marshal(requestBody)
		if err != nil {
			return errors.New("two img response error")
		}
		imgList = []string{
			utils.S3Url(imgLeft, userInfo.AppID),
			utils.S3Url(imgRight, userInfo.AppID),
		}
		// 发送 HTTP POST 请求
		url := "http://34.210.195.120:8018/combine"
		resp, err := http.Post(url, "application/json", bytes.NewBuffer(requestBodyJSON))
		if err != nil {
			return errors.New("two img response error")
		}
		defer resp.Body.Close()

		// 读取响应
		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			return errors.New("two img response error")
		}

		// 解析响应到结构体
		var apiResponse response.TwoImgResponse
		if err := json.Unmarshal(body, &apiResponse); err != nil {
			return errors.New("two img response error")
		}
		global.LOG.Info("test_two_img", zap.Any("apiResponse", apiResponse))
		imgUrl = apiResponse.Data.URL
	}

	imgUrl = utils.S3Url(imgUrl, userInfo.AppID)
	if imgUrl == "" || !strings.Contains(imgUrl, "http") {
		global.LOG.Info("test_two_img", zap.Any("img", imgUrl))
		return errors.New("img url error")
	}

	// 将视频生成任务插入到数据库和表里 redis中   生成成功后根据用户身份查询是否需要加水印
	var userVideo model.VideoSimplifyUserVideo
	userVideo.DigitalUserId = userId
	userVideo.TemplateType = videoTemplate.TemplateType
	userVideo.VideoCoverUrL = imgUrl
	userVideo.ImgUrL = imgUrl
	userVideo.Status = model.VideoSimplifyTaskProduct
	userVideo.TemplateId = videoTemplateId
	userVideo.Ext, _ = json.Marshal(model.Ext{Title: req.Title, KlingaiType: videoTemplate.KlingaiType})
	err = global.DB.Create(&userVideo).Error
	if err != nil {
		return errors.New("create error")
	}
	// 获取插入后的ID
	insertedID := userVideo.Id

	// 保存消费记录 扣钱
	var msgService MsgService
	if err = msgService.SaveVideoSimplifyConsumeRecord(userId, videoTemplate.Cost, model.VideoSimplifyConsumeTypeUserCreateImgToVideo, videoTemplate.Id, userVideo.Id); err != nil {
		return errors.New("create record error")
	}

	// 将数据放入到消息列表中 新建一个表 记录消息的状态 这里还有发
	var userMsg model.VideoSimplifyUserMsg
	userMsg.DigitalUserID = userId
	userMsg.ImgUrl = imgUrl
	userMsg.Content = "It will notice you when it's ready"
	userMsg.Title = "Your video is Generating"
	userMsg.Status = model.VideoSimplifyUserMsgUnRead
	err = global.DB.Create(&userMsg).Error
	if err != nil {
		return errors.New("create msg error")
	}

	var msg response.KLingMsg
	msg.DigitalUserId = userId
	msg.TemplateType = videoTemplate.TemplateType
	msg.ImgUrL = imgUrl
	msg.TemplateId = videoTemplateId
	msg.UserVideoId = insertedID
	msg.Duration = duration
	msg.Mode = modelType
	msg.Cost = videoTemplate.Cost
	msg.KlingaiType = videoTemplate.KlingaiType

	switch videoTemplate.KlingaiType {
	case model.KlingaiTypeVideo:
		var modelConfig response.KLingModelConfigResponse
		err = json.Unmarshal([]byte(videoTemplate.ModelConfig), &modelConfig)
		if err != nil {
			return errors.New("not found video template config")
		}
		msg.ModelName = modelConfig.ModelName
		msg.Mode = modelConfig.Mode
		msg.Duration = modelConfig.Duration
		msg.Prompt = modelConfig.Prompt
		msg.NegativePrompt = modelConfig.NegativePrompt
		msg.CfgScale = modelConfig.CfgScale
		msg.ImageTail = modelConfig.ImageTail
	case model.KlingaiTypeOne:
		fallthrough
	case model.KlingaiTypeTwo:
		msg.KlingaiEffectsData = json.RawMessage(videoTemplate.ModelConfig)
		msg.ImgList = imgList
	case model.KlingaiTypeVideo2:
		msg.Prompt = req.Text
		msg.ImgUrL = imgUrl
	}

	// push 到redis 中
	msgStr, _ := json.Marshal(msg)
	global.REDIS.LPush(c, global.VIDIFT_USER_TO_VIDEO_QUEUE, msgStr)
	return nil
}

func (v *VideoSimplifyImgVideoService) GetConsumeRecordList(c *gin.Context, page, pageSize int) (res []*model.VideoSimplifyConsumeRecord, total int64, err error) {
	userId := utils.GetDigitalUserID(c)
	// userId := uint(145966)
	if userId == 0 {
		return
	}
	// 查询总记录数
	err = global.DB.Model(&model.VideoSimplifyConsumeRecord{}).Where("digital_user_id = ?", userId).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 设置分页偏移量
	offset := (page - 1) * pageSize
	// 分页查询数据
	err = global.DB.Model(&model.VideoSimplifyConsumeRecord{}).Where("digital_user_id = ?", userId).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&res).Error
	if err != nil {
		return nil, 0, err
	}
	for key, value := range res {
		if value.ConsumeType == model.VideoSimplifyConsumeTypeUserCreateImgToVideo {
			res[key].ConsumeDesc = model.VideoSimplifyConsumeTypeMap[model.VideoSimplifyConsumeTypeUserCreateImgToVideo]
		}
		if value.ConsumeType != model.VideoSimplifyConsumeTypeUserCreateImgToVideo {
			res[key].ConsumeDesc = model.VideoSimplifyAddFlowerMap[value.ConsumeType]
		}
		res[key].Date = value.CreatedAt.Format("2006/01/02 15:04")
	}

	return res, total, nil

}

func (v *VideoSimplifyImgVideoService) FlowerShop(c *gin.Context) (res response.VideoSimplifyShopRes, err error) {
	// v.GetApiTaskStatus()
	var (
		productInfoList []*model.ProductInfo
		productList     []*model.Product
		resProductList  []response.VideoSimplifyShopItem
		appId           = utils.GetAppID(c)
	)
	// 不知道为什么这么写……
	if appId != 22 {
		appId = 16
	}
	// 查询商品
	productInfoList = global.AppInfoTypeListMap[appId][model.ProductInfoTypeFlowerShop].ProductInfoList
	productList = global.AppInfoTypeListMap[appId][model.ProductInfoTypeFlowerShop].ProductList
	if len(productList) == 0 {
		return
	}
	haveChecked := false
	for pIndex, product := range productList {
		productInfoRecord := productInfoList[pIndex]
		subProductItem := response.VideoSimplifyShopItem{
			Id:           product.Id,
			Name:         product.Name,
			Description:  product.Description,
			GooID:        product.GooID,
			IOSID:        product.IOSID,
			Amount:       product.Amount,
			RewardAmount: product.RewardAmount,
			OriginPrice:  product.OriginPrice,
			USDPrice:     product.USDPrice,
			Hot:          false,
			Bonus:        product.Bonus,
		}
		if !haveChecked && productInfoRecord.Checked {
			subProductItem.Hot = true
			haveChecked = true
		}

		resProductList = append(resProductList, subProductItem)
	}

	res = response.VideoSimplifyShopRes{
		List: resProductList,
	}

	return
}

func (v *VideoSimplifyImgVideoService) VipList(c *gin.Context) (data response.VideoSimplifyVipRes, err error) {

	// 定义固定数据
	MemberEquityList := map[model.ProductInfoType][]response.MemberEquityItem{
		model.ProductInfoTypeSub: {
			{
				Description: "Daily login to receive credits 40",
				IsOpen:      true,
			},
			{
				Description: "Get 10%more on Credit purchase",
				IsOpen:      true,
			},
			{
				Description: "3 Concurrent generations",
				IsOpen:      true,
			},

			{
				Description: "Exclusive fast generation channel",
				IsOpen:      true,
			},
			{
				Description: "High-quality video generation",
				IsOpen:      true,
			},
			{
				Description: "Unlimited Effect access",
				IsOpen:      true,
			},
		},
		model.ProductInfoTypeSVipNew: {
			{
				Description: "Daily login to receive credits 160",
				IsOpen:      true,
			},
			{
				Description: "Get 30%more on Credit purchase",
				IsOpen:      true,
			},
			{
				Description: "5 Concurrent generations",
				IsOpen:      true,
			},
			{
				Description: "Exclusive fast generation channel",
				IsOpen:      true,
			},
			{
				Description: "High-quality video generation",
				IsOpen:      true,
			},
			{
				Description: "Unlimited Effect access",
				IsOpen:      true,
			},
		},
		110: {
			{
				Description: "Registration credits 40 one-time",
				IsOpen:      true,
			},
			{
				Description: "1 Concurrent generations",
				IsOpen:      true,
			},
			{
				Description: "Daily login to receive credits 40",
				IsOpen:      false,
			},
			{
				Description: "No-Ads",
				IsOpen:      false,
			},
			{
				Description: "Exclusive fast generation channel",
				IsOpen:      false,
			},
			{
				Description: "High-quality video generation",
				IsOpen:      false,
			},
		},
	}
	productItem2 := response.VideoSimplifyProductInfo{
		Id:           0,
		Name:         "Basic",
		Description:  "Monthly",
		GooID:        "0",
		IOSID:        "0",
		Amount:       0,
		RewardAmount: 0,
		OriginPrice:  decimal.NewFromInt(0),
		USDPrice:     decimal.NewFromInt(0),
		Bonus:        "0",
		Hot:          false,
		// 处理其他的字段比如
		ByMonthAmount:     "0",
		MemberEquity:      MemberEquityList[110],
		VidifyProductType: 0,
	}

	productItem3 := response.VideoSimplifyProductInfo{
		Id:           0,
		Name:         "Basic",
		Description:  "Yearly",
		GooID:        "0",
		IOSID:        "0",
		Amount:       0,
		RewardAmount: 0,
		OriginPrice:  decimal.NewFromInt(0),
		USDPrice:     decimal.NewFromInt(0),
		Bonus:        "0",
		Hot:          false,
		// 处理其他的字段比如
		ByMonthAmount:     "0",
		MemberEquity:      MemberEquityList[110],
		VidifyProductType: 0,
	}

	// appId := utils.GetAppID(c)
	// var testRes response.VideoSimplifyProductTypeRes
	var yearProductList response.VideoSimplifyProductListRes
	var monthProductList response.VideoSimplifyProductListRes
	// var weekProductList response.VideoSimplifyProductListRes

	var YearProductInfo []response.VideoSimplifyProductInfo
	var MonthProductInfo []response.VideoSimplifyProductInfo
	// var WeekProductInfo []response.VideoSimplifyProductInfo

	YearProductInfo = append(YearProductInfo, productItem3)
	MonthProductInfo = append(MonthProductInfo, productItem2)
	// WeekProductInfo = append(WeekProductInfo, productItem2)

	// 年
	productionInfoType := model.ProductInfoTypeSub
	// 不知道为什么这么写……
	appId := utils.GetAppID(c)
	if appId != 22 {
		appId = 16
	}
	productInfoList := global.AppInfoTypeListMap[appId][productionInfoType].ProductInfoList
	productList := global.AppInfoTypeListMap[appId][productionInfoType].ProductList
	if len(productList) == 0 {
		return
	}
	// 选中的商品
	productIDCheckedMap := map[uint]bool{}
	for _, i := range productInfoList {
		productIDCheckedMap[i.ProductID] = i.Checked
	}
	byMonthAmount := 0
	for _, product := range productList {
		productType := 0
		if product.ProductType == model.PTSubYear {
			byMonthAmount = product.Amount / 12
		}
		if product.ProductType == model.PTSVip {
			byMonthAmount = product.Amount / 12
		}
		if product.ProductType == model.PTSVip || product.ProductType == model.PTSVipMonth {
			productType = 2
		}
		if product.ProductType == model.PTSubMonth || product.ProductType == model.PTSubYear {
			productType = 1
		}
		productItem := response.VideoSimplifyProductInfo{
			Id:           product.Id,
			Name:         product.Name,
			Description:  product.Description,
			GooID:        product.GooID,
			IOSID:        product.IOSID,
			Amount:       product.Amount,
			RewardAmount: product.RewardAmount,
			OriginPrice:  product.OriginPrice,
			USDPrice:     product.USDPrice,
			Bonus:        product.Bonus,
			Hot:          false,
			// 处理其他的字段比如
			ByMonthAmount:     strconv.Itoa(byMonthAmount),
			MemberEquity:      MemberEquityList[productionInfoType],
			VidifyProductType: productType,
		}
		// 设置商品选中
		productItem.Hot = productIDCheckedMap[product.Id]

		// 年
		if product.ProductType == model.PTSVip {
			YearProductInfo = append(YearProductInfo, productItem)

		}
		// 年
		if product.ProductType == model.PTSubYear {
			YearProductInfo = append(YearProductInfo, productItem)
		}
		// 月
		if product.ProductType == model.PTSubMonth {
			MonthProductInfo = append(MonthProductInfo, productItem)
		}
		// 月
		if product.ProductType == model.PTSVipMonth {
			MonthProductInfo = append(MonthProductInfo, productItem)
		}

		// // 周
		// if product.ProductType == model.PTSVipWeek {
		// 	WeekProductInfo = append(WeekProductInfo, productItem)
		// }
		// if product.ProductType == model.PTSubWeek {
		// 	WeekProductInfo = append(WeekProductInfo, productItem)
		// }

	}

	productionInfoType = model.ProductInfoTypeSVipNew
	productInfoList = global.AppInfoTypeListMap[appId][productionInfoType].ProductInfoList
	productList = global.AppInfoTypeListMap[appId][productionInfoType].ProductList
	if len(productList) == 0 {
		return
	}

	// 选中的商品
	productIDCheckedMap = map[uint]bool{}
	for _, i := range productInfoList {
		productIDCheckedMap[i.ProductID] = i.Checked
	}
	byMonthAmount = 0
	for _, product := range productList {
		productType := 0

		if product.ProductType == model.PTSubYear {
			byMonthAmount = product.Amount / 12
		}
		if product.ProductType == model.PTSVip {
			byMonthAmount = product.Amount / 12
		}

		if product.ProductType == model.PTSVip || product.ProductType == model.PTSVipMonth {
			productType = 2
		}
		if product.ProductType == model.PTSubMonth || product.ProductType == model.PTSubYear {
			productType = 1
		}
		productItem := response.VideoSimplifyProductInfo{
			Id:           product.Id,
			Name:         product.Name,
			Description:  product.Description,
			GooID:        product.GooID,
			IOSID:        product.IOSID,
			Amount:       product.Amount,
			RewardAmount: product.RewardAmount,
			OriginPrice:  product.OriginPrice,
			USDPrice:     product.USDPrice,
			Bonus:        product.Bonus,
			Hot:          false,
			// 处理其他的字段比如
			ByMonthAmount:     strconv.Itoa(byMonthAmount),
			MemberEquity:      MemberEquityList[productionInfoType],
			VidifyProductType: productType,
		}
		// 设置商品选中
		productItem.Hot = productIDCheckedMap[product.Id]

		// 年
		if product.ProductType == model.PTSVip {
			YearProductInfo = append(YearProductInfo, productItem)

		}
		// 年
		if product.ProductType == model.PTSubYear {
			YearProductInfo = append(YearProductInfo, productItem)
		}
		// 月
		if product.ProductType == model.PTSubMonth {
			MonthProductInfo = append(MonthProductInfo, productItem)
		}
		// 月
		if product.ProductType == model.PTSVipMonth {
			MonthProductInfo = append(MonthProductInfo, productItem)
		}

		// 周
		// if product.ProductType == model.PTSVipWeek {
		// 	WeekProductInfo = append(WeekProductInfo, productItem)
		// }
		// if product.ProductType == model.PTSubWeek {
		// 	WeekProductInfo = append(WeekProductInfo, productItem)
		// }
	}
	yearProductList.TypeTitle = "Yearly"
	yearProductList.ProductList = YearProductInfo
	yearProductList.Hot = true
	monthProductList.TypeTitle = "Monthly"
	monthProductList.ProductList = MonthProductInfo
	monthProductList.Hot = false
	// weekProductList.TypeTitle = "周度会员"
	// weekProductList.ProductList = WeekProductInfo
	// yearProductList.Hot = false

	// testRes.ProductTypes = append(testRes.ProductTypes, yearProductList)
	// testRes.ProductTypes = append(testRes.ProductTypes, yearProductList)
	// testRes.ProductTypes = append(testRes.ProductTypes, monthProductList)
	// testRes.ProductTypes = append(testRes.ProductTypes, weekProductList)
	data.ProductTypes = append(data.ProductTypes, yearProductList)
	data.ProductTypes = append(data.ProductTypes, monthProductList)
	// data.ProductTypes = append(data.ProductTypes, weekProductList)
	// 会员权益到期时间

	userId := utils.GetDigitalUserID(c)
	var userService DigitalUserService
	userInfo, err := userService.GetById(c, userId)
	if err != nil {
		return
	}
	data.UserDes = "No active subscription"
	if userInfo.IsSub() {
		data.UserDes = "Membership will expire on " + userInfo.SubExpire.Format("2006-01-02")
	}

	if userInfo.IsSVip() {
		data.UserDes = "Membership will expire on " + userInfo.SVipExpire.Format("2006-01-02")
	}

	return
}

func (v *VideoSimplifyImgVideoService) VideoSimplifyConsumeMsg() {
	var (
		ctx      = context.Background()
		redisKey = global.VIDIFT_USER_TO_VIDEO_QUEUE
	)
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Panic("VideoSimplifyConsumeMsg panic", zap.Any("err", err))
		}
	}()
	for {
		total := int64(0)
		err := global.DB.Model(&model.VideoSimplifyTaskRecord{}).Where("status = ?", model.VideoSimplifyTaskProduct).Count(&total).Error
		if err != nil {
			time.Sleep(10 * time.Second)
			continue
		}
		time.Sleep(2 * time.Second)
		if total >= 3 {
			time.Sleep(10 * time.Second)
			continue
		}
		// 获取要生成视频的消息
		consumeMsgStr, err := global.REDIS.RPop(ctx, redisKey).Result()
		time.Sleep(800 * time.Microsecond)
		if err != nil {
			continue
		}
		if consumeMsgStr == "" {
			continue
		}

		var msg response.KLingMsg
		// 将字符串数据解析到结构体
		err = json.Unmarshal([]byte(consumeMsgStr), &msg)
		if err != nil {
			global.LOG.Info("VideoSimplifyConsumeMsg is json", zap.Error(err))
			continue
		}
		// 判断 生成视频
		v.createVideoWithApi(msg)
		time.Sleep(500 * time.Microsecond)
	}
}

func (v *VideoSimplifyImgVideoService) VideoSimplifyUploadVideoConsumeMsg() {
	var (
		ctx      = context.Background()
		redisKey = global.VIDIFT_UPLOAD_VIDEO_QUEUE
	)
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Panic("VideoSimplifyUploadVideoConsumeMsg panic", zap.Any("err", err))
		}
	}()
	for {
		// 获取引导消息
		msgStr, err := global.REDIS.RPop(ctx, redisKey).Result()
		time.Sleep(800 * time.Microsecond)
		if err != nil {
			continue
		}
		if msgStr == "" {
			continue
		}

		var msg response.KLingUploadMsg
		// 将字符串数据解析到结构体
		err = json.Unmarshal([]byte(msgStr), &msg)
		if err != nil {
			// global.LOG.Info("VideoSimplifyUploadVideoConsumeMsg is json", zap.Error(err))
			continue
		}
		// global.LOG.Info("VideoSimplifyUploadVideoConsumeMsg msg", zap.Any("msg", msg))
		// 判断 生成视频
		v.downLoadVideoUrlAndUploadS3(ctx, msg.VideoUrl, msg.IsVip, msg.UserVideoTemplateId)
		time.Sleep(500 * time.Microsecond)

	}
}

func (v *VideoSimplifyImgVideoService) GetToken() (string, error) {

	tokenWithRedis := global.REDIS.Get(context.Background(), "token").Val()
	if tokenWithRedis != "" {
		return tokenWithRedis, nil
	}

	ak := "da78b578a85d416eaf2112dbeae128dd"
	sk := "6767945400454a8a8de4a81c48b05741"
	// 设置签发时间、过期时间和生效时间
	now := time.Now()
	expiredAt := now.Add(30 * time.Minute) // 当前时间+30分钟
	notBefore := now.Add(-5 * time.Second) // 当前时间-5秒

	// 创建JWT的头部和载荷
	claims := jwt.MapClaims{
		"iss": ak,               // issuer
		"exp": expiredAt.Unix(), // 过期时间
		"nbf": notBefore.Unix(), // 生效时间
	}
	// 使用HS256算法创建token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	// 自定义头部信息
	token.Header["alg"] = "HS256"

	// 使用secret key进行签名
	signedToken, err := token.SignedString([]byte(sk))
	if err != nil {
		return "", err
	}
	global.LOG.Info("signedToken", zap.Any("signedToken", signedToken))
	global.REDIS.Set(context.Background(), "token", signedToken, 25*time.Minute)
	return signedToken, nil
}

func (v *VideoSimplifyImgVideoService) createVideoWithApi(msg response.KLingMsg) {
	var h KlingaiHandler
	reqToken, err := v.GetToken()
	if err != nil {
		return
	}
	switch msg.KlingaiType {
	case model.KlingaiTypeVideo2:
		fallthrough
	case model.KlingaiTypeVideo:
		h = &KlingaiTypeVideo{
			KlingaiBase: KlingaiBase{
				CallBackUrl: global.CONFIG.System.UrlPrefix + "/Callback/VideoSimplifyCallBack",
				Token:       reqToken,
				msg:         &msg,
			},
		}
	case model.KlingaiTypeOne:
		fallthrough
	case model.KlingaiTypeTwo:
		h = &KlingaiTypeEffects{
			KlingaiBase: KlingaiBase{
				CallBackUrl: global.CONFIG.System.UrlPrefix + "/Callback/VideoSimplifyCallBack",
				Token:       reqToken,
				msg:         &msg,
			},
		}
	}

	apiResponse, err := h.Handler()
	if err != nil {
		global.LOG.Error("createVideoWithApi Handler fail", zap.Error(err))
		return
	}
	if err != nil {
		// 扣的钱加回去 并且还要修改下用户视频的状态 并且 用户费用记录要加回去 这里都没人任务表 不需要改
		// 用户视频状态改为失败
		global.DB.Model(&model.VideoSimplifyUserVideo{}).Where("id = ?", msg.UserVideoId).Update("status", 3)
		// todo 根据状态加上不同的错误信息

		// 保存消费记录 用户费用记录要加回去
		var msgService MsgService
		if err = msgService.SaveVideoSimplifyConsumeRecord(msg.DigitalUserId, msg.Cost, model.VideoSimplifyConsumeTypeCreateFail, msg.TemplateId, msg.UserVideoId); err != nil {
			return
		}

		// ToDo 失败发个消息 并写入到消息记录表里
		var userMsg model.VideoSimplifyUserMsg
		userMsg.DigitalUserID = msg.DigitalUserId
		userMsg.ImgUrl = msg.ImgUrL
		userMsg.Content = "Upload picture violation"
		userMsg.Title = "Your Video generation failed"
		userMsg.Status = model.VideoSimplifyUserMsgUnRead
		err = global.DB.Create(&userMsg).Error
		if err != nil {
			return
		}

		return
	}

	if apiResponse.Code == 0 {
		// 写入到生成视频的一个表里 // 用户id 模版 id  图片 信息  生成状态 任务id  task_id request_id
		var record model.VideoSimplifyTaskRecord
		record.ImgURL = msg.ImgUrL
		record.TaskId = apiResponse.Data.TaskID
		record.RequestId = apiResponse.RequestID
		record.Status = model.VideoSimplifyTaskProduct
		record.TemplateId = msg.TemplateId
		record.DigitalUserId = msg.DigitalUserId
		record.UserVideoRelationId = msg.UserVideoId
		record.KlingaiType = msg.KlingaiType
		err = global.DB.Create(&record).Error
		if err != nil {
			global.LOG.Error("createVideoWithApi table", zap.Error(err))
		}
	} else {
		var record model.VideoSimplifyTaskRecord
		record.ImgURL = msg.ImgUrL
		record.RequestId = apiResponse.RequestID
		record.Status = model.VideoSimplifyTaskFail
		record.TemplateId = msg.TemplateId
		record.DigitalUserId = msg.DigitalUserId
		record.ErrorMsg = apiResponse.Message
		record.UserVideoRelationId = msg.UserVideoId
		record.KlingaiType = msg.KlingaiType
		err = global.DB.Create(&record).Error
		if err != nil {
			global.LOG.Error("createVideoWithApi table", zap.Error(err))
		}
	}

	// 打印响应
	fmt.Printf("Response: %+v\n", apiResponse)
}

type KlingaiHandler interface {
	Handler() (apiResponse *response.KLingResponseData, err error)
}

type KlingaiBase struct {
	CallBackUrl string `json:"callback_url"`
	Token       string `json:"token"`
	msg         *response.KLingMsg
}

func (h *KlingaiBase) Init() (err error) {
	//callback url
	h.CallBackUrl = global.CONFIG.System.UrlPrefix + "/Callback/VideoSimplifyCallBack"

	// token
	var v VideoSimplifyImgVideoService
	h.Token, err = v.GetToken()
	if err != nil {
		return
	}
	return
}

type KlingaiTypeVideo struct {
	KlingaiBase
}

func (h *KlingaiTypeVideo) Handler() (apiResponse *response.KLingResponseData, err error) {
	err = h.Init()
	if err != nil {
		return
	}
	// 构造请求体
	payload := response.KLingRequestPayload{
		ModelName:      h.msg.ModelName,
		Mode:           h.msg.Mode,
		Duration:       h.msg.Duration,
		Image:          h.msg.ImgUrL,
		Prompt:         h.msg.Prompt,
		NegativePrompt: h.msg.NegativePrompt,
		CfgScale:       h.msg.CfgScale,
		ImageTail:      h.msg.ImageTail,
		CallbackUrl:    h.CallBackUrl,
	}
	reqData := utils.Struct2Map(payload)
	apiResponse = &response.KLingResponseData{}
	url := "https://api.klingai.com/v1/videos/image2video"
	headerMap := map[string]string{
		"Authorization": "Bearer " + h.Token,
	}
	apiLogStr, curlStr, curlErr := utils.HttpPostJsonWithHeader(url, reqData, headerMap, apiResponse, 10)
	if curlErr != nil {
		global.LOG.Error("createVideoWithApi Proxy fail", zap.Error(curlErr), zap.String("apiLog", apiLogStr), zap.String("curl", curlStr))
		return nil, curlErr
	}

	return apiResponse, nil
}

type KKlingaiTypeText2Video struct {
	KlingaiBase
}

func (h *KKlingaiTypeText2Video) Handler() (apiResponse *response.KLingResponseData, err error) {
	err = h.Init()
	if err != nil {
		return
	}
	var body interface{}
	var data response.KlingaiText2Video
	err = json.Unmarshal(h.msg.KlingaiEffectsData, &data)
	if err != nil {
		return
	}
	data.CallbackUrl = h.CallBackUrl
	body = data

	client := resty.New().
		SetTimeout(10 * time.Second).         // 设置超时时间
		SetRetryCount(3).                     // 失败时最大重试次数
		SetRetryWaitTime(2 * time.Second).    // 每次重试等待时间
		SetRetryMaxWaitTime(10 * time.Second) // 最大等待时间
	// 发送 POST 请求
	apiResponse = &response.KLingResponseData{}
	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Authorization", "Bearer "+h.Token).
		SetBody(body).
		SetResult(apiResponse).
		Post("https://api.klingai.com/v1/videos/text2video")

	if err != nil || resp.IsError() {
		// 处理请求体
		var reqBodyStr string
		if resp.Request.Body != nil {
			bodyBytes, _ := json.Marshal(resp.Request.Body) // 转换成 JSON
			reqBodyStr = string(bodyBytes)
		}
		global.LOG.Error("请求失败",
			zap.String("方法", resp.Request.Method),
			zap.String("URL", resp.Request.URL),
			zap.Any("请求头", resp.Request.Header),
			zap.String("请求体", reqBodyStr),
			zap.Int("状态码", resp.StatusCode()),
			zap.String("响应体", resp.String()),
		)
		return nil, errors.New(resp.String())
	}
	return
}

type KlingaiTypeEffects struct {
	KlingaiBase
}

func (h *KlingaiTypeEffects) Handler() (apiResponse *response.KLingResponseData, err error) {
	err = h.Init()
	if err != nil {
		return
	}
	var body interface{}
	if h.msg.KlingaiType == model.KlingaiTypeOne { //单图
		var data response.KlingaiEffects[response.KlingaiEffectsOne]
		err = json.Unmarshal(h.msg.KlingaiEffectsData, &data)
		if err != nil {
			return
		}
		data.CallbackUrl = h.CallBackUrl
		data.Input.Image = h.msg.ImgUrL
		body = data
	} else { //双图
		var data response.KlingaiEffects[response.KlingaiEffectsTwo]
		err = json.Unmarshal(h.msg.KlingaiEffectsData, &data)
		if err != nil {
			return
		}
		data.CallbackUrl = h.CallBackUrl
		data.Input.Images = [2]string{h.msg.ImgList[0], h.msg.ImgList[1]}
		body = data
	}

	client := resty.New().
		SetTimeout(10 * time.Second).         // 设置超时时间
		SetRetryCount(3).                     // 失败时最大重试次数
		SetRetryWaitTime(2 * time.Second).    // 每次重试等待时间
		SetRetryMaxWaitTime(10 * time.Second) // 最大等待时间
	// 发送 POST 请求
	apiResponse = &response.KLingResponseData{}
	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Authorization", "Bearer "+h.Token).
		SetBody(body).
		SetResult(apiResponse).
		Post("https://api.klingai.com/v1/videos/effects")

	if err != nil || resp.IsError() {
		// 处理请求体
		var reqBodyStr string
		if resp.Request.Body != nil {
			bodyBytes, _ := json.Marshal(resp.Request.Body) // 转换成 JSON
			reqBodyStr = string(bodyBytes)
		}
		global.LOG.Error("请求失败",
			zap.String("方法", resp.Request.Method),
			zap.String("URL", resp.Request.URL),
			zap.Any("请求头", resp.Request.Header),
			zap.String("请求体", reqBodyStr),
			zap.Int("状态码", resp.StatusCode()),
			zap.String("响应体", resp.String()),
		)
		return nil, errors.New(resp.String())
	}
	return
}

func (v *VideoSimplifyImgVideoService) VideoSimplifyCallBack(c *gin.Context, kLingCallbackRequest request.VideoSimplifyCallbackResponse) (err error) {

	if kLingCallbackRequest.TaskID == "" {
		return nil
	}
	global.LOG.Info("VideoSimplifyCallBack", zap.Any("kLingCallbackRequest", kLingCallbackRequest))
	if kLingCallbackRequest.TaskStatus != "succeed" && kLingCallbackRequest.TaskStatus != "failed" {
		return nil
	}
	// 通过任务id 查询对应的数据
	var videoApiTaskRecord model.VideoSimplifyTaskRecord
	err = global.DB.Model(&model.VideoSimplifyTaskRecord{}).Where("task_id = ? and status = ?", kLingCallbackRequest.TaskID, 1).First(&videoApiTaskRecord).Error
	if err != nil {
		return nil
	}
	status := ""
	if videoApiTaskRecord.Status == model.VideoSimplifyTaskComplete {
		status = "succeed"
	}
	if videoApiTaskRecord.Status == model.VideoSimplifyTaskFail {
		status = "failed"
	}
	if status == kLingCallbackRequest.TaskStatus {
		return nil
	}
	var userMsg model.VideoSimplifyUserMsg
	var (
		profileService DigitalUserProfileService
		userProfile    *model.DigitalUserProfile
		userService    DigitalUserService
		userInfo       *model.DigitalUser
	)
	// 获取用户信息
	if userInfo, err = userService.GetById(c, videoApiTaskRecord.DigitalUserId); err != nil {
		return
	}

	// 查询用户资料信息
	if userProfile, err = profileService.GetByUserId(videoApiTaskRecord.DigitalUserId); err != nil {
		return
	}

	if kLingCallbackRequest.TaskStatus == "succeed" {
		// 修改对应的状态
		transactionErr := global.DB.Transaction(func(tx *gorm.DB) error {
			// 更新task 表
			if transactionErr := tx.Model(&model.VideoSimplifyTaskRecord{}).
				Where("id = ?", videoApiTaskRecord.Id).
				Update("status", 2).Error; transactionErr != nil {
				return transactionErr
			}
			// 用户视频表
			if transactionErr := tx.Model(&model.VideoSimplifyUserVideo{}).
				Where("id = ?", videoApiTaskRecord.UserVideoRelationId).
				Updates(map[string]interface{}{
					"video_url": kLingCallbackRequest.TaskResult.Videos[0].URL,
					"status":    2,
				}).Error; err != nil {
				return transactionErr
			}

			userMsg.DigitalUserID = videoApiTaskRecord.DigitalUserId
			userMsg.ImgUrl = videoApiTaskRecord.ImgURL
			userMsg.Status = model.VideoSimplifyUserMsgUnRead
			userMsg.TemplateId = videoApiTaskRecord.TemplateId
			userMsg.VideoUrL = kLingCallbackRequest.TaskResult.Videos[0].URL
			userMsg.Title = "Your Video is ready ！"
			userMsg.Content = "visit your profile page to view"
			if transactionErr := tx.Model(&model.VideoSimplifyUserMsg{}).Create(&userMsg).Error; transactionErr != nil {
				return transactionErr
			}
			return nil
		})
		if transactionErr != nil {
			return
		}

		// 放到upload 的队列中
		var msg response.KLingUploadMsg
		msg.IsVip = true
		msg.VideoUrl = kLingCallbackRequest.TaskResult.Videos[0].URL
		msg.UserVideoTemplateId = videoApiTaskRecord.UserVideoRelationId
		// push 到redis 中
		msgStr, _ := json.Marshal(msg)
		global.REDIS.LPush(context.Background(), global.VIDIFT_UPLOAD_VIDEO_QUEUE, msgStr)

		// 通知成功
		fcmService := FcmService{}
		var body response.VideoSimplifyNoticeMsgBody
		body.Title = "Your Video is ready ！"
		body.Content = userMsg.Content
		body.ImgUrl = videoApiTaskRecord.ImgURL
		body.UserVideoId = videoApiTaskRecord.UserVideoRelationId
		bodyStr, _ := json.Marshal(body)
		_ = fcmService.PushCommonMsgSingle(c, videoApiTaskRecord.DigitalUserId, "Your Video is ready ！", string(bodyStr))
		seconds, _ := strconv.ParseFloat(kLingCallbackRequest.TaskResult.Videos[0].Duration, 64)
		millisecond := seconds * 1000
		TdPointer(userInfo, userProfile, td.ServerEvent, map[string]interface{}{
			"event_tag":      "video_generation",
			"event_type":     "success",
			"generatio_time": fmt.Sprintf("%.0f", millisecond),
			"effect_id":      videoApiTaskRecord.TemplateId,
		})
	}

	if kLingCallbackRequest.TaskStatus == "failed" {
		// 开启事务
		transactionFalseErr := global.DB.Transaction(func(tx *gorm.DB) error {
			// 更新 task 表
			if transactionErr := tx.Model(&model.VideoSimplifyTaskRecord{}).
				Where("id = ?", videoApiTaskRecord.Id).
				Updates(map[string]interface{}{
					"status":    3,
					"error_msg": kLingCallbackRequest.TaskStatusMsg,
				}).Error; transactionErr != nil {
				return transactionErr
			}

			// 更新 用户视频表
			if transactionErr := tx.Model(&model.VideoSimplifyUserVideo{}).
				Where("id = ?", videoApiTaskRecord.UserVideoRelationId).
				Update("status", 3).Error; transactionErr != nil {
				return transactionErr
			}

			userMsg.DigitalUserID = videoApiTaskRecord.DigitalUserId
			userMsg.ImgUrl = videoApiTaskRecord.ImgURL
			userMsg.Status = model.VideoSimplifyUserMsgUnRead
			userMsg.TemplateId = videoApiTaskRecord.TemplateId
			userMsg.VideoUrL = ""
			userMsg.Title = "Your Video generation failed"
			userMsg.Content = "Upload picture violation"
			if transactionFalseErr := tx.Model(&model.VideoSimplifyUserMsg{}).Create(&userMsg).Error; transactionFalseErr != nil {
				return transactionFalseErr

			}
			return nil
		})
		if transactionFalseErr != nil {
			return
		}

		// 保存消费记录 用户费用记录要加回去
		var templateInfo model.VideoSimplifyTemplate
		err = global.DB.Model(&model.VideoSimplifyTemplate{}).Where("id = ?", videoApiTaskRecord.TemplateId).First(&templateInfo).Error
		if err != nil {
			return
		}
		var msgService MsgService
		if err = msgService.SaveVideoSimplifyConsumeRecord(videoApiTaskRecord.DigitalUserId, templateInfo.Cost, model.VideoSimplifyConsumeTypeCreateFail, videoApiTaskRecord.TemplateId, videoApiTaskRecord.UserVideoRelationId); err != nil {
			return
		}

		// 通知失败
		fcmService := FcmService{}
		var body response.VideoSimplifyNoticeMsgBody
		body.Title = "Your Video generation failed"
		body.Content = userMsg.Content
		body.ImgUrl = videoApiTaskRecord.ImgURL
		body.UserVideoId = videoApiTaskRecord.UserVideoRelationId
		bodyStr, _ := json.Marshal(body)
		_ = fcmService.PushCommonMsgSingle(c, videoApiTaskRecord.DigitalUserId, "Your Video generation failed", string(bodyStr))
		TdPointer(userInfo, userProfile, td.ServerEvent, map[string]interface{}{
			"event_tag":  "video_generation",
			"event_type": "fail",
			"reason":     kLingCallbackRequest.TaskStatusMsg,
			"effect_id":  videoApiTaskRecord.TemplateId,
		})
	}
	return
}

// GetApiTaskStatus 启动一个cron 任务去主动查询任务状态 成功就更新
func (v *VideoSimplifyImgVideoService) GetApiTaskStatus(c *gin.Context) {
	// 先从数据库查询List 然后再循环查询
	// var userInfo *model.DigitalUser
	global.LOG.Info("GetApiTaskStatus_start")
	var userMsg model.VideoSimplifyUserMsg

	var records []*model.VideoSimplifyTaskRecord
	err := global.DB.Model(&model.VideoSimplifyTaskRecord{}).Where("status = ?", 1).Find(&records).Error
	if err != nil {
		return
	}

	for _, record := range records {
		time.Sleep(300 * time.Microsecond)
		var url string
		switch record.KlingaiType {
		case model.KlingaiTypeVideo:
			fallthrough
		case model.KlingaiTypeVideo2:
			url = fmt.Sprintf("https://api.klingai.com/v1/videos/image2video/%s", record.TaskId)
		case model.KlingaiTypeOne:
			fallthrough
		case model.KlingaiTypeTwo:
			url = fmt.Sprintf("https://api.klingai.com/v1/videos/effects/%s", record.TaskId)
		}
		var apiResponse response.KLingAPIResponse
		token, tokenErr := v.GetToken()
		if tokenErr != nil {
			continue
		}
		headerMap := map[string]string{
			"Authorization": "Bearer " + token,
		}
		apiLogStr, curlStr, curlErr := utils.HttpGetWithHeader(url, headerMap, &apiResponse, 10)
		if curlErr != nil {
			global.LOG.Error("GetApiTaskStatus Proxy fail", zap.Error(curlErr), zap.String("apiLog", apiLogStr), zap.String("curl", curlStr))
			continue
		}
		var (
			profileService DigitalUserProfileService
			userProfile    *model.DigitalUserProfile
			userService    DigitalUserService
			userInfo       *model.DigitalUser
		)
		// 获取用户信息
		if userInfo, err = userService.GetById(c, record.DigitalUserId); err != nil {
			return
		}

		// 查询用户资料信息
		if userProfile, err = profileService.GetByUserId(record.DigitalUserId); err != nil {
			return
		}
		if apiResponse.Code == 0 && apiResponse.Message == "SUCCEED" {
			if apiResponse.Data.TaskStatus == "succeed" {
				userMsg.Content = "visit your profile page to view"
				// if userInfo, err = userSer.GetById(context.Background(), record.DigitalUserId); err != nil {
				// 	return
				// }
				status := 1
				isVip := false
				videoUrl := ""
				// 暂时不要这个 全部都写入进去 更新状态
				// if userInfo.IsSub() || userInfo.IsSVip() {
				// 	isVip = true
				// 	status = 2
				// 	videoUrl = apiResponse.Data.TaskResult.Videos[0].URL
				// }
				isVip = true
				status = 2
				videoUrl = apiResponse.Data.TaskResult.Videos[0].URL
				// ToDo 如果不是会员 此时应该是没有连接的不应该更新

				// 判断是不是会员
				transactionErr := global.DB.Transaction(func(tx *gorm.DB) error {
					// 更新task 表
					if transactionErr := tx.Model(&model.VideoSimplifyTaskRecord{}).
						Where("id = ?", record.Id).
						Update("status", 2).Error; transactionErr != nil {
						return transactionErr
					}
					// 用户视频表
					if transactionErr := tx.Model(&model.VideoSimplifyUserVideo{}).
						Where("id = ?", record.UserVideoRelationId).
						Updates(map[string]interface{}{
							"video_url": videoUrl,
							"status":    status,
						}).Error; err != nil {
						return transactionErr
					}

					userMsg.DigitalUserID = record.DigitalUserId
					userMsg.ImgUrl = record.ImgURL
					userMsg.Status = model.VideoSimplifyUserMsgUnRead
					userMsg.TemplateId = record.TemplateId
					userMsg.VideoUrL = videoUrl
					userMsg.Title = "Your Video is ready ！"
					if transactionErr := tx.Model(&model.VideoSimplifyUserMsg{}).Create(&userMsg).Error; transactionErr != nil {
						return transactionErr
					}
					return nil
				})
				if transactionErr != nil {
					return
				}
				// 放到upload 的队列中
				var msg response.KLingUploadMsg
				msg.IsVip = isVip
				msg.VideoUrl = apiResponse.Data.TaskResult.Videos[0].URL
				msg.UserVideoTemplateId = record.UserVideoRelationId
				// push 到redis 中
				msgStr, _ := json.Marshal(msg)
				global.REDIS.LPush(context.Background(), global.VIDIFT_UPLOAD_VIDEO_QUEUE, msgStr)

				// 通知成功
				var body response.VideoSimplifyNoticeMsgBody
				body.Title = "Your Video is ready ！"
				body.Content = userMsg.Content
				body.ImgUrl = record.ImgURL
				body.UserVideoId = record.UserVideoRelationId
				bodyStr, _ := json.Marshal(body)
				fcmService := FcmService{}
				_ = fcmService.PushCommonMsgSingle(c, record.DigitalUserId, "Your Video is ready ！", string(bodyStr))
				seconds, _ := strconv.ParseFloat(apiResponse.Data.TaskResult.Videos[0].Duration, 64)
				millisecond := seconds * 1000
				TdPointer(userInfo, userProfile, td.ServerEvent, map[string]interface{}{
					"event_tag":      "video_generation",
					"event_type":     "success",
					"generatio_time": fmt.Sprintf("%.0f", millisecond),
					"effect_id":      record.TemplateId,
				})
			}

			if apiResponse.Data.TaskStatus == "failed" {
				userMsg.Content = fmt.Sprintf("Your Video generation failed")
				transactionErr := global.DB.Transaction(func(tx *gorm.DB) error {
					// 更新 task 表
					if transactionErr := tx.Model(&model.VideoSimplifyTaskRecord{}).
						Where("id = ?", record.Id).
						Updates(map[string]interface{}{
							"status":    3,
							"error_msg": apiResponse.Data.TaskStatusMsg,
						}).Error; transactionErr != nil {
						return transactionErr
					}

					// 更新 用户视频表
					if transactionErr := tx.Model(&model.VideoSimplifyUserVideo{}).
						Where("id = ?", record.UserVideoRelationId).
						Update("status", 3).Error; transactionErr != nil {
						return transactionErr
					}

					userMsg.DigitalUserID = record.DigitalUserId
					userMsg.ImgUrl = record.ImgURL
					userMsg.Status = model.VideoSimplifyUserMsgUnRead
					userMsg.TemplateId = record.TemplateId
					userMsg.VideoUrL = ""
					userMsg.Title = "Your Video generation failed"
					if transactionErr := tx.Model(&model.VideoSimplifyUserMsg{}).Create(&userMsg).Error; transactionErr != nil {
						return transactionErr

					}

					return nil
				})
				if transactionErr != nil {
					return
				}

				// 保存消费记录 用户费用记录要加回去
				var templateInfo model.VideoSimplifyTemplate
				err = global.DB.Model(&model.VideoSimplifyTemplate{}).Where("id = ?", record.TemplateId).First(&templateInfo).Error
				if err != nil {
					return
				}
				var msgService MsgService
				if err = msgService.SaveVideoSimplifyConsumeRecord(record.DigitalUserId, templateInfo.Cost, model.VideoSimplifyConsumeTypeCreateFail, record.TemplateId, record.UserVideoRelationId); err != nil {
					return
				}

				// 通知失败
				var body response.VideoSimplifyNoticeMsgBody
				body.Title = "Your Video generation failed"
				body.Content = userMsg.Content
				body.ImgUrl = record.ImgURL
				body.UserVideoId = record.UserVideoRelationId
				bodyStr, _ := json.Marshal(body)
				fcmService := FcmService{}
				_ = fcmService.PushCommonMsgSingle(c, record.DigitalUserId, "Your Video generation failed", string(bodyStr))

				TdPointer(userInfo, userProfile, td.ServerEvent, map[string]interface{}{
					"event_tag":  "video_generation",
					"event_type": "fail",
					"reason":     apiResponse.Data.TaskStatusMsg,
					"effect_id":  record.TemplateId,
				})
			}

		}
		if apiResponse.Code != 0 {
			errMsgResponseStr, _ := json.Marshal(apiResponse)
			err := global.DB.Model(&model.VideoSimplifyTaskRecord{}).
				Where("id = ?", record.Id).
				Updates(map[string]interface{}{
					"status":    3,
					"error_msg": string(errMsgResponseStr),
				}).Error

			if err != nil {
				var body response.VideoSimplifyNoticeMsgBody
				body.Title = "Your Video generation failed"
				body.Content = userMsg.Content
				body.ImgUrl = record.ImgURL
				body.UserVideoId = record.UserVideoRelationId
				bodyStr, _ := json.Marshal(body)
				fcmService := FcmService{}
				_ = fcmService.PushCommonMsgSingle(c, record.DigitalUserId, "Your Video generation failed", string(bodyStr))
				continue
			}
			TdPointer(userInfo, userProfile, td.ServerEvent, map[string]interface{}{
				"event_tag":  "video_generation",
				"event_type": "fail",
				"reason":     apiResponse.Message,
				"effect_id":  record.TemplateId,
			})
		}
	}

}

func (v *VideoSimplifyImgVideoService) downLoadVideoUrlAndUploadS3(c context.Context, videoUrl string, isVip bool, userVideoTemplateId uint) {
	// isVip = true
	// 发送 HTTP GET 请求
	resp, err := http.Get(videoUrl)
	if err != nil {
		global.LOG.Info("failed to download video", zap.Error(err))
		return
	}
	defer resp.Body.Close()

	// 检查 HTTP 响应状态码
	if resp.StatusCode != http.StatusOK {
		global.LOG.Info("failed to download video", zap.Error(err))
		return
	}

	// 这块是不是就是流啊
	s3Client := GetS3Client()
	business := "video"
	fileName := path.Base(videoUrl)

	// inputVideoPath := fmt.Sprintf("/tmp/%s", fileName)
	// outputVideoPath := fmt.Sprintf("/tmp/out_%s", fileName)
	// watermarkPath := "/tmp/watermark.png"

	// 下载视频
	// inputVideoPath := "/Users/<USER>/go/cashbox-ai/aimsg-server/test.mp4"
	// watermarkPath := "/Users/<USER>/go/cashbox-ai/aimsg-server/testwa1.png"
	// outputVideoPath := "/Users/<USER>/go/cashbox-ai/aimsg-server/testoutput.mp4"

	key := fmt.Sprintf("public/%s/%s", business, fileName) // 文件名格式 自己可以改 建议保证唯一性
	uploadVideoUrl := fmt.Sprintf("https://images.ihappy.ai/%s", key)
	// 直接上传
	by, errRead := io.ReadAll(resp.Body)
	if errRead != nil {
		return
	}
	contentType := resp.Header.Get("Content-Type")
	if contentType == "" {
		contentType = "video/mp4"
	}
	err = s3Client.UploadBytesToS3(by, key, contentType)
	if err != nil {
		return
	}
	// 改数据库的地址
	gormErr := global.DB.Model(&model.VideoSimplifyUserVideo{}).
		Where("id = ?", userVideoTemplateId).
		Updates(map[string]interface{}{
			"video_url": uploadVideoUrl,
		}).Error
	if gormErr != nil {
		global.LOG.Info("failed to update video url", zap.Error(gormErr))
		return
	}

	return

	// // 创建本地文件
	// out, err := os.Create(inputVideoPath)
	// if err != nil {
	// 	global.LOG.Info("failed to create video file", zap.Error(err))
	// 	return
	// }
	// defer out.Close()
	//
	// // 将响应内容写入文件
	// _, err = io.Copy(out, resp.Body)
	// if err != nil {
	// 	return
	// }
	//
	// // 添加水印到视频
	// fmt.Println("Adding watermark to video...")
	// if err := v.addWatermarkToVideo(inputVideoPath, watermarkPath, outputVideoPath); err != nil {
	// 	global.LOG.Info("failed to addWatermarkToVideo video", zap.Error(err))
	// 	return
	// }
	// defer func() {
	// 	_ = os.Remove(inputVideoPath)
	// 	_ = os.Remove(outputVideoPath)
	// }()
	// // 将视频上传到OSS 并修改数据库的URL
	//
	// err = s3Client.UploadFile(outputVideoPath, key)
	// if err != nil {
	// 	return
	// }
	// // 改数据库的地址
	// gormErr := global.DB.Model(&model.VideoSimplifyTaskRecord{}).
	// 	Where("id = ?", userVideoTemplateId).
	// 	Updates(map[string]interface{}{
	// 		"video_url": uploadVideoUrl,
	// 		"status":    2,
	// 	}).Error
	// if gormErr != nil {
	// 	return
	// }

}

// 添加水印到视频 增加爽视频
func (v *VideoSimplifyImgVideoService) addWatermarkToVideo(inputVideoPath, watermarkPath, outputVideoPath string) error {
	// 使用 FFmpeg 添加水印到视频
	cmd := exec.Command("ffmpeg",
		"-i", inputVideoPath, // 输入视频
		"-i", watermarkPath, // 输入水印图片（PNG 格式，带透明背景）
		"-filter_complex", "overlay=W-w-10:H-h-10:enable='between(t,0,10)'", // 水印右下角，透明部分保留
		"-c:v", "libx264", // 视频编码器
		"-preset", "fast", // 编码速度
		"-crf", "23", // 视频质量
		outputVideoPath, // 输出视频路径
	)

	// 运行命令
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to add watermark: %w", err)
	}
	return nil
}

// AddVideoSimplify 添加视频模板
func (v *VideoSimplifyImgVideoService) AddVideoSimplify(reqData request.AddTemplateReq) (err error) {
	var template model.VideoSimplifyTemplate
	template.VideoUrL = utils.S3Url(reqData.VideoUrL, uint(16))
	template.VideoCoverUrL = utils.S3Url(reqData.VideoCoverUrL, uint(16))
	template.Cost = reqData.Cost
	template.Title = reqData.Title
	template.Description = reqData.Description
	template.TemplateType = reqData.TemplateType
	template.Sort = reqData.Sort
	template.ModelConfig = reqData.ModelConfig
	template.KlingaiType = reqData.KlingaiType
	if reqData.Id != 0 {
		err = global.DB.Model(&model.VideoSimplifyTemplate{}).Where("id = ?", reqData.Id).Updates(&template).Error
	} else {
		err = global.DB.Model(&model.VideoSimplifyTemplate{}).Create(&template).Error
	}
	return
}

// GetVideoSimplifyList 分页查询视频模板
func (v *VideoSimplifyImgVideoService) GetVideoSimplifyList(page, pageSize, KlingaiType int, id uint) (res []*model.VideoSimplifyTemplate, total int64, err error) {
	// 设置分页偏移量
	offset := (page - 1) * pageSize
	// 初始化查询
	query := global.DB.Model(&model.VideoSimplifyTemplate{})
	// 如果 status 不为空，则添加 WHERE 条件
	if id != 0 {
		query = query.Where("id = ?", id)
	}
	query = query.Where("klingai_type = ?", KlingaiType)

	// 查询总记录数
	err = query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	// 分页查询数据
	err = query.
		Order("sort ASC").
		Offset(offset).
		Limit(pageSize).
		Find(&res).Error
	if err != nil {
		return nil, 0, err
	}

	// 为每个结果添加标签
	for k := range res {
		var tagList []string
		tagList = append(tagList, "5s")
		tagList = append(tagList, "HD")
		res[k].TagList = tagList
	}

	return res, total, nil
}

func (v *VideoSimplifyImgVideoService) DeleteVideoSimplify(id uint, title string) (err error) {
	err = global.DB.Where("id = ?", id).Delete(&model.VideoSimplifyTemplate{}).Error
	if err != nil {
		return
	}
	err = global.DB.Where("title = ?", title).Delete(&model.VideoSimplifyBanner{}).Error
	if err != nil {
		return
	}
	return
}

func (v *VideoSimplifyImgVideoService) SetBanner(id uint) (err error) {
	var info model.VideoSimplifyTemplate
	err = global.DB.Where("id = ?", id).First(&info).Error
	if err != nil {
		return
	}
	var banner model.VideoSimplifyBanner
	banner.Title = info.Title
	banner.VideoUrL = info.VideoUrL
	banner.VideoCoverUrL = info.VideoCoverUrL
	banner.Description = info.Description
	banner.RedirectUrL = strconv.Itoa(int(info.Id))
	banner.RedirectType = 1
	err = global.DB.Create(&banner).Error
	if err != nil {
		return
	}
	return
}

func (v *VideoSimplifyImgVideoService) GetBannerList(page, pageSize int) (res []*model.VideoSimplifyBanner, total int64, err error) {
	// 设置分页偏移量
	offset := (page - 1) * pageSize
	// 初始化查询
	query := global.DB.Model(&model.VideoSimplifyBanner{})

	// 分页查询数据
	err = query.
		Order("id asc").
		Offset(offset).
		Limit(pageSize).
		Find(&res).Error
	if err != nil {
		return nil, 0, err
	}
	return
}

func (v *VideoSimplifyImgVideoService) DeleteBanner(id uint) (err error) {
	err = global.DB.Where("id = ?", id).Delete(&model.VideoSimplifyBanner{}).Error
	if err != nil {
		return
	}
	return

}

// GetVideoSimplifyInfo 新增后台
func (v *VideoSimplifyImgVideoService) GetVideoSimplifyInfo(id uint) (info model.VideoSimplifyTemplate, err error) {
	err = global.DB.Where("id = ?", id).First(&info).Error
	if err != nil {
		return
	}
	return

}

var createIndexList = []*response.CreateIndexItem{
	{
		Id:            1,
		VideoUrL:      "https://images.ihappy.ai/public/video/tiaowu13.mp4",
		VideoCoverUrL: "https://images.ihappy.ai/public/image/f4ea1ed1-4d37-4d18-b46c-2089cc9533d0.png",
		Title:         "Kissing",
		Description:   "Turn your photo into a romantic masterpiece, where the people in your image share a tender, unforgettable kiss, bringing love to life.",
	},
	{
		Id:            2,
		VideoUrL:      "https://images.ihappy.ai/public/image/e4e044bc-b5bf-4b77-b94b-8a998b8369c7.mp4",
		VideoCoverUrL: "https://images.ihappy.ai/public/image/23d09736-6e3c-4e1e-b0aa-55f7e23230b0.jpg",
		Title:         "Arrogant",
		Description:   "Add a cool, cinematic touch to your photo with a relaxed cigarette in hand, creating a smoky, stylish vibe.",
	},
	{
		Id:            3,
		VideoUrL:      "https://images.ihappy.ai/public/image/31fc7f78-58a2-4952-930f-2832ed99390a.mp4",
		VideoCoverUrL: "https://images.ihappy.ai/public/image/19f70a2d-c002-48c1-a8f1-5811e0025197.jpg",
		Title:         "Jazz",
		Description:   "Watch the image come alive with energetic jazz dance moves, as figures twist, kick, and spin to a lively rhythm!",
	},
	{
		Id:            4,
		VideoUrL:      "https://images.ihappy.ai/public/image/a7f14ef6-bbe3-42cb-967e-a1e7f7084672.mp4",
		VideoCoverUrL: "https://images.ihappy.ai/public/image/7c21a2f1-353f-4240-8cef-24d5b01d10ce.png",
		Title:         "Hip-twisting",
		Description:   "Turn your photo into a lively moment as the person dances with a playful hip swing, bringing energy and fun to the scene!",
	},
}

func (v *VideoSimplifyImgVideoService) CreateIndex(c *gin.Context) (info *response.CreateIndexResp, err error) {
	var videoTemplate model.VideoSimplifyTemplate
	err = global.DB.Model(&model.VideoSimplifyTemplate{}).Where("klingai_type = ? ", model.KlingaiTypeVideo2).
		Order("id desc").
		Limit(1).
		First(&videoTemplate).Error
	if err != nil {
		return nil, nil
	}

	info = &response.CreateIndexResp{
		List: createIndexList,
		Cost: videoTemplate.Cost,
	}
	return info, nil
}
