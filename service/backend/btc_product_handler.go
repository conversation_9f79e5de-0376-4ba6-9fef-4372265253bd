package backend

import (
	"aimsg-server/global"
	reqBtc "aimsg-server/model/backend/request/btc"
	"aimsg-server/utils"
	"errors"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
)

type RewardsHandler interface {
	Handler() (int, error)
}

type AdHandler struct {
	ctx    *gin.Context
	req    *reqBtc.ClaimDailyRewardsReq
	UserId uint
	now    time.Time
}

type SignHandler struct {
	ctx    *gin.Context
	req    *reqBtc.ClaimDailyRewardsReq
	UserId uint
	now    time.Time
}

// 广告，签到
func (m *BtcService) ClaimDailyRewards(c *gin.Context, req *reqBtc.ClaimDailyRewardsReq) (resp interface{}, err error) {
	if !lo.Contains([]int{1, 2}, req.Type) { // 只能是这
		return nil, errors.New("非法请求， 商品类型不符合")
	}
	var h RewardsHandler
	userId := utils.GetDigitalUserID(c)
	curT := time.Now()
	if req.Type == 2 {
		h = &SignHandler{
			ctx:    c,
			req:    req,
			UserId: userId,
			now:    curT,
		}
	} else {
		h = &AdHandler{
			ctx:    c,
			req:    req,
			UserId: userId,
			now:    curT,
		}
	}
	ProductId, err := h.Handler()
	if err != nil {
		return nil, err
	}
	err = m.orderAfter(c, int(userId), ProductId, req.Type, curT)
	if err != nil {
		return nil, err
	}
	return
}

func (m *AdHandler) Handler() (int, error) {
	// global.REDIS.
	today := m.now.Format("2006-01-02")
	adKey := fmt.Sprintf("ai:btc:user:ad_watch:num1:%d:%s", m.UserId, today) // 今日观看次数
	lastAdKey := fmt.Sprintf("ai:btc:user:ad_watch_last:%d", m.UserId)       // 上次观看时间
	for {
		// 监视观看次数 & 上次观看时间
		err := global.REDIS.Watch(m.ctx, func(tx *redis.Tx) error {
			// 获取当前观看次数
			count, err := tx.Get(m.ctx, adKey).Int()
			if err != nil && err != redis.Nil {
				return err
			}
			// 获取上次观看时间
			lastWatchTime, err := tx.Get(m.ctx, lastAdKey).Int64()
			if err != nil && err != redis.Nil {
				return err
			}

			// 获取当前时间戳
			now := time.Now().Unix()

			// 检查是否超过每日限制
			if count >= ADMax {
				return fmt.Errorf("The maximum number of ad views has been reached for today. Please come back tomorrow.\n\n\n\n\n\n\n\n")
			}

			// 检查是否满足 5 秒间隔
			if lastWatchTime > 0 && now-lastWatchTime < 5 {
				return fmt.Errorf("观看广告间隔过短，请稍后再试")
			}

			// 执行事务
			_, err = tx.TxPipelined(m.ctx, func(pipe redis.Pipeliner) error {
				// 递增观看次数
				pipe.Incr(m.ctx, adKey)

				// 记录本次观看时间
				pipe.Set(m.ctx, lastAdKey, now, 24*time.Hour)

				// 第一次观看时，设置过期时间为当天 23:59:59
				if count == 0 {
					midnight := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 23, 59, 59, 0, time.Now().Location())
					expireTime := time.Until(midnight)
					pipe.Expire(m.ctx, adKey, expireTime)
					pipe.Expire(m.ctx, lastAdKey, expireTime)
				}

				return nil
			})
			return err
		}, adKey, lastAdKey)

		// 事务冲突时自动重试
		if err == redis.TxFailedErr {
			continue
		}
		if err != nil {
			return 0, err
		}
		break
	}
	return 0, nil
}
func (m *AdHandler) ADNum() (int, error) {
	today := m.now.Format("2006-01-02")
	adKey := fmt.Sprintf("ai:btc:user:ad_watch:num1:%d:%s", m.UserId, today) // 今日观看次数
	// 获取当前观看次数
	count, err := global.REDIS.Get(m.ctx, adKey).Int()
	if err != nil && err != redis.Nil {
		return 0, nil
	}
	return count, nil
}

func (m *SignHandler) Handler() (int, error) {
	// 尝试设置一个键，表示签到，并设置过期时间为一天
	today := m.now.Format("2006-01-02")
	key := fmt.Sprintf("user:checkin:%d:%s", m.UserId, today)
	result, err := global.REDIS.SetNX(m.ctx, key, "1", 24*time.Hour).Result()
	if err != nil {
		return 0, err
	}
	if !result {
		return 0, errors.New("You have already checked in today.")
	}
	return int(m.now.Weekday()), nil
}

// 1签到 2未签到
func (m *SignHandler) SignStatus(t time.Time) (int, error) {
	today := t.Format("2006-01-02")
	key := fmt.Sprintf("user:checkin:%d:%s", m.UserId, today)
	_, err := global.REDIS.Get(m.ctx, key).Result()
	if err != nil && err != redis.Nil {
		return 2, err
	}
	if err == redis.Nil {
		return 2, nil
	}
	return 1, nil
}

func (m *BtcService) MyNFT(c *gin.Context, req *reqBtc.NFTReq) (resp reqBtc.NFTResp, err error) {
	userId := utils.GetDigitalUserID(c)
	productList, err := m.GetProductToday(c, userId)
	if err != nil {
		return resp, err
	}
	resp = reqBtc.NFTResp{
		List: []reqBtc.NFTDetail{},
	}
	appId := utils.GetAppID(c)
	for _, product := range productList {
		isGo := false
		if req.Type == "paid" && product.ProductType == ProductTypeShop {
			isGo = true
		} else if req.Type == "free" && lo.Contains([]int{ProductTypeAD, ProductTypeSign}, product.ProductType) {
			isGo = true
		}
		if !isGo {
			continue
		}
		var img, desc, miningSpeed string
		var timeLeft int64
		hashrate, err := m.getProductHashrate(product.ProductID, product.ProductType, userId, product.StartTime)
		if err != nil {
			return resp, err
		}
		powerInfo, err := m.getFreePower(userId)
		if product.ProductType != ProductTypeShop {
			if product.ProductType == ProductTypeAD {
				img = "https://images.aipersona.cloud/public/image/46a71eb1-2618-47ac-88d8-effe2da91a61.png"
				if appId == 126 {
					img = "https://images.aipersona.cloud/public/image/60895919-205b-421b-a625-65c7d2a67c7d.png"
				}
				if appId == 128 {
					img = "https://images.aipersona.cloud/public/image/ae5a32ce-6b4d-47a7-a480-69684c5e774f.png"
				}

				if appId == 130 {
					img = "https://images.aipersona.cloud/public/image/f773ef0d-8471-4677-8f62-ae2f02a44159.png"
				}

				if appId == 131 {
					img = "https://images.aipersona.cloud/public/image/49f7c0d0-df5c-4ea3-8a6e-ed84d7588246.png"
				}

			} else if product.ProductType == ProductTypeSign {
				img = "https://images.aipersona.cloud/public/image/89f0a832-f829-4324-b07d-f8e280ccdfd6.png"
				if appId == 126 {
					img = "https://images.aipersona.cloud/public/image/bf37b75b-08c9-4a13-b65e-5faf30f8fdc5.png"
				}
				if appId == 128 {
					img = "https://images.aipersona.cloud/public/image/ae5a32ce-6b4d-47a7-a480-69684c5e774f.png"
				}

				if appId == 130 {
					img = "https://images.aipersona.cloud/public/image/f773ef0d-8471-4677-8f62-ae2f02a44159.png"
				}

				if appId == 131 {
					img = "https://images.aipersona.cloud/public/image/49f7c0d0-df5c-4ea3-8a6e-ed84d7588246.png"
				}
			}
			desc = "Daily rewards"
			timeLeft = product.EndTime.Unix()
			hashrate = hashrate * (1 + powerInfo.Total/100)
			rate, miningSpeedUnit := m.powerHash(uint64(hashrate), "H/s")
			miningSpeed = fmt.Sprintf("%s%s", rate, miningSpeedUnit)
		} else {
			desc = "Paid"
			data, err := m.getProductInfo(product.ProductID)
			if err != nil {
				return resp, err
			}
			img = fmt.Sprintf("%s%s", global.CONFIG.AwsS3.BaseURL, data.Product.GetImg(0))
			rate, miningSpeedUnit := m.powerHash(uint64(hashrate), "H/s")
			miningSpeed = fmt.Sprintf("%s%s", rate, miningSpeedUnit)
		}
		resp.List = append(resp.List, reqBtc.NFTDetail{
			Id:          product.ID,
			Img:         img,
			Desc:        desc,
			MiningSpeed: miningSpeed,
			TimeLeft:    timeLeft,
		})
	}
	return resp, nil
}

func (m *BtcService) ProductDetails(c *gin.Context) (interface{}, error) {
	var resp reqBtc.ProductDetailResp
	resp.Name = "Active Computing Power"

	data, err := m.GetRateByUser(c)
	if err != nil {
		return nil, err
	}
	resp.MiningSpeed = data.MiningSpeed
	resp.MiningSpeedUnit = data.MiningSpeedUnit
	var list []*ProductOther
	appId := utils.GetAppID(c)
	productList := m.getProductList(appId, nil)
	for _, v := range productList {
		switch v.Category {
		case 0:
			list = append(list, v)
		}
	}

	for _, v := range list {
		hashrate, unit := m.powerHash(v.Hashrate, "Gh/s")
		offer := ""
		if !v.Product.OriginPrice.IsZero() {
			// 定义现价和原价
			// 计算百分比变化: 1 - (现价 / 原价)
			percentageChange := decimal.NewFromInt(1).Sub(v.Product.USDPrice.Div(v.Product.OriginPrice)).Mul(decimal.NewFromInt(100))
			// 保留 1 位小数
			percentageChange = percentageChange.Round(1)
			offer = fmt.Sprintf("%.1f%%", percentageChange.InexactFloat64())
		}
		isFirstBuy, err := m.isFirstBuy(c, int(v.Product.Id))
		if err != nil {
			return nil, err
		}
		isBuy := !isFirstBuy
		buyDiscount := "Buy 1,Get 1 free"
		if isBuy {
			buyDiscount = ""
		}
		resp.ProductList = append(resp.ProductList, reqBtc.ProductDetail{
			GooID:               v.Product.GooID,
			IOSID:               v.Product.IOSID,
			Name:                v.Product.Name,
			MiningSpeed:         hashrate,
			MiningSpeedUnit:     unit,
			ID:                  int(v.Product.Id),
			Img:                 fmt.Sprintf("%s%s", global.CONFIG.AwsS3.BaseURL, v.Product.GetImg(0)),
			Description:         v.Product.Description,
			Bonus:               offer,
			APR:                 fmt.Sprintf("%s%%", v.Apr),
			OriginPrice:         v.Product.OriginPrice.String(),
			USDPrice:            v.Product.USDPrice.String(),
			IsBuy:               isBuy,
			FreeCoefficient:     fmt.Sprintf("%.1f%%", v.Ratio),
			FreeCoefficientDesc: "Free computing power",
			BuyDiscount:         buyDiscount,
		})
	}
	return resp, nil
}
