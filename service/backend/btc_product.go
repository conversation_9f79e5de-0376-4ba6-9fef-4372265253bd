package backend

import (
	"aimsg-server/config"
	"aimsg-server/global"
	reqBtc "aimsg-server/model/backend/request/btc"
	"aimsg-server/utils"
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"git.costnovel.com/center/middleware-sdk/thinkdata"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

type ProductExtraData struct {
	Key   string `json:"key"`
	Value string `json:"value"`
	Desc  string `json:"desc"`
}

type ProductOther struct {
	Product                *model.Product                `json:"product"`
	ProductType            int                           `json:"product_type"` // 0永久 1 24点消失
	Category               int                           `json:"category"`     // 1首页礼包 0商品
	Hashrate               uint64                        `json:"hashrate"`     // 算力
	Ratio                  float64                       `json:"ratio"`        // 系数
	Apr                    string                        `json:"apr"`          // apr
	Onhome                 int                           `json:"onhome"`       // 首页权重排序 从大到小
	BtcProductSubscription *model.BtcProductSubscription `json:"btc_product_subscription"`
}

const (
	ProductTypeShop    = 0
	ProductTypeAD      = 1
	ProductTypeSign    = 2
	FirstBuyProductSet = "ai:btc:first:buy1:%d"
	FirstBuyProductKey = "ai:btc:first:buy:%d:userid:%s"

	ADHashrate = "5.6"

	Sign1 = "1.2"
	Sign2 = "1.6"
	Sign3 = "2.2"
	Sign4 = "3.1"
	Sign5 = "5.0"
	Sign6 = "6.4"
	Sign7 = "8"
)

type PowerItem struct {
	Ratio float64   `json:"ratio"`
	Time  time.Time `json:"time"`
}
type Power struct {
	Items []*PowerItem `json:"items"`
	Total float64      `json:"total"`
}

func (m *BtcService) getSingHashrate(day int) string {
	switch day {
	case 1:
		return Sign1
	case 2:
		return Sign2
	case 3:
		return Sign3
	case 4:
		return Sign4
	case 5:
		return Sign5
	case 6:
		return Sign6
	case 7:
		return Sign7
	}
	return ""
}

func (m *BtcService) IsUserHashRateFix(u *model.DigitalUser) bool {
	if u == nil {
		return false
	}
	//双号不用
	if u.Id%2 == 0 {
		return false
	}
	// 注册时间在testRegisterFrom之后的用户
	testRegisterFrom := global.CONFIG.ABTest.GetFixHashRateRegisterFrom()
	if testRegisterFrom > 0 && u.CreatedAt.After(time.Unix(testRegisterFrom, 0)) {
		return true
	}
	return false
}

func (m *BtcService) IsUserIdHashRateFix(uid uint) bool {
	//双号不用
	if uid%2 == 0 {
		return false
	}
	var userService DigitalUserService
	u, err := userService.GetById(context.Background(), uid)
	if err != nil || u == nil {
		global.LOG.Error("isUserHashRateFix", zap.Error(err), zap.Uint("uid", uid))
		return false
	}
	return m.IsUserHashRateFix(u)
}

func (m *BtcService) fixHashRate(hashRate float64, productStartAt time.Time, uid uint) float64 {
	if productStartAt.IsZero() {
		global.LOG.Error("fixHashRate", zap.Error(errors.New("productStartAt is zero")), zap.Uint("uid", uid))
		return hashRate
	}
	diff := int(time.Since(productStartAt).Hours() / 24)
	fix := float64(config.GetBTCConfigsHashRateFix())
	fixHash := hashRate / (1.0 + fix*float64(diff))
	global.LOG.Debug("fixHashRate", zap.Float64("hashRate", hashRate), zap.Float64("fixHash", fixHash), zap.Int("diff", diff), zap.Uint("uid", uid))
	return fixHash
}

func (m *BtcService) getProductHashrate(productID, productType int, uid uint, productStartAt time.Time, fixHash bool) (value float64, err error) {

	switch productType {
	case ProductTypeShop:
		info, err := m.getProductInfo(productID)
		if err != nil {
			return 0, err
		}
		//对新注册用户进行50%算力衰减ab测
		hashRate := float64(info.Hashrate)
		if fixHash && m.IsUserIdHashRateFix(uid) {
			hashRate = m.fixHashRate(hashRate, productStartAt, uid)
		}
		value, err = m.convertHashrate(hashRate, "GH/s")
		if err != nil {
			return 0, err
		}
		return value, nil
	case ProductTypeAD:
		adRate, _ := strconv.ParseFloat(ADHashrate, 64)
		value, err = m.convertHashrate(adRate, "GH/s")
		if err != nil {
			return 0, err
		}
		return value, nil
	case ProductTypeSign:
		signRate, _ := strconv.ParseFloat(m.getSingHashrate(productID), 64)
		value, err = m.convertHashrate(signRate, "GH/s")
		if err != nil {
			return 0, err
		}
		return value, nil
	}
	return value, nil
}

// 获取用户的系数和时间
func (m *BtcService) getFreePower(userId uint) (resp Power, err error) {
	// 获取全部的产品id
	var (
		userService DigitalUserService
		userInfo    *model.DigitalUser
	)
	// 获取用户信息
	if userInfo, err = userService.GetById(context.Background(), userId); err != nil {
		return
	}
	productList := m.getProductList(userInfo.AppID, nil)
	c := context.Background()
	resp = Power{}
	for _, info := range productList {
		isMember := global.REDIS.SIsMember(c, fmt.Sprintf(FirstBuyProductSet, info.Product.Id), userId).Val()
		if !isMember {
			continue
		}
		epochStr := global.REDIS.Get(c, fmt.Sprintf(FirstBuyProductKey, info.Product.Id, userId)).Val()
		if epochStr == "" {
			continue
		}
		epoch, _ := strconv.ParseInt(epochStr, 10, 64)

		// 使用 time.Unix 函数将时间戳转换为 time.Time
		t := time.Unix(epoch, 0)
		resp.Items = append(resp.Items, &PowerItem{
			Ratio: info.Ratio,
			Time:  t,
		})
		resp.Total += info.Ratio

	}
	return
}

// 是否首次购买
func (m *BtcService) isFirstBuy(c *gin.Context, productId int) (bool, error) {
	userId := utils.GetDigitalUserID(c)
	val, err := global.REDIS.SIsMember(c, fmt.Sprintf(FirstBuyProductSet, productId), userId).Result()
	if err != nil && err != redis.Nil {
		return false, err
	}
	if err == redis.Nil {
		return true, nil
	}
	if val == true {
		return false, nil
	}
	return true, nil
}

func (m *BtcService) firstBuySet(c *gin.Context, productId int) error {
	userId := utils.GetDigitalUserID(c)
	err := global.REDIS.SAdd(c, fmt.Sprintf(FirstBuyProductSet, productId), userId, 0).Err()
	if err != nil {
		return err
	}
	global.REDIS.Set(c, fmt.Sprintf(FirstBuyProductKey, productId, userId), time.Now().Unix(), 0)
	return nil
}

func (m *BtcService) SelectNetwork(c *gin.Context) (reqBtc.SelectNetwork, error) {
	return reqBtc.SelectNetwork{
		List: []reqBtc.SelectNetworkItem{
			{
				Id:          1,
				Network:     "Lightning",
				IsRecommend: true,
			},
		},
	}, nil
}

// 获取用户当前的权益
func (m *BtcService) getUserProduct(c *gin.Context) (resp []*ProductOther, err error) {
	userId := utils.GetDigitalUserID(c)
	var list []*model.BtcProductSubscription
	err = global.DB.Where("user_id = ?", userId).
		Not("withdrawal_time >= end_time AND product_type = ?", 1).
		Find(&list).Error
	if err != nil {
		return
	}
	productIds := lo.Map(list, func(p *model.BtcProductSubscription, index int) int {
		return p.ProductID
	})
	uniqueProductIds := lo.Uniq(productIds)
	// 根据产品id查询查询信息
	appId := utils.GetAppID(c)
	productList := m.getProductList(appId, uniqueProductIds)
	productMap := make(map[uint]*ProductOther)
	for _, item := range productList {
		productMap[item.Product.Id] = item
	}
	for _, item := range list {
		if _, ok := productMap[uint(item.ProductID)]; !ok {
			continue
		}
		resp = append(resp, &ProductOther{
			Product:                productMap[uint(item.ProductID)].Product,
			ProductType:            productMap[uint(item.ProductID)].ProductType,
			Hashrate:               productMap[uint(item.ProductID)].Hashrate,
			BtcProductSubscription: item,
		})
	}
	return
}

// 订单结束后 根据商品id发放 权益 ， todo zwj 签到每天一次，广告每天30次
func (m *BtcService) orderAfter(c *gin.Context, digitalUserID, productId, productType int, current time.Time) (err error) {
	global.LOG.Info("CallBackFromPayCenter orderAfter start", zap.Any("productId", productId), zap.Any("productType", productType))
	var record model.BtcProductSubscription
	userId := utils.GetDigitalUserID(c)
	if userId == 0 {
		userId = uint(digitalUserID)
	}
	lockKey := fmt.Sprintf("ai:btc:orderAfter:userid:%d:productid:%d", userId, productId)
	isLock := global.REDIS.SetNX(c, lockKey, time.Now().Unix(), time.Second*5).Val()
	if isLock == false {
		return errors.New("权益发放失败")
	}
	defer global.REDIS.Del(c, lockKey)
	var endTime time.Time
	var isFirstBuy bool
	if lo.Contains([]int{ProductTypeSign, ProductTypeAD}, productType) {
		endTime = time.Date(current.Year(), current.Month(), current.Day(), 0, 0, 0, 0, current.Location()).Add(time.Hour * 24)
	} else {
		isFirstBuy, err = m.isFirstBuy(c, productId)
		if err != nil {
			return err
		}
	}

	if isFirstBuy { // 多买一个赠送
		record = model.BtcProductSubscription{
			UserID:      userId,
			ProductID:   productId,
			StartTime:   current,
			EndTime:     endTime,
			ProductType: productType,
		}
		err = global.DB.Create(&record).Error
		if err != nil {
			global.LOG.Error("CallBackFromPayCenter Create btc product subscription fail", zap.Error(err))
			return
		}
	}
	record = model.BtcProductSubscription{
		UserID:      userId,
		ProductID:   productId,
		StartTime:   current,
		EndTime:     endTime,
		ProductType: productType,
	}
	err = global.DB.Create(&record).Error
	if err != nil {
		global.LOG.Error("CallBackFromPayCenter Create btc product subscription fail", zap.Error(err))
		return
	}
	global.REDIS.Del(c, fmt.Sprintf(CacheGetProductToday, userId))
	if productType == 0 {
		err = m.firstBuySet(c, productId)
		if err != nil {
			global.LOG.Error("CallBackFromPayCenter FirstBuySet fail", zap.Error(err))
			return
		}
	}
	m.orderReport(c, userId, productId, productType)
	global.LOG.Info("CallBackFromPayCenter orderAfter end", zap.Any("productId", productId), zap.Any("userId", userId))
	return
}

func (m *BtcService) orderReport(c *gin.Context, digitalUserID uint, productId, productType int) {
	// 获取用户信息
	var (
		err error
	)
	userId := utils.GetDigitalUserID(c)
	if userId == 0 {
		userId = digitalUserID
	}
	dataMap := make(map[string]interface{})
	global.LOG.Info("CallBackFromPayCenter orderReport start", zap.Any("productId", productId), zap.Any("userId", userId))

	// 实时算力
	hashrateData, err := m.GetRateByUser(c, false)
	if err != nil {
		return
	}
	dataMap["active_mining_power"] = fmt.Sprintf("%s%s", hashrateData.MiningSpeed, hashrateData.MiningSpeedUnit)
	// 实时btc
	data, err := m.GetTotalByUser(c)
	if err != nil {
		global.LOG.Error("CallBackFromPayCenter GetTotalByUser fail", zap.Error(err))
		return
	}
	dataMap["taotal_assets_num"] = data.Total
	dataMap["event_tag"] = "active_mining_power_get"

	// 算力增加数量
	hashrate, err := m.getProductHashrate(productId, productType, userId, time.Now(), true)
	if err != nil {
		global.LOG.Error("CallBackFromPayCenter getProductHashrate fail", zap.Error(err))
		return
	}
	hashrateStr, hashrateUnit := m.powerHash(uint64(hashrate), "H/s")
	dataMap["mining_power_get_num"] = fmt.Sprintf("%s%s", hashrateStr, hashrateUnit)
	// 算力系数
	userHashrate, err := m.getFreePower(userId)
	if err != nil {
		global.LOG.Error("CallBackFromPayCenter getFreePower fail", zap.Error(err))
		return
	}
	dataMap["rate"] = userHashrate.Total

	// source
	source := ""
	switch productType {
	case ProductTypeSign:
		source = "home_free_sighin"
	case ProductTypeAD:
		source = "home_free_mining_power_ad_task"
	case ProductTypeShop:
		productInfo, err := m.getProductInfo(productId)
		if err != nil {
			return
		}
		if productInfo.Category == 1 { // 首页礼包
			source = "home_welcome_mining_power_package"
		} else if productInfo.Onhome != 0 { // 算力包推荐
			source = "home_mining_power_package_recommend"
		} else {
			source = "market_mining_power_market"
		}
		dataMap["amount"] = productInfo.Product.USDPrice
	}

	dataMap["source"] = source
	dataMap["product_id"] = productId
	isFirstBy, err := m.isFirstBuy(c, productId)
	if err != nil {
		global.LOG.Error("CallBackFromPayCenter isFirstBuy fail", zap.Error(err))
		return
	}

	// 是否首次购买
	dataMap["first_purchase"] = 2
	if isFirstBy {
		dataMap["first_purchase"] = 1
	}
	appId := utils.GetAppID(c)
	appInfo, appInfoExist := global.AppMap[appId]
	if !appInfoExist {
		global.LOG.Error("CallBackFromPayCenter appInfoExist fail", zap.Any("appId", appId))
		return
	}
	sendTdData := thinkdata.SendThinkData{
		IsDebug:    global.CONFIG.System.Env != "public",
		ServerURL:  "https://ssdata.bitcoinmining.studio",
		AppId:      appInfo.ThinkData.Data().AppId,
		EventName:  "server_event",
		AccountId:  fmt.Sprintf("%d", userId),
		DistinctId: "",
		Properties: dataMap,
	}
	if err := thinkdata.SendData(sendTdData); err != nil {
		global.LOG.Error("CallBackFromPayCenter TdPointerTwo fail", zap.Error(err))
		return
	}
}

func (m *BtcService) getProductInfo(productId int) (resp *ProductOther, err error) {
	// 获取App信息
	for appId := range global.AppInfoTypeListMap {
		for _, v := range global.AppInfoTypeListMap[appId] {
			for _, product := range v.ProductList {
				if int(product.Id) == productId {
					resp = m.getProductExtraData(product)
					return
				}
			}
		}
	}

	err = errors.New("商品不存在")
	return
}

func (m *BtcService) getProductList(appId uint, ids []int) (resp []*ProductOther) {
	for _, v := range global.AppInfoTypeListMap[appId] {
		for _, product := range v.ProductList {
			if ids != nil {
				if lo.Contains(ids, int(product.Id)) {
					resp = append(resp, m.getProductExtraData(product))
				}
			} else {
				resp = append(resp, m.getProductExtraData(product))
			}

		}
	}
	return
}

// 获取额外属性的字段
func (m *BtcService) getProductExtraData(product *model.Product) *ProductOther {
	productType, _ := strconv.Atoi(product.GetExtraData("product_type"))
	category, _ := strconv.Atoi(product.GetExtraData("category"))
	hashrateStr := product.GetExtraData("hashrate")
	hashrateValue, _ := strconv.ParseFloat(hashrateStr, 64)
	hashrate := uint64(hashrateValue)
	ratio, _ := strconv.ParseFloat(product.GetExtraData("ratio"), 64)
	onhome, _ := strconv.Atoi(product.GetExtraData("onhome"))
	apr := product.GetExtraData("apr")
	resp := &ProductOther{
		Product:     product,
		ProductType: productType,
		Hashrate:    hashrate,
		Category:    category,
		Ratio:       ratio,
		Onhome:      onhome,
		Apr:         apr,
	}
	return resp
}

func (m *BtcService) ADDetail(c *gin.Context) (*reqBtc.ComputingPowerHeader, error) {
	userId := utils.GetDigitalUserID(c)
	power, err := m.getFreePower(userId)
	if err != nil {
		return nil, err
	}
	ratio := fmt.Sprintf("%.1f%%", power.Total)
	coefficientImg := "https://images.aipersona.cloud/public/image/64573bae-1d6e-44a7-9a59-9605750678ef.png"
	adHalder := &AdHandler{
		ctx:    c,
		UserId: userId,
		now:    time.Now(),
	}
	adNum, err := adHalder.ADNum()
	if err != nil {
		return nil, err
	}
	data := reqBtc.ComputingPowerHeader{
		ID:             1,
		Name:           "5.6Gh/s",
		Img:            "https://images.aipersona.cloud/public/image/ad7000ce-f9a4-4953-a8e3-f2622ebf6f66.png",
		Desc:           "24 hours of validity",
		Coefficient:    ratio,
		CoefficientImg: coefficientImg,
		Used:           adNum,
		Total:          ADMax,
		Type:           ProductTypeAD,
	}
	appId := utils.GetAppID(c)
	if appId == 126 {
		data.Img = "https://images.aipersona.cloud/public/image/60895919-205b-421b-a625-65c7d2a67c7d.png"
	}
	if appId == 128 {
		data.Img = "https://images.aipersona.cloud/public/image/bf8c9b6b-5046-45a2-b28c-af18c8986ed9.png"
	}

	if appId == 131 {
		data.Img = "https://images.aipersona.cloud/public/image/0e7634b0-21b9-47cf-a639-1bc49e1c0431.png"
	}

	return &data, nil
}

func (m *BtcService) SignDetail(c *gin.Context) (resp *reqBtc.SignDetailResp, err error) {
	userId := utils.GetDigitalUserID(c)
	curT := time.Now()
	weekday := int(curT.Weekday())
	isToday := func(nowDay int) int {
		if nowDay == weekday {
			return 1
		}
		return 0
	}
	dayName := func(nowDay int) string {
		if nowDay == weekday {
			return "Available"
		}
		return fmt.Sprintf("Day%d", nowDay)
	}
	singDayMap := map[int]int{}
	now := curT
	year, month, day := now.Date()
	location := now.Location()

	// 计算当前日期是星期几（星期天=0，星期一=1，...，星期六=6）
	weekdayNum := int(now.Weekday())
	// 计算本周的星期一
	startOfWeek := time.Date(year, month, day-weekdayNum+1, 0, 0, 0, 0, location)
	// 创建数组存储一周的日期
	var weekDates []time.Time
	for i := 0; i < 7; i++ {
		weekDates = append(weekDates, startOfWeek.AddDate(0, 0, i))
	}
	singHalder := &SignHandler{
		ctx:    c,
		UserId: userId,
		now:    curT,
	}
	i := 1
	for _, v := range weekDates {
		signStatus, err := singHalder.SignStatus(v)
		if err != nil {
			return nil, err
		}
		singDayMap[i] = signStatus
		i++
	}

	resp = &reqBtc.SignDetailResp{}
	resp.SignList = []reqBtc.SignDetail{
		{
			ID:      1,
			Title:   dayName(1),
			Content: fmt.Sprintf("%sGh/s", m.getSingHashrate(1)),
			IsToday: isToday(1),
			Status:  singDayMap[1],
		},
		{
			ID:      2,
			Title:   dayName(2),
			Content: fmt.Sprintf("%sGh/s", m.getSingHashrate(2)),
			IsToday: isToday(2),
			Status:  singDayMap[2],
		},
		{
			ID:      3,
			Title:   dayName(3),
			Content: fmt.Sprintf("%sGh/s", m.getSingHashrate(3)),
			IsToday: isToday(3),
			Status:  singDayMap[3],
		},
		{
			ID:      4,
			Title:   dayName(4),
			Content: fmt.Sprintf("%sGh/s", m.getSingHashrate(4)),
			IsToday: isToday(4),
			Status:  singDayMap[4],
		},
		{
			ID:      5,
			Title:   dayName(5),
			Content: fmt.Sprintf("%sGh/s", m.getSingHashrate(5)),
			IsToday: isToday(5),
			Status:  singDayMap[5],
		},
		{
			ID:      6,
			Title:   dayName(6),
			Content: fmt.Sprintf("%sGh/s", m.getSingHashrate(6)),
			IsToday: isToday(6),
			Status:  singDayMap[6],
		},
		{
			ID:      7,
			Title:   dayName(7),
			Content: fmt.Sprintf("%sGh/s", m.getSingHashrate(7)),
			IsToday: isToday(7),
			Status:  singDayMap[7],
		},
	}
	return resp, nil
}

func (m *BtcService) UserInfo(c *gin.Context) (resp reqBtc.UserInfo, err error) {

	// btc 总额
	userId := utils.GetDigitalUserID(c)
	totalBtc, err := m.CashOutBtcNum(c, userId)
	if err != nil {
		return resp, err
	}
	resp.Total = fmt.Sprintf("%.16f", totalBtc)
	hashrateData, errr := m.GetRateByUser(c, false)
	if errr != nil {
		return resp, errr
	}
	resp.MiningSpeed = hashrateData.MiningSpeed
	resp.MiningSpeedUnit = hashrateData.MiningSpeedUnit
	return resp, nil
}

func (m *BtcService) GiftPack(c *gin.Context) (resp *reqBtc.ProductDetail, err error) {
	resp = &reqBtc.ProductDetail{}
	resp.Name = "Active Computing Power"

	var giftPack *ProductOther
	appId := utils.GetAppID(c)
	productList := m.getProductList(appId, nil)
	for _, v := range productList {
		switch v.Category {
		case 1:
			giftPack = v
		}
	}

	if giftPack == nil {
		return nil, nil
	}

	hashrate, unit := m.powerHash(giftPack.Hashrate, "Gh/s")
	offer := ""
	if !giftPack.Product.OriginPrice.IsZero() {
		// 定义现价和原价
		// 计算百分比变化: 1 - (现价 / 原价)
		percentageChange := decimal.NewFromInt(1).Sub(giftPack.Product.USDPrice.Div(giftPack.Product.OriginPrice)).Mul(decimal.NewFromInt(100))
		// 保留 1 位小数
		percentageChange = percentageChange.Round(1)
		offer = fmt.Sprintf("%.1f%%", percentageChange.InexactFloat64())
	}
	isFirstBuy, err := m.isFirstBuy(c, int(giftPack.Product.Id))
	if err != nil {
		return nil, err
	}
	if !isFirstBuy {
		return nil, nil
	}
	buyDiscount := "Buy 1,Get 1 free"
	resp = &reqBtc.ProductDetail{
		GooID:               giftPack.Product.GooID,
		IOSID:               giftPack.Product.IOSID,
		Name:                giftPack.Product.Name,
		MiningSpeed:         hashrate,
		MiningSpeedUnit:     unit,
		ID:                  int(giftPack.Product.Id),
		Img:                 fmt.Sprintf("%s%s", global.CONFIG.AwsS3.BaseURL, giftPack.Product.GetImg(0)),
		Description:         giftPack.Product.Description,
		Bonus:               offer,
		APR:                 fmt.Sprintf("%s%%", giftPack.Apr),
		OriginPrice:         giftPack.Product.OriginPrice.String(),
		USDPrice:            giftPack.Product.USDPrice.String(),
		IsBuy:               false,
		FreeCoefficient:     fmt.Sprintf("%.1f%%", giftPack.Ratio),
		FreeCoefficientDesc: "Free computing power",
		BuyDiscount:         buyDiscount,
	}
	return resp, nil
}
