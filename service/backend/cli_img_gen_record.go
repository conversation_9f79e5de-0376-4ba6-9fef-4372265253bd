package backend

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/utils"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"time"
)

func (m *ImgGenRecordService) RemainingGenCount(c *gin.Context, reqData *request.RemainingGenCountReq) (resMap gin.H, err error) {
	var (
		count             int64
		remainingGenCount int64
		userInfo          *model.DigitalUser
		userSer           DigitalUserService
		imgToVideoSer     ImgToVideoService
		userId            = utils.GetDigitalUserID(c)
		appId             = utils.GetAppID(c)
		appVersion        = c.GetHeader("App-Version")
	)
	// 查询用户信息
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		return
	}
	isSub := userInfo.IsSub()
	verCompare391 := utils.CompareVersions(appVersion, "3.9.1")
	if verCompare391 == 1 && appId == 4 && (userInfo.ChatVipStatus == model.SubStatusIng || userInfo.ContentVipStatus == model.SubStatusIng) {
		isSub = true
	}
	appInfo := global.AppMap[appId]
	// 查询生成记录数量
	switch reqData.Source {
	case "img_to_video":
		genVideoCount := imgToVideoSer.UserDayGenCount(c, userId)
		if isSub {
			// 剩余生成次数
			remainingGenCount = int64(appInfo.DetailConfig.Data().SubImgToVideoNum - genVideoCount)
		} else {
			// 剩余生成次数
			remainingGenCount = int64(appInfo.DetailConfig.Data().FreeImgToVideoNum - genVideoCount)
		}
	default:
		if isSub {
			remainingGenCount = 99999
		} else {
			if count, err = m.SearchCount(&model.ImgGenRecord{DigitalUserId: userId}); err != nil {
				return
			}
			// 剩余生成次数
			remainingGenCount = int64(appInfo.DetailConfig.Data().IMG_GEN_FREE) - count
		}
	}
	resMap = gin.H{
		"remaining_gen_count": remainingGenCount,
	}
	return
}

func (m *ImgGenRecordService) GenHistory(c *gin.Context, reqData req.ImgGenRecordSearch) (resMap gin.H, err error) {
	var (
		total             int64
		imgGenRecords     []*model.ImgGenRecord
		imgGenRecordItems []response.ImgGenRecordItem
		userId            = utils.GetDigitalUserID(c)
		appId             = utils.GetAppID(c)
	)
	reqData.DigitalUserId = userId
	if imgGenRecords, total, err = m.GetList(reqData); err != nil {
		return
	}
	if len(imgGenRecords) == 0 {
		return
	}
	for _, imgGenRecord := range imgGenRecords {
		imgRecord, imgRecordExist := global.ImgTemplateRecordMap[imgGenRecord.ImgRecordId]
		if !imgRecordExist {
			continue
		}
		imgCategory, imgCategoryExist := global.ImgTemplateCategoryMap[imgRecord.CategoryId]
		if !imgCategoryExist {
			continue
		}

		imgItem := response.ImgGenRecordItem{
			Id:              imgGenRecord.Id,
			ImgRecordId:     imgGenRecord.ImgRecordId,
			ImgRecordName:   imgRecord.Name,
			ImgCategoryName: imgCategory.Name,
			DigitalUserId:   imgGenRecord.DigitalUserId,
			UserImgUrl:      imgGenRecord.UserImgUrl,
			UserImgFullUrl:  utils.S3Url(imgGenRecord.UserImgUrl, appId),
			ResImgUrl:       imgGenRecord.ResImgUrl,
			GenStatus:       imgGenRecord.GenStatus,
			AiId:            imgGenRecord.AiId,
			AiRoleId:        imgGenRecord.AiRoleId,
		}
		if imgGenRecord.GenStatus == model.GenStatusSuccess {
			imgItem.ResImgFullUrl = utils.S3Url(imgGenRecord.ResImgUrl, appId)
		}
		imgGenRecordItems = append(imgGenRecordItems, imgItem)
	}
	resMap = gin.H{
		"list":  imgGenRecordItems,
		"total": total,
	}
	return
}

func (m *ImgGenRecordService) GenImgTask(c *gin.Context, reqData *request.GenImgTaskReq) (imgGenRecordRes response.ImgGenRecordItem, err error) {
	var (
		count      int64
		userInfo   *model.DigitalUser
		userSer    DigitalUserService
		userId     = utils.GetDigitalUserID(c)
		appId      = utils.GetAppID(c)
		appVersion = c.GetHeader("App-Version")
	)
	appInfo := global.AppMap[appId]
	// 查询用户信息
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		return
	}

	isSub := userInfo.IsSub()
	verCompare391 := utils.CompareVersions(appVersion, "3.9.1")
	if verCompare391 == 1 && appId == 4 && (userInfo.ChatVipStatus == model.SubStatusIng || userInfo.ContentVipStatus == model.SubStatusIng) {
		isSub = true
	}
	if !isSub {
		// 查询生成记录数量
		if count, err = m.SearchCount(&model.ImgGenRecord{DigitalUserId: userId}); err != nil {
			return
		}
		if count >= int64(appInfo.DetailConfig.Data().IMG_GEN_FREE) {
			err = errs.RequireSubErr
			return
		}
	}
	imgRecord, imgRecordExist := global.ImgTemplateRecordMap[reqData.ImgId]
	if !imgRecordExist {
		err = fmt.Errorf("img record not exist")
		return
	}
	imgCategory, imgCategoryExist := global.ImgTemplateCategoryMap[imgRecord.CategoryId]
	if !imgCategoryExist {
		err = fmt.Errorf("img category not exist")
		return
	}
	imgGenRecord := model.ImgGenRecord{
		ImgRecordId:   reqData.ImgId,
		DigitalUserId: userId,
		UserImgUrl:    reqData.UserImg,
		GenStatus:     model.GenStatusGenerating,
	}
	if err = m.Create(&imgGenRecord); err != nil {
		return
	}
	// 添加到redis里
	genReq := model.ImgToImgReq{
		TaskType: 2,
		TaskId:   imgGenRecord.Id,
	}
	genReqBytes, _ := json.Marshal(genReq)
	genReqStr := string(genReqBytes)
	score := time.Now().Unix()
	if err = global.REDIS.ZAdd(c, global.IMG_TO_IMG_TRAIN_KEY, redis.Z{Score: float64(score), Member: genReqStr}).Err(); err != nil {
		return
	}
	imgGenRecordRes = response.ImgGenRecordItem{
		Id:              imgGenRecord.Id,
		ImgRecordId:     imgGenRecord.ImgRecordId,
		ImgRecordName:   imgRecord.Name,
		ImgCategoryName: imgCategory.Name,
		DigitalUserId:   imgGenRecord.DigitalUserId,
		UserImgUrl:      imgGenRecord.UserImgUrl,
		UserImgFullUrl:  utils.S3Url(imgGenRecord.UserImgUrl, appId),
		ResImgUrl:       "",
		GenStatus:       imgGenRecord.GenStatus,
		AiId:            imgGenRecord.AiId,
		AiRoleId:        imgGenRecord.AiRoleId,
	}
	return
}
