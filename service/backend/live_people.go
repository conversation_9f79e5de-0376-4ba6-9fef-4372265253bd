package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
	"time"
)

type LivePeopleService struct{}

func (m *LivePeopleService) CopyInfo(sourceAppId, targetAppId uint) (err error) {
	var (
		sourceInfos []*model.LivePeople
		newInfos    []*model.LivePeople
		nowTime     = time.Now()
	)
	sourceInfos, err = m.FindByAppId(sourceAppId)
	if err != nil {
		return
	}
	for _, sourceInfo := range sourceInfos {
		sourceInfo.Id = 0
		sourceInfo.CreatedAt = nowTime
		sourceInfo.UpdatedAt = nowTime
		sourceInfo.AppID = targetAppId
		newInfos = append(newInfos, sourceInfo)
	}
	if len(newInfos) == 0 {
		return
	}
	err = global.DB.Model(&model.LivePeople{}).Create(&newInfos).Error
	return
}

func (m *LivePeopleService) FindByAppId(appId uint) (resList []*model.LivePeople, err error) {
	err = global.DB.Model(&model.LivePeople{}).Where("app_id = ?", appId).Find(&resList).Error
	return
}

func (m *LivePeopleService) Create(ctx context.Context, reqData *model.LivePeople) (err error) {
	err = global.DB.Model(&model.LivePeople{}).Create(reqData).Error
	m.SyncAll()
	return
}

func (m *LivePeopleService) DeleteById(ctx context.Context, id uint) (err error) {
	err = global.DB.Model(&model.LivePeople{}).Where("id = ?", id).Delete(&model.LivePeople{}).Error
	m.SyncAll()
	return
}

func (m *LivePeopleService) DeleteByIds(ctx context.Context, reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.LivePeople{}).Where("id IN (?)", reqData.Ids).Delete(&model.LivePeople{}).Error
	m.SyncAll()
	return
}

func (m *LivePeopleService) Update(ctx context.Context, reqData *model.LivePeople) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return
	}
	err = global.DB.Model(&model.LivePeople{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	m.SyncAll()
	return
}

func (m *LivePeopleService) GetById(ctx context.Context, id uint) (res *model.LivePeople, err error) {
	err = global.DB.Model(&model.LivePeople{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *LivePeopleService) GetAll(ctx context.Context) (resList []*model.LivePeople, err error) {
	err = global.DB.Model(&model.LivePeople{}).Find(&resList).Error
	return
}

func (m *LivePeopleService) SyncAll() {
	var (
		err       error
		dbResList []*model.LivePeople
		allMap    = map[uint]*model.LivePeople{}
		ctx       = context.Background()
	)
	dbResList, err = m.GetAll(ctx)
	if err != nil {
		global.LOG.Error("SyncAll 失败", zap.Error(err))
		return
	}
	for _, item := range dbResList {
		allMap[item.Id] = item
	}
	global.LivePeopleMap = allMap
	global.LivePeopleList = dbResList
}

func (m *LivePeopleService) GetList(ctx context.Context, info req.LivePeopleSearch) (resList []*model.LivePeople, total int64, err error) {
	db := global.DB.Model(&model.LivePeople{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
