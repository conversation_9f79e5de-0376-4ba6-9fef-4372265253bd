package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/model/cli/td"
	"aimsg-server/utils"
	"fmt"
	"math/rand"
	"time"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	jutils "github.com/jaryf/go_utils/utils"
	"gorm.io/gorm"
)

func (m *FlowerConsumeRecordService) ReviewFlowers(c *gin.Context) (err error) {
	var (
		userInfo       *model.DigitalUser
		userService    DigitalUserService
		profileService DigitalUserProfileService
		userProfile    *model.DigitalUserProfile
		userId         = utils.GetDigitalUserID(c)
	)
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	// 获取userProfile信息
	if userProfile, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	// 获取App信息
	appId := utils.GetAppID(c)
	appInfo, exist := global.AppMap[appId]
	if !exist {
		err = fmt.Errorf("app(%d) not found", appId)
		return
	}
	floCount := appInfo.DetailConfig.Data().ReviewSendFlo

	err = global.DB.Transaction(func(tx *gorm.DB) (tErr error) {
		go TdPointer(userInfo, userProfile, td.ServerEvent, map[string]interface{}{
			"event_tag":         "coins_consume",
			"coins_consume_num": floCount,
			"source":            "rate_get",
		})
		var flowerAmountNow = userProfile.FlowerAmount + floCount
		// 创建FlowerConsumeRecord记录
		var fRecord = &model.FlowerConsumeRecord{
			DigitalUserId: userId,
			AiId:          0,
			AiRoleId:      "",
			ConsumeType:   model.ConsumeTypeReviewFlowers,
			RelationID:    fmt.Sprintf("%d", userId),
			OriginAmount:  userProfile.FlowerAmount,
			ConsumeAmount: floCount,
		}
		if tErr = tx.Model(&model.FlowerConsumeRecord{}).Create(fRecord).Error; tErr != nil {
			return
		}
		updateMap := map[string]interface{}{
			"review_state":  true,
			"flower_amount": flowerAmountNow,
		}
		db := tx.Model(&model.DigitalUserProfile{}).Where("id = ? AND flower_amount = ?", userProfile.Id, userProfile.FlowerAmount).Updates(updateMap)
		if tErr = db.Error; tErr != nil {
			return
		}
		if db.RowsAffected == 0 {
			tErr = fmt.Errorf("update userProfile failed")
			return
		}

		if userInfo.AppID == 16 {
			// 保存消费记录 用户费用记录要加回去
			var msgService MsgService
			_ = msgService.SaveVideoSimplifyConsumeRecord(userInfo.Id, floCount, model.VideoSimplifyConsumeTypeRatings, 0, 0)
		}
		return
	})
	return
}

func (m *FlowerConsumeRecordService) ReceiveFlowers(c *gin.Context) (res gin.H, err error) {
	var (
		userService    DigitalUserService
		profileService DigitalUserProfileService
		userInfo       *model.DigitalUser
		userProfile    *model.DigitalUserProfile
		userId         = utils.GetDigitalUserID(c)
		now            = time.Now()
		dayStr         = now.Format(time.DateOnly)
	)
	rKey := userService.SignFloRdsKey(userId)
	// 判断用户今天是否领取过了
	isMem := global.REDIS.SIsMember(c, rKey, dayStr).Val()
	if isMem {
		err = fmt.Errorf("already receive")
		return
	}

	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	isSVip := userInfo.IsSVip()
	isSub := userInfo.IsSub()
	isChatVip := userInfo.IsChatVip()
	isContentVip := userInfo.IsContentVip()
	// 获取userProfile信息
	if userProfile, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	// 获取签到的鲜花数
	dayInt := int(now.Weekday())
	rewardFlowersConfigIndex := global.WeekdayToRewardFlowersKeyMap[dayInt]
	floCount := global.RewardFlowersFreeUserConfig[rewardFlowersConfigIndex]
	if isSub || isSVip || isChatVip || isContentVip {
		floCount = global.RewardFlowersVipUserConfig[rewardFlowersConfigIndex]
	}

	// 如果是星期日，判断有没有签一周，赠送叠加奖励
	if dayInt == 0 {
		signCount := global.REDIS.SCard(c, rKey).Val()
		if signCount >= 6 {
			floCount += 10 // 连续签到一周的叠加奖励
		}
	}

	err = global.DB.Transaction(func(tx *gorm.DB) (tErr error) {
		if userInfo.DistinctID != "" {
			go TdPointer(userInfo, userProfile, td.ServerEvent, map[string]interface{}{
				"event_tag":         "coins_consume",
				"coins_consume_num": floCount,
				"source":            "signin_get",
			})
		}
		var flowerAmountNow = userProfile.FlowerAmount + floCount
		var signCountNow = userProfile.SignCount + 1
		// 创建FlowerConsumeRecord记录
		var fRecord = &model.FlowerConsumeRecord{
			DigitalUserId: userId,
			AiId:          0,
			AiRoleId:      "",
			ConsumeType:   model.ConsumeTypeSignFlowers,
			RelationID:    fmt.Sprintf("%d", userId),
			OriginAmount:  userProfile.FlowerAmount,
			ConsumeAmount: floCount,
		}
		if tErr = tx.Model(&model.FlowerConsumeRecord{}).Create(fRecord).Error; tErr != nil {
			return
		}
		updateMap := map[string]interface{}{
			"sign_count":    signCountNow,
			"flower_amount": flowerAmountNow,
		}
		db := tx.Model(&model.DigitalUserProfile{}).Where("id = ? AND flower_amount = ?", userProfile.Id, userProfile.FlowerAmount).Updates(updateMap)
		if tErr = db.Error; tErr != nil {
			return
		}
		if db.RowsAffected == 0 {
			tErr = fmt.Errorf("update userProfile failed")
			return
		}
		if tErr = global.REDIS.SAdd(c, rKey, dayStr).Err(); tErr != nil {
			return
		}
		if tErr = global.REDIS.Expire(c, rKey, time.Hour*24*7).Err(); tErr != nil {
			return
		}
		return
	})
	// 是否显示评价弹窗 需求文档 https://hdj2p0.axshare.com/#id=8rzbg1
	showReviewDialog := false
	if !userProfile.ReviewState {
		randomFloat := rand.Float64()
		if userInfo.IsSub() {
			if randomFloat <= global.SIGN_REVIEW_SUB_RATE {
				if userProfile.SignCount == 2 || userProfile.SignCount == 4 || userProfile.SignCount == 6 {
					showReviewDialog = true
				}
			}
		} else {
			if randomFloat <= global.SIGN_REVIEW_SUB_RATE {
				if userProfile.SignCount == 2 || userProfile.SignCount == 4 || userProfile.SignCount == 6 {
					showReviewDialog = true
				}
			}
		}
	}
	if !showReviewDialog {
		showReviewDialog = global.REDIS.SIsMember(c, global.REVIEW_TEST_KEY, fmt.Sprintf("%d", userId)).Val()
	}
	res = gin.H{
		"show_review_dialog": showReviewDialog,
	}
	return
}

func (m *FlowerConsumeRecordService) ReceiveFlowersCoins(c *gin.Context) (*response.ReceiveFlowersCoinsResp, error) {
	var (
		userService DigitalUserService
		userInfo    *model.DigitalUser
		userId      = utils.GetDigitalUserID(c)
		now         = time.Now()
		dayStr      = now.Format(time.DateOnly)
		err         error
	)
	rKey := userService.SignFloRdsKey(userId)
	// 判断用户今天是否领取过了
	isMem := global.REDIS.SIsMember(c, rKey, dayStr).Val()
	if isMem {
		return &response.ReceiveFlowersCoinsResp{
			Coins: 0,
		}, nil
	}
	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return nil, err
	}
	isSVip := userInfo.IsSVip()
	isSub := userInfo.IsSub()

	floCount := 0
	if isSub {
		floCount = 40
	}
	if isSVip {
		floCount = 160
	}
	return &response.ReceiveFlowersCoinsResp{
		Coins: floCount,
	}, nil
}

func (m *FlowerConsumeRecordService) ReceiveFlowersVideoSimplify(c *gin.Context) (res gin.H, err error) {
	var (
		userService    DigitalUserService
		profileService DigitalUserProfileService
		userInfo       *model.DigitalUser
		userProfile    *model.DigitalUserProfile
		userId         = utils.GetDigitalUserID(c)
		now            = time.Now()
		dayStr         = now.Format(time.DateOnly)
	)
	rKey := userService.SignFloRdsKey(userId)
	// 判断用户今天是否领取过了
	isMem := global.REDIS.SIsMember(c, rKey, dayStr).Val()
	if isMem {
		err = fmt.Errorf("already receive")
		return
	}

	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	isSVip := userInfo.IsSVip()
	isSub := userInfo.IsSub()
	// 获取userProfile信息
	if userProfile, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	if !isSVip && !isSub {
		return
	}

	floCount := 0
	if isSub {
		floCount = 40
	}
	if isSVip {
		floCount = 160
	}

	err = global.DB.Transaction(func(tx *gorm.DB) (tErr error) {
		if userInfo.DistinctID != "" {
			go TdPointer(userInfo, userProfile, td.ServerEvent, map[string]interface{}{
				"event_tag":         "coins_consume",
				"coins_consume_num": floCount,
				"source":            "signin_get",
			})
		}
		var flowerAmountNow = userProfile.FlowerAmount + floCount
		var signCountNow = userProfile.SignCount + 1
		// 创建FlowerConsumeRecord记录
		var fRecord = &model.FlowerConsumeRecord{
			DigitalUserId: userId,
			AiId:          0,
			AiRoleId:      "",
			ConsumeType:   model.ConsumeTypeSignFlowers,
			RelationID:    fmt.Sprintf("%d", userId),
			OriginAmount:  userProfile.FlowerAmount,
			ConsumeAmount: floCount,
		}
		if tErr = tx.Model(&model.FlowerConsumeRecord{}).Create(fRecord).Error; tErr != nil {
			return
		}
		updateMap := map[string]interface{}{
			"sign_count":    signCountNow,
			"flower_amount": flowerAmountNow,
		}
		db := tx.Model(&model.DigitalUserProfile{}).Where("id = ? AND flower_amount = ?", userProfile.Id, userProfile.FlowerAmount).Updates(updateMap)
		if tErr = db.Error; tErr != nil {
			return
		}

		if db.RowsAffected == 0 {
			tErr = fmt.Errorf("update userProfile failed")
			return
		}
		// 保存消费记录 用户费用记录要加回去
		var msgService MsgService
		_ = msgService.SaveVideoSimplifyConsumeRecord(userInfo.Id, floCount, model.VideoSimplifyConsumeTypeVipDays, 0, 0)

		if tErr = global.REDIS.SAdd(c, rKey, dayStr).Err(); tErr != nil {
			return
		}
		if tErr = global.REDIS.Expire(c, rKey, time.Hour*24*7).Err(); tErr != nil {
			return
		}
		return
	})
	return
}

func (m *FlowerConsumeRecordService) ReceiveAiRoleFlowers(c *gin.Context, reqData *request.AiRoleIdReq) (res response.ReceiveAiRoleFlowersRes, err error) {
	var (
		userProfile    *model.DigitalUserProfile
		profileService DigitalUserProfileService
		aiRoleInfo     *model.AiRole
		aiSer          AiRoleService
		userId         = utils.GetDigitalUserID(c)
	)
	// 获取userProfile信息
	if userProfile, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	// 获取AiRole信息
	if aiRoleInfo, err = aiSer.GetByRoleId(reqData.AiRoleId); err != nil {
		return
	}
	if aiRoleInfo.DeepFlo == 0 {
		return
	}

	// 从redis检测是否有领取过
	rdsKey := fmt.Sprintf("deeplink_flo:%d:%s", userId, reqData.AiRoleId)
	isReceive := global.REDIS.Exists(c, rdsKey).Val()
	if isReceive > 0 {
		err = fmt.Errorf("already receive")
		return
	}
	floCount := aiRoleInfo.DeepFlo
	var fRecord = &model.FlowerConsumeRecord{
		DigitalUserId: userId,
		AiId:          aiRoleInfo.Id,
		AiRoleId:      aiRoleInfo.RoleID,
		ConsumeType:   model.ConsumeTypeDeepLinkFlowers,
		RelationID:    fmt.Sprintf("%d", userId),
		OriginAmount:  userProfile.FlowerAmount,
		ConsumeAmount: floCount,
	}
	err = global.DB.Transaction(func(tx *gorm.DB) (tErr error) {
		// 创建FlowerConsumeRecord记录
		if tErr = tx.Model(&model.FlowerConsumeRecord{}).Create(fRecord).Error; tErr != nil {
			return
		}
		updateMap := map[string]interface{}{
			"flower_amount": gorm.Expr("flower_amount + ?", floCount),
		}
		if tErr = tx.Model(&model.DigitalUserProfile{}).Where("id = ?", userProfile.Id).Updates(updateMap).Error; tErr != nil {
			return
		}
		return
	})
	if err != nil {
		return
	}
	// 设置redis,已领取
	global.REDIS.SetEx(c, rdsKey, 1, time.Hour*24*30)
	res.DeepFlo = floCount
	return
}

func (m *FlowerConsumeRecordService) ReceiveAdFlowers(c *gin.Context) (res gin.H, err error) {
	var (
		userInfo       *model.DigitalUser
		userSer        DigitalUserService
		profileService DigitalUserProfileService
		userProfile    *model.DigitalUserProfile
		userId         = utils.GetDigitalUserID(c)
		now            = time.Now()
		hourStr        = now.Format("2006010215")
	)

	hourKey := fmt.Sprintf(global.AD_HOUR_COUNT_KEY, hourStr, userId)
	hourCount, _ := global.REDIS.Get(c, hourKey).Int()
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		return
	}
	// 获取userProfile信息
	if userProfile, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	// 获取App信息
	appId := utils.GetAppID(c)
	appInfo, exist := global.AppMap[appId]
	if !exist {
		err = fmt.Errorf("app(%d) not found", appId)
		return
	}
	// 检测是否超出限制
	if hourCount >= appInfo.DetailConfig.Data().AdMaxPerHourCount {
		err = fmt.Errorf("Maximum viewing limit reached within 1 hour: %d times", appInfo.DetailConfig.Data().AdMaxPerHourCount)
		return
	}
	// 看广告奖励的鲜花数
	floCount := appInfo.DetailConfig.Data().AdRewardFlo
	err = global.DB.Transaction(func(tx *gorm.DB) (tErr error) {
		var flowerAmountNow = userProfile.FlowerAmount + floCount
		// 创建FlowerConsumeRecord记录
		var fRecord = &model.FlowerConsumeRecord{
			DigitalUserId: userId,
			AiId:          0,
			AiRoleId:      "",
			ConsumeType:   model.ConsumeTypeWatchAdFlowers,
			RelationID:    fmt.Sprintf("%d", userId),
			OriginAmount:  userProfile.FlowerAmount,
			ConsumeAmount: floCount,
		}
		if tErr = tx.Model(&model.FlowerConsumeRecord{}).Create(fRecord).Error; tErr != nil {
			return
		}
		updateMap := map[string]interface{}{
			"flower_amount": flowerAmountNow,
		}
		go TdPointer(userInfo, userProfile, td.ServerEvent, map[string]interface{}{
			"event_tag":         "coins_consume",
			"coins_consume_num": floCount,
			"source":            "whatch_ad_get",
		})
		db := tx.Model(&model.DigitalUserProfile{}).Where("id = ? AND flower_amount = ?", userProfile.Id, userProfile.FlowerAmount).Updates(updateMap)
		if tErr = db.Error; tErr != nil {
			return
		}
		if db.RowsAffected == 0 {
			tErr = fmt.Errorf("update userProfile failed")
			return
		}
		// 更新redis
		tErr = global.REDIS.Incr(c, hourKey).Err()
		if tErr != nil {
			return
		}
		// 更新redis过期时间
		tErr = global.REDIS.Expire(c, hourKey, time.Hour*24).Err()
		if tErr != nil {
			return
		}
		return
	})
	return
}

func (m *FlowerConsumeRecordService) SignInList(c *gin.Context) (res response.SignInListRes, err error) {
	var (
		tip1           string
		userService    DigitalUserService
		profileService DigitalUserProfileService
		userInfo       *model.DigitalUser
		userProfile    *model.DigitalUserProfile
		userId         = utils.GetDigitalUserID(c)
		now            = time.Now()
		dayStr         = now.Format(time.DateOnly)
		hourStr        = now.Format("2006010215")
	)
	weekRangeTime := utils.CurrentWeekRangeTime(true)
	rKey := userService.SignFloRdsKey(userId)
	// 获取用户信息
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	isSub := userInfo.IsSub()
	// 获取userProfile信息
	if userProfile, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	// 获取App信息
	appId := utils.GetAppID(c)
	appInfo, exist := global.AppMap[appId]
	if !exist {
		err = fmt.Errorf("app(%d) not found", appId)
		return
	}

	signMembers := global.REDIS.SMembers(c, rKey).Val()
	// 把记录处理成一个map
	signMap := map[string]bool{}
	for _, signDay := range signMembers {
		signMap[signDay] = true
	}

	// 判断今天是否已经签到
	todayIsSign := global.REDIS.SIsMember(c, rKey, dayStr).Val()
	nextSignSeconds := int(jutils.EndOfDay().Sub(now).Seconds())

	userRewardFlowers := global.RewardFlowersFreeUserConfig
	if isSub {
		userRewardFlowers = global.RewardFlowersVipUserConfig
		tip1 = "If you sign in every day from Monday to Sunday, you will get 10+10 Rewards on Sunday."
	} else {
		tip1 = "If you sign in every day from Monday to Sunday, you will get 5+10 Rewards on Sunday."
	}

	// SiginRewardData待完成
	var siginRewardList []response.SiginRewardData

	for i, weekDayTime := range weekRangeTime {
		weekDay := weekDayTime.Format(time.DateOnly)
		// 判断是否签到
		isSign := 3
		if _, exists := signMap[weekDay]; exists {
			isSign = 1 // 已签到
		} else if weekDayTime.Before(now) {
			isSign = 2 // 过期不可签
		}

		isToday := false
		if weekDay == dayStr {
			isToday = true
		}

		siginRewardList = append(siginRewardList, response.SiginRewardData{
			FlowerNum:    userRewardFlowers[i],
			FlowerNumSub: global.RewardFlowersVipUserConfig[i],
			Date:         weekDayTime.Format("Mon"),
			IsSigin:      isSign,
			IsToday:      isToday,
		})
	}

	// 当前小时用户看广告获取鲜花的次数
	hourKey := fmt.Sprintf(global.AD_HOUR_COUNT_KEY, hourStr, userId)
	hourCount, _ := global.REDIS.Get(c, hourKey).Int()

	res = response.SignInListRes{
		MyFlowerCount: userProfile.FlowerAmount,
		SiginInfo: response.SiginInfo{
			TodayIsSign:     todayIsSign,
			NextSignSeconds: nextSignSeconds,
			Tip1:            tip1,
			Tip2:            "Time Zone: UTC+0",
		},
		SiginRewardData: siginRewardList,
		WatchVideoInfo: response.WatchVideoInfo{
			Title:              fmt.Sprintf("Watch Videos Earn %d", appInfo.DetailConfig.Data().AdRewardFlo),
			FlowerCountForHour: hourCount,
			MaxCountForHour:    appInfo.DetailConfig.Data().AdMaxPerHourCount,
		},
	}
	return
}
