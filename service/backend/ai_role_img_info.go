package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
)

type AiRoleImgInfoService struct{}

func (m *AiRoleImgInfoService) Create(reqData *model.AiRoleImgInfo) (err error) {
	err = global.DB.Model(&model.AiRoleImgInfo{}).Create(reqData).Error
	m.SyncAll()
	return
}

func (m *AiRoleImgInfoService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.AiRoleImgInfo{}).Where("id = ?", id).Delete(&model.AiRoleImgInfo{}).Error
	m.SyncAll()
	return
}

func (m *AiRoleImgInfoService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.AiRoleImgInfo{}).Where("id IN (?)", reqData.Ids).Delete(&model.AiRoleImgInfo{}).Error
	m.SyncAll()
	return
}

func (m *AiRoleImgInfoService) Update(reqData *model.AiRoleImgInfo) (err error) {
	err = global.DB.Model(&model.AiRoleImgInfo{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	m.SyncAll()
	return
}

func (m *AiRoleImgInfoService) GetById(id uint) (res *model.AiRoleImgInfo, err error) {
	err = global.DB.Model(&model.AiRoleImgInfo{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *AiRoleImgInfoService) GetAll() (resList []*model.AiRoleImgInfo, err error) {
	err = global.DB.Model(&model.AiRoleImgInfo{}).Find(&resList).Error
	return
}

func (m *AiRoleImgInfoService) SyncAll() {
	var (
		err     error
		resList []*model.AiRoleImgInfo
		idMap   = map[uint]*model.AiRoleImgInfo{}
	)
	resList, err = m.GetAll()
	if err != nil {
		global.LOG.Error("SyncAll 失败", zap.Error(err))
		return
	}

	for _, info := range resList {
		idMap[info.Id] = info
	}
	global.AiRoleImgInfoList = resList
	global.AiRoleImgInfoIdMap = idMap
}

func (m *AiRoleImgInfoService) GetList(info req.AiRoleImgInfoSearch) (resList []*model.AiRoleImgInfo, total int64, err error) {
	db := global.DB.Model(&model.AiRoleImgInfo{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.Avatar != "" {
		db = db.Where("avatar = ?", info.Avatar)
	}
	if info.FirstImg != "" {
		db = db.Where("first_img = ?", info.FirstImg)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
