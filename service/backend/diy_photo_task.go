package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
	"time"
)

type DiyPhotoTaskService struct{}

func (m *DiyPhotoTaskService) Create(reqData *model.DiyPhotoTask) (err error) {
	err = global.DB.Model(&model.DiyPhotoTask{}).Create(reqData).Error
	return
}

func (m *DiyPhotoTaskService) DeleteById(ctx context.Context, taskId uint) (err error) {
	err = global.DB.Model(&model.DiyPhotoTask{}).Where("id = ?", taskId).Delete(&model.DiyPhotoTask{}).Error
	if err != nil {
		m.reduceRedisTask(ctx, taskId)
	}
	return
}

func (m *DiyPhotoTaskService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.DiyPhotoTask{}).Where("id IN (?)", reqData.Ids).Delete(&model.DiyPhotoTask{}).Error
	return
}

func (m *DiyPhotoTaskService) Update(ctx context.Context, reqData *model.DiyPhotoTask) (err error) {
	err = global.DB.Model(&model.DiyPhotoTask{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	return
}

func (m *DiyPhotoTaskService) UpdateByMap(ctx context.Context, recId uint, editMap map[string]interface{}) (err error) {
	err = global.DB.Model(&model.DiyPhotoTask{}).Where("id = ?", recId).Updates(editMap).Error
	return
}

func (m *DiyPhotoTaskService) GetById(id uint) (res *model.DiyPhotoTask, err error) {
	err = global.DB.Model(&model.DiyPhotoTask{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *DiyPhotoTaskService) GetAll() (resList []*model.DiyPhotoTask, err error) {
	err = global.DB.Model(&model.DiyPhotoTask{}).Find(&resList).Error
	return
}

func (m *DiyPhotoTaskService) SearchAll(ctx context.Context, reqData *model.DiyPhotoTask) (resList []*model.DiyPhotoTask, err error) {
	db := global.DB.Model(&model.DiyPhotoTask{})
	if reqData.AiRoleId != 0 {
		db = db.Where("ai_role_id = ?", reqData.AiRoleId)
	}
	if reqData.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", reqData.DigitalUserId)
	}
	if reqData.TaskState != 0 {
		db = db.Where("task_state = ?", reqData.TaskState)
	}
	if reqData.DiyConfigId != 0 {
		db = db.Where("diy_config_id = ?", reqData.DiyConfigId)
	}
	if reqData.DiyConfigName != "" {
		db = db.Where("diy_config_name LIKE ?", "%"+reqData.DiyConfigName+"%")
	}
	if reqData.UserStatus != 0 {
		db = db.Where("user_status = ?", reqData.UserStatus)
	}
	err = db.Find(&resList).Error
	return
}

func (m *DiyPhotoTaskService) SearchFirst(reqData *model.DiyPhotoTask) (res *model.DiyPhotoTask, err error) {
	db := global.DB.Model(&model.DiyPhotoTask{})
	if reqData.Id != 0 {
		db = db.Where("id = ?", reqData.Id)
	}
	if reqData.AiRoleId != 0 {
		db = db.Where("ai_role_id = ?", reqData.AiRoleId)
	}
	if reqData.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", reqData.DigitalUserId)
	}
	if reqData.Email != "" {
		db = db.Where("email LIKE ?", "%"+reqData.Email+"%")
	}
	if reqData.ImgCount != 0 {
		db = db.Where("img_count = ?", reqData.ImgCount)
	}
	if reqData.DiyConfigName != "" {
		db = db.Where("diy_config_name LIKE ?", "%"+reqData.DiyConfigName+"%")
	}
	if reqData.TaskState != 0 {
		db = db.Where("task_state = ?", reqData.TaskState)
	}
	if reqData.UserStatus != 0 {
		db = db.Where("user_status = ?", reqData.UserStatus)
	}
	err = db.First(&res).Error
	return
}

func (m *DiyPhotoTaskService) RestartTask(ctx context.Context, reqData *req.RestartTaskReq) (err error) {
	var (
		maskImgList []string
		taskInfo    *model.DiyPhotoTask
	)
	searchOneReq := &model.DiyPhotoTask{}
	searchOneReq.Id = reqData.Id
	if taskInfo, err = m.SearchFirst(searchOneReq); err != nil {
		return
	}
	if len(taskInfo.MaskImgs) != taskInfo.ImgCount {
		if maskImgList, err = m.fixTaskMaskImgs(ctx, taskInfo); err != nil {
			return
		}
		taskInfo.MaskImgs = maskImgList
		if err = m.Update(ctx, taskInfo); err != nil {
			return
		}
	}
	if err = m.addRedisTask(ctx, taskInfo.Id); err != nil {
		return
	}
	return
}

func (m *DiyPhotoTaskService) addDelayRedisTask(ctx context.Context, taskId uint) (err error) {
	taskRedisReq := model.ImgToImgReq{
		TaskType: 3,
		TaskId:   taskId,
	}
	taskRedisReqBytes, _ := json.Marshal(taskRedisReq)
	score := time.Now().Add(time.Minute * 1).Unix()
	err = global.REDIS.ZAdd(ctx, global.IMG_TO_IMG_DELAY_KEY, redis.Z{Score: float64(score), Member: string(taskRedisReqBytes)}).Err()
	return
}

func (m *DiyPhotoTaskService) addRedisTask(ctx context.Context, taskId uint) (err error) {
	taskRedisReq := model.ImgToImgReq{
		TaskType: 3,
		TaskId:   taskId,
	}
	taskRedisReqBytes, _ := json.Marshal(taskRedisReq)
	err = global.REDIS.ZAdd(ctx, global.IMG_TO_IMG_TRAIN_KEY, redis.Z{Score: float64(time.Now().Unix()), Member: string(taskRedisReqBytes)}).Err()
	return
}

func (m *DiyPhotoTaskService) reduceRedisTask(ctx context.Context, taskId uint) {
	taskRedisReq := model.ImgToImgReq{
		TaskType: 3,
		TaskId:   taskId,
	}
	taskRedisReqBytes, _ := json.Marshal(taskRedisReq)
	taskRedisReqStr := string(taskRedisReqBytes)
	global.REDIS.ZRem(ctx, global.IMG_TO_IMG_TRAIN_KEY, redis.Z{Member: taskRedisReqStr})
	return
}

func (m *DiyPhotoTaskService) FailTaskAddRds() (err error) {
	var (
		res []*model.DiyPhotoTask
		ctx = context.Background()
	)
	db := global.DB.Model(&model.DiyPhotoTask{})
	db = db.Where("task_state = ?", model.DiyPhotoTaskStateFailed)
	db = db.Where("result_msg != 'user is deleted'")
	db = db.Where("created_at >= '2024-09-11 10:13:18'")
	err = db.Find(&res).Error
	if err != nil {
		global.LOG.Error("FailTaskAddRds", zap.Error(err))
		return
	}
	for taskIndex, taskInfo := range res {
		if err = m.addRedisTask(ctx, taskInfo.Id); err != nil {
			global.LOG.Error("FailTaskAddRds", zap.Error(err))
		}
		global.LOG.Info("FailTaskAddRds", zap.Int("task index", taskIndex), zap.Uint("task id", taskInfo.Id))
	}
	global.LOG.Info("FailTaskAddRds", zap.Int("task count", len(res)))
	return
}

func (m *DiyPhotoTaskService) GetCount(reqData *model.DiyPhotoTask) (total int64, err error) {
	db := global.DB.Model(&model.DiyPhotoTask{})
	if reqData.AiRoleId != 0 {
		db = db.Where("ai_role_id = ?", reqData.AiRoleId)
	}
	if reqData.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", reqData.DigitalUserId)
	}

	if reqData.Email != "" {
		db = db.Where("email LIKE ?", "%"+reqData.Email+"%")
	}
	if reqData.ImgCount != 0 {
		db = db.Where("img_count = ?", reqData.ImgCount)
	}
	if reqData.DiyConfigName != "" {
		db = db.Where("diy_config_name LIKE ?", "%"+reqData.DiyConfigName+"%")
	}
	if reqData.TaskState != 0 {
		db = db.Where("task_state = ?", reqData.TaskState)
	}
	if reqData.UserStatus != 0 {
		db = db.Where("user_status = ?", reqData.UserStatus)
	}
	err = db.Count(&total).Error
	return
}

func (m *DiyPhotoTaskService) GetList(info req.DiyPhotoTaskSearch) (resList []*model.DiyPhotoTask, total int64, err error) {
	db := global.DB.Model(&model.DiyPhotoTask{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if !info.CreatedAtMin.IsZero() {
		db = db.Where("created_at > ?", info.CreatedAtMin)
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.AiRoleId != 0 {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if info.Email != "" {
		db = db.Where("email LIKE ?", "%"+info.Email+"%")
	}
	if info.ImgCount != 0 {
		db = db.Where("img_count = ?", info.ImgCount)
	}
	if info.DiyType != 0 {
		db = db.Where("diy_type = ?", info.DiyType)
	}
	if info.DiyConfigName != "" {
		db = db.Where("diy_config_name LIKE ?", "%"+info.DiyConfigName+"%")
	}
	if info.TaskState != 0 {
		db = db.Where("task_state = ?", info.TaskState)
	}
	if info.UserStatus != 0 {
		db = db.Where("user_status = ?", info.UserStatus)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
