package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"aimsg-server/utils"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
)

type PushMsgService struct{}

func (m *PushMsgService) Create(reqData *model.PushMsg) (err error) {
	err = global.DB.Model(&model.PushMsg{}).Create(reqData).Error
	m.SyncPushMsgList()
	return
}

func (m *PushMsgService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.PushMsg{}).Where("id = ?", id).Delete(&model.PushMsg{}).Error
	m.SyncPushMsgList()
	return
}

func (m *PushMsgService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.PushMsg{}).Where("id IN (?)", reqData.Ids).Delete(&model.PushMsg{}).Error
	m.SyncPushMsgList()
	return
}

func (m *PushMsgService) Update(reqData *model.PushMsg) (err error) {
	err = global.DB.Model(&model.PushMsg{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	m.SyncPushMsgList()
	return
}

func (m *PushMsgService) GetById(id uint) (res *model.PushMsg, err error) {
	err = global.DB.Model(&model.PushMsg{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *PushMsgService) GetAll() (resList []*model.PushMsg, err error) {
	err = global.DB.Model(&model.PushMsg{}).Find(&resList).Error
	return
}

// SyncPushMsgList sync push msg list
func (m *PushMsgService) SyncPushMsgList() {
	var (
		err         error
		resList     []*model.PushMsg
		checkedList []*model.PushMsg
	)
	mLog := global.LOG.With(zap.String("PushMsgService", "SyncPushMsgList"))
	// get all push msg
	resList, err = m.GetAll()
	if err != nil {
		mLog.Error("SyncPushMsgList error", zap.Error(err))
		return
	}
	// check push msg
	for _, pushMsg := range resList {
		if len(pushMsg.TimeRange) != 2 {
			mLog.Error("len(pushMsg.TimeRange) != 2", zap.Any("pushMsg", pushMsg))
			continue
		}
		if len(pushMsg.MsgList) == 0 {
			mLog.Error("len(pushMsg.MsgList) == 0", zap.Any("pushMsg", pushMsg))
			continue
		}
		if !utils.IsValidTimeRange(pushMsg.TimeRange[0], pushMsg.TimeRange[1]) {
			mLog.Error("invalid time range", zap.Any("pushMsg", pushMsg))
			continue
		}
		checkedList = append(checkedList, pushMsg)
	}
	global.PushMsgList = checkedList
}

func (m *PushMsgService) GetList(info req.PushMsgSearch) (resList []*model.PushMsg, total int64, err error) {
	db := global.DB.Model(&model.PushMsg{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.Remark != "" {
		db = db.Where("remark = ?", info.Remark)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
