package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
	"time"
)

type BadgeService struct{}

func (m *BadgeService) CopyInfo(sourceAppId, targetAppId uint) (err error) {
	var (
		sourceInfos []*model.Badge
		newInfos    []*model.Badge
		nowTime     = time.Now()
	)
	sourceInfos, err = m.FindByAppId(sourceAppId)
	if err != nil {
		return
	}
	for _, sourceInfo := range sourceInfos {
		sourceInfo.Id = 0
		sourceInfo.CreatedAt = nowTime
		sourceInfo.UpdatedAt = nowTime
		sourceInfo.AppID = targetAppId
		newInfos = append(newInfos, sourceInfo)
	}
	if len(newInfos) == 0 {
		return
	}
	err = global.DB.Model(&model.Badge{}).Create(&newInfos).Error
	return
}

func (m *BadgeService) FindByAppId(appId uint) (resList []*model.Badge, err error) {
	err = global.DB.Model(&model.Badge{}).Where("app_id = ?", appId).Find(&resList).Error
	return
}

func (m *BadgeService) Create(reqData *model.Badge) (err error) {
	err = global.DB.Model(&model.Badge{}).Create(reqData).Error
	m.SyncAll()
	return
}

func (m *BadgeService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.Badge{}).Where("id = ?", id).Delete(&model.Badge{}).Error
	m.SyncAll()
	return
}

func (m *BadgeService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.Badge{}).Where("id IN (?)", reqData.Ids).Delete(&model.Badge{}).Error
	m.SyncAll()
	return
}

func (m *BadgeService) Update(reqData *model.Badge) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.Badge{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	m.SyncAll()
	return
}

func (m *BadgeService) GetById(id uint) (res *model.Badge, err error) {
	err = global.DB.Model(&model.Badge{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *BadgeService) GetAll() (resList []*model.Badge, err error) {
	err = global.DB.Model(&model.Badge{}).Order("sort DESC, id DESC").Find(&resList).Error
	return
}

func (m *BadgeService) SyncAll() {
	var (
		err                 error
		dbResList           []*model.Badge
		triggerTypeBadgeMap = map[model.TriggerType][]*model.Badge{}
	)
	dbResList, err = m.GetAll()
	if err != nil {
		global.LOG.Error("SyncGiftAll 失败", zap.Error(err))
		return
	}
	for _, badge := range dbResList {
		triggerArrays := triggerTypeBadgeMap[badge.TriggerType]
		if triggerArrays == nil {
			triggerArrays = make([]*model.Badge, 0)
		}
		triggerArrays = append(triggerArrays, badge)
		triggerTypeBadgeMap[badge.TriggerType] = triggerArrays
	}
	global.BadgeList = dbResList
	global.TriggerTypeBadgeMap = triggerTypeBadgeMap
}

func (m *BadgeService) badgeKey(userId uint, badgeInfo *model.Badge) string {
	return fmt.Sprintf(global.USER_BADGE_KEY, userId, badgeInfo.Id)
}

func (m *BadgeService) BadgeExist(ctx context.Context, userId uint, roleId string, badgeInfo *model.Badge) (exist bool) {
	redisKey := m.badgeKey(userId, badgeInfo)
	if badgeInfo.Single {
		keyCount := global.REDIS.Exists(ctx, redisKey).Val()
		exist = keyCount > 0
	} else {
		exist = global.REDIS.SIsMember(ctx, redisKey, roleId).Val()
	}
	return
}

func (m *BadgeService) LoginBadgeExist(ctx context.Context, userId uint, badgeInfo *model.Badge, dayStr string) (exist bool) {
	redisKey := m.badgeKey(userId, badgeInfo)
	exist = global.REDIS.SIsMember(ctx, redisKey, dayStr).Val()
	return
}

func (m *BadgeService) LoginBadgeSet(ctx context.Context, userId uint, badgeInfo *model.Badge, dayStr string) (err error) {
	redisKey := m.badgeKey(userId, badgeInfo)
	err = global.REDIS.SAdd(ctx, redisKey, dayStr).Err()
	return
}

func (m *BadgeService) BadgeSet(ctx context.Context, userId uint, roleId string, badgeInfo *model.Badge) (err error) {
	redisKey := m.badgeKey(userId, badgeInfo)
	err = global.REDIS.SAdd(ctx, redisKey, roleId).Err()
	return
}

func (m *BadgeService) GetList(info req.BadgeSearch) (resList []*model.Badge, total int64, err error) {
	db := global.DB.Model(&model.Badge{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.Name != "" {
		db = db.Where("name LIKE ?", "%"+info.Name+"%")
	}
	if info.TriggerType != 0 {
		db = db.Where("trigger_type = ?", info.TriggerType)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("sort DESC, id DESC")
	}
	err = db.Find(&resList).Error
	return
}
