package backend

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/model/cli/td"
	"aimsg-server/service/backend/call"
	"aimsg-server/utils"
	"aimsg-server/utils/agora"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"net/http"
	"strconv"
	"time"
)

// Connected 通话接听
func (m *VideoCallService) Connected(c *gin.Context, reqData *request.ConnectedReq) (err error) {
	c.Header("X-IgnoreSignature", "297dc6634dd420872c10444946aadd55")
	gLog := global.LOG.With(zap.String("func", "VideoCallService.Connected"), zap.Any("reqHeader", c.Request.Header), zap.Any("reqData", reqData))
	var (
		callRecord *model.VideoCall
	)
	if callRecord, err = m.GetByRoomId(c, reqData.RoomID); err != nil {
		gLog.Error("GetByRoomId", zap.Error(err))
		return
	}
	if callRecord.CallStatus != model.CallStatusCreate {
		err = fmt.Errorf("call status is not create")
		gLog.Error("call status is not create", zap.Error(err))
		return
	}
	if reqData.Value == "1" {
		callRecord.CallStatus = model.CallStatusCalling
		callRecord.StartTime = time.Now()
	} else {
		callRecord.CallStatus = model.CallStatusRejected
	}
	if err = m.Update(c, callRecord); err != nil {
		gLog.Error("Update", zap.Error(err))
		return
	}
	ddNotifyMsg := fmt.Sprintf(`## 通话应答详情
- 通话记录ID: %d
- AI角色RoleID: %s
- 用户ID: %d
- 通话频道: %s
- 通话状态: %s
`, callRecord.Id, callRecord.AiRoleId, callRecord.DigitalUserId, callRecord.RoomId, model.CallStatusMap[callRecord.CallStatus])
	SendDDNotice(ddNotifyMsg)

	if reqData.Value != "1" {
		// 拒绝
		var (
			userInfo *model.DigitalUser
			aiInfo   *model.AiRole
			aiSer    AiRoleService
			userSer  DigitalUserService
			msgSer   MsgService
		)
		if aiInfo, err = aiSer.GetByRoleId(callRecord.AiRoleId); err != nil {
			gLog.Error("GetByRoleId", zap.Error(err))
			return
		}
		if userInfo, err = userSer.GetById(c, callRecord.DigitalUserId); err != nil {
			gLog.Error("GetById", zap.Error(err))
			return
		}
		appInfo := global.AppMap[userInfo.AppID]
		hangUpMsg := model.MsgInfo{
			MessageID:      uuid.NewString(),
			Channel:        nil,
			SendTime:       time.Now().Unix(),
			DeletedAt:      nil,
			MessageType:    model.MsgTypeRealCallHangUp,
			SenderID:       callRecord.AiRoleId,
			SenderName:     aiInfo.Nickname,
			ReceiverID:     fmt.Sprintf("%d", userInfo.Id),
			ReceiverName:   userInfo.Nickname,
			MessageContent: model.MessageContent{},
		}
		if err = msgSer.SendMsgToAgora(&hangUpMsg, appInfo); err != nil {
			return err
		}
	}
	return
}

// HangUp 通话挂断
func (m *VideoCallService) HangUp(c *gin.Context, reqData *request.HangUpReq) (err error) {
	gLog := global.LOG.With(zap.String("func", "VideoCallService.HangUp"), zap.Any("reqHeader", c.Request.Header), zap.Any("reqData", reqData))
	var (
		callRecord *model.VideoCall
		aiInfo     *model.AiRole
		aiSer      AiRoleService
		userInfo   *model.DigitalUser
		userSer    DigitalUserService
		msgSer     MsgService
	)
	if callRecord, err = m.GetByRoomId(c, reqData.RoomID); err != nil {
		gLog.Error("GetByRoomId", zap.Error(err))
		return
	}
	callRecord.CallStatus = model.CallStatusClose
	callRecord.EndTime = time.Now()
	callRecord.CallSecond = int(callRecord.EndTime.Sub(callRecord.StartTime).Seconds())
	callRecord.CloseId = callRecord.RealAnchorId
	if err = m.Update(c, callRecord); err != nil {
		gLog.Error("Update", zap.Error(err))
		return
	}
	ddNotifyMsg := fmt.Sprintf(`## 通话已挂断
- 通话记录ID: %d
- AI角色RoleID: %s
- 用户ID: %d
- 通话频道: %s
`, callRecord.Id, callRecord.AiRoleId, callRecord.DigitalUserId, callRecord.RoomId)
	SendDDNotice(ddNotifyMsg)
	if aiInfo, err = aiSer.GetByRoleId(callRecord.AiRoleId); err != nil {
		gLog.Error("GetByRoleId", zap.Error(err))
		return
	}
	if userInfo, err = userSer.GetById(c, callRecord.DigitalUserId); err != nil {
		gLog.Error("GetById", zap.Error(err))
		return
	}
	appInfo := global.AppMap[userInfo.AppID]
	hangUpMsg := model.MsgInfo{
		MessageID:      uuid.NewString(),
		Channel:        nil,
		SendTime:       time.Now().Unix(),
		DeletedAt:      nil,
		MessageType:    model.MsgTypeRealCallHangUp,
		SenderID:       callRecord.AiRoleId,
		SenderName:     aiInfo.Nickname,
		ReceiverID:     fmt.Sprintf("%d", userInfo.Id),
		ReceiverName:   userInfo.Nickname,
		MessageContent: model.MessageContent{},
	}
	if err = msgSer.SendMsgToAgora(&hangUpMsg, appInfo); err != nil {
		return err
	}
	return
}

// Msg 通话消息
func (m *VideoCallService) Msg(c *gin.Context, reqData *request.MsgReq) (err error) {

	return
}

func (m *VideoCallService) getHeaderMap(userInfo *model.DigitalUser) (headerMap map[string]string, err error) {
	userIdStr := fmt.Sprintf("%d", userInfo.Id)
	appInfo := global.AppMap[userInfo.AppID]
	vitaAppData := appInfo.VitaApp.Data()
	nowTime := time.Now()
	nowTimeUnix := nowTime.Unix()

	headerMap = map[string]string{
		"X-Mid":            fmt.Sprintf("%d", vitaAppData.Id),
		"X-Uid":            userIdStr,
		"X-Timestamp":      fmt.Sprintf("%d", nowTimeUnix),
		"Content-Language": userInfo.Language,
	}
	// 生成sign
	signPreStr := fmt.Sprintf("%d%d%d%s", vitaAppData.Id, nowTimeUnix, userInfo.Id, vitaAppData.Key)
	signMd5Str := utils.MD5(signPreStr)
	headerMap["X-Sign"] = signMd5Str
	// 生成X-Data
	userClaims := jwt.MapClaims{
		"uid":     userIdStr,
		"name":    userInfo.Nickname,
		"gender":  userInfo.Gender,
		"picture": utils.S3Url(userInfo.Avatar, userInfo.AppID),
		"email":   userInfo.Email,
	}
	tokenObj := jwt.NewWithClaims(jwt.SigningMethodHS256, userClaims)
	tokenStr, tokenErr := tokenObj.SignedString([]byte(vitaAppData.Key))
	if tokenErr == nil {
		headerMap["X-Data"] = tokenStr
	}
	headerMap["X-Data"] = ""

	return
}

// OnlineStatus 主播在线状态
func (m *VideoCallService) OnlineStatus(realAnchorId uint, userInfo *model.DigitalUser) (onlineRes response.MicroUserOnlineRes, err error) {
	gLog := global.LOG.With(zap.String("func", "VideoCallService.AnchorOnlineStatus"), zap.Any("realAnchorId", realAnchorId))
	var (
		apiLog    string
		curlStr   string
		reqUrl    = fmt.Sprintf("%s/micro/v2/anchor/userOnline", global.CONFIG.Vita.Url)
		reqMap    = map[string]interface{}{"remoteUid": realAnchorId}
		headerMap = map[string]string{}
	)
	if headerMap, err = m.getHeaderMap(userInfo); err != nil {
		return
	}
	if apiLog, curlStr, err = utils.HttpPostJsonWithHeader(reqUrl, reqMap, headerMap, &onlineRes, 5); err != nil {
		// 记录日志
		gLog.Error("HttpPostJson error", zap.String("apiLog", apiLog), zap.String("curlStr", curlStr), zap.Any("err", err))
		ddNotifyMsg := fmt.Sprintf(`## 查询主播中台主播在线状态接口异常
- 主播ID: %d
- 请求日志: %s
- curl请求: %s
- 错误信息: %v
`, realAnchorId, apiLog, curlStr, err)
		SendDDNotice(ddNotifyMsg)
		return
	}
	if onlineRes.Status != 0 {
		err = fmt.Errorf("anchor online error, %s", onlineRes.Msg)
		gLog.Error("anchorOnlineRes.Status is not 0", zap.Error(err), zap.String("apiLog", apiLog), zap.String("curlStr", curlStr))
		return
	}
	return
}

func (m *VideoCallService) VideoCall(realAnchorId uint, roomId, remoteToken string, userInfo *model.DigitalUser) (callRes response.MicroCallRes, resHeader http.Header, err error) {
	gLog := global.LOG.With(zap.String("func", "VideoCallService.CallAnchor"), zap.Any("realAnchorId", realAnchorId))
	var (
		apiLog    string
		curlStr   string
		reqUrl    = fmt.Sprintf("%s/micro/v2/video/call", global.CONFIG.Vita.Url)
		headerMap = map[string]string{}
	)
	appInfo := global.AppMap[userInfo.AppID]
	reqMap := map[string]interface{}{
		"roomId":      roomId,
		"agoId":       appInfo.AgoraData.Data().AppId,
		"remoteUid":   realAnchorId,
		"remoteToken": remoteToken,
	}
	if headerMap, err = m.getHeaderMap(userInfo); err != nil {
		return
	}
	if apiLog, curlStr, resHeader, err = utils.HttpPostJsonWithHeaderReturnHeader(reqUrl, reqMap, headerMap, &callRes, 5); err != nil {
		// 记录日志
		gLog.Error("HttpPostJson error", zap.String("apiLog", apiLog), zap.String("curlStr", curlStr), zap.Any("err", err))
		ddNotifyMsg := fmt.Sprintf(`## 主播中台呼叫接口异常
- 主播ID: %d
- 请求日志: %s
- curl请求: %s
- 错误信息: %v
`, realAnchorId, apiLog, curlStr, err)
		SendDDNotice(ddNotifyMsg)
		return
	}
	if callRes.Status != 0 {
		err = fmt.Errorf("video call error, %s", callRes.Msg)
		gLog.Error("video call.Status is not 0", zap.Error(err), zap.String("apiLog", apiLog), zap.String("curlStr", curlStr))
		return
	}
	return
}

func (m *VideoCallService) VideoCallClose(roomId string, realAnchorId uint, userInfo *model.DigitalUser) (callRes response.MicroVideoCallCloseRes, err error) {
	gLog := global.LOG.With(zap.String("func", "VideoCallService.VideoCallClose"), zap.String("roomId", roomId))
	var (
		apiLog  string
		curlStr string
		reqUrl  = fmt.Sprintf("%s/micro/v2/video/call/close", global.CONFIG.Vita.Url)
		reqMap  = map[string]interface{}{
			"roomId":    roomId,
			"remoteUid": realAnchorId,
		}
		headerMap = map[string]string{}
	)
	if headerMap, err = m.getHeaderMap(userInfo); err != nil {
		return
	}
	if apiLog, curlStr, err = utils.HttpPostJsonWithHeader(reqUrl, reqMap, headerMap, &callRes, 5); err != nil {
		// 记录日志
		gLog.Error("HttpPostJson error", zap.String("apiLog", apiLog), zap.String("curlStr", curlStr), zap.Any("err", err))
		ddNotifyMsg := fmt.Sprintf(`## 主播中台呼叫挂断接口异常
- 通话频道: %s
- 请求日志: %s
- curl请求: %s
- 错误信息: %v
`, roomId, apiLog, curlStr, err)
		SendDDNotice(ddNotifyMsg)
		return
	}
	if callRes.Status != 0 {
		err = fmt.Errorf("video call close error, %s", callRes.Msg)
		gLog.Error("video call close status is not 0", zap.Error(err), zap.String("apiLog", apiLog), zap.String("curlStr", curlStr))
		return
	}
	return
}

func (m *VideoCallService) VideoCallCancel(roomId string, realAnchorId uint, userInfo *model.DigitalUser) (callRes response.MicroVideoCallCancelRes, err error) {
	gLog := global.LOG.With(zap.String("func", "VideoCallService.VideoCallClose"), zap.String("roomId", roomId))
	var (
		apiLog    string
		curlStr   string
		reqUrl    = fmt.Sprintf("%s/micro/v2/video/call/cancel", global.CONFIG.Vita.Url)
		headerMap = map[string]string{}
	)
	appInfo := global.AppMap[userInfo.AppID]
	reqMap := map[string]interface{}{
		"roomId":    roomId,
		"remoteUid": realAnchorId,
		"agoId":     appInfo.AgoraData.Data().AppId,
	}
	if headerMap, err = m.getHeaderMap(userInfo); err != nil {
		return
	}
	if apiLog, curlStr, err = utils.HttpPostJsonWithHeader(reqUrl, reqMap, headerMap, &callRes, 5); err != nil {
		// 记录日志
		gLog.Error("HttpPostJson error", zap.String("apiLog", apiLog), zap.String("curlStr", curlStr), zap.Any("err", err))
		ddNotifyMsg := fmt.Sprintf(`## 主播中台取消呼叫接口异常
- 通话频道: %s
- 请求日志: %s
- curl请求: %s
- 错误信息: %v
`, roomId, apiLog, curlStr, err)
		SendDDNotice(ddNotifyMsg)
		return
	}
	if callRes.Status != 0 {
		err = fmt.Errorf("video call cancel error, %s", callRes.Msg)
		gLog.Error("video call cancel status is not 0", zap.Error(err), zap.String("apiLog", apiLog), zap.String("curlStr", curlStr))
		return
	}
	return
}

func (m *VideoCallService) GetRealAnchorInfo(c *gin.Context, reqData *request.GetRealAnchorInfoReq) (res response.RealAnchorInfoRes, err error) {
	gLog := global.LOG.With(zap.String("func", "VideoCallService.GetAnchorOnlineStatus"), zap.Any("reqData", reqData))
	var (
		onlineRes response.MicroUserOnlineRes
		userInfo  *model.DigitalUser
		aiInfo    *model.AiRole
		aiSer     AiRoleService
		userSer   DigitalUserService
		userId    = utils.GetDigitalUserID(c)
	)
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		gLog.Error("userSer.GetById", zap.Error(err))
		return
	}
	// appInfo := global.AppMap[userInfo.AppID]
	// isSub := userInfo.IsSub()
	// isSVip := userInfo.IsSVip()
	// isContentVip := userInfo.IsContentVip()
	// isChatVip := userInfo.IsChatVip()
	if aiInfo, err = aiSer.GetByRoleId(reqData.RoleId); err != nil {
		gLog.Error("aiSer.GetByRoleId", zap.Error(err))
		return
	}
	if aiInfo.RealAnchorId == 0 {
		err = fmt.Errorf("aiInfo.RealAnchorId is 0")
		gLog.Error("aiInfo.RealAnchorId is 0", zap.Error(err))
		return
	}
	// 获取主播在线状态
	if onlineRes, err = m.OnlineStatus(aiInfo.RealAnchorId, userInfo); err != nil {
		err = fmt.Errorf("AnchorOnlineStatus error: %v", err)
		gLog.Error("AnchorOnlineStatus", zap.Error(err))
		return
	}
	res.Online = onlineRes.Data.Online
	res.CanCall = onlineRes.Data.Online == model.OnlineStatusOnline
	return
}

// CallStart 开始通话
func (m *VideoCallService) CallStart(c *gin.Context, reqData *request.CallStartReq) (res response.CallStartRes, err error) {
	gLog := global.LOG.With(zap.String("func", "VideoCallService.StartCall"), zap.Any("reqData", reqData))
	var (
		anchorRtcToken string
		userRtcToken   string
		resHeader      http.Header
		onlineRes      response.MicroUserOnlineRes
		callRes        response.MicroCallRes
		userInfo       *model.DigitalUser
		aiInfo         *model.AiRole
		aiSer          AiRoleService
		userSer        DigitalUserService
		profileInfo    *model.DigitalUserProfile
		profileSer     DigitalUserProfileService
		userId         = utils.GetDigitalUserID(c)
		busyTip        = "anchor is busy line"
	)
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		gLog.Error("userSer.GetById", zap.Error(err))
		return
	}
	appInfo := global.AppMap[userInfo.AppID]
	isSub := userInfo.IsSub()
	isSVip := userInfo.IsSVip()
	isContentVip := userInfo.IsContentVip()
	isChatVip := userInfo.IsChatVip()
	isAudit := userInfo.IsAudit()
	if isAudit {
		err = fmt.Errorf(busyTip)
		return
	}
	if !isSub {
		err = errs.RequireSubErr
		return
	}
	appDetailConfig := appInfo.DetailConfig.Data()
	appAgoraData := appInfo.AgoraData.Data()
	var minPrice = appDetailConfig.VideoCallMinPriceFree
	if isSVip {
		minPrice = appDetailConfig.VideoCallMinPriceSVip
	} else if isSub {
		minPrice = appDetailConfig.VideoCallMinPriceSub
	} else if isContentVip {
		minPrice = appDetailConfig.VideoCallMinPriceContentVip
	} else if isChatVip {
		minPrice = appDetailConfig.VideoCallMinPriceChatVip
	}
	gLog = gLog.With(zap.Int("minPrice", minPrice))
	if profileInfo, err = profileSer.GetByUserId(userId); err != nil {
		gLog.Error("profileSer.GetByUserId", zap.Error(err))
		return
	}

	freeTime, err := call.GetFreeTime(c, userId)
	if err != nil && err != redis.Nil {
		return
	}
	if freeTime <= 0 && profileInfo.FlowerAmount < minPrice {
		err = errs.FloShopErr
		return
	}

	if aiInfo, err = aiSer.GetByRoleId(reqData.RoleId); err != nil {
		gLog.Error("aiSer.GetByRoleId", zap.Error(err))
		return
	}
	if aiInfo.RealAnchorId == 0 {
		err = fmt.Errorf("aiInfo.RealAnchorId is 0")
		gLog.Error("aiInfo.RealAnchorId is 0", zap.Error(err))
		return
	}
	// 获取主播在线状态
	if onlineRes, err = m.OnlineStatus(aiInfo.RealAnchorId, userInfo); err != nil {
		err = fmt.Errorf("AnchorOnlineStatus error: %v", err)
		gLog.Error("AnchorOnlineStatus", zap.Error(err))
		return
	}
	gLog = gLog.With(zap.Any("onlineRes", onlineRes))
	if onlineRes.Data.Online == model.OnlineStatusBusy {
		err = fmt.Errorf(busyTip)
		gLog.Error("anchor is busy")
		return
	}
	if onlineRes.Data.Online != model.OnlineStatusOnline {
		err = fmt.Errorf("anchor is not online")
		gLog.Error("anchor is not online", zap.Error(err))
		return
	}
	roomId := uuid.NewString()
	gLog = gLog.With(zap.String("roomId", roomId))
	// 生成声网token
	if anchorRtcToken, err = agora.BuildTokenWithUid(appAgoraData.AppId, appAgoraData.AppCert, roomId, aiInfo.RealAnchorId, global.RtcExpireSec); err != nil {
		gLog.Error("BuildTokenWithUid error", zap.Error(err))
		return
	}
	gLog = gLog.With(zap.String("anchorRtcToken", anchorRtcToken))
	if callRes, resHeader, err = m.VideoCall(aiInfo.RealAnchorId, roomId, anchorRtcToken, userInfo); err != nil {
		err = fmt.Errorf("CallAnchor error: %v", err)
		gLog.Error("CallAnchor error", zap.Error(err))
		ddNotifyMsg := fmt.Sprintf(`## 主播中台呼叫接口异常
- 用户ID: %d
- 真人主播ID: %d
- 错误信息: %v
`, userId, aiInfo.RealAnchorId, err)
		SendDDNotice(ddNotifyMsg)
		return
	}
	xWho := resHeader.Get("X-Who")
	if xWho == "" {
		xWho = fmt.Sprintf("%d", userInfo.Id)
	}
	xWhoInt, convErr := strconv.Atoi(xWho)
	if convErr != nil {
		xWhoInt = int(userInfo.Id)
	}
	gLog = gLog.With(zap.Any("callRes", callRes))
	// 记录通话记录
	callRecord := model.VideoCall{
		AppID:         userInfo.AppID,
		AiId:          aiInfo.Id,
		AiRoleId:      aiInfo.RoleID,
		RoomId:        callRes.Data.RoomId,
		UserVersion:   userInfo.Version,
		RealAnchorId:  aiInfo.RealAnchorId,
		DigitalUserId: userId,
		CallType:      model.CallTypeUserCall,
		CallStatus:    model.CallStatusCreate,
		StartTime:     time.Time{},
		EndTime:       time.Time{},
		CallSecond:    0,
		CloseId:       0,
		MinPrice:      minPrice,
		Minute:        0,
		TotalPrice:    0,
	}
	if err = m.Create(c, &callRecord); err != nil {
		gLog.Error("create VideoCall error", zap.Error(err))
		return
	}
	// 生成用户的声网token
	if userRtcToken, err = agora.BuildTokenWithUid(appAgoraData.AppId, appAgoraData.AppCert, roomId, uint(xWhoInt), global.RtcExpireSec); err != nil {
		gLog.Error("BuildTokenWithUid error", zap.Error(err))
		return
	}
	res = response.CallStartRes{
		VideoCallId:    callRecord.Id,
		RoomId:         roomId,
		JoinRoomUserId: xWhoInt,
		RtcToken:       userRtcToken,
	}
	return
}

// CallCancel 取消通话
func (m *VideoCallService) CallCancel(c *gin.Context, reqData *request.CallCancelReq) (err error) {
	gLog := global.LOG.With(zap.String("func", "VideoCallService.CallCancel"), zap.Any("reqData", reqData))
	var (
		cancelRes  response.MicroVideoCallCancelRes
		callRecord *model.VideoCall
		userInfo   *model.DigitalUser
		aiInfo     *model.AiRole
		aiSer      AiRoleService
		userSer    DigitalUserService
		userId     = utils.GetDigitalUserID(c)
	)
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		gLog.Error("userSer.GetById", zap.Error(err))
		return
	}
	if aiInfo, err = aiSer.GetByRoleId(reqData.RoleId); err != nil {
		gLog.Error("aiSer.GetByRoleId", zap.Error(err))
		return
	}
	if aiInfo.RealAnchorId == 0 {
		err = fmt.Errorf("aiInfo.RealAnchorId is 0")
		gLog.Error("aiInfo.RealAnchorId is 0", zap.Error(err))
		return
	}
	// 查询通话记录
	if callRecord, err = m.GetByIdWithCache(c, reqData.VideoCallId); err != nil {
		err = fmt.Errorf("get video call record error")
		gLog.Error("get video call record error", zap.Error(err))
		return
	}
	// 取消通话
	if cancelRes, err = m.VideoCallCancel(reqData.RoomId, aiInfo.RealAnchorId, userInfo); err != nil {
		gLog.Error("VideoCallCancel error", zap.Error(err))
		return
	}
	gLog = gLog.With(zap.Any("cancelRes", cancelRes))
	// 修改通话记录
	callRecord.CallStatus = model.CallStatusClose
	callRecord.CloseId = userId
	callRecord.EndTime = time.Now()
	if err = m.Update(c, callRecord); err != nil {
		err = fmt.Errorf("update video call error")
		gLog.Error("update VideoCall error", zap.Error(err))
		return
	}
	return
}

// CallClose 通话挂断
func (m *VideoCallService) CallClose(c *gin.Context, reqData *request.CallCloseReq) (err error) {
	gLog := global.LOG.With(zap.String("func", "VideoCallService.CallClose"), zap.Any("reqData", reqData))
	var (
		callRecord *model.VideoCall
		closeRes   response.MicroVideoCallCloseRes
		userInfo   *model.DigitalUser
		aiInfo     *model.AiRole
		aiSer      AiRoleService
		userSer    DigitalUserService
		msgService MsgService
		userId     = utils.GetDigitalUserID(c)
	)
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		gLog.Error("userSer.GetById", zap.Error(err))
		return
	}
	if aiInfo, err = aiSer.GetByRoleId(reqData.RoleId); err != nil {
		gLog.Error("aiSer.GetByRoleId", zap.Error(err))
		return
	}
	if aiInfo.RealAnchorId == 0 {
		err = fmt.Errorf("aiInfo.RealAnchorId is 0")
		gLog.Error("aiInfo.RealAnchorId is 0", zap.Error(err))
		return
	}
	// 查询通话记录
	if callRecord, err = m.GetByIdWithCache(c, reqData.VideoCallId); err != nil {
		err = fmt.Errorf("get video call record error")
		gLog.Error("get video call record error", zap.Error(err))
		return
	}
	if callRecord.CallStatus != model.CallStatusCalling {
		err = fmt.Errorf("call status is not calling")
		gLog.Error("call status is not calling", zap.Error(err))
		return
	}
	// 挂断通话
	if closeRes, err = m.VideoCallClose(reqData.RoomId, aiInfo.RealAnchorId, userInfo); err != nil {
		return
	}
	gLog = gLog.With(zap.Any("closeRes", closeRes))
	// 修改通话记录
	callRecord.CallStatus = model.CallStatusClose
	callRecord.CloseId = userId
	callRecord.EndTime = time.Now()
	if callRecord.TotalPrice > 0 {
		callRecord.CallSecond = int(callRecord.EndTime.Sub(callRecord.StartTime).Seconds())
		// 增加亲密值
		// 保存亲密度
		if err = msgService.SaveIntimateRecord(c, userId, aiInfo, model.IntimateTypeAdd, model.SubTypeVideoCall); err != nil {
			return
		}
	}
	if err = m.Update(c, callRecord); err != nil {
		err = fmt.Errorf("update video call error")
		gLog.Error("update VideoCall error", zap.Error(err))
		return
	}

	return
}

// CallDeduction 通话扣费
func (m *VideoCallService) CallDeduction(c *gin.Context, reqData *request.CallDeductionReq) (res *response.CallDeductionRes, err error) {
	gLog := global.LOG.With(zap.String("func", "VideoCallService.CallDeduction"), zap.Any("reqData", reqData))
	var (
		userInfo    *model.DigitalUser
		userSer     DigitalUserService
		userId      = utils.GetDigitalUserID(c)
		profileSer  DigitalUserProfileService
		profileInfo *model.DigitalUserProfile
	)
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		gLog.Error("userSer.GetById", zap.Error(err))
		return
	}

	appInfo := global.AppMap[userInfo.AppID]
	isSub := userInfo.IsSub()
	isSVip := userInfo.IsSVip()
	isContentVip := userInfo.IsContentVip()
	isChatVip := userInfo.IsChatVip()
	appDetailConfig := appInfo.DetailConfig.Data()
	var minPrice = appDetailConfig.VideoCallMinPriceFree
	if isSVip {
		minPrice = appDetailConfig.VideoCallMinPriceSVip
	} else if isSub {
		minPrice = appDetailConfig.VideoCallMinPriceSub
	} else if isContentVip {
		minPrice = appDetailConfig.VideoCallMinPriceContentVip
	} else if isChatVip {
		minPrice = appDetailConfig.VideoCallMinPriceChatVip
	}

	if profileInfo, err = profileSer.GetByUserId(userId); err != nil {
		gLog.Error("profileSer.GetByUserId", zap.Error(err))
		return
	}

	//免费通话扣除
	freeStatus, err := call.DelFreeTime(c, userId, 60)
	if err != nil {
		return
	}
	var h CallChargeInterface
	base := callBase{
		c:           c,
		reqData:     reqData,
		userInfo:    userInfo,
		minPrice:    minPrice,
		profileInfo: profileInfo,
		m:           m,
	}
	if freeStatus {
		h = &callFree{base}
	} else {
		h = &callCharge{base}
	}
	res, err = h.Handler()
	if err != nil {
		return
	}

	return
}

type CallChargeInterface interface {
	Handler() (res *response.CallDeductionRes, err error)
}

type callBase struct {
	c           *gin.Context
	reqData     *request.CallDeductionReq
	userInfo    *model.DigitalUser
	minPrice    int
	profileInfo *model.DigitalUserProfile
	m           *VideoCallService
}

type callCharge struct {
	callBase
}

type callFree struct {
	callBase
}

func (h *callCharge) Handler() (res *response.CallDeductionRes, err error) {
	reqData := h.reqData
	c := h.c
	minPrice := h.minPrice
	m := h.m
	gLog := global.LOG.With(zap.String("func", "VideoCallService.CallDeduction"), zap.Any("reqData", reqData))
	var (
		callRecord  *model.VideoCall
		profileInfo = h.profileInfo
		userInfo    = h.userInfo
		aiInfo      *model.AiRole
		aiSer       AiRoleService
		userId      = utils.GetDigitalUserID(c)
	)
	res = &response.CallDeductionRes{}
	res.MinPrice = minPrice
	if profileInfo.FlowerAmount < minPrice {
		err = errs.FloShopErr
		return
	}
	if aiInfo, err = aiSer.GetByRoleId(reqData.RoleId); err != nil {
		gLog.Error("aiSer.GetByRoleId", zap.Error(err))
		return
	}
	// 查询通话记录
	if callRecord, err = m.GetByIdWithCache(c, reqData.VideoCallId); err != nil {
		err = fmt.Errorf("get video call record error")
		gLog.Error("get video call record error", zap.Error(err))
		return
	}
	if callRecord.CallStatus != model.CallStatusCalling {
		err = fmt.Errorf("call status is not calling")
		gLog.Error("call status is not calling", zap.Error(err))
		return
	}
	// 通话扣费
	err = global.DB.Transaction(func(tx *gorm.DB) (tErr error) {
		go TdPointer(userInfo, profileInfo, td.ServerEvent, map[string]interface{}{
			"event_tag":         "coins_consume",
			"coins_consume_num": -minPrice,
			"ai_role_id":        aiInfo.RoleID,
			"ai_role_type":      aiInfo.RoleType,
			"source":            "real_call_consume",
		})
		flowerAmountNow := profileInfo.FlowerAmount - minPrice
		// 创建FlowerConsumeRecord记录
		var fRecord = &model.FlowerConsumeRecord{
			DigitalUserId: userId,
			AiId:          aiInfo.Id,
			AiRoleId:      aiInfo.RoleID,
			ConsumeType:   model.ConsumeTypeRealVideoCall,
			RelationID:    fmt.Sprintf("%d", callRecord.Id),
			OriginAmount:  profileInfo.FlowerAmount,
			ConsumeAmount: minPrice,
		}
		if tErr = tx.Model(&model.FlowerConsumeRecord{}).Create(fRecord).Error; tErr != nil {
			return
		}
		updateMap := map[string]interface{}{
			"flower_amount": flowerAmountNow,
		}
		db := tx.Model(&model.DigitalUserProfile{}).Where("id = ? AND flower_amount = ?", profileInfo.Id, profileInfo.FlowerAmount).Updates(updateMap)
		if tErr = db.Error; tErr != nil {
			return
		}
		if db.RowsAffected == 0 {
			tErr = fmt.Errorf("update userProfile failed")
			return
		}
		res.CoinAmount = flowerAmountNow
		res.CanCallMin = flowerAmountNow / minPrice
		return
	})
	// 修改通话记录
	callRecord.Minute += 1
	callRecord.TotalPrice += minPrice
	if err = m.Update(c, callRecord); err != nil {
		err = fmt.Errorf("update video call error")
		gLog.Error("update VideoCall error", zap.Error(err))
		return
	}
	return
}

func (h *callFree) Handler() (res *response.CallDeductionRes, err error) {
	res = &response.CallDeductionRes{
		MinPrice:   h.minPrice,
		CoinAmount: h.profileInfo.FlowerAmount,
		CanCallMin: h.profileInfo.FlowerAmount / h.minPrice,
	}
	return
}
