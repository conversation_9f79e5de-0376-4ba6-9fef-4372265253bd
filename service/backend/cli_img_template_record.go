package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/utils"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
)

func (m *ImgTemplateRecordService) CliList(c *gin.Context) (resMap gin.H, err error) {
	appId := utils.GetAppID(c)
	categoryMap := make(map[uint][]*model.ImgTemplateRecord)
	for _, record := range global.ImgTemplateRecordList {
		categoryList := categoryMap[record.CategoryId]
		if categoryList == nil {
			categoryList = []*model.ImgTemplateRecord{}
		}
		categoryMap[record.CategoryId] = append(categoryList, record)
	}
	categoryContentList := []response.CategoryContentItem{}
	for categoryId, imgRecordList := range categoryMap {
		categoryRecordInfo, categoryRecordInfoExist := global.ImgTemplateCategoryMap[categoryId]
		if !categoryRecordInfoExist {
			continue
		}
		imgList := []response.ImgItem{}
		for _, imgRecord := range imgRecordList {
			imgItem := response.ImgItem{
				ImgId:         imgRecord.Id,
				ImgName:       imgRecord.Name,
				Thumbnail:     utils.S3Url(imgRecord.Thumbnail, appId),
				OriginalImage: utils.S3Url(imgRecord.OriginalImage, appId),
			}
			imgList = append(imgList, imgItem)
		}
		categoryContent := response.CategoryContentItem{
			CategoryId:   categoryId,
			CategoryName: categoryRecordInfo.Name,
			ImgList:      imgList,
		}
		categoryContentList = append(categoryContentList, categoryContent)
	}
	resMap = gin.H{}
	resMap["list"] = categoryContentList
	return
}

func (m *ImgTemplateRecordService) CliFindOne(c *gin.Context, reqData *request.ImgTemplateRecordFindOneReq) (imgItemRes response.ImgItem, err error) {
	var (
		appId = utils.GetAppID(c)
	)
	imgItem, imgItemExist := global.ImgTemplateRecordMap[reqData.ImgId]
	if !imgItemExist {
		err = fmt.Errorf("img record not found")
		return
	}
	imgItemRes = response.ImgItem{
		ImgId:         imgItem.Id,
		ImgName:       imgItem.Name,
		Thumbnail:     utils.S3Url(imgItem.Thumbnail, appId),
		OriginalImage: utils.S3Url(imgItem.OriginalImage, appId),
	}
	return
}
