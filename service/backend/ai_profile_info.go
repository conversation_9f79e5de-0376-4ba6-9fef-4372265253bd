package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
	"time"
)

type AiProfileInfoService struct{}

func (m *AiProfileInfoService) Create(c *gin.Context, reqData *model.AiProfileInfo) (err error) {
	err = global.DB.Model(&model.AiProfileInfo{}).Create(reqData).Error
	if err == nil {
		m.SetCache(c, reqData)
	}
	return
}

func (m *AiProfileInfoService) DeleteById(c *gin.Context, id uint) (err error) {
	err = global.DB.Model(&model.AiProfileInfo{}).Where("id = ?", id).Delete(&model.AiProfileInfo{}).Error
	return
}

func (m *AiProfileInfoService) DeleteByIds(c *gin.Context, reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.AiProfileInfo{}).Where("id IN (?)", reqData.Ids).Delete(&model.AiProfileInfo{}).Error
	return
}

func (m *AiProfileInfoService) Update(c *gin.Context, reqData *model.AiProfileInfo) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.AiProfileInfo{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	if err == nil {
		m.DelCache(c, reqData.AiRoleId)
	}
	return
}

func (m *AiProfileInfoService) GetById(c *gin.Context, id uint) (res *model.AiProfileInfo, err error) {
	err = global.DB.Model(&model.AiProfileInfo{}).Where("id = ?", id).First(&res).Error
	if err == nil {
		m.SetCache(c, res)
	}
	return
}

func (m *AiProfileInfoService) GetByAiRoleId(c *gin.Context, aiRoleId string) (res *model.AiProfileInfo, err error) {
	res, err = m.GetCache(c, aiRoleId)
	if err != nil {
		err = global.DB.Model(&model.AiProfileInfo{}).Where("ai_role_id = ?", aiRoleId).First(&res).Error
		if err == nil {
			m.SetCache(c, res)
		}
	}
	return
}

func (m AiProfileInfoService) CacheKey(aiRoleId string) string {
	return fmt.Sprintf("ai_profile_info:%s", aiRoleId)
}

func (m *AiProfileInfoService) SetCache(ctx context.Context, record *model.AiProfileInfo) {
	var (
		err           error
		recordBytes   []byte
		cacheRedisKey = m.CacheKey(record.AiRoleId)
	)
	recordBytes, err = json.Marshal(record)
	if err != nil {
		global.LOG.Error("SetCache", zap.Error(err))
	}
	recordStr := string(recordBytes)
	if err = global.REDIS.SetEx(ctx, cacheRedisKey, recordStr, time.Hour).Err(); err != nil {
		global.LOG.Error("SetCache", zap.Error(err))
	}
	return
}

func (m *AiProfileInfoService) DelCache(ctx context.Context, aiRoleId string) {
	var (
		cacheRedisKey = m.CacheKey(aiRoleId)
	)
	global.REDIS.Del(ctx, cacheRedisKey)
	return
}

func (m *AiProfileInfoService) GetCache(ctx context.Context, aiRoleId string) (record *model.AiProfileInfo, err error) {
	var (
		cacheRedisStr string
		cacheRedisKey = m.CacheKey(aiRoleId)
	)
	if cacheRedisStr, err = global.REDIS.Get(ctx, cacheRedisKey).Result(); err != nil {
		return
	}
	if err = json.Unmarshal([]byte(cacheRedisStr), &record); err != nil {
		return
	}
	return
}

func (m *AiProfileInfoService) GetAll(c *gin.Context) (resList []*model.AiProfileInfo, err error) {
	err = global.DB.Model(&model.AiProfileInfo{}).Find(&resList).Error
	return
}

func (m *AiProfileInfoService) GetList(c *gin.Context, info req.AiProfileInfoSearch) (resList []*model.AiProfileInfo, total int64, err error) {
	db := global.DB.Model(&model.AiProfileInfo{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.PageStatus != 0 {
		db = db.Where("page_status = ?", info.PageStatus)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
