package backend

import (
	"aimsg-server/config"
	"aimsg-server/global"
	"aimsg-server/model/cfgs"

	"go.uber.org/zap"
)

type BtcConfigService struct{}

func (m *BtcConfigService) SyncAll() {
	var (
		err       error
		dbResList []*cfgs.BTCConfigs
	)
	dbResList, err = m.GetAll()
	if err != nil {
		global.LOG.Error("SyncAll 失败", zap.Error(err))
		return
	}

	cfgMaps := map[string]string{}
	for _, v := range dbResList {
		cfgMaps[v.CfgKey] = v.CfgVal
	}
	config.UpdateBTCConfigs(cfgMaps)
}
func (m *BtcConfigService) GetAll() (resList []*cfgs.BTCConfigs, err error) {
	err = global.DB.Model(&cfgs.BTCConfigs{}).Find(&resList).Error
	return
}
