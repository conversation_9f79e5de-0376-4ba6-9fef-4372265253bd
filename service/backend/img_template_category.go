package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
)

type ImgTemplateCategoryService struct{}

func (m *ImgTemplateCategoryService) Create(reqData *model.ImgTemplateCategory) (err error) {
	err = global.DB.Model(&model.ImgTemplateCategory{}).Create(reqData).Error
	m.SyncAll()
	return
}

func (m *ImgTemplateCategoryService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.ImgTemplateCategory{}).Where("id = ?", id).Delete(&model.ImgTemplateCategory{}).Error
	m.SyncAll()
	return
}

func (m *ImgTemplateCategoryService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.ImgTemplateCategory{}).Where("id IN (?)", reqData.Ids).Delete(&model.ImgTemplateCategory{}).Error
	m.SyncAll()
	return
}

func (m *ImgTemplateCategoryService) Update(reqData *model.ImgTemplateCategory) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.ImgTemplateCategory{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	m.SyncAll()
	return
}

func (m *ImgTemplateCategoryService) GetById(id uint) (res *model.ImgTemplateCategory, err error) {
	err = global.DB.Model(&model.ImgTemplateCategory{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *ImgTemplateCategoryService) GetAll() (resList []*model.ImgTemplateCategory, err error) {
	err = global.DB.Model(&model.ImgTemplateCategory{}).Order("sort DESC").Find(&resList).Error
	return
}

func (m *ImgTemplateCategoryService) SyncAll() {
	var (
		err     error
		resList []*model.ImgTemplateCategory
		resMap  = map[uint]*model.ImgTemplateCategory{}
	)
	resList, err = m.GetAll()
	if err != nil {
		global.LOG.Error("SyncAll 失败", zap.Error(err))
		return
	}
	for _, category := range resList {
		resMap[category.Id] = category
	}
	global.ImgTemplateCategoryList = resList
	global.ImgTemplateCategoryMap = resMap
}

func (m *ImgTemplateCategoryService) GetList(info req.ImgTemplateCategorySearch) (resList []*model.ImgTemplateCategory, total int64, err error) {
	db := global.DB.Model(&model.ImgTemplateCategory{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.Status != 0 {
		db = db.Where("status = ?", info.Status)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
