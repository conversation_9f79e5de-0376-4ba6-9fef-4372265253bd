package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"encoding/json"
	"errors"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type UserBadgeService struct{}

func (m *UserBadgeService) Create(reqData *model.UserBadge) (err error) {
	err = global.DB.Model(&model.UserBadge{}).Create(reqData).Error
	return
}

func (m *UserBadgeService) SaveUserBadge(userId, badgeId uint) (err error) {
	var (
		searchReq = &model.UserBadge{
			DigitalUserId: userId,
			BadgeId:       badgeId,
		}
		userBadgeInfo *model.UserBadge
	)
	if userBadgeInfo, err = m.SearchOne(searchReq); err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		// 不存在, 创建
		userBadgeInfo = &model.UserBadge{
			DigitalUserId: userId,
			BadgeId:       badgeId,
			Count:         1,
		}
		err = m.Create(userBadgeInfo)
	} else {
		// 存在, 更新
		userBadgeInfo.Count++
		err = m.Update(userBadgeInfo)
	}
	return
}

func (m *UserBadgeService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.UserBadge{}).Where("id = ?", id).Delete(&model.UserBadge{}).Error
	return
}

func (m *UserBadgeService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.UserBadge{}).Where("id IN (?)", reqData.Ids).Delete(&model.UserBadge{}).Error
	return
}

func (m *UserBadgeService) Update(reqData *model.UserBadge) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.UserBadge{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	return
}

func (m *UserBadgeService) GetById(id uint) (res *model.UserBadge, err error) {
	err = global.DB.Model(&model.UserBadge{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *UserBadgeService) GetAll() (resList []*model.UserBadge, err error) {
	err = global.DB.Model(&model.UserBadge{}).Find(&resList).Error
	return
}

func (m *UserBadgeService) SearchOne(reqData *model.UserBadge) (resItem *model.UserBadge, err error) {
	db := global.DB.Model(&model.UserBadge{})
	if reqData.Id != 0 {
		db = db.Where("id = ?", reqData.Id)
	}
	if reqData.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", reqData.DigitalUserId)
	}
	if reqData.BadgeId != 0 {
		db = db.Where("badge_id = ?", reqData.BadgeId)
	}
	err = db.First(&resItem).Error
	return
}

func (m *UserBadgeService) SearchAll(reqData *model.UserBadge) (resList []*model.UserBadge, err error) {
	db := global.DB.Model(&model.UserBadge{})
	if reqData.Id != 0 {
		db = db.Where("id = ?", reqData.Id)
	}
	if reqData.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", reqData.DigitalUserId)
	}
	if reqData.BadgeId != 0 {
		db = db.Where("badge_id = ?", reqData.BadgeId)
	}
	err = db.Find(&resList).Error
	return
}

func (m *UserBadgeService) GetList(info req.UserBadgeSearch) (resList []*model.UserBadge, total int64, err error) {
	db := global.DB.Model(&model.UserBadge{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if info.BadgeId != 0 {
		db = db.Where("badge_id = ?", info.BadgeId)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
