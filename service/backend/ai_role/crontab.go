package ai_role

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/utils"
	"aimsg-server/utils/alarm"
	"context"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	"sort"
	"strconv"
	"time"
)

type AiRoleIndexService struct {
	Cur time.Time
	Ctx context.Context
}
type RoleScore struct {
	AiID  int64
	Score float64
}

type rolePageCache struct {
	List  []*response.AiRoleListV2Res `json:"list"`
	Total int64                       `json:"total"`
}

func GetIndexPagePkg(ctx *gin.Context, req *request.IndexReq, isAudit bool) (res []*response.AiRoleListV2Res, total int64, err error) {
	appId := utils.GetAppID(ctx)
	eamine := model.ExamineTypeNormal
	cacheKey := fmt.Sprintf("ai:index:pkg:v8:app%d:%d:%d:%d", appId, req.RoleType, req.Page, req.PageSize)
	if isAudit {
		eamine = model.ExamineTypeAudit
		cacheKey = fmt.Sprintf("ai:index:pkg:v8:app:eamine%d:%d:%d:%d", appId, req.RoleType, req.Page, req.PageSize)
	}
	cacheStr, _ := global.REDIS.Get(ctx, cacheKey).Result()

	// 如果缓存命中，直接反序列化并返回
	if cacheStr != "" {
		var cached rolePageCache
		if json.Unmarshal([]byte(cacheStr), &cached) == nil {
			return cached.List, cached.Total, nil
		}
	}
	// 缓存未命中，查数据库
	var list []*model.AiRole
	query := global.DB.
		Model(&model.AiRole{})
	if req.RoleType == model.AiRoleTypePublic {
		query = query.Where("enable = ? AND role_type in (?) AND examine = ? AND app_id = ?", model.SwitchStatusNormal, []model.AiRoleType{
			model.AiRoleTypePublic, model.AiRoleTypeHot, model.AiRoleTypeAnime,
		}, eamine, appId)

	} else {
		query = query.Where("enable = ? AND role_type = ? AND examine = ? AND app_id = ?", model.SwitchStatusNormal, req.RoleType, eamine, appId)
	}
	// 查总数
	query.Count(&total)

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	err = query.Order("id desc").Offset(offset).Limit(req.PageSize).Find(&list).Error
	if err != nil {
		return nil, 0, err
	}

	// 构造响应结构
	res = []*response.AiRoleListV2Res{}
	for _, v := range list {
		res = append(res, buildRoleListV2Res(ctx, v, appId))
	}

	// 写入缓存
	cacheData, _ := json.Marshal(&rolePageCache{
		List:  res,
		Total: total,
	})
	_ = global.REDIS.Set(ctx, cacheKey, cacheData, 5*time.Minute).Err()

	return
}

func GetIndexPagePkg4(ctx *gin.Context, req *request.IndexReq) (res []*response.AiRoleListV2Res, total int64, err error) {
	appId := utils.GetAppID(ctx)
	cacheKey := fmt.Sprintf("ai:index:pkg4:v4:app%d:%d:%d:%d", appId, req.RoleType, req.Page, req.PageSize)
	cacheStr, _ := global.REDIS.Get(ctx, cacheKey).Result()

	// 如果缓存命中，直接反序列化并返回
	if cacheStr != "" {
		var cached rolePageCache
		if json.Unmarshal([]byte(cacheStr), &cached) == nil {
			return cached.List, cached.Total, nil
		}
	}

	// 缓存未命中，查数据库
	var list []*model.AiRole
	query := global.DB.
		Model(&model.AiRole{})

	if req.RoleType == model.AiRoleTypePublic {
		query = query.Where("enable = ? AND role_type in (?) AND examine = ? AND app_id = ?", model.SwitchStatusNormal, []model.AiRoleType{
			model.AiRoleTypePublic, model.AiRoleTypeHot, model.AiRoleTypeAnime,
		}, model.ExamineTypeAudit, appId)

	} else {
		query = query.Where("enable = ? AND role_type = ? AND examine = ? AND app_id = ?", model.SwitchStatusNormal, req.RoleType, model.ExamineTypeAudit, appId)
	}
	// 查总数
	query.Count(&total)

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	err = query.Offset(offset).Limit(req.PageSize).Find(&list).Error
	if err != nil {
		return nil, 0, err
	}

	// 构造响应结构
	res = []*response.AiRoleListV2Res{}
	for _, v := range list {
		res = append(res, buildRoleListV2Res(ctx, v, appId))
	}

	// 写入缓存
	cacheData, _ := json.Marshal(&rolePageCache{
		List:  res,
		Total: total,
	})
	_ = global.REDIS.Set(ctx, cacheKey, cacheData, 5*time.Minute).Err()

	return
}
func buildRoleListV2Res(ctx *gin.Context, v2Res *model.AiRole, appId uint) *response.AiRoleListV2Res {
	labelArr := make([]response.AiRoleLabelAndIcon, 0)
	if v2Res.RoleType != 4 {
		labelArr = append(labelArr, response.AiRoleLabelAndIcon{
			RoleLabel: "send photo",
			LabelIcon: "https://images.aipersona.cloud/public/image/b96512b1-c057-40f8-a55b-23c586036e54.webp",
		}, response.AiRoleLabelAndIcon{
			RoleLabel: "send video",
			LabelIcon: "https://images.aipersona.cloud/public/image/8f05d34d-a81f-4d9a-918b-ed925add6068.webp",
		})
	}
	if v2Res.RealAnchorId != 0 {
		labelArr = append(labelArr, response.AiRoleLabelAndIcon{
			RoleLabel: "video calling",
			LabelIcon: "https://images.aipersona.cloud/public/image/85a31d1b-9823-4be1-bc95-0573de45b3a0.webp",
		})
	}
	for _, v := range v2Res.RoleLabels {
		labelArr = append(labelArr, response.AiRoleLabelAndIcon{
			RoleLabel: v,
			LabelIcon: global.REDIS.Get(ctx, fmt.Sprintf(global.AI_ROLE_LABEL_KEY, v)).Val(),
		})
	}
	return &response.AiRoleListV2Res{
		Id:           v2Res.Id,
		RoleID:       v2Res.RoleID,
		RoleType:     v2Res.RoleType,
		RoleLabels:   v2Res.RoleLabels,
		Nickname:     v2Res.Nickname,
		Describe:     v2Res.Describe,
		Gender:       v2Res.Gender,
		Avatar:       utils.S3Url(v2Res.Avatar, appId),
		GifUrl:       utils.S3Url(v2Res.GifUrl, appId),
		VideoUrl:     utils.S3Url(v2Res.VideoUrl, appId),
		WebThumbnail: utils.S3Url(v2Res.WebThumbnail, appId),
		ChatBG:       utils.S3Url(v2Res.ChatBG, appId),
		FirstMsg:     v2Res.FirstMsg,
		RoleDesc:     v2Res.RoleDesc,
		FirstVoice:   utils.S3Url(v2Res.FirstVoice, appId),
		FirstImg:     utils.S3Url(v2Res.FirstImg, appId),
		ChatCount:    v2Res.ChatCount,
		RolePlay:     v2Res.RolePlay,
		RealAnchorId: v2Res.RealAnchorId,
		OnlineStatus: v2Res.OnlineStatus,
		CountStr:     utils.FormatNumber(v2Res.ChatCount),
		LabelList:    labelArr,
	}
}

func GetIndexPage(ctx *gin.Context, req *request.IndexReq) (res []*response.AiRoleListV2Res, total int64, err error) {
	appId := utils.GetAppID(ctx)
	data, total, err := getIndexData(ctx, req)
	if err != nil {
		return
	}
	res = []*response.AiRoleListV2Res{}
	appVersion := ctx.GetHeader("App-Version")
	versionCompare := utils.CompareVersions(appVersion, "3.9.1")
	for _, v2Res := range data {
		// 2025-01-06 孙宏伟需求 ai_label
		labelArr := make([]response.AiRoleLabelAndIcon, 0)
		if v2Res.RoleType != 4 {
			// 创建 AiRoleLabelAndIcon 实例并追加到 labelArr
			labelArr = append(labelArr, response.AiRoleLabelAndIcon{
				RoleLabel: "send photo",
				LabelIcon: "https://images.aipersona.cloud/public/image/b96512b1-c057-40f8-a55b-23c586036e54.webp",
			})
			labelArr = append(labelArr, response.AiRoleLabelAndIcon{
				RoleLabel: "send video",
				LabelIcon: "https://images.aipersona.cloud/public/image/8f05d34d-a81f-4d9a-918b-ed925add6068.webp",
			})
		}
		if v2Res.RealAnchorId != 0 {
			labelArr = append(labelArr, response.AiRoleLabelAndIcon{
				RoleLabel: "video calling",
				LabelIcon: "https://images.aipersona.cloud/public/image/85a31d1b-9823-4be1-bc95-0573de45b3a0.webp",
			})
		}
		if len(v2Res.RoleLabels) > 0 {
			for _, v := range v2Res.RoleLabels {
				labelArr = append(labelArr, response.AiRoleLabelAndIcon{
					RoleLabel: v,
					LabelIcon: global.REDIS.Get(ctx, fmt.Sprintf(global.AI_ROLE_LABEL_KEY, v)).Val(),
				})
			}
		}
		video := utils.S3Url(v2Res.VideoUrl, appId)
		if appId != 4 {
			if v2Res.VideoUrl != "" {
				video = utils.S3Url(v2Res.VideoUrl, appId)
			}
		} else {
			if versionCompare >= 0 {
				if v2Res.VideoUrl != "" {
					video = utils.S3Url(v2Res.VideoUrl, appId)
				}
			} else {
				video = ""
			}
		}

		newObj := &response.AiRoleListV2Res{
			Id:           v2Res.Id,
			RoleID:       v2Res.RoleID,
			RoleType:     v2Res.RoleType,
			RoleLabels:   v2Res.RoleLabels,
			Nickname:     v2Res.Nickname,
			Describe:     v2Res.Describe,
			Gender:       v2Res.Gender,
			Avatar:       utils.S3Url(v2Res.Avatar, appId),
			GifUrl:       utils.S3Url(v2Res.GifUrl, appId),
			VideoUrl:     video,
			WebThumbnail: utils.S3Url(v2Res.WebThumbnail, appId),
			ChatBG:       utils.S3Url(v2Res.ChatBG, appId),
			FirstMsg:     v2Res.FirstMsg,
			RoleDesc:     v2Res.RoleDesc,
			FirstVoice:   utils.S3Url(v2Res.FirstVoice, appId),
			FirstImg:     utils.S3Url(v2Res.FirstImg, appId),
			ChatCount:    v2Res.ChatCount,
			RolePlay:     v2Res.RolePlay,
			RealAnchorId: v2Res.RealAnchorId,
			OnlineStatus: v2Res.OnlineStatus,
			CountStr:     utils.FormatNumber(v2Res.ChatCount),
			LabelList:    labelArr, // 2025-01-06 孙宏伟需求 ai_label
		}
		res = append(res, newObj)
	}
	return
}

func getIndexData(ctx *gin.Context, req *request.IndexReq) (res []*model.AiRole, total int64, err error) {
	appId := utils.GetAppID(ctx)
	zsetKey := fmt.Sprintf("ai:crontab:index:type:v6:%d", req.RoleType)
	if appId == 13 && req.AiType == 1 && req.RoleType == model.AiRoleTypePublic {
		zsetKey = fmt.Sprintf("ai:crontab:index:type:v6:anchor:%d", model.AiRoleTypePublic)
	} else if appId == 13 && req.AiType == 2 && req.RoleType == model.AiRoleTypePublic {
		zsetKey = fmt.Sprintf("ai:crontab:index:type:v6:noanchor:%d", model.AiRoleTypePublic)
	}
	hashKey := "ai:crontab:role:data"

	start := int64((req.Page - 1) * req.PageSize)
	end := start + int64(req.PageSize) - 1

	// 获取总数
	total, err = global.REDIS.ZCard(ctx, zsetKey).Result()
	if err != nil {
		return nil, 0, err
	}

	if total == 0 || start >= total {
		return []*model.AiRole{}, total, nil
	}

	// 分页获取 ID 列表
	idStrs, err := global.REDIS.ZRange(ctx, zsetKey, start, end).Result()
	if err != nil {
		return nil, 0, err
	}

	// 批量获取结构体 JSON
	dataMap, err := global.REDIS.HMGet(ctx, hashKey, idStrs...).Result()
	if err != nil {
		return nil, 0, err
	}

	// 反序列化
	res = make([]*model.AiRole, 0, len(dataMap))
	for _, item := range dataMap {
		if str, ok := item.(string); ok && str != "" {
			var role model.AiRole
			if err := json.Unmarshal([]byte(str), &role); err == nil {
				res = append(res, &role)
			}
		}
	}

	return res, total, nil
}

// 角色兜底排序（3天内）后续角色按照=（3天内）付费人数*0.2+人均付费金额*0.1+人均金币消耗数*0.5+人均对话次数*0.2
func Crontab() {
	var (
		ctx = context.Background()
	)
	m := AiRoleIndexService{
		Cur: time.Now(),
		Ctx: ctx,
	}

	//获取排序列表
	list, err := m.getAiRoleList()
	if err != nil {
		alarm.P0("首页推荐", err)
		return
	}
	//设置推荐列表
	err = m.setIndex(list)
	if err != nil {
		alarm.P0("首页推荐", err)
		return
	}
	return
}

func (m *AiRoleIndexService) setIndex(list []*RoleScore) (err error) {
	// 获取角色信息
	roleList, err := m.getListInfo(list)
	if err != nil {
		return
	}

	ctx := context.Background()
	pipe := global.REDIS.Pipeline()
	zsetKey := fmt.Sprintf("ai:crontab:index:type:v6:%d", model.AiRoleTypePublic)
	zsetKeyAnchor := fmt.Sprintf("ai:crontab:index:type:v6:anchor:%d", model.AiRoleTypePublic)
	zsetKeyNoAnchor := fmt.Sprintf("ai:crontab:index:type:v6:noanchor:%d", model.AiRoleTypePublic)
	zsetKey2 := fmt.Sprintf("ai:crontab:index:type:v6:%d", model.AiRoleTypeHot)
	zsetKey3 := fmt.Sprintf("ai:crontab:index:type:v6:%d", model.AiRoleTypeAnime)
	zsetKey4 := fmt.Sprintf("ai:crontab:index:type:v6:%d", model.AiRoleTypeFans)
	pipe.Del(ctx, zsetKey, zsetKey2, zsetKey3, zsetKey4, zsetKeyAnchor, zsetKeyNoAnchor) // 清空旧排序数据（可选）
	hashKey := "ai:crontab:role:data"
	for k, role := range roleList {
		if role.RoleType == model.AiRoleTypePublic || role.RoleType == model.AiRoleTypeHot || role.RoleType == model.AiRoleTypeAnime {
			if role.RealAnchorId != 0 {
				pipe.ZAdd(ctx, zsetKeyAnchor, redis.Z{
					Score:  float64(k),
					Member: role.Id,
				})
			} else {
				pipe.ZAdd(ctx, zsetKeyNoAnchor, redis.Z{
					Score:  float64(k),
					Member: role.Id,
				})
			}
		}
		roleZsetKey := fmt.Sprintf("ai:crontab:index:type:v6:%d", role.RoleType)
		if role.RoleType == model.AiRoleTypeHot || role.RoleType == model.AiRoleTypeAnime {
			// 加入排序集合
			pipe.ZAdd(ctx, fmt.Sprintf("ai:crontab:index:type:v6:%d", model.AiRoleTypePublic), redis.Z{
				Score:  float64(k),
				Member: role.Id,
			})
		}
		// 加入排序集合
		pipe.ZAdd(ctx, roleZsetKey, redis.Z{
			Score:  float64(k),
			Member: role.Id,
		})
		// 存结构体到 hash（ID -> JSON）
		data, _ := json.Marshal(role)
		pipe.HSet(ctx, hashKey, role.Id, data)
	}
	pipe.Expire(ctx, zsetKey, 24*time.Hour*10)
	pipe.Expire(ctx, zsetKeyAnchor, 24*time.Hour*10)
	pipe.Expire(ctx, zsetKeyNoAnchor, 24*time.Hour*10)
	pipe.Expire(ctx, zsetKey2, 24*time.Hour*10)
	pipe.Expire(ctx, zsetKey3, 24*time.Hour*10)
	pipe.Expire(ctx, zsetKey4, 24*time.Hour*10)
	pipe.Expire(ctx, hashKey, 24*time.Hour*10) // 可选设置过期时间
	if _, err = pipe.Exec(ctx); err != nil {
		return err
	}

	return
}

func (m *AiRoleIndexService) getListInfo(list []*RoleScore) (res []*model.AiRole, err error) {
	const batchSize = 500
	if len(list) == 0 {
		return []*model.AiRole{}, nil
	}

	// 收集 AiRoleID
	ids := make([]int64, 0, len(list))
	for _, v := range list {
		ids = append(ids, v.AiID)
	}

	// 查询所有 AiRole，并构建 map
	roleMap := make(map[int64]*model.AiRole, len(ids))
	for i := 0; i < len(ids); i += batchSize {
		end := i + batchSize
		if end > len(ids) {
			end = len(ids)
		}
		subIDs := ids[i:end]

		var batch []*model.AiRole
		err = global.DB.
			Model(&model.AiRole{}).
			Where("id IN (?) AND enable = ? AND role_type != ? AND examine != ?", subIDs, model.SwitchStatusNormal, model.AiRoleTypePrivate, model.ExamineTypeAudit).
			Find(&batch).Error
		if err != nil {
			alarm.P0("首页推荐", err)
			return
		}

		for _, role := range batch {
			roleMap[int64(role.Id)] = role
		}
	}

	// 按照 list 顺序重新组装结果
	res = make([]*model.AiRole, 0, len(list))
	for _, item := range list {
		if role, ok := roleMap[item.AiID]; ok {
			res = append(res, role)
		}
	}

	return
}

func (m *AiRoleIndexService) getAiRoleList() (res []*RoleScore, err error) {
	list, err := m.flowerConsumeRecord()
	if err != nil {
		return
	}
	list2, err := m.RoleOrderRecord()
	if err != nil {
		return
	}
	list3, err := m.AiRoleMsgRecord()
	if err != nil {
		return
	}
	aiRoleScoreMap := map[int64]float64{}
	for _, v := range list {
		aiRoleScoreMap[v.AiID] += v.Score
	}
	for _, v := range list2 {
		aiRoleScoreMap[v.AiID] += v.Score
	}
	for _, v := range list3 {
		aiRoleScoreMap[v.AiID] += v.Score
	}
	sorted := lo.Entries(aiRoleScoreMap) // []lo.Entry[int64, float64]
	sort.Slice(sorted, func(i, j int) bool {
		return sorted[i].Value > sorted[j].Value // 从大到小
	})
	// 转成 Entry 切片
	entries := lo.Entries(aiRoleScoreMap) // []lo.Entry[int64, float64]

	// 按 value 从大到小排序
	sort.Slice(entries, func(i, j int) bool {
		return entries[i].Value > entries[j].Value
	})

	// 转成 []*RoleScore
	res = lo.Map(entries, func(e lo.Entry[int64, float64], _ int) *RoleScore {
		return &RoleScore{
			AiID:  e.Key,
			Score: e.Value,
		}
	})
	return
}

type FlowerRoleConsumeStats struct {
	AiRoleID   string  `gorm:"column:ai_id"`
	AvgConsume float64 `gorm:"column:avg_consume"`
}

// 人均金币消耗数
func (c *AiRoleIndexService) flowerConsumeRecord() (res []*RoleScore, err error) {

	var list []*FlowerRoleConsumeStats
	err = global.DB.
		Table("flower_consume_record").
		Select("ai_id, AVG(ABS(consume_amount)) AS avg_consume").
		Where("created_at >= ? AND ai_id != '' AND ai_id IS NOT NULL AND deleted_at IS NULL", c.Cur.Add(-time.Hour*24*3)).
		Group("ai_id").
		Scan(&list).Error
	if err != nil {
		return nil, err
	}

	res = make([]*RoleScore, 0, len(list))
	for _, v := range list {
		var aiRoleId int64
		aiRoleId, err = strconv.ParseInt(v.AiRoleID, 10, 64)
		res = append(res, &RoleScore{
			AiID:  aiRoleId,
			Score: v.AvgConsume * 0.5,
		})
	}
	return
}

type RoleOrderStats struct {
	AiRoleID     int64   `gorm:"column:ai_role_id"`
	RecordCount  int64   `gorm:"column:record_count"`   //付费人数
	AvgPayAmount float64 `gorm:"column:avg_pay_amount"` //人均付费金额
}

// 付费人数,人均付费金额
func (c *AiRoleIndexService) RoleOrderRecord() (res []*RoleScore, err error) {
	var list []*RoleOrderStats
	err = global.DB.
		Table("orders").
		Select("ai_role_id, COUNT(DISTINCT(digital_user_id)) AS record_count, AVG(pay_amount) AS avg_pay_amount").
		Where("ai_role_id != 0 AND ai_role_id IS NOT NULL AND deleted_at IS NULL AND order_status = ? and created_at >= ?", model.OSPayGoodSuccess, c.Cur.Add(-time.Hour*24*3)).
		Group("ai_role_id").
		Scan(&list).Error

	if err != nil {
		return nil, err
	}

	res = make([]*RoleScore, 0, len(list))
	for _, v := range list {
		res = append(res, &RoleScore{
			AiID:  v.AiRoleID,
			Score: float64(v.RecordCount)*0.2 + v.AvgPayAmount*0.1,
		})
	}
	return
}

type AiRoleMsgCount struct {
	AiID        int64 `gorm:"column:ai_id"`
	RecordCount int64 `gorm:"column:record_count"`
}

// 人均对话次数
func (c *AiRoleIndexService) AiRoleMsgRecord() (res []*RoleScore, err error) {
	var list []*AiRoleMsgCount
	err = global.DB.
		Table("user_ai_msg_count").
		Select("ai_id, COUNT(*) AS record_count").
		Where("ai_id != '' AND ai_id IS NOT NULL").
		Group("ai_id").
		Scan(&list).Error

	if err != nil {
		return nil, err
	}

	res = make([]*RoleScore, 0, len(list))
	for _, v := range list {
		res = append(res, &RoleScore{
			AiID:  v.AiID,
			Score: float64(v.RecordCount) * 0.2,
		})
	}
	return
}
