package ai_role

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/utils"
	"aimsg-server/utils/alarm"
	"context"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
	"github.com/redis/go-redis/v9"
	"log"
	"time"
)

// RecommendResponse 用于解析推荐接口的响应
type RecommendResponse struct {
	Roles         []uint `json:"roles"`
	RecommendType string `json:"recommend_type"`
}

func IndexRecommendV2(ctx *gin.Context, req *request.IndexReq) (res []*response.AiRoleListV2Res, total int64, err error) {
	userId := utils.GetDigitalUserID(ctx)
	data, err := GetRecommendations(userId, -1)
	if err != nil {
		return
	}
	return RecommendType(ctx, data, req)
}

func RecommendType(ctx *gin.Context, data *RecommendResponse, req *request.IndexReq) (res []*response.AiRoleListV2Res, total int64, err error) {
	res = []*response.AiRoleListV2Res{}
	if len(data.Roles) == 0 {
		return
	}
	roleMap, err := loadRolesFromRecommendIDs(ctx, data.Roles)
	if err != nil {
		return
	}
	appId := utils.GetAppID(ctx)
	appVersion := ctx.GetHeader("App-Version")
	versionCompare := utils.CompareVersions(appVersion, "3.9.1")
	index := -1
	ids := make([]uint, 0, len(data.Roles))
	for _, roleId := range data.Roles {
		if _, ok := roleMap[roleId]; !ok {
			continue
		}
		v2Res := roleMap[roleId]
		if req.RoleType == 1 {
			if v2Res.RoleType != model.AiRoleTypePublic && v2Res.RoleType != model.AiRoleTypeHot && v2Res.RoleType != model.AiRoleTypeAnime {
				continue
			}
			if appId == 13 && v2Res.RealAnchorId == 0 {
				continue
			}
		} else if req.RoleType != v2Res.RoleType {
			continue
		}
		index++
		if index < req.PageSize*(req.Page-1) {
			continue
		}
		ids = append(ids, roleId)
		if index == req.PageSize {
			break
		}
	}
	total = int64(len(ids))
	if total == 0 {
		return
	}
	for _, roleId := range ids {
		v2Res := roleMap[roleId]
		// 2025-01-06 孙宏伟需求 ai_label
		labelArr := make([]response.AiRoleLabelAndIcon, 0)
		if v2Res.RoleType != 4 {
			// 创建 AiRoleLabelAndIcon 实例并追加到 labelArr
			labelArr = append(labelArr, response.AiRoleLabelAndIcon{
				RoleLabel: "send photo",
				LabelIcon: "https://images.aipersona.cloud/public/image/b96512b1-c057-40f8-a55b-23c586036e54.webp",
			})
			labelArr = append(labelArr, response.AiRoleLabelAndIcon{
				RoleLabel: "send video",
				LabelIcon: "https://images.aipersona.cloud/public/image/8f05d34d-a81f-4d9a-918b-ed925add6068.webp",
			})
		}
		if v2Res.RealAnchorId != 0 {
			labelArr = append(labelArr, response.AiRoleLabelAndIcon{
				RoleLabel: "video calling",
				LabelIcon: "https://images.aipersona.cloud/public/image/85a31d1b-9823-4be1-bc95-0573de45b3a0.webp",
			})
		}
		if len(v2Res.RoleLabels) > 0 {
			for _, v := range v2Res.RoleLabels {
				labelArr = append(labelArr, response.AiRoleLabelAndIcon{
					RoleLabel: v,
					LabelIcon: global.REDIS.Get(ctx, fmt.Sprintf(global.AI_ROLE_LABEL_KEY, v)).Val(),
				})
			}
		}
		video := utils.S3Url(v2Res.VideoUrl, appId)
		if appId != 4 {
			if v2Res.VideoUrl != "" {
				video = utils.S3Url(v2Res.VideoUrl, appId)
			}
		} else {
			if versionCompare >= 0 {
				if v2Res.VideoUrl != "" {
					video = utils.S3Url(v2Res.VideoUrl, appId)
				}
			} else {
				video = ""
			}
		}

		newObj := &response.AiRoleListV2Res{
			Id:           v2Res.Id,
			RoleID:       v2Res.RoleID,
			RoleType:     v2Res.RoleType,
			RoleLabels:   v2Res.RoleLabels,
			Nickname:     v2Res.Nickname,
			Describe:     v2Res.Describe,
			Gender:       v2Res.Gender,
			Avatar:       utils.S3Url(v2Res.Avatar, appId),
			GifUrl:       utils.S3Url(v2Res.GifUrl, appId),
			VideoUrl:     video,
			WebThumbnail: utils.S3Url(v2Res.WebThumbnail, appId),
			ChatBG:       utils.S3Url(v2Res.ChatBG, appId),
			FirstMsg:     v2Res.FirstMsg,
			RoleDesc:     v2Res.RoleDesc,
			FirstVoice:   utils.S3Url(v2Res.FirstVoice, appId),
			FirstImg:     utils.S3Url(v2Res.FirstImg, appId),
			ChatCount:    v2Res.ChatCount,
			RolePlay:     v2Res.RolePlay,
			RealAnchorId: v2Res.RealAnchorId,
			OnlineStatus: v2Res.OnlineStatus,
			CountStr:     utils.FormatNumber(v2Res.ChatCount),
			LabelList:    labelArr, // 2025-01-06 孙宏伟需求 ai_label
		}
		res = append(res, newObj)
	}
	return res, total, nil
}

func loadRolesFromRecommendIDs(ctx *gin.Context, ids []uint) (map[uint]*model.AiRole, error) {
	if len(ids) == 0 {
		return nil, nil
	}

	const batchSize = 500
	roleMap := make(map[uint]*model.AiRole, len(ids))

	// 1. Redis 批量获取缓存
	missingIDs := make([]uint, 0)
	cachedRoles := make(map[uint]*model.AiRole)
	redisKey := "ai:role:recommend:v2:%d"
	pipe := global.REDIS.Pipeline()
	cmds := make([]*redis.StringCmd, len(ids))
	for i, id := range ids {
		cmds[i] = pipe.Get(ctx, fmt.Sprintf(redisKey, id))
	}
	_, _ = pipe.Exec(ctx)

	for i, cmd := range cmds {
		val, err := cmd.Result()
		id := uint(ids[i])
		if err == redis.Nil {
			missingIDs = append(missingIDs, ids[i]) // 未命中
		} else if err != nil {
			log.Printf("Redis error: %v", err)
			missingIDs = append(missingIDs, ids[i])
		} else {
			// 假设你使用 JSON 存的，反序列化
			var role model.AiRole
			if err := json.Unmarshal([]byte(val), &role); err == nil {
				cachedRoles[id] = &role
			} else {
				log.Printf("反序列化失败: %v", err)
				missingIDs = append(missingIDs, ids[i]) // fallback
			}
		}
	}

	// 2. 查询数据库
	for i := 0; i < len(missingIDs); i += batchSize {
		end := i + batchSize
		if end > len(missingIDs) {
			end = len(missingIDs)
		}
		subIDs := missingIDs[i:end]

		var batch []*model.AiRole
		err := global.DB.
			Model(&model.AiRole{}).
			Where("id IN (?) AND enable = ? AND role_type != ? AND examine != ?", subIDs, model.SwitchStatusNormal, model.AiRoleTypePrivate, model.ExamineTypeAudit).
			Find(&batch).Error
		if err != nil {
			return nil, fmt.Errorf("数据库查询失败: %w", err)
		}

		// 放入 Redis 缓存和结果 map
		pipe = global.REDIS.Pipeline()
		for _, role := range batch {
			roleMap[role.Id] = role

			// 缓存
			bytes, _ := json.Marshal(role)
			pipe.Set(ctx, fmt.Sprintf(redisKey, role.Id), bytes, time.Minute*5)
		}
		_, _ = pipe.Exec(ctx)
	}

	// 3. 合并 Redis 缓存数据
	for id, role := range cachedRoles {
		roleMap[id] = role
	}

	return roleMap, nil
}

// GetRecommendations 向推荐接口发送请求并返回推荐结果
func GetRecommendations(userID uint, topN int) (*RecommendResponse, error) {
	client := resty.New()

	//url := "https://u459706-a1f3-a8d209b7.nmb1.seetacloud.com:8443/recommend"
	url := "https://u459706-a1f3-a8d209b7.nmb1.seetacloud.com:8443/recommend_test"

	reqBody := map[string]interface{}{
		"user_id": userID,
		"top_n":   topN,
	}

	var respData RecommendResponse

	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(reqBody).
		SetResult(&respData).
		Post(url)

	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}
	if resp.Time() > time.Second {
		Alarm(context.Background(), "大模型首页超时1s", "index_err")
	}

	if resp.IsError() {
		return nil, fmt.Errorf("请求返回错误状态码: %d", resp.StatusCode())
	}

	return &respData, nil
}

func Alarm(ctx context.Context, title, redisKey string) {
	lockKey := fmt.Sprintf("PreventTriggerWithin5Min:trigger_lockv1:%s", redisKey)
	countKey := fmt.Sprintf("PreventTriggerWithin5Min:count:%s", redisKey)

	// 自增计数器
	count, err := global.REDIS.Incr(ctx, countKey).Result()
	if err != nil {
		return
	}

	// 设置计数器的过期时间（仅第一次设置）
	if count == 1 {
		global.REDIS.Expire(ctx, countKey, 5*time.Minute)
	}

	// 如果达到10次，触发报警
	if count <= 10 {
		return
	}
	global.REDIS.Del(ctx, countKey)

	// 尝试设置防重复的 key
	ok, err := global.REDIS.SetNX(ctx, lockKey, time.Now().Unix(), 5*time.Minute).Result()
	if err != nil || !ok {
		// 锁存在，已经触发过报警，直接 return
		return
	}

	// 第一次触发报警
	alarm.P0(title, "5分钟内触发次数大于10次触发")
}
