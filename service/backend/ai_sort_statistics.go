package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"context"
	"errors"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type AiSortStatisticsService struct{}

func (m *AiSortStatisticsService) Save(ctx context.Context, reqData *model.AiSortStatistics) (err error) {
	var (
		searchOneInfo *model.AiSortStatistics
		searchOneReq  = &model.AiSortStatistics{
			StartTime: reqData.StartTime,
			EndTime:   reqData.EndTime,
			AiId:      reqData.AiId,
			AiRoleId:  reqData.AiRoleId,
		}
	)
	searchOneInfo, err = m.SearchOne(ctx, searchOneReq)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = m.Create(ctx, reqData)
			return
		}
		return
	}
	// 更新
	reqData.Id = searchOneInfo.Id
	reqData.CreatedAt = searchOneInfo.CreatedAt
	err = m.Update(ctx, reqData)
	return
}

func (m *AiSortStatisticsService) Create(ctx context.Context, reqData *model.AiSortStatistics) (err error) {
	err = global.DB.Model(&model.AiSortStatistics{}).Create(reqData).Error
	return
}

func (m *AiSortStatisticsService) DeleteById(ctx context.Context, id uint) (err error) {
	err = global.DB.Model(&model.AiSortStatistics{}).Where("id = ?", id).Delete(&model.AiSortStatistics{}).Error
	return
}

func (m *AiSortStatisticsService) DeleteByIds(ctx context.Context, reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.AiSortStatistics{}).Where("id IN (?)", reqData.Ids).Delete(&model.AiSortStatistics{}).Error
	return
}

func (m *AiSortStatisticsService) Update(ctx context.Context, reqData *model.AiSortStatistics) (err error) {
	err = global.DB.Model(&model.AiSortStatistics{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	return
}

func (m *AiSortStatisticsService) GetById(ctx context.Context, id uint) (res *model.AiSortStatistics, err error) {
	err = global.DB.Model(&model.AiSortStatistics{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *AiSortStatisticsService) GetAll(ctx context.Context) (resList []*model.AiSortStatistics, err error) {
	err = global.DB.Model(&model.AiSortStatistics{}).Find(&resList).Error
	return
}

func (m *AiSortStatisticsService) SearchOne(ctx context.Context, info *model.AiSortStatistics) (res *model.AiSortStatistics, err error) {
	db := global.DB.Model(&model.AiSortStatistics{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if !info.StartTime.IsZero() {
		db = db.Where("start_time = ?", info.StartTime)
	}
	if !info.EndTime.IsZero() {
		db = db.Where("end_time = ?", info.EndTime)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.RoleType != 0 {
		db = db.Where("role_type = ?", info.RoleType)
	}
	if info.Nickname != "" {
		db = db.Where("nickname LIKE ?", "%"+info.Nickname+"%")
	}
	if info.Gender != 0 {
		db = db.Where("gender = ?", info.Gender)
	}
	if err = db.First(&res).Error; err != nil {
		return
	}
	return
}

func (m *AiSortStatisticsService) SearchCount(ctx context.Context, info *model.AiSortStatistics) (total int64, err error) {
	db := global.DB.Model(&model.AiSortStatistics{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if !info.StartTime.IsZero() {
		db = db.Where("start_time = ?", info.StartTime)
	}
	if !info.EndTime.IsZero() {
		db = db.Where("end_time = ?", info.EndTime)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.RoleType != 0 {
		db = db.Where("role_type = ?", info.RoleType)
	}
	if info.Nickname != "" {
		db = db.Where("nickname LIKE ?", "%"+info.Nickname+"%")
	}
	if info.Gender != 0 {
		db = db.Where("gender = ?", info.Gender)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	return
}

func (m *AiSortStatisticsService) GetList(ctx context.Context, info req.AiSortStatisticsSearch) (resList []*model.AiSortStatistics, total int64, err error) {
	db := global.DB.Model(&model.AiSortStatistics{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if !info.StartTime.IsZero() {
		db = db.Where("start_time = ?", info.StartTime)
	}
	if !info.EndTime.IsZero() {
		db = db.Where("end_time = ?", info.EndTime)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.RoleType != 0 {
		db = db.Where("role_type = ?", info.RoleType)
	}
	if info.Nickname != "" {
		db = db.Where("nickname LIKE ?", "%"+info.Nickname+"%")
	}
	if info.Gender != 0 {
		db = db.Where("gender = ?", info.Gender)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
