package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm/clause"
)

type ReSubOrdersService struct{}

func (m *ReSubOrdersService) Create(reqData *model.ReSubOrder) (err error) {
	err = global.DB.Model(&model.ReSubOrder{}).Create(reqData).Error
	return
}

func (m *ReSubOrdersService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.ReSubOrder{}).Where("id = ?", id).Delete(&model.ReSubOrder{}).Error
	return
}

func (m *ReSubOrdersService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.ReSubOrder{}).Where("id IN (?)", reqData.Ids).Delete(&model.ReSubOrder{}).Error
	return
}

func (m *ReSubOrdersService) Update(reqData *model.ReSubOrder) (err error) {
	err = global.DB.Model(&model.ReSubOrder{}).Where("id = ?", reqData.Id).Updates(reqData).Error
	return
}

func (m *ReSubOrdersService) GetById(id uint) (res *model.ReSubOrder, err error) {
	err = global.DB.Model(&model.ReSubOrder{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *ReSubOrdersService) GetAll() (resList []*model.ReSubOrder, err error) {
	err = global.DB.Model(&model.ReSubOrder{}).Find(&resList).Error
	return
}

func (m *ReSubOrdersService) GetList(info req.ReSubOrdersSearch) (resList []*model.ReSubOrder, total int64, err error) {
	db := global.DB.Model(&model.ReSubOrder{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.OrderNo != "" {
		db = db.Where("order_no LIKE ?", "%"+info.OrderNo+"%")
	}
	if info.TestStatus != 0 {
		if info.TestStatus == model.SwitchStatusNormal {
			db = db.Where("order_test = 1")
		} else {
			db = db.Where("order_test = 0")
		}
	}
	if info.TradeNo != "" {
		db = db.Where("trade_no LIKE ?", "%"+info.TradeNo+"%")
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.DigitalUserID != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserID)
	}
	if info.ProductID != 0 {
		db = db.Where("product_id = ?", info.ProductID)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
