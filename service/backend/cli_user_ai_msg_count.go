package backend

import (
	"aimsg-server/global"
	"context"
	"errors"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"gorm.io/gorm"
)

func (m *UserAiMsgCountService) FindUpdate(ctx context.Context, userId uint, aiRoleInfo *model.AiRole, editMap map[string]interface{}) (err error) {
	var (
		infoRecord *model.UserAiMsgCount
	)
	infoRecord, err = m.SearchOne(ctx, &model.UserAiMsgCount{
		DigitalUserId: userId,
		AiId:          aiRoleInfo.Id,
	})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 创建
		infoRecord = &model.UserAiMsgCount{
			DigitalUserId: userId,
			AiId:          aiRoleInfo.Id,
			AiRoleId:      aiRoleInfo.RoleID,
			RoleType:      aiRoleInfo.RoleType,
			Gender:        aiRoleInfo.Gender,
		}
		if err = m.Create(ctx, infoRecord); err != nil {
			return
		}
	}
	// 更新
	err = global.DB.Model(&model.UserAiMsgCount{}).Where("id = ?", infoRecord.Id).Updates(editMap).Error
	return
}
