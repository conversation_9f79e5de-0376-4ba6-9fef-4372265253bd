package backend

import (
	"aimsg-server/global"
	"aimsg-server/pkg/channel"
	"aimsg-server/pkg/channel/types"
	"aimsg-server/utils"
	"aimsg-server/utils/notify"
	"context"
	"fmt"
	"time"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

// SpeedBalanceMonitorService Speed 余额监控服务
type SpeedBalanceMonitorService struct{}

// CheckBalance 检查余额并发送通知
func (s *SpeedBalanceMonitorService) CheckBalance() error {

	btcChannels := []model.BtcChannel{}
	if err := global.DB.Find(&btcChannels, " channel_status = ?", model.BtcChannelStatusNormal).Error; err != nil {
		global.LOG.Error("Get btc channel failed", zap.Error(err))
		return err
	}

	for _, btcChannel := range btcChannels {
		ch, err := channel.NewChannel(channel.ChannelCode(btcChannel.ChannelCode), &types.InitChannel{
			AppId:        btcChannel.AppId,
			AppKey:       btcChannel.AppKey,
			AppSecretId:  btcChannel.AppSecretId,
			AppSecretKey: btcChannel.AppSecretKey,
		})

		if err != nil {
			global.LOG.Error("New channel init failed", zap.Error(err))
			continue
		}

		balance, err := ch.BalanceQuery(context.Background())
		if err != nil {
			global.LOG.Error("Balance query failed", zap.Error(err))
			continue
		}

		if balance.AvailableBalance.LessThan(decimal.NewFromInt(global.CONFIG.SpeedAPI.Threshold)) {
			s.sendLowBalanceNotification(balance.AvailableBalance)
		}
	}
	// 检查是否启用监控
	// if !global.CONFIG.SpeedAPI.Enabled {
	// 	global.LOG.Debug("Speed API 余额监控未启用")
	// 	return nil
	// }

	// // 创建 API 客户端
	// client := utils.NewSpeedAPIClient(
	// 	global.CONFIG.SpeedAPI.BaseURL,
	// 	global.CONFIG.SpeedAPI.Authorization,
	// )

	// // 获取余额
	// balance, err := client.GetSATSBalance()
	// if err != nil {
	// 	global.LOG.Error("获取 Speed API 余额失败", zap.Error(err))
	// 	return err
	// }

	// global.LOG.Info("Speed API 余额检查",
	// 	zap.Int64("balance", balance),
	// 	zap.Int64("threshold", global.CONFIG.SpeedAPI.Threshold))

	// // 检查是否低于阈值
	// if balance < global.CONFIG.SpeedAPI.Threshold {
	// 	return s.sendLowBalanceNotification(balance)
	// }

	return nil
}

// sendLowBalanceNotification 发送余额不足通知
func (s *SpeedBalanceMonitorService) sendLowBalanceNotification(balance decimal.Decimal) error {
	if global.CONFIG.SpeedAPI.FeiShuBotID == "" {
		global.LOG.Warn("飞书 Bot ID 未配置，跳过通知发送")
		return nil
	}

	// 构建通知消息
	title := "⚠️ Speed API 余额不足警告"
	content := fmt.Sprintf(`## Speed API 余额监控警告

**当前余额**: %d SATS
**警告阈值**: %d SATS
**检查时间**: %s

余额已低于设定阈值，请及时充值！

---
*此消息由系统自动发送*`,
		balance,
		global.CONFIG.SpeedAPI.Threshold,
		time.Now().Format("2006-01-02 15:04:05"))

	// 发送飞书通知
	err := notify.SendFeiShuTalkV2WithRetry(
		global.CONFIG.SpeedAPI.FeiShuBotID,
		content,
		title,
	)

	if err != nil {
		global.LOG.Error("发送 Speed API 余额不足通知失败", zap.Error(err))
		return err
	}

	global.LOG.Info("Speed API 余额不足通知发送成功",
		zap.String("balance", balance.String()),
		zap.Int64("threshold", global.CONFIG.SpeedAPI.Threshold))

	return nil
}

// GetBalanceStatus 获取余额状态（用于手动查询）
func (s *SpeedBalanceMonitorService) GetBalanceStatus() (map[string]interface{}, error) {
	if !global.CONFIG.SpeedAPI.Enabled {
		return map[string]interface{}{
			"enabled": false,
			"message": "Speed API 余额监控未启用",
		}, nil
	}

	client := utils.NewSpeedAPIClient(
		global.CONFIG.SpeedAPI.BaseURL,
		global.CONFIG.SpeedAPI.Authorization,
	)

	balance, err := client.GetSATSBalance()
	if err != nil {
		return nil, err
	}

	status := map[string]interface{}{
		"enabled":    true,
		"balance":    balance,
		"threshold":  global.CONFIG.SpeedAPI.Threshold,
		"is_low":     balance < global.CONFIG.SpeedAPI.Threshold,
		"check_time": time.Now().Format("2006-01-02 15:04:05"),
		"currency":   "SATS",
	}

	return status, nil
}
