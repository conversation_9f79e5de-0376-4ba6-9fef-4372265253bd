package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/response"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"time"
)

type GuidedGiftGivingService struct{}

// GuidedGiftGivingMsg 2024-12-26 新增礼物引导 孙宏伟需求
func (guide *GuidedGiftGivingService) GuidedGiftGivingMsg() {
	var (
		ctx      = context.Background()
		redisKey = global.GUIDED_GIFT_GIVING_KEY
		m        = MsgService{}
	)
	defer func() {
		if err := recover(); err != nil {
			global.LOG.Panic("guidedGiftMsg panic", zap.Any("err", err))
		}
	}()
	for {
		// 获取引导消息
		guidedGiftGivingMsgStr, err := global.REDIS.RPop(ctx, redisKey).Result()
		time.Sleep(2 * time.Second)
		if err != nil {
			continue
		}
		if guidedGiftGivingMsgStr == "" {
			continue
		}

		var msg response.GuidedGiftGivingMsg
		// 将字符串数据解析到结构体
		err = json.Unmarshal([]byte(guidedGiftGivingMsgStr), &msg)
		if err != nil {
			// global.LOG.Info("guidedGiftMsg is json", zap.Error(err))
			continue
		}
		// global.LOG.Info("guidedGiftMsg msg", zap.Any("msg", msg))
		if msg.UserId != 0 && msg.RoleId != 0 && msg.AppId != 0 {

			// redis 查询 redis 没有去数据库查询 如果存在就直接跳过
			checkRecord := guide.GetGuidedGiftRecord(msg.UserId, msg.RoleId, msg.AppId)
			if checkRecord {
				// global.LOG.Info("guidedGiftMsg record is not empty", zap.Any("msg", msg), zap.Uint("userId", msg.UserId), zap.Uint("roleId", msg.RoleId), zap.Uint("appId", msg.AppId))
				continue
			}
			// 获取对应的次数和配置
			configStr := global.REDIS.Get(ctx, fmt.Sprintf(global.GUIDED_GIFT_CONFIG_KEY, msg.AppId)).Val()
			if configStr == "" {
				// global.LOG.Info("guidedGiftMsg config is empty", zap.Any("msg", msg), zap.Uint("userId", msg.UserId), zap.Uint("roleId", msg.RoleId), zap.Uint("appId", msg.AppId))
				continue
			}
			var config map[int64]int64
			err = json.Unmarshal([]byte(configStr), &config)
			if err != nil {
				// global.LOG.Info("guidedGiftMsg config json is err", zap.Any("msg", msg), zap.Uint("userId", msg.UserId), zap.Uint("roleId", msg.RoleId), zap.Uint("appId", msg.AppId))
				continue
			}

			// 消费查询redis 如果有就跳过 如果没有就继续 redis 没有从数据库查询 保留10天
			num := global.REDIS.IncrBy(ctx, fmt.Sprintf(global.GUIDED_GIFT_NUM_KEY, msg.UserId, msg.RoleId, msg.AppId), 1).Val()
			global.REDIS.Expire(ctx, fmt.Sprintf(global.GUIDED_GIFT_NUM_KEY, msg.UserId, msg.RoleId, msg.AppId), 60*24*time.Hour)
			round, RoundRrr := global.REDIS.Get(ctx, fmt.Sprintf(global.GUIDED_GIFT_ROUND_KEY, msg.UserId, msg.RoleId, msg.AppId)).Int64()
			if RoundRrr != nil && !errors.Is(RoundRrr, redis.Nil) {
				// global.LOG.Info("guidedGiftMsg round is err", zap.Any("msg", msg), zap.Uint("userId", msg.UserId), zap.Uint("roleId", msg.RoleId), zap.Uint("appId", msg.AppId))
				continue
			}
			if round == 0 {
				round = global.REDIS.IncrBy(ctx, fmt.Sprintf(global.GUIDED_GIFT_ROUND_KEY, msg.UserId, msg.RoleId, msg.AppId), 1).Val()
			}
			if num >= config[round] {
				// global.LOG.Info("guidedGiftMsg send msg", zap.Any("msg", msg), zap.Uint("userId", msg.UserId), zap.Uint("roleId", msg.RoleId), zap.Uint("appId", msg.AppId))
				// 超过了对应的次数就要修改回合数并且发送消息 并且次数改为1
				roundNum := global.REDIS.IncrBy(ctx, fmt.Sprintf(global.GUIDED_GIFT_ROUND_KEY, msg.UserId, msg.RoleId, msg.AppId), 1).Val()
				global.REDIS.Expire(ctx, fmt.Sprintf(global.GUIDED_GIFT_ROUND_KEY, msg.UserId, msg.RoleId, msg.AppId), 60*24*time.Hour)
				global.REDIS.Set(ctx, fmt.Sprintf(global.GUIDED_GIFT_NUM_KEY, msg.UserId, msg.RoleId, msg.AppId), 0, 60*24*time.Hour)
				m.AISendUserGiftMsg(ctx, msg.UserId, msg.AiRoleId)
				// 插入数据库 如果大于最终次数才会插入数据库 不然就只是修改redis 发消息
				if _, exists := config[roundNum]; !exists && roundNum != 0 {
					err := guide.Create(ctx, &model.GuidedGiftRecord{
						DigitalUserID: msg.UserId,
						AiRoleId:      msg.RoleId,
						AppID:         msg.AppId,
					})
					if err != nil {
						continue
					}
				}
			}
		}
	}
}

func (guide *GuidedGiftGivingService) Create(ctx context.Context, reqData *model.GuidedGiftRecord) (err error) {
	err = global.DB.Model(&model.GuidedGiftRecord{}).Create(reqData).Error
	return
}

func (guide *GuidedGiftGivingService) GetGuidedGiftRecord(userId, aiRoleId, appId uint) bool {
	// redis里先取 redis没有再取数据库
	recordStr := global.REDIS.Get(context.Background(), fmt.Sprintf(global.GUIDED_GIFT_Record_KEY, userId, aiRoleId, appId)).Val()
	if recordStr != "" {
		return true
	}
	var record model.GuidedGiftRecord
	err := global.DB.Model(&model.GuidedGiftRecord{}).Where("digital_user_id = ? and ai_role_id = ? and app_id = ?", userId, aiRoleId, appId).First(&record).Error
	if err != nil {
		return false
	}
	if record.Id != 0 {
		global.REDIS.Set(context.Background(), fmt.Sprintf(global.GUIDED_GIFT_Record_KEY, userId, aiRoleId, appId), 1, 10*24*time.Hour)
		return true
	}
	return false
}
