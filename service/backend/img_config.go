package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"aimsg-server/utils"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
	"time"
)

type ImgConfigService struct{}

func (m *ImgConfigService) CopyInfo(sourceAppId, targetAppId uint) (err error) {
	var (
		sourceInfos []*model.ImgConfig
		newInfos    []*model.ImgConfig
		nowTime     = time.Now()
	)
	sourceInfos, err = m.FindByAppId(sourceAppId)
	if err != nil {
		return
	}
	for _, sourceInfo := range sourceInfos {
		sourceInfo.Id = 0
		sourceInfo.CreatedAt = nowTime
		sourceInfo.UpdatedAt = nowTime
		sourceInfo.AppID = targetAppId
		newInfos = append(newInfos, sourceInfo)
	}
	if len(newInfos) == 0 {
		return
	}
	err = global.DB.Model(&model.ImgConfig{}).Create(&newInfos).Error
	return
}

func (m *ImgConfigService) FindByAppId(appId uint) (resList []*model.ImgConfig, err error) {
	err = global.DB.Model(&model.ImgConfig{}).Where("app_id = ?", appId).Find(&resList).Error
	return
}

func (m *ImgConfigService) Create(reqData *model.ImgConfig) (err error) {
	err = global.DB.Model(&model.ImgConfig{}).Create(reqData).Error
	m.SyncImgConfig()
	return
}

func (m *ImgConfigService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.ImgConfig{}).Where("id = ?", id).Delete(&model.ImgConfig{}).Error
	m.SyncImgConfig()
	return
}

func (m *ImgConfigService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.ImgConfig{}).Where("id IN (?)", reqData.Ids).Delete(&model.ImgConfig{}).Error
	m.SyncImgConfig()
	return
}

func (m *ImgConfigService) Update(reqData *model.ImgConfig) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.ImgConfig{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	m.SyncImgConfig()
	return
}

func (m *ImgConfigService) GetById(id uint) (res *model.ImgConfig, err error) {
	err = global.DB.Model(&model.ImgConfig{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *ImgConfigService) GetAll() (resList []*model.ImgConfig, err error) {
	err = global.DB.Model(&model.ImgConfig{}).Find(&resList).Error
	return
}

func (m *ImgConfigService) SearchAll(reqData *model.ImgConfig) (resList []*model.ImgConfig, err error) {
	db := global.DB.Model(&model.ImgConfig{})
	if reqData.Id != 0 {
		db = db.Where("id = ?", reqData.Id)
	}
	if reqData.ImgConfigType != 0 {
		db = db.Where("img_config_type = ?", reqData.ImgConfigType)
	}
	if reqData.ConfigStatus != 0 {
		db = db.Where("config_status = ?", reqData.ConfigStatus)
	}
	if reqData.UserStatus != 0 {
		db = db.Where("user_status = ?", reqData.UserStatus)
	}
	err = db.Find(&resList).Error
	return
}

func (m *ImgConfigService) SyncImgConfig() {
	var (
		err     error
		resList []*model.ImgConfig
		imgMap  = map[uint]map[model.ImgConfigType]request.SyncImgConfigItem{}
	)
	searchReq := model.ImgConfig{ConfigStatus: model.SwitchStatusNormal}
	resList, err = m.SearchAll(&searchReq)
	if err != nil {
		global.LOG.Error("SyncImgConfig 失败", zap.Error(err))
		return
	}
	for _, r := range resList {
		appImgTypeMap := imgMap[r.AppID]
		if appImgTypeMap == nil {
			appImgTypeMap = map[model.ImgConfigType]request.SyncImgConfigItem{}
		}
		syncImgConfigItem := appImgTypeMap[r.ImgConfigType]
		if r.UserStatus == model.UserStatusNormal {
			syncImgConfigItem.NormalList = append(syncImgConfigItem.NormalList, utils.S3Url(r.MediaURL, r.AppID))
		} else {
			syncImgConfigItem.AuditList = append(syncImgConfigItem.AuditList, utils.S3Url(r.MediaURL, r.AppID))
		}
		appImgTypeMap[r.ImgConfigType] = syncImgConfigItem
		imgMap[r.AppID] = appImgTypeMap
	}
	global.AppImgMap = imgMap
}

func (m *ImgConfigService) GetList(info req.ImgConfigSearch) (resList []*model.ImgConfig, total int64, err error) {
	db := global.DB.Model(&model.ImgConfig{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.ImgConfigType != 0 {
		db = db.Where("img_config_type = ?", info.ImgConfigType)
	}
	if info.ConfigStatus != 0 {
		db = db.Where("config_status = ?", info.ConfigStatus)
	}
	if info.UserStatus != 0 {
		db = db.Where("user_status = ?", info.UserStatus)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
