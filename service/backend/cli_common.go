package backend

import (
	"aimsg-server/global"
	"aimsg-server/utils"
	"context"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"time"
)

func GetChannel(userId, realAnchorId uint) (channel string) {
	if userId == 0 || realAnchorId == 0 {
		return
	}
	// chanel的生成规则是, 两个用户的id按照从小到大的顺序拼接
	if userId < realAnchorId {
		channel = fmt.Sprintf("%d%d", userId, realAnchorId)
	} else {
		channel = fmt.Sprintf("%d%d", realAnchorId, userId)
	}
	return
}

func IsVipSVipUser(userInfo *model.DigitalUser) bool {
	isSVip := userInfo.IsSVip()
	isSub := userInfo.IsSub()
	// isChatVip := userInfo.IsChatVip()
	// isContentVip := userInfo.IsContentVip()
	appInfo := global.AppMap[userInfo.AppID]
	if appInfo.VipMode == model.VipModeNormal {
		return true
	} else {
		if isSVip {
			return true
		} else if isSub {
			return true
		}
		// 孙宏伟需求 2024-12-24 新增app_id 4号包数据 亲密度功能
		verCompare355 := utils.CompareVersions(userInfo.Version, "3.5.5")
		// verCompare364 := utils.CompareVersions(userInfo.Version, "3.6.4")
		if verCompare355 < 0 && userInfo.AppID == 1 {
			return true
		}
		// if userInfo.AppID == 1 && verCompare364 < 0 {
		// 	return true
		// }
		return false
	}
}

func GetUserUnlockLv1ImgFreeCount(userInfo *model.DigitalUser) (unlockCount int) {
	isSVip := userInfo.IsSVip()
	isSub := userInfo.IsSub()
	isChatVip := userInfo.IsChatVip()
	isContentVip := userInfo.IsContentVip()
	appInfo, appInfoExist := global.AppMap[userInfo.AppID]
	if !appInfoExist {
		return
	}
	if IsVipSVipUser(userInfo) {
		unlockCount = appInfo.DetailConfig.Data().Lv1UnlockImgFreeCount
		if isSVip {
			unlockCount = appInfo.DetailConfig.Data().Lv1UnlockImgSVipCount
		} else if isSub {
			unlockCount = appInfo.DetailConfig.Data().Lv1UnlockImgSubCount
		}
	} else {
		unlockCount = appInfo.DetailConfig.Data().Lv1UnlockImgFreeCount
		if isChatVip {
			unlockCount = appInfo.DetailConfig.Data().Lv1UnlockImgChatVipCount
		} else if isContentVip {
			unlockCount = appInfo.DetailConfig.Data().Lv1UnlockImgContentVipCount
		}
	}
	return
}

func GetUserUnlockLv1ImgCountDayRdsKey(userInfo *model.DigitalUser, dayStr string) (rdsKey string) {
	return fmt.Sprintf("unlock_lv1_img_count_%d_%s", userInfo.Id, dayStr)
}

func GetUserUnlockLv1ImgCountToday(userInfo *model.DigitalUser) (unlockCount int) {
	nowTime := time.Now()
	dayStr := nowTime.Format(time.DateOnly)
	rdsKey := GetUserUnlockLv1ImgCountDayRdsKey(userInfo, dayStr)
	ctx := context.Background()
	unlockCount, _ = global.REDIS.Get(ctx, rdsKey).Int()
	return
}

func UserUnlockLv1ImgCountTodayIncr(userInfo *model.DigitalUser) (err error) {
	nowTime := time.Now()
	dayStr := nowTime.Format(time.DateOnly)
	rdsKey := GetUserUnlockLv1ImgCountDayRdsKey(userInfo, dayStr)
	ctx := context.Background()
	err = global.REDIS.Incr(ctx, rdsKey).Err()
	return
}
