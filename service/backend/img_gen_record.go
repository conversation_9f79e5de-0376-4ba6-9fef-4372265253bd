package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm/clause"
)

type ImgGenRecordService struct{}

func (m *ImgGenRecordService) Create(reqData *model.ImgGenRecord) (err error) {
	err = global.DB.Model(&model.ImgGenRecord{}).Create(reqData).Error
	return
}

func (m *ImgGenRecordService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.ImgGenRecord{}).Where("id = ?", id).Delete(&model.ImgGenRecord{}).Error
	return
}

func (m *ImgGenRecordService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.ImgGenRecord{}).Where("id IN (?)", reqData.Ids).Delete(&model.ImgGenRecord{}).Error
	return
}

func (m *ImgGenRecordService) Update(reqData *model.ImgGenRecord) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.ImgGenRecord{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	return
}

func (m *ImgGenRecordService) UpdateMap(c *gin.Context, recordId uint, updateMap map[string]interface{}) (err error) {
	err = global.DB.Model(&model.ImgGenRecord{}).Where("id = ?", recordId).Updates(updateMap).Error
	return
}

func (m *ImgGenRecordService) GetById(id uint) (res *model.ImgGenRecord, err error) {
	err = global.DB.Model(&model.ImgGenRecord{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *ImgGenRecordService) SearchOne(reqData *model.ImgGenRecord) (res *model.ImgGenRecord, err error) {
	db := global.DB.Model(&model.ImgGenRecord{})
	if reqData.Id != 0 {
		db = db.Where("id = ?", reqData.Id)
	}
	if reqData.AiId != 0 {
		db = db.Where("ai_id = ?", reqData.AiId)
	}
	if reqData.ImgRecordId != 0 {
		db = db.Where("img_record_id = ?", reqData.ImgRecordId)
	}
	if reqData.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", reqData.DigitalUserId)
	}
	if reqData.GenStatus != 0 {
		db = db.Where("gen_status = ?", reqData.GenStatus)
	}
	if reqData.AiId != 0 {
		db = db.Where("ai_id = ?", reqData.AiId)
	}
	if reqData.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", reqData.AiRoleId)
	}
	err = db.First(&res).Error
	return
}

func (m *ImgGenRecordService) SearchCount(reqData *model.ImgGenRecord) (total int64, err error) {
	db := global.DB.Model(&model.ImgGenRecord{})
	if reqData.Id != 0 {
		db = db.Where("id = ?", reqData.Id)
	}
	if reqData.AiId != 0 {
		db = db.Where("ai_id = ?", reqData.AiId)
	}
	if reqData.ImgRecordId != 0 {
		db = db.Where("img_record_id = ?", reqData.ImgRecordId)
	}
	if reqData.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", reqData.DigitalUserId)
	}
	if reqData.GenStatus != 0 {
		db = db.Where("gen_status = ?", reqData.GenStatus)
	}
	if reqData.AiId != 0 {
		db = db.Where("ai_id = ?", reqData.AiId)
	}
	if reqData.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", reqData.AiRoleId)
	}
	err = db.Count(&total).Error
	return
}

func (m *ImgGenRecordService) GetAll() (resList []*model.ImgGenRecord, err error) {
	err = global.DB.Model(&model.ImgGenRecord{}).Find(&resList).Error
	return
}

func (m *ImgGenRecordService) GetList(info req.ImgGenRecordSearch) (resList []*model.ImgGenRecord, total int64, err error) {
	db := global.DB.Model(&model.ImgGenRecord{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if info.AiId != 0 {
		db = db.Where("ai_id = ?", info.AiId)
	}
	if info.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.DigitalUserId != 0 {
		db = db.Where("digital_user_id = ?", info.DigitalUserId)
	}
	if info.GenStatus != 0 {
		db = db.Where("gen_status = ?", info.GenStatus)
	}
	if info.ImgRecordId != 0 {
		db = db.Where("img_record_id = ?", info.ImgRecordId)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("id DESC")
	}
	err = db.Find(&resList).Error
	return
}
