package backend

import (
	"aimsg-server/errs"
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/model/cli/td"
	"aimsg-server/utils"
	"aimsg-server/utils/notify"
	"context"
	"fmt"
	"strings"
	"time"

	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

func (m *DiyPhotoTaskService) GetDiyConfig(c *gin.Context, reqData *request.GetDiyConfigReq) (res gin.H, err error) {
	var (
		aiRoleInfo     *model.AiRole
		aiRoleSer      AiRoleService
		diyCount       int64
		userInfo       *model.DigitalUser
		userSer        DigitalUserService
		profileInfo    *model.DigitalUserProfile
		profileService DigitalUserProfileService
		msgService     MsgService
		diyConfigArr   []model.DiyConfig
		appId          = utils.GetAppID(c)
		userId         = utils.GetDigitalUserID(c)
		now            = time.Now()
		showLv2        = true
	)
	appDiyConfig := global.AppDiyConfigMap[appId]
	// 应用的私人订制Lv2配置
	diyLv2AppCfg := GetDiyPhotoLv2TacticsCfg(c, appId)

	// 获取用户信息
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		return
	}
	// 获取用户的profile信息
	if profileInfo, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	// 是否订阅用户
	isSub := userInfo.IsSub()
	isSVip := userInfo.IsSVip()
	isChatVip := userInfo.IsChatVip()
	isContentVip := userInfo.IsContentVip()
	isAudit := userInfo.IsAudit()
	if diyLv2AppCfg.UserIsSub && !isSub {
		showLv2 = false
	}
	// 注册天数
	regDays := utils.DaysBetween(userInfo.CreatedAt, now)
	if regDays < diyLv2AppCfg.RegDays {
		showLv2 = false
	}
	// 私人定制次数
	if diyCount, err = m.GetCount(&model.DiyPhotoTask{DigitalUserId: userId}); err != nil {
		return
	}
	if diyCount < diyLv2AppCfg.DiyCount {
		showLv2 = false
	}
	// nsfw状态
	if diyLv2AppCfg.Nsfw && !profileInfo.Nsfw {
		showLv2 = false
	}
	// 和AI角色的聊天次数
	if reqData.AiRoleId != "" {
		if aiRoleInfo, err = aiRoleSer.GetByRoleId(reqData.AiRoleId); err != nil {
			return
		}
		// 用户和该AI角色的消息发送总数--->用户发送的数量
		sendTotal := msgService.GetUserSendMsgTotal(c, userId, reqData.AiRoleId)
		if sendTotal < diyLv2AppCfg.ChatCount {
			showLv2 = false
		}
	}
	if isSVip && profileInfo.Nsfw && !isAudit {
		// 如果是SVip用户,并且开启了NSFW,并且不是审核用户,那么可以显示Lv2的主题
		showLv2 = true
	}
	if !IsVipSVipUser(userInfo) {
		if isChatVip || isContentVip {
			showLv2 = true
		}
		if isAudit {
			showLv2 = false
		}
	}

	// 如果不能显示Lv2的主题,那就去掉lv2的主题
	if !showLv2 {
		for _, diyItem := range appDiyConfig {
			if diyItem.Level > 1 {
				continue
			}
			diyConfigArr = append(diyConfigArr, diyItem)
		}
	} else {
		diyConfigArr = appDiyConfig
	}
	var newDiyConfigArr []model.DiyConfig
	var diyConfigTypeArr = map[model.DiyConfigType][]model.DiyConfig{}
	for _, d := range diyConfigArr {
		if isSVip {
			d.Price = d.Price / 2
		}
		diyConfigTypeArr[d.DiyConfigType] = append(diyConfigTypeArr[d.DiyConfigType], d)
		newDiyConfigArr = append(newDiyConfigArr, d)
	}
	if aiRoleInfo != nil {
		if aiRoleInfo.RoleType == model.AiRoleTypeAnime {
			newDiyConfigArr = diyConfigTypeArr[model.DiyConfigTypeAnime]
		} else {
			if aiRoleInfo.Gender == model.GenderTypeMale {
				newDiyConfigArr = diyConfigTypeArr[model.DiyConfigTypeMale]
			} else {
				newDiyConfigArr = diyConfigTypeArr[model.DiyConfigTypeFemale]
			}
		}
	}
	appDiyPhotoCfg := GetDiyPhotoConfig(c, appId)
	appCountRangeCfg := GetDiyPhotoCountRangeCfg(c, appId)
	res = gin.H{
		"theme_list":  newDiyConfigArr,
		"count_range": appCountRangeCfg.CountRange,
		"disabled":    false,
		"email":       "",
		"tg_link":     appDiyPhotoCfg.TgLink,
	}
	return
}

func (m *DiyPhotoTaskService) CliCreateDiyPhotoTask(c *gin.Context, reqData *request.CreateDiyPhotoTaskReq) (diyPhotoTaskRecord *model.DiyPhotoTask, err error) {
	var (
		consume        int
		taskMaskImgs   []string
		taskList       []*model.DiyPhotoTask
		aiRoleInfo     *model.AiRole
		aiRoleSer      AiRoleService
		userInfo       *model.DigitalUser
		userService    DigitalUserService
		userProfile    *model.DigitalUserProfile
		profileService DigitalUserProfileService
		msgCountSer    UserAiMsgCountService
		msgService     MsgService
		userId         = utils.GetDigitalUserID(c)
		appVersion     = c.GetHeader("App-Version")
	)
	verCompare := utils.CompareVersions(appVersion, "2.2.0")
	diyConfigItem, diyConfigItemExist := global.DiyConfigMap[reqData.DiyConfigId]
	if !diyConfigItemExist {
		err = fmt.Errorf("diy_config: %d not exist", reqData.DiyConfigId)
		return
	}
	if userInfo, err = userService.GetById(c, userId); err != nil {
		return
	}
	isSVip := userInfo.IsSVip()
	isSub := userInfo.IsSub()
	// isChatVip := userInfo.IsChatVip()
	isContentVip := userInfo.IsContentVip()
	verCompare320 := utils.CompareVersions(userInfo.Version, "3.2.0")
	if IsVipSVipUser(userInfo) {
		// 需求地址 https://shimo.im/docs/B1Awd4W7m8FKN3m8/ 《persona.AI双订阅权益优化2.0》，可复制链接后用石墨文档 App 打开
		// 需求取消(2024-11-06 17:21): 免费用户阶段，私人订制可见, 点击私人订制生成，拉起VIP私人订制权益半弹窗
		if userInfo.AppID == 13 {
			if !isSub {
				err = errs.RequireSubErr
				return
			}
		} else {
			if !isSVip && verCompare320 >= 0 && userInfo.AppID != 4 {
				err = errs.RequireSVipErr
				return
			}
		}

	} else {
		if !isContentVip {
			err = errs.ContentVipErr
			return
		}
	}
	if userProfile, err = profileService.GetByUserId(userId); err != nil {
		return
	}
	// 消耗
	if verCompare320 >= 0 {
		consume = reqData.ImgCount * diyConfigItem.Price
		if isSVip {
			consume = reqData.ImgCount * diyConfigItem.Price / 2
		}
	} else {
		if isSub && verCompare >= 0 {
			consume = reqData.ImgCount * diyConfigItem.Price / 2
		} else {
			consume = reqData.ImgCount * diyConfigItem.Price
		}
	}
	// 判断用户是否有足够的余额
	if userProfile.FlowerAmount < consume {
		err = errs.FloShopErr
		return
	}

	diyPhotoTaskRecord = &model.DiyPhotoTask{
		DigitalUserId: userInfo.Id,
		UserStatus:    userInfo.UserStatus,
		AiRoleId:      0,
		DiyConfigId:   reqData.DiyConfigId,
		DiyConfigName: diyConfigItem.Name,
		ImgCount:      reqData.ImgCount,
		Consume:       consume,
		Email:         reqData.Email,
		TaskState:     model.DiyPhotoTaskStateCreate,
		MaskImgs:      []string{},
		ResImgs:       []string{},
	}
	switch reqData.DiyType {
	case 0, model.DiyTypeAiRole:
		if aiRoleInfo, err = aiRoleSer.GetByRoleId(reqData.AiRoleId); err != nil {
			return
		}
		diyPhotoTaskRecord.AiRoleId = aiRoleInfo.Id
		diyPhotoTaskRecord.DiyType = model.DiyTypeAiRole
		diyPhotoTaskRecord.FaceImg = aiRoleInfo.Avatar
	case model.DiyTypeUploadImg:
		diyPhotoTaskRecord.DiyType = model.DiyTypeUploadImg
		if reqData.UploadImg == "" {
			err = fmt.Errorf("upload_img is empty")
			return
		}
		diyPhotoTaskRecord.FaceImg = reqData.UploadImg
	default:
		err = fmt.Errorf("diy_type: %d not exist", reqData.DiyType)
		return
	}
	// 查询用户历史的任务
	searchReq := &model.DiyPhotoTask{
		DigitalUserId: userInfo.Id,
		DiyConfigId:   reqData.DiyConfigId,
	}
	if taskList, err = m.SearchAll(c, searchReq); err != nil {
		return
	}
	// 已使用过的蒙版数量
	usedMaskMap := map[string]bool{}
	for _, task := range taskList {
		if len(task.MaskImgs) == 0 {
			continue
		}
		for _, maskImgKey := range task.MaskImgs {
			usedMaskMap[maskImgKey] = true
		}
	}
	err = global.DB.Transaction(func(tx *gorm.DB) (tErr error) {
		tdPointerMap := map[string]interface{}{
			"event_tag":         "coins_consume",
			"coins_consume_num": -consume,
			"customize_id":      diyConfigItem.Id,
			"customize_num":     reqData.ImgCount,
			"source":            "customize_consume",
		}

		// 创建FlowerConsumeRecord记录
		var fRecord = &model.FlowerConsumeRecord{
			DigitalUserId: userId,
			AiId:          0,
			AiRoleId:      "",
			ConsumeType:   model.ConsumeTypeGenDiyPhotoTask,
			RelationID:    fmt.Sprintf("%d", userId),
			OriginAmount:  userProfile.FlowerAmount,
			ConsumeAmount: consume,
		}
		if aiRoleInfo != nil {
			tdPointerMap["ai_role_id"] = aiRoleInfo.RoleID
			tdPointerMap["ai_role_type"] = aiRoleInfo.RoleType
			fRecord.AiId = aiRoleInfo.Id
			fRecord.AiRoleId = aiRoleInfo.RoleID
		}
		go TdPointer(userInfo, userProfile, td.ServerEvent, tdPointerMap)
		if tErr = tx.Model(&model.FlowerConsumeRecord{}).Create(fRecord).Error; tErr != nil {
			return
		}
		updateMap := map[string]interface{}{
			"flower_amount": gorm.Expr("flower_amount - ?", consume),
		}
		if tErr = tx.Model(&model.DigitalUserProfile{}).Where("id = ?", userProfile.Id).Updates(updateMap).Error; tErr != nil {
			return
		}
		// 生成DiyPhotoTask记录
		if tErr = tx.Model(&model.DiyPhotoTask{}).Create(diyPhotoTaskRecord).Error; tErr != nil {
			return
		}
		// 取蒙版
		diyConfigMaskItem, diyConfigMaskItemExist := global.DiyConfigMaskMap[diyConfigItem.MaskPrefix]
		if !diyConfigMaskItemExist {
			diyPhotoTaskRecord.TaskState = model.DiyPhotoTaskStateFailed
			diyPhotoTaskRecord.ResultMsg = fmt.Sprintf("diy_config_mask: %s not exist", diyConfigItem.MaskPrefix)
			m.SendDDMsgMaskImgNotEnough(diyPhotoTaskRecord, userInfo, diyConfigItem, 0)
			if tErr = tx.Model(&model.DiyPhotoTask{}).Where("id = ?", diyPhotoTaskRecord.Id).Updates(diyPhotoTaskRecord).Error; tErr != nil {
				return
			}
			return
		}
		if diyConfigMaskItem.MaskCount <= 0 || diyConfigMaskItem.MaskCount < reqData.ImgCount {
			diyPhotoTaskRecord.TaskState = model.DiyPhotoTaskStateFailed
			diyPhotoTaskRecord.ResultMsg = fmt.Sprintf("mask img :%s not enough", diyConfigItem.MaskPrefix)
			m.SendDDMsgMaskImgNotEnough(diyPhotoTaskRecord, userInfo, diyConfigItem, 0)
			if tErr = tx.Model(&model.DiyPhotoTask{}).Where("id = ?", diyPhotoTaskRecord.Id).Updates(diyPhotoTaskRecord).Error; tErr != nil {
				return
			}
			return
		}
		for _, maskImgKey := range diyConfigMaskItem.MaskImgs {
			if !usedMaskMap[maskImgKey] {
				taskMaskImgs = append(taskMaskImgs, maskImgKey)
				if len(taskMaskImgs) == reqData.ImgCount {
					break
				}
			}
		}
		// 使用蒙版图列表
		diyPhotoTaskRecord.MaskImgs = taskMaskImgs
		// 已使用过的蒙版数量
		usedMaskCount := len(usedMaskMap) + len(taskMaskImgs)
		// 剩余蒙版数量
		remainMaskCount := diyConfigMaskItem.MaskCount - usedMaskCount
		diyPhotoTaskRecord.RemainMaskCount = remainMaskCount
		if len(taskMaskImgs) < reqData.ImgCount {
			diyPhotoTaskRecord.TaskState = model.DiyPhotoTaskStateFailed
			diyPhotoTaskRecord.ResultMsg = fmt.Sprintf("mask img :%s not enough", diyConfigItem.MaskPrefix)
			m.SendDDMsgMaskImgNotEnough(diyPhotoTaskRecord, userInfo, diyConfigItem, remainMaskCount)
			if tErr = tx.Model(&model.DiyPhotoTask{}).Where("id = ?", diyPhotoTaskRecord.Id).Updates(diyPhotoTaskRecord).Error; tErr != nil {
				return
			}
			return
		}
		if float64(remainMaskCount)/float64(diyConfigMaskItem.MaskCount) <= 0.2 {
			global.LOG.Info("剩余蒙版数量不足20%")
			m.SendDDMsgMaskImgTooLittle(diyConfigItem, remainMaskCount, diyConfigMaskItem.MaskCount)
		}
		if tErr = tx.Model(&model.DiyPhotoTask{}).Where("id = ?", diyPhotoTaskRecord.Id).Updates(diyPhotoTaskRecord).Error; tErr != nil {
			return
		}
		if diyConfigItem.NoSwap {
			// 延时生成
			if tErr = m.addDelayRedisTask(c, diyPhotoTaskRecord.Id); tErr != nil {
				return
			}
		} else {
			// 生成任务
			if tErr = m.addRedisTask(c, diyPhotoTaskRecord.Id); tErr != nil {
				return
			}
		}
		return
	})
	if err != nil {
		return
	}
	if aiRoleInfo != nil {
		_ = msgService.SaveIntimateRecord(c, userInfo.Id, aiRoleInfo, model.IntimateTypeAdd, model.SubTypeDiyPhoto)
		// 更新UserAiMsgCount
		if err = msgCountSer.FindUpdate(c, userId, aiRoleInfo, map[string]interface{}{
			"diy_count": gorm.Expr("diy_count + 1"),
		}); err != nil {
			return
		}
	}
	return
}

func (m *DiyPhotoTaskService) fixTaskMaskImgs(ctx context.Context, taskInfo *model.DiyPhotoTask) (taskMaskImgList []string, err error) {
	var (
		taskList []*model.DiyPhotoTask
	)
	diyConfigItem, diyConfigItemExist := global.DiyConfigMap[taskInfo.DiyConfigId]
	if !diyConfigItemExist {
		err = fmt.Errorf("diy_config: %d not exist", taskInfo.DiyConfigId)
		return
	}
	// 查询用户历史的任务
	searchReq := &model.DiyPhotoTask{
		DigitalUserId: taskInfo.DigitalUserId,
		DiyConfigId:   taskInfo.DiyConfigId,
	}
	if taskList, err = m.SearchAll(ctx, searchReq); err != nil {
		return
	}
	// 已使用过的蒙版数量
	usedMaskMap := map[string]bool{}
	for _, task := range taskList {
		if len(task.MaskImgs) == 0 {
			continue
		}
		for _, maskImgKey := range task.MaskImgs {
			usedMaskMap[maskImgKey] = true
		}
	}
	// 取蒙版
	diyConfigMaskItem, diyConfigMaskItemExist := global.DiyConfigMaskMap[diyConfigItem.MaskPrefix]
	if !diyConfigMaskItemExist {
		err = fmt.Errorf("diy_config_mask: %s not exist", diyConfigItem.MaskPrefix)
		return
	}
	if diyConfigMaskItem.MaskCount <= 0 || diyConfigMaskItem.MaskCount < taskInfo.ImgCount {
		err = fmt.Errorf("mask img :%s not enough", diyConfigItem.MaskPrefix)
		return
	}
	for _, maskImgKey := range diyConfigMaskItem.MaskImgs {
		if !usedMaskMap[maskImgKey] {
			taskMaskImgList = append(taskMaskImgList, maskImgKey)
			if len(taskMaskImgList) == taskInfo.ImgCount {
				break
			}
		}
	}
	if len(taskMaskImgList) < taskInfo.ImgCount {
		err = fmt.Errorf("mask img :%s not enough", diyConfigItem.MaskPrefix)
		return
	}
	return
}

func (m *DiyPhotoTaskService) CliDiyPhotoTaskHistory(c *gin.Context, reqData req.DiyPhotoTaskSearch) (res response.DiyPhotoTaskHistoryRes, err error) {
	var (
		total         int64
		aiRoleInfoErr error
		itemList      []*response.DiyPhotoTaskHistoryItem
		taskList      []*model.DiyPhotoTask
		userInfo      *model.DigitalUser
		userSer       DigitalUserService
		aiRoleInfo    *model.AiRole
		aiRoleSer     AiRoleService
		userId        = utils.GetDigitalUserID(c)
		appId         = utils.GetAppID(c)
	)
	if userInfo, err = userSer.GetById(c, userId); err != nil {
		return
	}
	reqData.DigitalUserId = userInfo.Id
	reqData.CreatedAtMin = time.Date(2024, 6, 26, 11, 27, 8, 0, time.Local)
	if taskList, total, err = m.GetList(reqData); err != nil {
		return
	}
	if len(taskList) == 0 {
		return
	}
	for _, task := range taskList {
		historyItem := &response.DiyPhotoTaskHistoryItem{
			Id:            task.Id,
			CreatedAt:     task.CreatedAt.Format("2006/01/02"),
			Tittle:        fmt.Sprintf("%s album", task.DiyConfigName),
			DiyConfigName: task.DiyConfigName,
			ImgCount:      task.ImgCount,
			TaskState:     task.TaskState,
			FaceImg:       utils.S3Url(task.FaceImg, appId),
			ResImgs:       []string{},
		}
		if task.TaskState == model.DiyPhotoTaskStateCompleted {
			for _, img := range task.ResImgs {
				historyItem.ResImgs = append(historyItem.ResImgs, utils.S3Url(img, appId))
			}
		}
		if task.DiyType == model.DiyTypeAiRole {
			if aiRoleInfo, aiRoleInfoErr = aiRoleSer.GetById(c, task.AiRoleId); aiRoleInfoErr == nil {
				historyItem.Tittle = fmt.Sprintf("%s album", aiRoleInfo.Nickname)
			}
		} else if task.DiyType == model.DiyTypeUploadImg {
			historyItem.Tittle = fmt.Sprintf("%s album", userInfo.Nickname)
		}
		itemList = append(itemList, historyItem)
	}

	res = response.DiyPhotoTaskHistoryRes{
		List:  itemList,
		Total: total,
	}
	return
}

func (m *DiyPhotoTaskService) SendDDMsgMaskImgTooLittle(diyConfigItem model.DiyConfig, remainCount, maskCount int) {
	var (
		err     error
		resBody string
		data    = notify.Message{Msgtype: ""}
	)
	appInfo := global.AppMap[diyConfigItem.AppID]
	msgText := fmt.Sprintf(`
** <font color=#FF0000>【❗❗❗模板库不足80%%警告❗❗❗】</font> **
- 主题名称: %s
- 主题ID: %d
- 应用名称: %s
- 应用ID: %d
- 当前模蒙版库剩余数量: %d
- 蒙版库模板数量: %d
- [蒙版库前缀: %s](https://us-west-2.console.aws.amazon.com/s3/buckets/cashbox-ai?region=us-west-2&bucketType=general&prefix=%s&showversions=false)

@15230463042`,
		diyConfigItem.Name,
		diyConfigItem.Id,
		appInfo.Name,
		diyConfigItem.AppID,
		remainCount,
		maskCount,
		diyConfigItem.MaskPrefix, diyConfigItem.MaskPrefix)
	data.Markdown.Title = "模板库不足80%警告"
	data.Msgtype = "markdown"
	data.Markdown.Text = msgText
	data.At.AtMobiles = global.DiyDDAtMobiles
	resBody, err = notify.SendDingTalk(data, global.CONFIG.DiyDD.Token, global.CONFIG.DiyDD.Secret)
	if err != nil {
		global.LOG.Error("发送钉钉通知 error", zap.Any("err", err), zap.String("resBody", resBody))
		return
	}
}

func (m *DiyPhotoTaskService) SendDDMsgMaskImgNotEnough(taskInfo *model.DiyPhotoTask, userInfo *model.DigitalUser, diyConfigItem model.DiyConfig, remainCount int) {
	var (
		err     error
		resBody string
		data    = notify.Message{Msgtype: ""}
	)
	appInfo := global.AppMap[diyConfigItem.AppID]
	msgText := fmt.Sprintf(`** <font color=#FF0000>【❗❗❗订单数量不足警告❗❗❗】</font> **
- 订单编号: %d
- 主题名称: %s
- 主题ID: %d
- 应用名称: %s
- 应用ID: %d
- 用户ID: %d
- 用户名称: %s
- 本次使用蒙版图片数量: %d
- 当前剩余蒙版图片数量: %d
- 订单状态: %s
- 消耗鲜花数量: %d
- 创建订单时间: %s
- [蒙版库前缀: %s](https://us-west-2.console.aws.amazon.com/s3/buckets/cashbox-ai?region=us-west-2&bucketType=general&prefix=%s&showversions=false)
@15388536513 @15230463042`,
		taskInfo.Id,
		diyConfigItem.Name,
		diyConfigItem.Id,
		appInfo.Name,
		appInfo.Id,
		userInfo.Id,
		userInfo.Nickname,
		taskInfo.ImgCount,
		remainCount,
		model.DiyPhotoTaskStateMap[taskInfo.TaskState],
		taskInfo.Consume,
		taskInfo.CreatedAt.Format(time.DateTime),
		diyConfigItem.MaskPrefix, diyConfigItem.MaskPrefix)
	data.Markdown.Title = "订单数量不足警告"
	data.Msgtype = "markdown"
	data.Markdown.Text = msgText
	data.At.AtMobiles = global.DiyDDAtMobiles
	resBody, err = notify.SendDingTalk(data, global.CONFIG.DiyDD.Token, global.CONFIG.DiyDD.Secret)
	if err != nil {
		global.LOG.Error("发送钉钉通知 error", zap.Any("err", err), zap.String("resBody", resBody))
		return
	}
}

func (m *DiyPhotoTaskService) SendDDMsgTaskSuccess(taskInfo *model.DiyPhotoTask, userInfo *model.DigitalUser, diyConfigItem model.DiyConfig, remainCount int) {
	var (
		err     error
		resBody string
		data    = notify.Message{Msgtype: ""}
	)
	appInfo := global.AppMap[diyConfigItem.AppID]
	msgText := fmt.Sprintf(`
** 【订单完成通知】 **
- 订单编号: %d
- 主题名称: %s
- 主题ID: %d
- 应用名称: %s
- 应用ID: %d
- 用户ID: %d
- 用户名称: %s
- 本次使用蒙版图片数量: %d
- 当前剩余蒙版图片数量: %d
- 订单状态: %s
- 消耗鲜花数量: %d
- 创建订单时间: %s
- 完成时间: %s
- [蒙版库前缀: %s](https://us-west-2.console.aws.amazon.com/s3/buckets/cashbox-ai?region=us-west-2&bucketType=general&prefix=%s&showversions=false)`,
		taskInfo.Id,
		diyConfigItem.Name,
		diyConfigItem.Id,
		appInfo.Name,
		appInfo.Id,
		userInfo.Id,
		userInfo.Nickname,
		taskInfo.ImgCount,
		remainCount,
		model.DiyPhotoTaskStateMap[taskInfo.TaskState],
		taskInfo.Consume,
		taskInfo.CreatedAt.Format(time.DateTime),
		taskInfo.CompleteTime.Format(time.DateTime),
		diyConfigItem.MaskPrefix, diyConfigItem.MaskPrefix)
	if taskInfo.Email != "" {
		msgText += fmt.Sprintf(`
- 邮箱: %s
`, taskInfo.Email)
	}

	data.Markdown.Title = "订单完成通知"
	data.Msgtype = "markdown"
	data.Markdown.Text = msgText
	data.At.AtMobiles = global.DiyDDAtMobiles
	resBody, err = notify.SendDingTalk(data, global.CONFIG.DiyDD.Token, global.CONFIG.DiyDD.Secret)
	if err != nil {
		global.LOG.Error("发送钉钉通知 error", zap.Any("err", err), zap.String("resBody", resBody))
		return
	}
}

func (m *DiyPhotoTaskService) SendSuccessEmail(ctx context.Context, taskInfo *model.DiyPhotoTask) (err error) {
	var (
		userInfo   *model.DigitalUser
		userSer    DigitalUserService
		aiRoleInfo *model.AiRole
		aiRoleSer  AiRoleService
		now        = time.Now()
		dayStr     = now.Format(time.DateOnly)
	)

	if userInfo, err = userSer.GetById(ctx, taskInfo.DigitalUserId); err != nil {
		global.LOG.Error("GetById error", zap.Any("err", err))
		return
	}
	if aiRoleInfo, err = aiRoleSer.GetById(ctx, taskInfo.AiRoleId); err != nil {
		global.LOG.Error("GetById error", zap.Any("err", err))
		return
	}
	appInfo := global.AppMap[userInfo.AppID]

	appName := appInfo.Name

	appName = strings.ReplaceAll(appName, "Android", "")
	appName = strings.ReplaceAll(appName, "IOS", "")
	appName = strings.ReplaceAll(appName, "Web", "")

	// SMTP服务器的地址和端口
	smtpHost := appInfo.SmtpConfig.Data().Host
	smtpPort := appInfo.SmtpConfig.Data().Port // 如果使用SSL/TLS，通常是465或587

	// 发件人邮箱和密码
	from := appInfo.SmtpConfig.Data().From
	password := appInfo.SmtpConfig.Data().Password

	// 收件人邮箱
	to := taskInfo.Email

	// 邮件主题
	subject := fmt.Sprintf("Your %s customized photo album has been completed", appName)
	// 邮件内容，使用HTML格式
	body := `
	<html>
	<body>
	<p><br></p><p>Dear {{nickname}}:</p><p>Hello!</p><p>Thank you for choosing {{appname}}'s personal customization service to create a photo album of {{ainame}} for you. We are very pleased to inform you that your customized {{ainame}} photo album has been produced and is now being sent to you via email.</p><p>Please check the attachment of this email, which contains the {{ainame}} photo album you are looking forward to. These pictures are carefully produced by us through AI technology according to your requirements, aiming to present you the most perfect moments. We believe that this photo album will leave you with unforgettable memories.</p><p>In order to bring you a better experience, we continuously optimize and adjust during the production process to ensure that every picture can achieve the effect you expect. At the same time, we also very much welcome your valuable comments and suggestions after use so that we can continue to improve and enhance service quality.</p><p>If you encounter any problems downloading or viewing the photo album, or need further help and support, please feel free to contact us through the following methods:</p><p>Customer service email: {{emailaddress}}</p><p>We will be happy to answer your questions and provide you with the best service.</p><p>Thank you again for your support and trust in {{appname}}. We look forward to continuing to provide you with more high-quality, personalized services in the future.</p><p>wish you a happy life!</p><p>Acknowledgments:</p><p>Sincerely, {{appname}} Team</p><p>{{current date}}</p><p>click link below to view album:</p><p>{{albumlink}}</p><p><br></p>
	</body>
	</html>
	`
	body = strings.ReplaceAll(body, "{{nickname}}", userInfo.Nickname)
	body = strings.ReplaceAll(body, "{{ainame}}", aiRoleInfo.Nickname)
	body = strings.ReplaceAll(body, "{{emailaddress}}", from)
	body = strings.ReplaceAll(body, "{{appname}}", appName)
	body = strings.ReplaceAll(body, "{{current date}}", dayStr)
	// 图片列表
	if len(taskInfo.ResImgs) > 0 {
		htmlContent := utils.ImagesToHTML(taskInfo.ResImgs, appInfo.S3ImgPrefix)
		body = strings.ReplaceAll(body, "{{albumlink}}", htmlContent)
	}
	// 调用SendEmail方法发送邮件
	if err = utils.SendEmailHtml(smtpHost, smtpPort, from, password, to, subject, body); err != nil {
		global.LOG.Error("SendEmailHtml error", zap.Any("err", err))
		return
	}
	global.LOG.Info("SendEmailHtml success", zap.String("to", to), zap.String("subject", subject))
	return
}

func (m *DiyPhotoTaskService) ReSendEmail(c *gin.Context, reqData *req.ReSendEmailReq) (err error) {
	var (
		taskInfo *model.DiyPhotoTask
	)
	if taskInfo, err = m.GetById(reqData.Id); err != nil {
		return
	}
	if taskInfo.Email == "" {
		err = fmt.Errorf("email is empty")
		return
	}
	err = m.SendSuccessEmail(c, taskInfo)
	return
}

func (m *DiyPhotoTaskService) ReSendPushMsg(c *gin.Context, reqData *req.ReSendPushMsgReq) (err error) {
	var (
		taskInfo *model.DiyPhotoTask
		fcmSer   FcmService
	)
	if taskInfo, err = m.GetById(reqData.Id); err != nil {
		return
	}
	err = fcmSer.PushDiySuccessMsg(c, taskInfo)
	return
}
