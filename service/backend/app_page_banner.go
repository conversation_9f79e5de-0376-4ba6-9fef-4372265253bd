package backend

import (
	"aimsg-server/global"
	req "aimsg-server/model/backend/request"
	"aimsg-server/model/common/request"
	"encoding/json"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
	"time"
)

type AppPageBannerService struct{}

func (m *AppPageBannerService) CopyInfo(sourceAppId, targetAppId uint) (err error) {
	var (
		sourceInfos []*model.AppPageBanner
		newInfos    []*model.AppPageBanner
		nowTime     = time.Now()
	)
	sourceInfos, err = m.FindByAppId(sourceAppId)
	if err != nil {
		return
	}
	for _, sourceInfo := range sourceInfos {
		sourceInfo.Id = 0
		sourceInfo.CreatedAt = nowTime
		sourceInfo.UpdatedAt = nowTime
		sourceInfo.AppID = targetAppId
		newInfos = append(newInfos, sourceInfo)
	}
	if len(newInfos) == 0 {
		return
	}
	err = global.DB.Model(&model.AppPageBanner{}).Create(&newInfos).Error
	return
}

func (m *AppPageBannerService) FindByAppId(appId uint) (resList []*model.AppPageBanner, err error) {
	err = global.DB.Model(&model.AppPageBanner{}).Where("app_id = ?", appId).Find(&resList).Error
	return
}

func (m *AppPageBannerService) Create(reqData *model.AppPageBanner) (err error) {
	err = global.DB.Model(&model.AppPageBanner{}).Create(reqData).Error
	m.SyncAll()
	return
}

func (m *AppPageBannerService) DeleteById(id uint) (err error) {
	err = global.DB.Model(&model.AppPageBanner{}).Where("id = ?", id).Delete(&model.AppPageBanner{}).Error
	m.SyncAll()
	return
}

func (m *AppPageBannerService) DeleteByIds(reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.AppPageBanner{}).Where("id IN (?)", reqData.Ids).Delete(&model.AppPageBanner{}).Error
	m.SyncAll()
	return
}

func (m *AppPageBannerService) Update(reqData *model.AppPageBanner) (err error) {
	var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return err
	}
	err = global.DB.Model(&model.AppPageBanner{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	m.SyncAll()
	return
}

func (m *AppPageBannerService) GetById(id uint) (res *model.AppPageBanner, err error) {
	err = global.DB.Model(&model.AppPageBanner{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *AppPageBannerService) GetAll() (resList []*model.AppPageBanner, err error) {
	err = global.DB.Model(&model.AppPageBanner{}).Find(&resList).Error
	return
}

func (m *AppPageBannerService) SyncAll() {
	var (
		err          error
		dbResList    []*model.AppPageBanner
		allBannerMap = map[uint][]*model.AppPageBanner{}
	)
	dbResList, err = m.GetAll()
	if err != nil {
		global.LOG.Error("SyncAll 失败", zap.Error(err))
		return
	}
	for _, bannerItem := range dbResList {
		if bannerItem.ShowStatus == model.SwitchStatusDisabled {
			continue
		}
		appBannerList := allBannerMap[bannerItem.AppID]
		appBannerList = append(appBannerList, bannerItem)
		allBannerMap[bannerItem.AppID] = appBannerList
	}
	global.AllBannerMap = allBannerMap
}

func (m *AppPageBannerService) GetList(info req.AppPageBannerSearch) (resList []*model.AppPageBanner, total int64, err error) {
	db := global.DB.Model(&model.AppPageBanner{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
	if info.ImgTemplateRecordId != 0 {
		db = db.Where("img_template_record_id = ?", info.ImgTemplateRecordId)
	}
	if info.AppID != 0 {
		db = db.Where("app_id = ?", info.AppID)
	}
	if info.AuditShow != 0 {
		db = db.Where("audit_show = ?", info.AuditShow)
	}
	if info.ShowStatus != 0 {
		db = db.Where("show_status = ?", info.ShowStatus)
	}
	if info.TurnPage != 0 {
		db = db.Where("turn_page = ?", info.TurnPage)
	}
	if info.AiRoleId != "" {
		db = db.Where("ai_role_id = ?", info.AiRoleId)
	}
	if info.FuncType != 0 {
		db = db.Where("func_type = ?", info.FuncType)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
		db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
	} else {
		db = db.Order("sort DESC, id DESC")
	}
	err = db.Find(&resList).Error
	return
}
