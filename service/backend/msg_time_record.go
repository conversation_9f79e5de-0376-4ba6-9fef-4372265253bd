package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/backend/request"
	"context"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/qiniu/qmgo"
	"go.mongodb.org/mongo-driver/bson"
)

type MsgTimeRecordService struct{}

func (m *MsgTimeRecordService) Collection() (collection *qmgo.Collection) {
	db := global.MONGO.Client.Database(global.CONFIG.Mongo.Database)
	return db.Collection(model.MsgTimeRecordCollection)
}

func (m *MsgTimeRecordService) Insert(ctx context.Context, msgTimeRecord *model.MsgTimeRecord) (res *qmgo.InsertOneResult, err error) {
	collection := m.Collection()
	return collection.InsertOne(ctx, msgTimeRecord)
}

func (m *MsgTimeRecordService) GetFilter(reqData request.MsgTimeRecordSearchReq) (filter bson.M) {
	filter = bson.M{}
	if len(reqData.CreatedAtRange) == 2 {
		filter["created_at"] = bson.M{"$gte": reqData.CreatedAtRange[0], "$lte": reqData.CreatedAtRange[1]}
	}
	if reqData.MessageID != "" {
		filter["message_id"] = reqData.MessageID
	}
	if reqData.DigitalUserId != 0 {
		filter["digital_user_id"] = reqData.DigitalUserId
	}
	if reqData.AiId != 0 {
		filter["ai_id"] = reqData.AiId
	}
	if reqData.AiRoleId != "" {
		filter["ai_role_id"] = reqData.AiRoleId
	}
	if reqData.JobName != "" {
		filter["job_name"] = reqData.JobName
	}
	if reqData.ReqUrl != "" {
		filter["req_url"] = reqData.ReqUrl
	}
	return
}

func (m *MsgTimeRecordService) Count(ctx context.Context, reqData request.MsgTimeRecordSearchReq) (count int64, err error) {
	collection := m.Collection()
	filter := m.GetFilter(reqData)
	count, err = collection.Find(ctx, filter).Count()
	return
}

func (m *MsgTimeRecordService) Find(ctx context.Context, reqData request.MsgTimeRecordSearchReq) (res []*model.MsgTimeRecord, err error) {
	collection := m.Collection()
	filter := m.GetFilter(reqData)
	col := collection.Find(ctx, filter)
	if reqData.SortProp != "" {
		if reqData.SortDesc {
			col = col.Sort("-" + reqData.SortProp)
		} else {
			col = col.Sort(reqData.SortProp)
		}
	}
	err = col.Limit(reqData.PageSize).Skip((reqData.Page - 1) * reqData.PageSize).All(&res)
	return
}

func (m *MsgTimeRecordService) FindOne(ctx context.Context, reqData request.MsgTimeRecordSearchReq) (res *model.MsgTimeRecord, err error) {
	collection := m.Collection()
	filter := m.GetFilter(reqData)
	err = collection.Find(ctx, filter).One(&res)
	return
}
