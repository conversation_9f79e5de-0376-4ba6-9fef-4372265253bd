package backend

import (
	"aimsg-server/global"
	"aimsg-server/model/cli/request"
	"aimsg-server/model/cli/response"
	"aimsg-server/utils"
	"context"
	"encoding/json"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"time"
)

type FireBaseUtilSer struct{}

func (m *FireBaseUtilSer) FirebaseReportKey(userId uint) string {
	return fmt.Sprintf(global.FIREBASE_REPORT, userId)
}

func (m *FireBaseUtilSer) SetCacheForApp(ctx context.Context, orderNo string) {
	var (
		err             error
		reportListStr   string
		reportListBytes []byte
		reportList      []request.FirebaseReport
		appInfo         model.App
		appInfoExist    bool
		orderInfo       *model.Order
		orderSer        OrdersService
	)
	if orderNo == "" {
		return
	}
	defer func() {
		if err == nil {
			return
		}
		appInfo, appInfoExist = global.AppMap[orderInfo.AppID]
		if !appInfoExist {
			global.LOG.Error("appInfoNotExist", zap.Uint("AppID", orderInfo.AppID))
			return
		}
		ddNotifyMsg := fmt.Sprintf(`## func SetCacheForApp 设置Firebase上报缓存到redis失败
- [应用名称: %s](https://aiadmin.aigirlchat.net/#/layout/backend_app/app)
- [应用ID: %d](https://aiadmin.aigirlchat.net/#/layout/backend_app/app)
- [OrderNo: %s](https://aiadmin.aigirlchat.net/#/layout/backendOrders/orders?order_no=%s)
- 错误信息: %s
`,
			appInfo.Name,
			appInfo.Id,
			orderNo, orderNo,
			err.Error(),
		)
		SendDDAtAll(ddNotifyMsg)
	}()
	if orderInfo, err = orderSer.GetByOrderNo(orderNo); err != nil {
		global.LOG.Error("SetCacheForApp error", zap.String("order_no", orderNo), zap.Error(err))
		return
	}
	appInfo, appInfoExist = global.AppMap[orderInfo.AppID]
	if !appInfoExist {
		return
	}
	// if orderInfo.AppID != 6 {
	// 	return
	// }
	redisKey := m.FirebaseReportKey(orderInfo.DigitalUserID)
	if redisVal := global.REDIS.Get(ctx, redisKey).Val(); redisVal != "" {
		if err = json.Unmarshal([]byte(redisVal), &reportList); err != nil {
			global.LOG.Error("SetCacheForApp Unmarshal error", zap.Error(err))
			return
		}
	}
	rateOne := appInfo.DetailConfig.Data().RateOne
	rateTwo := appInfo.DetailConfig.Data().RateTwo
	realPrice := orderInfo.USDPrice.Mul(rateOne).Mul(rateTwo).Round(2)
	reportList = append(reportList, request.FirebaseReport{UsdPrice: realPrice.InexactFloat64(), Currency: "USD"})
	if reportListBytes, err = json.Marshal(reportList); err != nil {
		global.LOG.Error("SetCacheForApp json.Marshal error", zap.Error(err))
		return
	}
	reportListStr = string(reportListBytes)
	if _, err = global.REDIS.Set(ctx, redisKey, reportListStr, time.Hour*48).Result(); err != nil {
		global.LOG.Error("SetCacheForApp redis.Set error", zap.Error(err), zap.String("redis_key", redisKey))
		return
	}
	return
}

func (m *FireBaseUtilSer) GetFirebaseReport(c *gin.Context) (res gin.H, err error) {
	var (
		reportList []request.FirebaseReport
		reportNew  []request.FirebaseReportNew
		userId     = utils.GetDigitalUserID(c)
	)
	redisKey := m.FirebaseReportKey(userId)
	if redisVal := global.REDIS.Get(c, redisKey).Val(); redisVal != "" {
		if err = json.Unmarshal([]byte(redisVal), &reportList); err != nil {
			global.LOG.Error("SetCacheForApp Unmarshal error", zap.Error(err))
			return
		}
		global.REDIS.Del(c, redisKey)
	}

	for _, report := range reportList {
		reportNew = append(reportNew, request.FirebaseReportNew{
			UsdPrice: decimal.NewFromFloat(report.UsdPrice),
			Value:    report.UsdPrice,
			Currency: report.Currency,
		})
	}

	res = gin.H{
		"list": reportNew,
	}
	return
}

func (m *FireBaseUtilSer) FirebaseReportEventKey(userId uint) string {
	return fmt.Sprintf(global.FIREBASE_REPORT_USER, userId)
}

func (m *FireBaseUtilSer) SetFirebaseReportEvent(ctx context.Context, userId uint, eventItem response.FirebaseReportItem) (err error) {
	var (
		eventItemBytes []byte
		rdsKey         = m.FirebaseReportEventKey(userId)
	)
	if eventItemBytes, err = json.Marshal(eventItem); err != nil {
		return
	}
	eventItemJsonStr := string(eventItemBytes)
	if err = global.REDIS.HSet(ctx, rdsKey, eventItem.EventID, eventItemJsonStr).Err(); err != nil {
		return
	}
	if err = global.REDIS.Expire(ctx, rdsKey, time.Hour*25).Err(); err != nil {
		return
	}
	return
}

func (m *FireBaseUtilSer) GetFirebaseReportEvent(ctx context.Context, userId uint) (eventList []response.FirebaseReportItem, err error) {
	var (
		keyResMap = map[string]string{}
	)
	rdsKey := m.FirebaseReportEventKey(userId)
	if keyResMap, err = global.REDIS.HGetAll(ctx, rdsKey).Result(); err != nil {
		return
	}
	for _, v := range keyResMap {
		eventItem := response.FirebaseReportItem{}
		if jsonErr := json.Unmarshal([]byte(v), &eventItem); jsonErr != nil {
			global.LOG.Error("GetFirebaseReportEvent json.Unmarshal error", zap.Error(jsonErr))
			continue
		}
		eventList = append(eventList, eventItem)
	}
	if eventList == nil {
		eventList = []response.FirebaseReportItem{}
	}
	return
}

func (m *FireBaseUtilSer) FinishFirebaseReportEvent(c *gin.Context, reqData *request.FinishFirebaseReportEventReq) (err error) {
	var (
		userId = utils.GetDigitalUserID(c)
	)
	return m.DelFirebaseReportEvent(c, userId, reqData.EventID)
}

func (m *FireBaseUtilSer) DelFirebaseReportEvent(ctx context.Context, userId uint, eventId string) (err error) {
	rdsKey := m.FirebaseReportEventKey(userId)
	err = global.REDIS.HDel(ctx, rdsKey, eventId).Err()
	return
}
