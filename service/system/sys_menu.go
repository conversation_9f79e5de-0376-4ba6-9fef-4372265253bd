package system

import (
	"errors"
	"fmt"
	"strconv"

	"aimsg-server/global"
	"aimsg-server/model/common/request"
	"aimsg-server/model/system"
	"gorm.io/gorm"
)

// @author: [piexlmax](https://github.com/piexlmax)
// @function: getMenuTreeMap
// @description: 获取路由总树map
// @param: authorityId string
// @return: treeMap map[string][]system.SysMenu, err error

type MenuService struct{}

var MenuServiceApp = new(MenuService)

func (menuService *MenuService) getMenuTreeMap(authorityId uint) (treeMap map[string][]system.SysMenu, err error) {
	var allMenus []system.SysMenu
	var baseMenu []system.SysBaseMenu
	var btns []system.SysAuthorityBtn
	treeMap = make(map[string][]system.SysMenu)

	var SysAuthorityMenus []system.SysAuthorityMenu
	err = global.DB.Where("sys_authority_authority_id = ?", authorityId).Find(&SysAuthorityMenus).Error
	if err != nil {
		return
	}

	var MenuIds []string

	for i := range SysAuthorityMenus {
		MenuIds = append(MenuIds, SysAuthorityMenus[i].MenuId)
	}

	err = global.DB.Where("id in (?)", MenuIds).Order("sort").Preload("Parameters").Find(&baseMenu).Error
	if err != nil {
		return
	}

	for i := range baseMenu {
		allMenus = append(allMenus, system.SysMenu{
			SysBaseMenu: baseMenu[i],
			AuthorityId: authorityId,
			MenuId:      strconv.Itoa(int(baseMenu[i].ID)),
			Parameters:  baseMenu[i].Parameters,
		})
	}

	err = global.DB.Where("authority_id = ?", authorityId).Preload("SysBaseMenuBtn").Find(&btns).Error
	if err != nil {
		return
	}
	var btnMap = make(map[uint]map[string]uint)
	for _, v := range btns {
		if btnMap[v.SysMenuID] == nil {
			btnMap[v.SysMenuID] = make(map[string]uint)
		}
		btnMap[v.SysMenuID][v.SysBaseMenuBtn.Name] = authorityId
	}
	for _, v := range allMenus {
		v.Btns = btnMap[v.ID]
		treeMap[v.ParentId] = append(treeMap[v.ParentId], v)
	}
	return treeMap, err
}

// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetMenuTree
// @description: 获取动态菜单树
// @param: authorityId string
// @return: menus []system.SysMenu, err error

func (menuService *MenuService) GetMenuTree(authorityId uint) (menus []system.SysMenu, err error) {
	menuTree, err := menuService.getMenuTreeMap(authorityId)
	menus = menuTree["0"]
	for i := 0; i < len(menus); i++ {
		err = menuService.getChildrenList(&menus[i], menuTree)
	}
	return menus, err
}

// @author: [piexlmax](https://github.com/piexlmax)
// @function: getChildrenList
// @description: 获取子菜单
// @param: menu *model.SysMenu, treeMap map[string][]model.SysMenu
// @return: err error

func (menuService *MenuService) getChildrenList(menu *system.SysMenu, treeMap map[string][]system.SysMenu) (err error) {
	menu.Children = treeMap[menu.MenuId]
	for i := 0; i < len(menu.Children); i++ {
		err = menuService.getChildrenList(&menu.Children[i], treeMap)
	}
	return err
}

// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetInfoList
// @description: 获取路由分页
// @return: list interface{}, total int64,err error

func (menuService *MenuService) GetInfoList() (list interface{}, total int64, err error) {
	var menuList []system.SysBaseMenu
	treeMap, err := menuService.getBaseMenuTreeMap()
	menuList = treeMap["0"]
	for i := 0; i < len(menuList); i++ {
		err = menuService.getBaseChildrenList(&menuList[i], treeMap)
	}
	return menuList, total, err
}

// @author: [piexlmax](https://github.com/piexlmax)
// @function: getBaseChildrenList
// @description: 获取菜单的子菜单
// @param: menu *model.SysBaseMenu, treeMap map[string][]model.SysBaseMenu
// @return: err error

func (menuService *MenuService) getBaseChildrenList(menu *system.SysBaseMenu, treeMap map[string][]system.SysBaseMenu) (err error) {
	menu.Children = treeMap[strconv.Itoa(int(menu.ID))]
	for i := 0; i < len(menu.Children); i++ {
		err = menuService.getBaseChildrenList(&menu.Children[i], treeMap)
	}
	return err
}

// @author: [piexlmax](https://github.com/piexlmax)
// @function: AddBaseMenu
// @description: 添加基础路由
// @param: menu model.SysBaseMenu
// @return: error

func (menuService *MenuService) AddBaseMenu(menu system.SysBaseMenu) (err error) {
	var nameCount int64
	if err = global.DB.Model(&system.SysBaseMenu{}).Where("name = ?", menu.Name).Count(&nameCount).Error; err != nil {
		return
	}
	if nameCount > 0 {
		return errors.New("存在重复name，请修改name")
	}
	return global.DB.Transaction(func(tx *gorm.DB) (tErr error) {
		if tErr = tx.Model(&system.SysBaseMenu{}).Create(&menu).Error; tErr != nil {
			return
		}
		// 添加完基础路由后，需要给超级管理员的角色添加基础路由的权限
		if tErr = tx.Model(&system.SysAuthorityMenu{}).Create(&system.SysAuthorityMenu{
			MenuId:      fmt.Sprintf("%d", menu.ID),
			AuthorityId: "888",
		}).Error; tErr != nil {
			return
		}
		return
	})
}

// @author: [piexlmax](https://github.com/piexlmax)
// @function: getBaseMenuTreeMap
// @description: 获取路由总树map
// @return: treeMap map[string][]system.SysBaseMenu, err error

func (menuService *MenuService) getBaseMenuTreeMap() (treeMap map[string][]system.SysBaseMenu, err error) {
	var allMenus []system.SysBaseMenu
	treeMap = make(map[string][]system.SysBaseMenu)
	err = global.DB.Order("sort").Preload("MenuBtn").Preload("Parameters").Find(&allMenus).Error
	for _, v := range allMenus {
		treeMap[v.ParentId] = append(treeMap[v.ParentId], v)
	}
	return treeMap, err
}

// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetBaseMenuTree
// @description: 获取基础路由树
// @return: menus []system.SysBaseMenu, err error

func (menuService *MenuService) GetBaseMenuTree() (menus []system.SysBaseMenu, err error) {
	treeMap, err := menuService.getBaseMenuTreeMap()
	menus = treeMap["0"]
	for i := 0; i < len(menus); i++ {
		err = menuService.getBaseChildrenList(&menus[i], treeMap)
	}
	return menus, err
}

// @author: [piexlmax](https://github.com/piexlmax)
// @function: AddMenuAuthority
// @description: 为角色增加menu树
// @param: menus []model.SysBaseMenu, authorityId string
// @return: err error

func (menuService *MenuService) AddMenuAuthority(menus []system.SysBaseMenu, authorityId uint) (err error) {
	var auth system.SysAuthority
	auth.AuthorityId = authorityId
	auth.SysBaseMenus = menus
	err = AuthorityServiceApp.SetMenuAuthority(&auth)
	return err
}

// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetMenuAuthority
// @description: 查看当前角色树
// @param: info *request.GetAuthorityId
// @return: menus []system.SysMenu, err error

func (menuService *MenuService) GetMenuAuthority(info *request.GetAuthorityId) (menus []system.SysMenu, err error) {
	var baseMenu []system.SysBaseMenu
	var SysAuthorityMenus []system.SysAuthorityMenu
	err = global.DB.Where("sys_authority_authority_id = ?", info.AuthorityId).Find(&SysAuthorityMenus).Error
	if err != nil {
		return
	}

	var MenuIds []string

	for i := range SysAuthorityMenus {
		MenuIds = append(MenuIds, SysAuthorityMenus[i].MenuId)
	}

	err = global.DB.Where("id in (?) ", MenuIds).Order("sort").Find(&baseMenu).Error

	for i := range baseMenu {
		menus = append(menus, system.SysMenu{
			SysBaseMenu: baseMenu[i],
			AuthorityId: info.AuthorityId,
			MenuId:      strconv.Itoa(int(baseMenu[i].ID)),
			Parameters:  baseMenu[i].Parameters,
		})
	}
	return menus, err
}

// UserAuthorityDefaultRouter 用户角色默认路由检查
//
//	Author [SliverHorn](https://github.com/SliverHorn)
func (menuService *MenuService) UserAuthorityDefaultRouter(user *system.SysUser) {
	var menuIds []string
	err := global.DB.Model(&system.SysAuthorityMenu{}).Where("sys_authority_authority_id = ?", user.AuthorityId).Pluck("sys_base_menu_id", &menuIds).Error
	if err != nil {
		return
	}
	var am system.SysBaseMenu
	err = global.DB.First(&am, "name = ? and id in (?)", user.Authority.DefaultRouter, menuIds).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		user.Authority.DefaultRouter = "404"
	}
}
