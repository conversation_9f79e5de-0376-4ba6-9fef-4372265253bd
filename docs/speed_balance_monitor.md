# Speed API 余额监控功能

## 功能概述

Speed API 余额监控功能可以定时检查 Speed API 账户余额，当余额低于设定阈值时自动发送飞书通知。

## 功能特性

- ✅ 每小时自动检查余额
- ✅ 余额低于阈值时发送飞书通知
- ✅ 支持手动查询余额状态
- ✅ 支持立即检查余额
- ✅ 可配置开关、阈值和通知方式

## 配置说明

在 `config.yaml` 中添加以下配置：

```yaml
# Speed API 余额监控配置
speed_api:
  enabled: true                                                    # 是否启用监控
  base_url: "https://api.tryspeed.com"                            # Speed API 基础 URL
  authorization: "Basic c2tfdGVzdF9tYmthamk2bVhGTjV1Q29IbWJrYWptbnZtZ25rNWF2YW1ia2FqbW53S2Ztc3pDRlY6"  # API 授权头
  threshold: 100000                                                # 余额阈值，单位：SATS (10万)
  feishu_bot_id: "your-feishu-bot-id-here"                       # 飞书机器人 ID
```

### 配置参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `enabled` | bool | 是 | 是否启用余额监控功能 |
| `base_url` | string | 是 | Speed API 的基础 URL |
| `authorization` | string | 是 | API 授权头，格式为 "Basic base64(api_key:)" |
| `threshold` | int64 | 是 | 余额阈值，单位为 SATS，低于此值时发送通知 |
| `feishu_bot_id` | string | 否 | 飞书机器人 ID，用于发送通知 |

## API 接口

### 1. 获取余额状态

**接口地址**: `GET /api/speedBalance/status`

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "enabled": true,
    "balance": 3956,
    "threshold": 100000,
    "is_low": true,
    "check_time": "2025-01-08 15:30:00",
    "currency": "SATS"
  },
  "msg": "操作成功"
}
```

### 2. 立即检查余额

**接口地址**: `POST /api/speedBalance/check`

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "status": {
      "enabled": true,
      "balance": 3956,
      "threshold": 100000,
      "is_low": true,
      "check_time": "2025-01-08 15:30:00",
      "currency": "SATS"
    },
    "message": "余额不足，已发送通知"
  },
  "msg": "操作成功"
}
```

## 定时任务

系统会自动创建定时任务，每小时检查一次余额：

- **Cron 表达式**: `0 0 * * * *` (每小时的整点执行)
- **任务名称**: `SpeedBalanceMonitor`
- **执行逻辑**: 调用 Speed API 获取余额，如果低于阈值则发送飞书通知

## 通知格式

当余额低于阈值时，会发送以下格式的飞书通知：

```
⚠️ Speed API 余额不足警告

## Speed API 余额监控警告

**当前余额**: 3956 SATS
**警告阈值**: 100000 SATS
**检查时间**: 2025-01-08 15:30:00

余额已低于设定阈值，请及时充值！

---
*此消息由系统自动发送*
```

## 使用步骤

1. **配置 Speed API 信息**
   - 在 `config.yaml` 中配置 Speed API 的相关信息
   - 设置合适的余额阈值

2. **配置飞书通知**
   - 创建飞书机器人并获取 Bot ID
   - 将 Bot ID 配置到 `feishu_bot_id` 字段

3. **启动服务**
   - 重启应用程序
   - 系统会自动开始监控余额

4. **验证功能**
   - 调用 `/api/speedBalance/status` 接口查看当前状态
   - 调用 `/api/speedBalance/check` 接口手动触发检查

## 注意事项

1. **API 密钥安全**: 请妥善保管 Speed API 的授权信息，不要泄露给他人
2. **网络连接**: 确保服务器能够访问 Speed API 和飞书 API
3. **阈值设置**: 建议根据实际业务需求设置合理的余额阈值
4. **通知频率**: 目前每小时检查一次，如果需要更频繁的检查可以修改 Cron 表达式

## 故障排查

1. **检查配置**: 确认 `config.yaml` 中的配置信息正确
2. **检查网络**: 确认服务器能够访问外部 API
3. **检查日志**: 查看应用程序日志中的错误信息
4. **手动测试**: 使用 API 接口手动测试功能是否正常
