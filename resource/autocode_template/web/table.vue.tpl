<template>
  <div>
    <el-card shadow="hover">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" @keyup.enter="onSubmit">
      <el-form-item label="ID" prop="id">
        <el-input-number v-model="searchInfo.id" :controls="false" :precision="0" :min="1"/>
      </el-form-item>
      {{- range .Fields}}
        {{- if .FieldSearchType}}
          {{- if eq .FieldType "bool" }}
            <el-form-item label="{{.FieldDesc}}" prop="{{.ColumnName}}"><el-select v-model="searchInfo.{{.ColumnName}}" clearable placeholder="请选择"><el-option key="true" label="是" value="true"></el-option><el-option key="false" label="否" value="false"></el-option></el-select></el-form-item>
          {{- else if .DictType}}
            <el-form-item label="{{.FieldDesc}}" prop="{{.ColumnName}}"><el-select v-model="searchInfo.{{.ColumnName}}" clearable placeholder="请选择" @clear="()=>{searchInfo.{{.ColumnName}}=undefined}"><el-option v-for="(item,key) in {{ .DictType }}Options" :key="key" :label="item.label" :value="item.value" /></el-select></el-form-item>
          {{- else}}
            <el-form-item label="{{.FieldDesc}}" prop="{{.ColumnName}}">
              {{- if eq .FieldType "float64" "int"}}
                {{if eq .FieldSearchType "BETWEEN" "NOT BETWEEN"}}
                  <el-input v-model.number="searchInfo.start{{.FieldName}}" placeholder="搜索条件(起)" />—<el-input v-model.number="searchInfo.end{{.FieldName}}" placeholder="搜索条件(止)" />
                {{- else}}
                {{- if .DictType}}
                  <el-select v-model="searchInfo.{{.ColumnName}}" placeholder="请选择" clearable>
                    <el-option v-for="(item,key) in {{ .DictType }}Options" :key="key" :label="item.label" :value="item.value" />
                  </el-select>
                {{- else}}
                  <el-input v-model.number="searchInfo.{{.ColumnName}}" placeholder="请输入" />
                {{- end }}
              {{- end}}
              {{- else if eq .FieldType "time.Time"}}
                  {{if eq .FieldSearchType "BETWEEN" "NOT BETWEEN"}}
                  <template #label>
                  <span>
                    {{.FieldDesc}}
                    <el-tooltip content="搜索范围是开始日期（包含）至结束日期（不包含）">
                      <el-icon><QuestionFilled /></el-icon>
                    </el-tooltip>
                  </span>
                </template>
                  <el-date-picker v-model="searchInfo.start{{.FieldName}}" type="datetime" placeholder="开始日期" :disabled-date="time=> searchInfo.end{{.FieldName}} ? time.getTime() > searchInfo.end{{.FieldName}}.getTime() : false"></el-date-picker>
                  —
                  <el-date-picker v-model="searchInfo.end{{.FieldName}}" type="datetime" placeholder="结束日期" :disabled-date="time=> searchInfo.start{{.FieldName}} ? time.getTime() < searchInfo.start{{.FieldName}}.getTime() : false"></el-date-picker>
                 {{- else}}
                 <el-date-picker v-model="searchInfo.{{.ColumnName}}" type="datetime" placeholder="搜索条件"></el-date-picker>
                {{- end}}
              {{- else}}<el-input v-model="searchInfo.{{.ColumnName}}" placeholder="请输入" />
              {{- end}}
              </el-form-item>{{ end }}{{ end }}{{ end }}
          <el-form-item>
            <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
            <el-button icon="refresh" @click="onReset">重置</el-button>
            <el-button type="success" icon="plus" @click="openDialog">新增</el-button>
          </el-form-item>
        </el-form>
        <el-table
        ref="tableRef"
        :data="tableData"
        :max-height="590"
        row-key="id"
        border
        @sort-change="sortChange"
        show-overflow-tooltip>
                <el-table-column align="center" label="ID" prop="id" :width="100" fixed="left" />
                <el-table-column align="center" label="创建时间" prop="created_at" sortable :width="160"></el-table-column>
                {{- range .Fields}}
                {{- if .DictType}}
                <el-table-column align="center" label="{{.FieldDesc}}" prop="{{.ColumnName}}" {{- if .Sort}} sortable{{- end}} :min-width="120">
                    <template #default="scope">
                    {{"{{"}} filterDict(scope.row.{{.ColumnName}},{{.DictType}}Options) {{"}}"}}
                    </template>
                </el-table-column>
                {{- else if eq .FieldType "bool" }}
                <el-table-column align="center" label="{{.FieldDesc}}" prop="{{.ColumnName}}" {{- if .Sort}} sortable{{- end}} :width="120">
                    <template #default="scope">{{"{{"}} formatBoolean(scope.row.{{.ColumnName}}) {{"}}"}}</template>
                </el-table-column>
                 {{- else if eq .FieldType "time.Time" }}
                 <el-table-column align="center" label="{{.FieldDesc}}" {{- if .Sort}} sortable{{- end}} :width="180">
                    <template #default="scope">{{"{{"}} formatDate(scope.row.{{.ColumnName}}) {{"}}"}}</template>
                 </el-table-column>
                  {{- else if eq .FieldType "picture" }}
                  <el-table-column label="{{.FieldDesc}}" :width="200">
                      <template #default="scope">
                        <el-image :src="getUrl(scope.row.{{.ColumnName}})" fit="cover"/>
                      </template>
                  </el-table-column>
                   {{- else if eq .FieldType "pictures" }}
                   <el-table-column label="{{.FieldDesc}}" :width="200">
                      <template #default="scope">
                         <div class="multiple-img-box">
                            <el-image v-for="(item,index) in scope.row.{{.ColumnName}}" style="width: 80px; height: 80px" :src="getUrl(item)" fit="cover"/>
                        </div>
                      </template>
                   </el-table-column>
                   {{- else if eq .FieldType "file" }}
                            <el-table-column label="{{.FieldDesc}}" :width="200">
                                <template #default="scope">
                                     <div class="file-list">
                                       <el-tag v-for="file in scope.row.{{.ColumnName}}" :key="file.uid">{{"{{"}}file.name{{"}}"}}</el-tag>
                                     </div>
                                </template>
                            </el-table-column>
                {{- else }}
                <el-table-column align="center" label="{{.FieldDesc}}" prop="{{.ColumnName}}" {{- if .Sort}} sortable{{- end}} :min-width="120" />
                {{- end }}
                {{- end }}
                <el-table-column align="center" label="操作" :width="160" fixed="right">
                    <template #default="scope">
                    <el-button type="primary" link icon="edit" @click="updateFunc(scope.row)">变更</el-button>
                    <el-button type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
                </el-table>
                <el-pagination
                  :layout="PageLayout"
                  :current-page="page"
                  :page-size="pageSize"
                  :page-sizes="PageSizes"
                  :total="total"
                  @current-change="pageChange"
                  @size-change="pageSizeChange"
                />
  </el-card>
    <el-dialog
        class="form-dialog"
        v-model="dialogShow"
        draggable
        destroy-on-close
        :before-close="closeDialog"
        :title="dTitle"
        top="10px"
    >
      <el-form
          :model="formData"
          label-position="right"
          ref="elFormRef"
          :rules="formDataRule"
          :disabled="dialogDisabled"
          label-width="auto"
      >
    {{- range .Fields}}
        <el-form-item label="{{.FieldDesc}}" prop="{{.ColumnName}}">
      {{- if eq .FieldType "bool" }}
          <el-switch v-model="formData.{{.ColumnName}}" active-color="#13ce66" inactive-color="#ff4949" active-text="是" inactive-text="否" clearable ></el-switch>
      {{- end }}
      {{- if eq .FieldType "string" }}
          <el-input v-model="formData.{{.ColumnName}}" clearable :maxlength="{{.DataTypeLong}}" show-word-limit />
      {{- end }}
      {{- if eq .FieldType "int" }}
      {{- if .DictType}}
          <el-select v-model="formData.{{.ColumnName}}" style="width:100%" clearable >
            <el-option v-for="(item,key) in {{ .DictType }}Options" :key="key" :label="item.label" :value="item.value" />
          </el-select>
      {{- else }}<el-input-number v-model="formData.{{.ColumnName}}" />
      {{- end }}
      {{- end }}
      {{- if eq .FieldType "time.Time" }}
          <el-date-picker v-model="formData.{{.ColumnName}}" type="date" clearable  />
      {{- end }}
      {{- if eq .FieldType "float64" }}
          <el-input-number v-model="formData.{{.ColumnName}}"  style="width:100%" :precision="2" clearable  />
      {{- end }}
      {{- if eq .FieldType "enum" }}
            <el-select v-model="formData.{{.ColumnName}}" style="width:100%" clearable >
               <el-option v-for="item in [{{.DataTypeLong}}]" :key="item" :label="item" :value="item" />
            </el-select>
      {{- end }}
      {{- if eq .FieldType "picture" }}
            <SelectImage v-model="formData.{{.ColumnName}}" />
      {{- end }}
      {{- if eq .FieldType "pictures" }}
            <SelectImage v-model="formData.{{.ColumnName}}" multiple />
      {{- end }}
      {{- if eq .FieldType "file" }}
            <SelectFile v-model="formData.{{.ColumnName}}" />
      {{- end }}
        </el-form-item>
      {{- end }}
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { create{{.StructName}}, update{{.StructName}}, delete{{.StructName}}ById, delete{{.StructName}}ByIds, get{{.StructName}}ById, get{{.StructName}}List} from '@/api/backend/{{.PackageName}}'
import { getDictFunc, formatDate, formatBoolean, filterDict } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onBeforeMount } from 'vue'
import { PageLayout, PageSizes } from "@/utils/const"
import { copyText } from '@/utils/stringFun'
import { useRouter, useRoute } from 'vue-router'
import { useAppStore } from "@/pinia/modules/app"
import { useTurnToUser } from '@/hooks/turnPage'

const { turnToUser } = useTurnToUser()
const router = useRouter()
const route = useRoute()
const appStore = useAppStore()
{{- range $index, $element := .DictTypes}}
const {{ $element }}Options = ref([])
{{- end }}
const dialogShow = ref(false)
const dTitle = ref('')
const dType = ref('')
const elFormRef = ref()
const elSearchFormRef = ref()
const page = ref(1)
const total = ref(0)
const pageSize = ref(100)
const tableData = ref([])
const searchInfo = ref({})
const dialogDisabled = ref(false)
const formData = ref({
                   {{- range .Fields}}
                     {{- if eq .FieldType "bool" }}{{.ColumnName}}: false,{{- end }}
                     {{- if eq .FieldType "string" }}{{.ColumnName}}: '',{{- end }}
                     {{- if eq .FieldType "int" }}{{.ColumnName}}: {{- if .DictType }} undefined{{ else }} 0{{- end }},{{- end }}
                     {{- if eq .FieldType "time.Time" }}{{.ColumnName}}: new Date(),{{- end }}
                     {{- if eq .FieldType "float64" }}{{.ColumnName}}: 0,{{- end }}
                     {{- if eq .FieldType "picture" }}{{.ColumnName}}: "",{{- end }}
                     {{- if eq .FieldType "pictures" }}{{.ColumnName}}: [],{{- end }}
                     {{- if eq .FieldType "file" }}{{.ColumnName}}: [],{{- end }}
                   {{- end }}})
const formDataRule = reactive({
  {{- range .Fields }}{{- if eq .Require true }}{{.FieldJson }} : [{required: true,message: '请完善该值',trigger: ['input','blur']},{{- if eq .FieldType "string" }}{whitespace: true,message: '不能只输入空格',trigger: ['input', 'blur']}{{- end }}],{{- end }}{{- end }}})

const execItem = (item) => {
  return item
}

const getTableData = async() => {
  const table = await get{{.StructName}}List({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    let resDataList = table.data.list || []
    let newList = []
    resDataList.forEach(item => {
      item = execItem(item)
      newList.push(item)
    })
    tableData.value = newList
    total.value = table.data.total
  }
}

const sortChange = ({ prop, order }) => {
  searchInfo.value.sort_prop = prop
  searchInfo.value.sort_desc = order === 'ascending'
  getTableData()
}

const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    {{- range .Fields}}{{- if eq .FieldType "bool" }}
    if (searchInfo.value.{{.ColumnName}} === ""){
      searchInfo.value.{{.ColumnName}}=null
    }{{ end }}{{ end }}
    getTableData()
  })
}

const pageSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const pageChange = (val) => {
  page.value = val
  getTableData()
}

{{- if .DictTypes }}
const setOptions = async () =>{
{{- range $index, $element := .DictTypes }}
  {{ $element }}Options.value = await getDictFunc('{{$element}}')
{{- end }}
}
{{- end }}

const deleteRow = (row) => {
  ElMessageBox.confirm('确定删除?').then(() => {
    delete{{.StructName}}Func(row)
  })
}

const updateFunc = async(row) => {
  const res = await get{{.StructName}}ById({ id: row.id })
  dType.value = 'update'
  dTitle.value = '更新'
  if (res.code === 0) {
    formData.value = res.data || {}
    dialogShow.value = true
  }
}

const delete{{.StructName}}Func = async (row) => {
  const res = await delete{{.StructName}}ById({ id: row.id })
  if (res.code === 0) {
    ElMessage({type: 'success',message: res.msg})
    getTableData()
  }
}

const openDialog = () => {
  dType.value = 'create'
  dTitle.value = '创建'
  dialogShow.value = true
}

const closeDialog = () => {
  dialogShow.value = false
  dialogDisabled.value = false
  elFormRef.value?.resetFields()
  formData.value = {
                     {{- range .Fields}}
                       {{- if eq .FieldType "bool" }}{{.ColumnName}}: false,{{- end }}
                       {{- if eq .FieldType "string" }}{{.ColumnName}}: '',{{- end }}
                       {{- if eq .FieldType "int" }}{{.ColumnName}}: {{- if .DictType }} undefined{{ else }} 0{{- end }},{{- end }}
                       {{- if eq .FieldType "time.Time" }}{{.ColumnName}}: new Date(),{{- end }}
                       {{- if eq .FieldType "float64" }}{{.ColumnName}}: 0,{{- end }}
                       {{- if eq .FieldType "picture" }}{{.ColumnName}}: "",{{- end }}
                       {{- if eq .FieldType "pictures" }}{{.ColumnName}}: [],{{- end }}
                       {{- if eq .FieldType "file" }}{{.ColumnName}}: [],{{- end }}
                     {{- end }}}
}

const enterDialog = async () => {
  elFormRef.value?.validate( async (valid) => {
    if (!valid) return
    let res
    dialogDisabled.value = true
    switch (dType.value) {
      case 'create':
        res = await create{{.StructName}}(formData.value)
        break
      case 'update':
        res = await update{{.StructName}}(formData.value)
        break
      default:
        res = await create{{.StructName}}(formData.value)
        break
    }
    dialogDisabled.value = false
    if (res.code === 0) {
      ElMessage({type: 'success', message: res.msg})
      closeDialog()
      getTableData()
    }
  })
}

const initQueryParams = () => {
  let routeQuery = route.query
  if (routeQuery) {
    if (routeQuery.id) {
      searchInfo.value.id = parseInt(routeQuery.id)
    }
  }
}

onBeforeMount(()=>{
  initQueryParams()
  getTableData()
})
</script>

<style></style>
