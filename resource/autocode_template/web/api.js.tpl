import service from '@/utils/request'

export const create{{.StructName}} = (data) => {
  return service({
    url: '/{{.Abbreviation}}/Create',
    method: 'post',
    data
  })
}

export const delete{{.StructName}}ById = (data) => {
  return service({
    url: '/{{.Abbreviation}}/DeleteById',
    method: 'delete',
    data
  })
}

export const delete{{.StructName}}ByIds = (data) => {
  return service({
    url: '/{{.Abbreviation}}/DeleteByIds',
    method: 'delete',
    data
  })
}

export const update{{.StructName}} = (data) => {
  return service({
    url: '/{{.Abbreviation}}/Update',
    method: 'put',
    data
  })
}

export const get{{.StructName}}ById = (params) => {
  return service({
    url: '/{{.Abbreviation}}/GetById',
    method: 'get',
    params
  })
}

export const get{{.StructName}}All = (params) => {
  return service({
    url: '/{{.Abbreviation}}/GetAll',
    method: 'get',
    params
  })
}

export const get{{.StructName}}List = (params) => {
  return service({
    url: '/{{.Abbreviation}}/GetList',
    method: 'get',
    params
  })
}
