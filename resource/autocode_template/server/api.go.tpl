package {{.Package}}

import (
    "aimsg-server/global"
    req "aimsg-server/model/{{.Package}}/request"
    "aimsg-server/model/common/request"
    "aimsg-server/model/common/response"
    "aimsg-server/service"
    "git.costnovel.com/cashbox-ai/ai-par-model/model"
    "github.com/gin-gonic/gin"
)

type {{.StructName}}Api struct {}

var {{.Abbreviation}}Service = service.ServiceGroupApp.{{.PackageT}}ServiceGroup.{{.StructName}}Service

func (m *{{.StructName}}Api) Create(c *gin.Context) {
	var reqData model.{{.StructName}}
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
        response.FailWithMessage(err.<PERSON>(), c)
        return
    }
	if err := {{.Abbreviation}}Service.Create(c, &reqData); err != nil {
		response.FailWithMessage(err.<PERSON>rror(), c)
	} else {
		response.Ok(c)
	}
}

func (m *{{.StructName}}Api) DeleteById(c *gin.Context) {
	var reqData model.{{.StructName}}
    if err := c.ShouldBind(&reqData); err != nil {
        response.FailWithMessage(err.Error(), c)
        return
    }
    if err := global.Validate.Struct(reqData); err != nil {
        response.FailWithMessage(err.Error(), c)
        return
    }
	if err := {{.Abbreviation}}Service.DeleteById(c, reqData.Id); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *{{.StructName}}Api) DeleteByIds(c *gin.Context) {
	var reqData request.IdsReq
    if err := c.ShouldBind(&reqData); err != nil {
        response.FailWithMessage(err.Error(), c)
        return
    }
	if err := {{.Abbreviation}}Service.DeleteByIds(c, reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *{{.StructName}}Api) Update(c *gin.Context) {
	var reqData model.{{.StructName}}
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := global.Validate.Struct(reqData); err != nil {
        response.FailWithMessage(err.Error(), c)
        return
    }
	if err := {{.Abbreviation}}Service.Update(c, &reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (m *{{.StructName}}Api) GetById(c *gin.Context) {
	var reqData model.{{.StructName}}
    if err := c.ShouldBind(&reqData); err != nil {
        response.FailWithMessage(err.Error(), c)
        return
    }
    if err := global.Validate.Struct(reqData); err != nil {
        response.FailWithMessage(err.Error(), c)
        return
    }
    if res, err := {{.Abbreviation}}Service.GetById(c, reqData.Id); err != nil {
        response.FailWithMessage(err.Error(), c)
    } else {
        response.OkWithData(res, c)
    }
}

func (m *{{.StructName}}Api) GetAll(c *gin.Context) {
    if res, err := {{.Abbreviation}}Service.GetAll(c); err != nil {
        response.FailWithMessage(err.Error(), c)
    } else {
        response.OkWithData(res, c)
    }
}

func (m *{{.StructName}}Api) GetList(c *gin.Context) {
	var reqData req.{{.StructName}}Search
	if err := c.ShouldBind(&reqData); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := {{.Abbreviation}}Service.GetList(c, reqData); err != nil {
        response.FailWithMessage(err.Error(), c)
    } else {
        response.OkWithData(response.PageResult{
            List:     list,
            Total:    total,
            Page:     reqData.Page,
            PageSize: reqData.PageSize,
        }, c)
    }
}
