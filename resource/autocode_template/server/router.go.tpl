package {{.Package}}

import (
	"aimsg-server/api/v1"
	"aimsg-server/middleware"
	"github.com/gin-gonic/gin"
)

type {{.StructName}}Router struct {}

func (s *{{.StructName}}Router) Init{{.StructName}}Router(Router *gin.RouterGroup) {
	opRouter := Router.Group("{{.Abbreviation}}").Use(middleware.OperationRecord())
	aRouter := Router.Group("{{.Abbreviation}}")
	var {{.Abbreviation}}Api = v1.ApiGroupApp.{{.PackageT}}ApiGroup.{{.StructName}}Api
	opRouter.POST("Create", {{.Abbreviation}}Api.Create)
    opRouter.DELETE("DeleteById", {{.Abbreviation}}Api.DeleteById)
    opRouter.DELETE("DeleteByIds", {{.Abbreviation}}Api.DeleteByIds)
    opRouter.PUT("Update", {{.Abbreviation}}Api.Update)

	aRouter.GET("GetById", {{.Abbreviation}}Api.GetById)
	aRouter.GET("GetAll", {{.Abbreviation}}Api.GetAll)
    aRouter.GET("GetList", {{.Abbreviation}}Api.GetList)
}
