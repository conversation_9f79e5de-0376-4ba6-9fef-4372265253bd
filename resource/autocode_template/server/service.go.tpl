package {{.Package}}

import (
	"aimsg-server/global"
    req "aimsg-server/model/{{.Package}}/request"
    "aimsg-server/model/common/request"
    "context"
    "encoding/json"
    "git.costnovel.com/cashbox-ai/ai-par-model/model"
    "gorm.io/gorm/clause"
)

type {{.StructName}}Service struct {}

func (m *{{.StructName}}Service) Create(ctx context.Context, reqData *model.{{.StructName}}) (err error) {
	err = global.DB.Model(&model.{{.StructName}}{}).Create(reqData).Error
	return
}

func (m *{{.StructName}}Service) DeleteById(ctx context.Context, id uint) (err error) {
	err = global.DB.Model(&model.{{.StructName}}{}).Where("id = ?", id).Delete(&model.{{.StructName}}{}).Error
	return
}

func (m *{{.StructName}}Service) DeleteByIds(ctx context.Context, reqData request.IdsReq) (err error) {
	err = global.DB.Model(&model.{{.StructName}}{}).Where("id IN (?)", reqData.Ids).Delete(&model.{{.StructName}}{}).Error
	return
}

func (m *{{.StructName}}Service) Update(ctx context.Context, reqData *model.{{.StructName}}) (err error) {
    var (
		reqDataBytes []byte
		updateMap    map[string]interface{}
	)
	if reqDataBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if err = json.Unmarshal(reqDataBytes, &updateMap); err != nil {
		return
	}
	err = global.DB.Model(&model.{{.StructName}}{}).Where("id = ?", reqData.Id).Updates(updateMap).Error
	return
}

func (m *{{.StructName}}Service) GetById(ctx context.Context, id uint) (res *model.{{.StructName}}, err error) {
	err = global.DB.Model(&model.{{.StructName}}{}).Where("id = ?", id).First(&res).Error
	return
}

func (m *{{.StructName}}Service) GetAll(ctx context.Context) (resList []*model.{{.StructName}}, err error) {
	err = global.DB.Model(&model.{{.StructName}}{}).Find(&resList).Error
	return
}

func (m *{{.StructName}}Service) SearchOne(ctx context.Context, info *model.{{.StructName}}) (res *model.{{.StructName}}, err error) {
	db := global.DB.Model(&model.{{.StructName}}{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
    {{- range .Fields}}
        {{- if .FieldSearchType}}
            {{- if or (eq .FieldType "string") (eq .FieldType "enum") (eq .FieldType "richtext") }}
    if info.{{.FieldName}} != "" {
        db = db.Where("{{.ColumnName}} {{.FieldSearchType}} ?",{{if eq .FieldSearchType "LIKE"}}"%"+ {{ end }}info.{{.FieldName}}{{if eq .FieldSearchType "LIKE"}}+"%"{{ end }})
    }
            {{- else if eq .FieldType "int"}}
    if info.{{.FieldName}} != 0 {
        db = db.Where("{{.ColumnName}} {{.FieldSearchType}} ?", info.{{.FieldName}})
    }
            {{- else}}
    if info.{{.FieldName}} != nil {
        db = db.Where("{{.ColumnName}} {{.FieldSearchType}} ?",{{if eq .FieldSearchType "LIKE"}}"%"+{{ end }}info.{{.FieldName}}{{if eq .FieldSearchType "LIKE"}}+"%"{{ end }})
    }
            {{- end }}
        {{- end }}
    {{- end }}
	if err = db.First(&res).Error; err != nil {
    	return
    }
	return
}

func (m *{{.StructName}}Service) SearchCount(ctx context.Context, info *model.{{.StructName}}) (total int64, err error) {
	db := global.DB.Model(&model.{{.StructName}}{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
    {{- range .Fields}}
        {{- if .FieldSearchType}}
            {{- if or (eq .FieldType "string") (eq .FieldType "enum") (eq .FieldType "richtext") }}
    if info.{{.FieldName}} != "" {
        db = db.Where("{{.ColumnName}} {{.FieldSearchType}} ?",{{if eq .FieldSearchType "LIKE"}}"%"+ {{ end }}info.{{.FieldName}}{{if eq .FieldSearchType "LIKE"}}+"%"{{ end }})
    }
            {{- else if eq .FieldType "int"}}
    if info.{{.FieldName}} != 0 {
        db = db.Where("{{.ColumnName}} {{.FieldSearchType}} ?", info.{{.FieldName}})
    }
            {{- else}}
    if info.{{.FieldName}} != nil {
        db = db.Where("{{.ColumnName}} {{.FieldSearchType}} ?",{{if eq .FieldSearchType "LIKE"}}"%"+{{ end }}info.{{.FieldName}}{{if eq .FieldSearchType "LIKE"}}+"%"{{ end }})
    }
            {{- end }}
        {{- end }}
    {{- end }}
	if err = db.Count(&total).Error; err != nil {
    	return
    }
	return
}

func (m *{{.StructName}}Service) GetList(ctx context.Context, info req.{{.StructName}}Search) (resList []*model.{{.StructName}}, total int64, err error) {
	db := global.DB.Model(&model.{{.StructName}}{})
	if info.Id != 0 {
		db = db.Where("id = ?", info.Id)
	}
    if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}
	if len(info.UpdatedAtRange) == 2 {
		db = db.Where("updated_at BETWEEN ? AND ?", info.UpdatedAtRange[0], info.UpdatedAtRange[1])
	}
    {{- range .Fields}}
        {{- if .FieldSearchType}}
            {{- if or (eq .FieldType "string") (eq .FieldType "enum") (eq .FieldType "richtext") }}
    if info.{{.FieldName}} != "" {
        db = db.Where("{{.ColumnName}} {{.FieldSearchType}} ?",{{if eq .FieldSearchType "LIKE"}}"%"+ {{ end }}info.{{.FieldName}}{{if eq .FieldSearchType "LIKE"}}+"%"{{ end }})
    }
            {{- else if eq .FieldType "int"}}
    if info.{{.FieldName}} != 0 {
        db = db.Where("{{.ColumnName}} {{.FieldSearchType}} ?", info.{{.FieldName}})
    }
            {{- else}}
    if info.{{.FieldName}} != nil {
        db = db.Where("{{.ColumnName}} {{.FieldSearchType}} ?",{{if eq .FieldSearchType "LIKE"}}"%"+{{ end }}info.{{.FieldName}}{{if eq .FieldSearchType "LIKE"}}+"%"{{ end }})
    }
            {{- end }}
        {{- end }}
    {{- end }}
	if err = db.Count(&total).Error; err != nil {
    	return
    }
	db = db.Limit(info.PageSize).Offset((info.Page - 1) * info.PageSize)
	if info.SortProp != "" {
        db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: info.SortProp}, Desc: info.SortDesc})
    } else {
        db = db.Order("id DESC")
    }
	err = db.Find(&resList).Error
	return
}
