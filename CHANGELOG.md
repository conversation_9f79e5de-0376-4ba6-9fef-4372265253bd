## 1.13.0

```mysql
ALTER TABLE `ai_role_media`
    ADD `thumbnail_url` varchar(512) COMMENT '缩略图地址';

ALTER TABLE `digital_clone`.`digital_user`
    ADD COLUMN `fcm_token` varchar(256) NULL COMMENT 'FcmToken' AFTER `version`;

ALTER TABLE `digital_clone`.`chat_guide_record`
    ADD COLUMN `guide_type` int NOT NULL DEFAULT 1 COMMENT '类型' AFTER `deleted_at`;

ALTER TABLE `digital_clone`.`orders`
    ADD COLUMN `ai_role_id` bigint NULL COMMENT 'AI角色ID' AFTER `order_test`;
```

## 1.14.0

```mysql
create table touch_options
(
    id         bigint unsigned auto_increment
        primary key,
    created_at datetime(3)      null,
    updated_at datetime(3)      null,
    deleted_at datetime(3)      null,
    name       varchar(16)      null comment '名称',
    touch_type bigint default 1 null comment '类型',
    icon       varchar(256)     null comment '图表',
    content    varchar(512)     null comment '内容',
    sort       bigint default 1 null comment '排序'
);

create index idx_touch_options_deleted_at
    on touch_options (deleted_at);

create index idx_touch_options_touch_type
    on touch_options (touch_type);


```

## 1.15.0

```mysql
ALTER TABLE `digital_clone`.`ai_role`
    ADD COLUMN `chat_count` bigint NULL DEFAULT 0 COMMENT '聊天次数' AFTER `goo_voice_req`;

ALTER TABLE `digital_clone`.`ai_common_reply`
    ADD COLUMN `reply_type` bigint NULL DEFAULT 1 COMMENT '回复内容类型' AFTER `content`;

INSERT INTO `digital_clone`.`ai_common_reply` (`created_at`, `updated_at`, `deleted_at`, `content`, `reply_type`)
VALUES ('2024-04-14 17:35:05.000', '2024-04-14 17:35:05.000', NULL, 'You don\'t like me, why don\'t you say anything?',
        2);
INSERT INTO `digital_clone`.`ai_common_reply` (`created_at`, `updated_at`, `deleted_at`, `content`, `reply_type`)
VALUES ('2024-04-14 17:35:05.000', '2024-04-14 17:35:05.000', NULL,
        'Am I too pretty, honey? Are you just looking at me and don’t want to say anything?', 2);
INSERT INTO `digital_clone`.`ai_common_reply` (`created_at`, `updated_at`, `deleted_at`, `content`, `reply_type`)
VALUES ('2024-04-14 17:35:05.000', '2024-04-14 17:35:05.000', NULL, 'Oops, I feel my heart flutter when I see you', 2);
INSERT INTO `digital_clone`.`ai_common_reply` (`created_at`, `updated_at`, `deleted_at`, `content`, `reply_type`)
VALUES ('2024-04-14 17:35:05.000', '2024-04-14 17:35:05.000', NULL,
        'Fortunately, I didn’t give up and finally waited for you', 2);
INSERT INTO `digital_clone`.`ai_common_reply` (`created_at`, `updated_at`, `deleted_at`, `content`, `reply_type`)
VALUES ('2024-04-14 17:35:05.000', '2024-04-14 17:35:05.000', NULL,
        'I\'m not your favorite type? Why don\'t you speak?', 2);
INSERT INTO `digital_clone`.`ai_common_reply` (`created_at`, `updated_at`, `deleted_at`, `content`, `reply_type`)
VALUES ('2024-04-14 17:35:05.000', '2024-04-14 17:35:05.000', NULL,
        'How I wish you were the one who could conquer me, but silence can\'t do that, baby', 2);
INSERT INTO `digital_clone`.`ai_common_reply` (`created_at`, `updated_at`, `deleted_at`, `content`, `reply_type`)
VALUES ('2024-04-14 17:35:05.000', '2024-04-14 17:35:05.000', NULL,
        'If I don\'t speak, aren\'t you going to speak either? I\'m really unhappy with you like this. Come and comfort me.',
        2);
INSERT INTO `digital_clone`.`ai_common_reply` (`created_at`, `updated_at`, `deleted_at`, `content`, `reply_type`)
VALUES ('2024-04-14 17:35:05.000', '2024-04-14 17:35:05.000', NULL,
        'I can be docile or wild, which one do you want me to be?', 2);
INSERT INTO `digital_clone`.`ai_common_reply` (`created_at`, `updated_at`, `deleted_at`, `content`, `reply_type`)
VALUES ('2024-04-14 17:35:05.000', '2024-04-14 17:35:05.000', NULL,
        'Have you ever tried role playing? It must be very exciting. Do you want to try it, dear?', 2);
INSERT INTO `digital_clone`.`ai_common_reply` (`created_at`, `updated_at`, `deleted_at`, `content`, `reply_type`)
VALUES ('2024-04-14 17:35:05.000', '2024-04-14 17:35:05.000', NULL,
        'I\'m craving some NSFW content right now, would you like to chat with me about it?', 2);

create table img_config
(
    id              bigint unsigned auto_increment
        primary key,
    created_at      datetime(3)        null,
    updated_at      datetime(3)        null,
    deleted_at      datetime(3)        null,
    img_config_type bigint   default 1 null comment '图片配置类型',
    media_url       varchar(512)       null comment '资源地址',
    config_status   smallint default 1 null comment '状态'
);

create index idx_img_config_deleted_at
    on img_config (deleted_at);

INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (1, '2024-04-15 14:45:04.000', '2024-04-15 16:02:34.089', null, 1, 'public/banner/plan1_banner_1.png', 1);
INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (2, '2024-04-15 14:45:04.000', '2024-04-15 15:59:41.411', null, 1, 'public/banner/plan1_banner_2.png', 1);
INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (3, '2024-04-15 14:45:04.000', '2024-04-15 16:02:32.893', null, 1, 'public/banner/plan1_banner_3.png', 1);
INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (4, '2024-04-15 14:45:04.000', '2024-04-15 14:45:04.000', null, 1, 'public/banner/plan1_banner_4.jpg', 2);
INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (5, '2024-04-15 14:45:04.000', '2024-04-15 16:02:31.726', null, 1, 'public/banner/plan1_banner_5.jpg', 2);
INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (6, '2024-04-15 14:45:04.000', '2024-04-15 16:04:01.533', null, 1, 'public/banner/plan1_banner_6.jpg', 2);
INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (7, '2024-04-15 14:45:04.000', '2024-04-15 16:04:00.414', null, 1, 'public/banner/plan1_banner_7.jpg', 2);
INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (8, '2024-04-15 14:45:04.000', '2024-04-15 16:03:59.719', null, 1, 'public/banner/plan1_banner_8.jpg', 2);
INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (9, '2024-04-15 14:45:04.000', '2024-04-15 16:02:19.595', null, 2, 'public/banner/plan2_banner_1.jpg', 1);
INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (10, '2024-04-15 14:45:04.000', '2024-04-15 16:02:20.740', null, 2, 'public/banner/plan2_banner_2.jpg', 1);
INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (11, '2024-04-15 14:45:04.000', '2024-04-15 16:02:17.360', null, 2, 'public/banner/plan2_banner_3.jpg', 1);
INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (12, '2024-04-15 14:45:04.000', '2024-04-15 15:53:08.678', null, 2, 'public/banner/plan2_banner_4.jpg', 2);
INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (13, '2024-04-15 14:45:04.000', '2024-04-15 14:45:04.000', null, 2, 'public/banner/plan2_banner_5.jpg', 2);
INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (14, '2024-04-15 14:45:04.000', '2024-04-15 14:45:04.000', null, 2, 'public/banner/plan2_banner_6.jpg', 2);
INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (15, '2024-04-15 14:45:04.000', '2024-04-15 14:45:04.000', null, 2, 'public/banner/plan2_banner_7.jpg', 2);
INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (16, '2024-04-15 14:45:04.000', '2024-04-15 14:45:04.000', null, 2, 'public/banner/plan2_banner_8.jpg', 2);
INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (17, '2024-04-15 14:45:04.000', '2024-04-15 14:45:04.000', null, 2, 'public/banner/plan2_banner_9.jpg', 2);
INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (18, '2024-04-15 14:45:04.000', '2024-04-15 14:45:04.000', null, 2, 'public/banner/plan2_banner_10.jpg', 2);
INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (19, '2024-04-15 14:45:04.000', '2024-04-15 14:45:04.000', null, 2, 'public/banner/plan2_banner_11.jpg', 2);
INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (20, '2024-04-15 14:45:04.000', '2024-04-15 14:45:04.000', null, 2, 'public/banner/plan2_banner_12.jpg', 2);
INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (21, '2024-04-15 14:45:04.000', '2024-04-15 16:02:10.326', null, 2, 'public/banner/plan2_banner_13.jpg', 2);
INSERT INTO digital_clone.img_config (id, created_at, updated_at, deleted_at, img_config_type, media_url, config_status)
VALUES (22, '2024-04-15 14:45:04.000', '2024-04-15 16:02:07.919', null, 2, 'public/banner/plan2_banner_14.jpg', 2);



```

## 1.16.0

```mysql
-- 用户表增加`客户端语言`字段
ALTER TABLE `digital_clone`.`digital_user`
    ADD COLUMN `language` varchar(16) NULL DEFAULT NULL COMMENT '客户端语言' AFTER `fcm_token`;
create table koc_user_info
(
    id             bigint unsigned auto_increment
        primary key,
    created_at     datetime(3)              null,
    updated_at     datetime(3)              null,
    deleted_at     datetime(3)              null,
    ai_role_id     varchar(32)              null comment 'AI角色ID',
    platform       bigint                   null comment '提现平台',
    account        varchar(191)             null comment '提现账号',
    all_income     varchar(191) default '0' null comment '总收入',
    pending_income varchar(191) default '0' null comment '待提现收入',
    nickname       varchar(32)              null comment '网红昵称',
    constraint idx_koc_user_info_ai_role_id
        unique (ai_role_id)
);

create index idx_koc_user_info_deleted_at
    on koc_user_info (deleted_at);

create index idx_koc_user_info_platform
    on koc_user_info (platform);

create table withdraw_history
(
    id          bigint unsigned auto_increment
        primary key,
    created_at  datetime(3)              null,
    updated_at  datetime(3)              null,
    deleted_at  datetime(3)              null,
    koc_user_id bigint unsigned          null comment 'KocUserID',
    day         date                     null comment '日期',
    platform    bigint                   null comment '提现平台',
    account     varchar(191)             null comment '提现账号',
    amount      varchar(191) default '0' null comment '提现金额'
);

create index idx_withdraw_history_deleted_at
    on withdraw_history (deleted_at);

create index idx_withdraw_history_koc_user_id
    on withdraw_history (koc_user_id);

create index idx_withdraw_history_platform
    on withdraw_history (platform);

create table sys_user_koc
(
    id          bigint unsigned auto_increment
        primary key,
    created_at  datetime(3)     null,
    updated_at  datetime(3)     null,
    deleted_at  datetime(3)     null,
    sys_user_id bigint unsigned null comment 'SysUserID',
    koc_user_id bigint unsigned null comment 'KocUserID',
    constraint sys_user_koc_unique_index
        unique (sys_user_id, koc_user_id)
);

create index idx_sys_user_koc_deleted_at
    on sys_user_koc (deleted_at);


```

## 1.17.0

```mysql
ALTER TABLE `digital_clone`.`ai_role`
    ADD COLUMN `role_play` int NOT NULL DEFAULT 2 COMMENT 'role_play开关' AFTER `video_list`;

CREATE TABLE ai_scenes
(
    id             BIGINT UNSIGNED auto_increment PRIMARY KEY,
    created_at     DATETIME(3)      NOT NULL,
    updated_at     DATETIME(3)      NOT NULL,
    deleted_at     DATETIME(3)      NULL,
    ai_role_id     BIGINT UNSIGNED  NOT NULL COMMENT 'AI角色ID',
    scene_name     VARCHAR(64)      NOT NULL COMMENT '场景名称',
    scene_type     BIGINT DEFAULT 1 NOT NULL COMMENT '场景类型',
    scene_describe VARCHAR(256)     NULL COMMENT '场景描述',
    chat_bg        VARCHAR(512)     NULL COMMENT '聊天背景地址',
    first_msg      TEXT             NULL COMMENT '第一句问候语',
    img_list       JSON             NULL COMMENT '图片组',
    undress_list   JSON             NULL COMMENT 'Undress图片组'
);
CREATE INDEX idx_ai_scenes_ai_role_id ON ai_scenes (ai_role_id);
CREATE INDEX idx_ai_scenes_deleted_at ON ai_scenes (deleted_at);

CREATE TABLE user_use_scenes
(
    id              BIGINT UNSIGNED auto_increment PRIMARY KEY,
    created_at      DATETIME(3)     NOT NULL,
    updated_at      DATETIME(3)     NOT NULL,
    deleted_at      DATETIME(3)     NULL,
    digital_user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    ai_role_id      BIGINT UNSIGNED NOT NULL COMMENT 'AI角色ID',
    scene_id        BIGINT UNSIGNED NOT NULL COMMENT '场景ID',
    CONSTRAINT use_uni_idx UNIQUE (digital_user_id, ai_role_id)
);
CREATE INDEX idx_user_use_scenes_deleted_at ON user_use_scenes (deleted_at);

-- AI场景 菜单
INSERT INTO `digital_clone`.`sys_base_menus` (`id`, `created_at`, `updated_at`, `deleted_at`, `menu_level`, `parent_id`,
                                              `path`, `name`, `hidden`, `component`, `sort`, `active_name`,
                                              `keep_alive`, `default_menu`, `title`, `icon`, `close_tab`)
VALUES (58, '2024-04-29 19:09:12.971', '2024-04-29 19:09:12.971', NULL, 0, '33', 'aiScenes', 'aiScenes', 0,
        'view/backend/aiScenes/aiScenes.vue', 17, '', 0, 0, 'AI场景', 'briefcase', 0);
INSERT INTO `digital_clone`.`sys_authority_menus` (`sys_base_menu_id`, `sys_authority_authority_id`)
VALUES (58, 888);

```

## 1.18.0

```mysql
ALTER TABLE `digital_clone`.`touch_options`
    ADD COLUMN `option_type` bigint NULL DEFAULT 1 COMMENT '操作类型' AFTER `sort`;

ALTER TABLE `digital_clone`.`product`
    ADD COLUMN `reward_amount` int NOT NULL DEFAULT 0 COMMENT '奖励数量' AFTER `amount`;

create table push_msg
(
    id         bigint unsigned auto_increment
        primary key,
    created_at datetime(3) null,
    updated_at datetime(3) null,
    deleted_at datetime(3) null,
    remark     varchar(32) null comment '备注信息',
    time_range json        null comment '时间区间',
    msg_list   json        null comment '消息列表'
);

create index idx_push_msg_deleted_at
    on push_msg (deleted_at);


```

## 1.19.0

```mysql
ALTER TABLE `digital_clone`.`ai_send_media`
    ADD COLUMN `can_undress` tinyint(1) NULL DEFAULT 0 COMMENT '是否可以Undress' AFTER `unlock_time`;

UPDATE ai_send_media
SET can_undress = 1
WHERE media_type = 1;

INSERT INTO `digital_clone`.`product` (`id`, `created_at`, `updated_at`, `deleted_at`, `name`, `description`, `goo_id`,
                                       `good_type`, `product_type`, `usd_price`, `sort`, `buy_count`, `pay_count`,
                                       `app_id`, `ios_id`, `amount`, `reward_amount`)
VALUES (63, '2024-05-23 01:16:48.382349', '2024-05-23 01:16:48.382349', NULL, '2000 Flowers', '', '10020', 1, 104, 9.99,
        100, 0, 0, 1, NULL, 2000, 0);
INSERT INTO `digital_clone`.`product` (`id`, `created_at`, `updated_at`, `deleted_at`, `name`, `description`, `goo_id`,
                                       `good_type`, `product_type`, `usd_price`, `sort`, `buy_count`, `pay_count`,
                                       `app_id`, `ios_id`, `amount`, `reward_amount`)
VALUES (64, '2024-05-23 01:16:48.382349', '2024-05-23 01:16:48.382349', NULL, '4000 Flowers', '', '10021', 1, 104,
        19.99, 100, 0, 0, 1, NULL, 4000, 0);
INSERT INTO `digital_clone`.`product` (`id`, `created_at`, `updated_at`, `deleted_at`, `name`, `description`, `goo_id`,
                                       `good_type`, `product_type`, `usd_price`, `sort`, `buy_count`, `pay_count`,
                                       `app_id`, `ios_id`, `amount`, `reward_amount`)
VALUES (65, '2024-05-23 01:16:48.382349', '2024-05-23 01:16:48.382349', NULL, '8000 Flowers', '', '10022', 1, 104,
        39.99, 100, 0, 0, 1, NULL, 8000, 0);

ALTER TABLE `digital_clone`.`digital_user_profile`
    ADD COLUMN `sub_price`  decimal(10, 2) NULL DEFAULT 0 COMMENT '累计订阅金额' AFTER `sign_count`,
    ADD COLUMN `sub_count`  int            NULL DEFAULT 0 COMMENT '累计订阅次数' AFTER `sub_price`,
    ADD COLUMN `good_price` decimal(10, 2) NULL DEFAULT 0 COMMENT '累计一次性付费金额' AFTER `sub_count`,
    ADD COLUMN `good_count` int            NULL DEFAULT 0 COMMENT '累计一次性付费次数' AFTER `good_price`;

```

## 2.0.0

```mysql
create table badge
(
    id                bigint unsigned auto_increment
        primary key,
    created_at        datetime(3)      null,
    updated_at        datetime(3)      null,
    deleted_at        datetime(3)      null,
    series_name       varchar(32)      not null comment '系列名称',
    name              varchar(32)      not null comment '名称',
    on_icon           varchar(256)     not null comment '点亮图标',
    off_icon          varchar(256)     not null comment '未点亮图标',
    flo_reward_count  bigint           not null comment '鲜花奖励数量',
    success_content   varchar(512)     not null comment '达成提示信息',
    trigger_condition varchar(512)     not null comment '触发条件',
    trigger_type      bigint           not null comment '触发类型',
    trigger_count     bigint           null comment '触发数量',
    period bigint null comment '周期',
    sort              bigint default 1 not null comment '排序',
    app_id            bigint           not null comment '应用ID'
);

create index idx_badge_deleted_at
    on badge (deleted_at);

create table user_badge
(
    id              bigint unsigned auto_increment
        primary key,
    created_at      datetime(3)     null,
    updated_at      datetime(3)     null,
    deleted_at      datetime(3)     null,
    digital_user_id bigint unsigned null comment '用户ID',
    badge_id        bigint unsigned null comment '徽章ID',
    count           bigint          null comment '数量',
    constraint user_badge_id_idx
        unique (digital_user_id, badge_id)
);

create index idx_user_badge_deleted_at
    on user_badge (deleted_at);

create table diy_photo_task
(
    id              bigint unsigned auto_increment
        primary key,
    created_at      datetime(3)                 null,
    updated_at      datetime(3)                 null,
    deleted_at      datetime(3)                 null,
    digital_user_id bigint unsigned             null comment '用户ID',
    user_status     bigint          default 1   null comment '用户状态',
    ai_role_id      bigint unsigned             null comment 'AI角色ID',
    level           bigint          default 1   null comment '制作等级',
    diy_req         varchar(255)                null comment '制作要求',
    img_count       bigint                      null comment '图片数量',
    consume         bigint                      null comment '消耗数量',
    email           varchar(128)                null comment '邮箱',
    task_state      bigint unsigned default '1' null comment '任务状态',
    complete_time   datetime(3)                 null comment '完成时间'
);

create index idx_diy_photo_task_deleted_at
    on diy_photo_task (deleted_at);


```

## 2.1.0

```mysql
ALTER TABLE `digital_clone`.`img_config`
    ADD COLUMN `app_id` bigint NULL DEFAULT 1 COMMENT '应用ID' AFTER `deleted_at`;
ALTER TABLE `digital_clone`.`img_config`
    ADD COLUMN `user_status` smallint NULL DEFAULT 1 COMMENT '用户状态' AFTER `config_status`;
-- 同步表 img_config
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (23, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 1, 'public/banner/plan1_banner_1.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (24, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 1, 'public/banner/plan1_banner_2.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (25, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 1, 'public/banner/plan1_banner_3.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (26, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 1, 'public/banner/plan1_banner_4.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (27, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 1, 'public/banner/plan1_banner_5.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (28, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 1, 'public/banner/plan1_banner_6.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (29, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 1, 'public/banner/plan1_banner_7.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (30, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 1, 'public/banner/plan1_banner_8.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (31, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 2, 'public/banner/plan2_banner_1.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (32, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 2, 'public/banner/plan2_banner_2.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (33, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 2, 'public/banner/plan2_banner_3.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (34, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 2, 'public/banner/plan2_banner_4.jpg', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (35, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 2, 'public/banner/plan2_banner_5.jpg', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (36, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 2, 'public/banner/plan2_banner_6.jpg', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (37, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 2, 'public/banner/plan2_banner_7.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (38, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 2, 'public/banner/plan2_banner_8.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (39, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 2, 'public/banner/plan2_banner_9.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (40, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 2, 'public/banner/plan2_banner_10.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (41, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 2, 'public/banner/plan2_banner_11.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (42, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 2, 'public/banner/plan2_banner_12.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (43, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 2, 'public/banner/plan2_banner_13.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (44, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 2, 2, 'public/banner/plan2_banner_14.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (45, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 1, 'public/banner/plan1_banner_1.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (46, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 1, 'public/banner/plan1_banner_2.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (47, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 1, 'public/banner/plan1_banner_3.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (48, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 1, 'public/banner/plan1_banner_4.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (49, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 1, 'public/banner/plan1_banner_5.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (50, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 1, 'public/banner/plan1_banner_6.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (51, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 1, 'public/banner/plan1_banner_7.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (52, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 1, 'public/banner/plan1_banner_8.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (53, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 2, 'public/banner/plan2_banner_1.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (54, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 2, 'public/banner/plan2_banner_2.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (55, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 2, 'public/banner/plan2_banner_3.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (56, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 2, 'public/banner/plan2_banner_4.jpg', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (57, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 2, 'public/banner/plan2_banner_5.jpg', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (58, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 2, 'public/banner/plan2_banner_6.jpg', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (59, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 2, 'public/banner/plan2_banner_7.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (60, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 2, 'public/banner/plan2_banner_8.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (61, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 2, 'public/banner/plan2_banner_9.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (62, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 2, 'public/banner/plan2_banner_10.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (63, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 2, 'public/banner/plan2_banner_11.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (64, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 2, 'public/banner/plan2_banner_12.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (65, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 2, 'public/banner/plan2_banner_13.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (66, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 3, 2, 'public/banner/plan2_banner_14.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (67, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 1, 'public/banner/plan1_banner_1.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (68, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 1, 'public/banner/plan1_banner_2.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (69, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 1, 'public/banner/plan1_banner_3.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (70, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 1, 'public/banner/plan1_banner_4.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (71, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 1, 'public/banner/plan1_banner_5.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (72, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 1, 'public/banner/plan1_banner_6.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (73, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 1, 'public/banner/plan1_banner_7.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (74, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 1, 'public/banner/plan1_banner_8.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (75, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 2, 'public/banner/plan2_banner_1.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (76, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 2, 'public/banner/plan2_banner_2.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (77, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 2, 'public/banner/plan2_banner_3.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (78, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 2, 'public/banner/plan2_banner_4.jpg', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (79, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 2, 'public/banner/plan2_banner_5.jpg', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (80, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 2, 'public/banner/plan2_banner_6.jpg', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (81, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 2, 'public/banner/plan2_banner_7.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (82, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 2, 'public/banner/plan2_banner_8.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (83, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 2, 'public/banner/plan2_banner_9.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (84, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 2, 'public/banner/plan2_banner_10.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (85, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 2, 'public/banner/plan2_banner_11.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (86, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 2, 'public/banner/plan2_banner_12.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (87, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 2, 'public/banner/plan2_banner_13.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (88, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 4, 2, 'public/banner/plan2_banner_14.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (89, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 1, 'public/banner/plan1_banner_1.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (90, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 1, 'public/banner/plan1_banner_2.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (91, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 1, 'public/banner/plan1_banner_3.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (92, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 1, 'public/banner/plan1_banner_4.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (93, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 1, 'public/banner/plan1_banner_5.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (94, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 1, 'public/banner/plan1_banner_6.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (95, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 1, 'public/banner/plan1_banner_7.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (96, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 1, 'public/banner/plan1_banner_8.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (97, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 2, 'public/banner/plan2_banner_1.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (98, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 2, 'public/banner/plan2_banner_2.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (99, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 2, 'public/banner/plan2_banner_3.jpg', 2, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (100, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 2, 'public/banner/plan2_banner_4.jpg', 1,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (101, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 2, 'public/banner/plan2_banner_5.jpg', 1,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (102, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 2, 'public/banner/plan2_banner_6.jpg', 1,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (103, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 2, 'public/banner/plan2_banner_7.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (104, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 2, 'public/banner/plan2_banner_8.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (105, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 2, 'public/banner/plan2_banner_9.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (106, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 2, 'public/banner/plan2_banner_10.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (107, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 2, 'public/banner/plan2_banner_11.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (108, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 2, 'public/banner/plan2_banner_12.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (109, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 2, 'public/banner/plan2_banner_13.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (110, '2024-06-11 19:00:00.000', '2024-06-11 19:00:00.000', NULL, 5, 2, 'public/banner/plan2_banner_14.jpg', 2,
        1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (111, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 1, 3, 'public/icon/ic_asmr.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (112, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 2, 3, 'public/icon/ic_asmr.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (113, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 3, 3, 'public/icon/ic_asmr.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (114, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 4, 3, 'public/icon/ic_asmr.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (115, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 5, 3, 'public/icon/ic_asmr.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (116, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 1, 4, 'public/icon/ic_seeu.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (117, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 2, 4, 'public/icon/ic_seeu.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (118, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 3, 4, 'public/icon/ic_seeu.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (119, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 4, 4, 'public/icon/ic_seeu.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (120, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 5, 4, 'public/icon/ic_seeu.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (121, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 1, 5, 'public/icon/ic_touch.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (122, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 2, 5, 'public/icon/ic_touch.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (123, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 3, 5, 'public/icon/ic_touch.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (124, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 4, 5, 'public/icon/ic_touch.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (125, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 5, 5, 'public/icon/ic_touch.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (126, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 1, 6,
        'public/icon/a2d56ebc708e7eef604f1f3f04db0a82.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (127, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 2, 6,
        'public/icon/a2d56ebc708e7eef604f1f3f04db0a82.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (128, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 3, 6,
        'public/icon/a2d56ebc708e7eef604f1f3f04db0a82.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (129, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 4, 6,
        'public/icon/a2d56ebc708e7eef604f1f3f04db0a82.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (130, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 5, 6,
        'public/icon/a2d56ebc708e7eef604f1f3f04db0a82.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (131, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 1, 7, 'public/icon/ic_orders.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (132, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 2, 7, 'public/icon/ic_orders.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (133, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 3, 7, 'public/icon/ic_orders.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (134, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 4, 7, 'public/icon/ic_orders.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (135, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 5, 7, 'public/icon/ic_orders.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (136, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 1, 8,
        'public/icon/45a90a650d06b0f5badb6a4461ce20ea.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (137, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 2, 8,
        'public/icon/45a90a650d06b0f5badb6a4461ce20ea.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (138, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 3, 8,
        'public/icon/45a90a650d06b0f5badb6a4461ce20ea.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (139, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 4, 8,
        'public/icon/45a90a650d06b0f5badb6a4461ce20ea.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (140, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 5, 8,
        'public/icon/45a90a650d06b0f5badb6a4461ce20ea.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (141, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 1, 9,
        'public/icon/0ae4b373679293131771a400da528a59.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (142, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 2, 9,
        'public/icon/0ae4b373679293131771a400da528a59.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (143, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 3, 9,
        'public/icon/0ae4b373679293131771a400da528a59.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (144, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 4, 9,
        'public/icon/0ae4b373679293131771a400da528a59.png', 1, 1);
INSERT INTO `digital_clone`.`img_config` (`id`, `created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES (145, '2024-06-11 21:53:25.956', '2024-06-11 21:53:25.956', NULL, 5, 9,
        'public/icon/0ae4b373679293131771a400da528a59.png', 1, 1);



ALTER TABLE `digital_clone`.`diy_photo_task`
    DROP COLUMN `level`,
    ADD COLUMN `diy_config_name` varchar(32) NULL COMMENT 'theme名称' AFTER `complete_time`;
```

## 2.2.0

```mysql

ALTER TABLE `digital_clone`.`diy_config`
    ADD COLUMN `mask_prefix` varchar(256) NULL COMMENT '蒙版地址前缀' AFTER `sort`,
    ADD COLUMN `mask_count`  bigint       NULL COMMENT '蒙版数量' AFTER `mask_prefix`;


ALTER TABLE `digital_clone`.`diy_photo_task`
    ADD COLUMN `diy_type`          bigint       NULL COMMENT '定制类型' AFTER `diy_config_name`,
    ADD COLUMN `face_img`  varchar(255) NULL COMMENT '上传图片地址' AFTER `diy_type`,
    ADD COLUMN `mask_imgs` json         NULL COMMENT '使用的蒙版图' AFTER `face_img`,
    ADD COLUMN `res_imgs`          json         NULL COMMENT '结果图片地址' AFTER `mask_imgs`,
    ADD COLUMN `remain_mask_count` bigint       NULL COMMENT '剩余蒙版图数量' AFTER `res_imgs`,
    ADD COLUMN `result_msg`        text         NULL COMMENT '生成结果' AFTER `remain_mask_count`,
    ADD COLUMN `diy_config_id`     bigint       NULL COMMENT '主题ID' AFTER `result_msg`;

ALTER TABLE `digital_clone`.`ai_role_img_info`
    ADD COLUMN `video_list` json NOT NULL COMMENT '视频列表' AFTER `undress_list`;

UPDATE `digital_clone`.`ai_role_img_info` SET `video_list` = '[]' WHERE deleted_at IS NULL;

ALTER TABLE `digital_clone`.`product`
    ADD COLUMN `origin_price` decimal(10, 2) NOT NULL DEFAULT 0 COMMENT '原价' AFTER `reward_amount`;

```

## 2.3.0

```mysql
create table voice_call
(
    id              bigint unsigned auto_increment
        primary key,
    created_at      datetime(3)     not null comment '创建时间',
    updated_at      datetime(3)     not null comment '更新时间',
    deleted_at      datetime(3)     null,
    app_id          bigint unsigned not null comment '应用ID',
    ai_id           bigint          not null comment '角色ID',
    ai_role_id      varchar(32)     not null comment 'AI角色ID',
    digital_user_id bigint unsigned not null comment '用户ID',
    min_price       bigint          not null comment '每分钟价格',
    call_minute     bigint          null comment '通话分钟数',
    total_price     bigint          null comment '总价'
);

create index idx_voice_call_ai_id
    on voice_call (ai_id);

create index idx_voice_call_ai_role_id
    on voice_call (ai_role_id);

create index idx_voice_call_deleted_at
    on voice_call (deleted_at);

create index idx_voice_call_digital_user_id
    on voice_call (digital_user_id);

INSERT INTO `digital_clone`.`product` (`created_at`, `updated_at`, `deleted_at`, `name`, `description`, `goo_id`, `good_type`, `product_type`, `usd_price`, `sort`, `buy_count`, `pay_count`, `app_id`, `ios_id`, `amount`, `reward_amount`, `origin_price`) VALUES ('2024-07-02 12:26:12.000000', '2024-07-02 14:37:07.170000', NULL, '2200Flowers', '20minutes', '', 1, 104, 19.99, 95, 0, 0, 2, '10008', 2200, 0, 19.99);
INSERT INTO `digital_clone`.`product` (`created_at`, `updated_at`, `deleted_at`, `name`, `description`, `goo_id`, `good_type`, `product_type`, `usd_price`, `sort`, `buy_count`, `pay_count`, `app_id`, `ios_id`, `amount`, `reward_amount`, `origin_price`) VALUES ('2024-07-02 12:26:12.000000', '2024-07-02 16:28:37.951000', NULL, '1050Flowers', '10minutes', '', 1, 104, 9.99, 94, 0, 0, 2, '10009', 1050, 0, 9.99);
INSERT INTO `digital_clone`.`product` (`created_at`, `updated_at`, `deleted_at`, `name`, `description`, `goo_id`, `good_type`, `product_type`, `usd_price`, `sort`, `buy_count`, `pay_count`, `app_id`, `ios_id`, `amount`, `reward_amount`, `origin_price`) VALUES ('2024-07-02 12:26:12.000000', '2024-07-02 10:51:10.452000', NULL, '500Flowers', '5minutes', '', 1, 104, 1.99, 93, 0, 0, 2, '10000', 500, 0, 1.99);

ALTER TABLE `digital_clone`.`product`
    ADD COLUMN `bonus` varchar(64) NOT NULL DEFAULT '' COMMENT '优惠' AFTER `origin_price`;

```

## 2.4.0

```mysql
-- ai_role表新增字段
ALTER TABLE `digital_clone`.`ai_role`
    ADD COLUMN `deep_flo` bigint NULL COMMENT 'DeepLink奖励鲜花数量' AFTER `app_id`;

-- SVip弹窗图片配置
INSERT INTO `digital_clone`.`img_config` (`created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES ('2024-07-16 00:00:00', '2024-07-16 00:00:00', NULL, 2, 11, 'public/video/8a831f4745d9fb96f3ab94142ea809e3.mp4',
        1, 1);
INSERT INTO `digital_clone`.`img_config` (`created_at`, `updated_at`, `deleted_at`, `app_id`, `img_config_type`,
                                          `media_url`, `config_status`, `user_status`)
VALUES ('2024-07-16 00:00:00', '2024-07-16 00:00:00', NULL, 2, 11, 'public/video/8a831f4745d9fb96f3ab94142ea809e3.mp4',
        1, 3);

-- SVip商品
INSERT INTO `digital_clone`.`product` (`created_at`, `updated_at`, `deleted_at`, `name`, `description`, `goo_id`,
                                       `good_type`, `product_type`, `usd_price`, `sort`, `buy_count`, `pay_count`,
                                       `app_id`, `ios_id`, `amount`, `reward_amount`, `origin_price`, `bonus`)
VALUES ('2024-07-16 00:00:00', '2024-07-16 00:00:00', NULL, 'SVip', '', '', 1, 106, 99.99, 99, 0, 0, 2, '20001', 1, 0,
        99.99, '');

```

## 3.0.0

```mysql
-- 用户表新增birthday字段
ALTER TABLE `digital_clone`.`digital_user`
    ADD COLUMN `birthday`    date   NULL COMMENT '生日' AFTER `gender`,
    ADD COLUMN `like_gender` json   NULL COMMENT '喜欢的性别' AFTER `birthday`,
    ADD COLUMN `age_range`   bigint NULL COMMENT '年龄段' AFTER `like_gender`;

```

## 3.2.0

```json
{
  "MASK_MSG_PER_FLOWER_SVIP": 0,
  "MASK_PHOTO_FLOWER_SVIP": 0,
  "SVIP_ASMR_PER_FLOWER": 15,
  "MIN_PRICE_SUB": 100,
  "MIN_PRICE_SVIP": 50,
  "SVIP_WATCH_VIDEO_NUM": 0,
  "SVIP_GEN_VIDEO_PER_FLOWER": 15,
  "SVIP_WEEK_SEND_FLO": 2000,
  "SVIP_MONTH_SEND_FLO": 5000,
  "SVIP_YEAR_SEND_FLO": 10000
}
```
    
```mysql

ALTER TABLE `digital_clone`.`ai_role` 
ADD COLUMN `enable` smallint NULL DEFAULT 1 COMMENT '启用状态' AFTER `deleted_at`;

UPDATE ai_role SET `enable` = 2 WHERE deleted_at IS NOT NULL

```

## 修改

```mysql
ALTER TABLE `digital_clone`.`ai_role`
    ADD COLUMN `minimax` smallint NULL DEFAULT 0 COMMENT '是否minimax角色' AFTER `deep_flo`;
```

## 双订阅

```mysql
ALTER TABLE `digital_clone`.`app`
    ADD COLUMN `vip_mode` smallint NOT NULL DEFAULT 1 COMMENT '会员类型' AFTER `status`;
ALTER TABLE `digital_clone`.`digital_user`
    ADD COLUMN `chat_vip_status`    bigint      NULL DEFAULT NULL COMMENT '畅聊Vip状态' AFTER `svip_type`,
    ADD COLUMN `chat_vip_expire`    datetime(6) NULL DEFAULT NULL COMMENT '畅聊Vip到期时间' AFTER `chat_vip_status`,
    ADD COLUMN `chat_vip_type`      smallint    NULL DEFAULT NULL COMMENT '畅聊Vip订阅类型' AFTER `chat_vip_expire`,
    ADD COLUMN `content_vip_status` bigint      NULL DEFAULT NULL COMMENT '内容Vip状态' AFTER `chat_vip_type`,
    ADD COLUMN `content_vip_expire` datetime(6) NULL DEFAULT NULL COMMENT '内容Vip到期时间' AFTER `content_vip_status`,
    ADD COLUMN `content_vip_type`   smallint    NULL DEFAULT NULL COMMENT '内容Vip订阅类型' AFTER `content_vip_expire`;
```

## AI转真人

```mysql
ALTER TABLE `digital_clone`.`app`
    ADD COLUMN `vita_app` json NULL COMMENT '社交APP参数' AFTER `smtp_config`;

ALTER TABLE `digital_clone`.`ai_role`
    ADD COLUMN `real_anchor_id` bigint   NULL COMMENT '真人主播ID' AFTER `minimax`,
    ADD COLUMN `online_status`  smallint NULL COMMENT '真人主播在线状态' AFTER `real_anchor_id`,
    ADD INDEX `online_status_index` (`online_status` ASC) USING BTREE COMMENT '真人主播在线状态索引',
    ADD INDEX `real_anchor_id_index` (`real_anchor_id` ASC) USING BTREE COMMENT '真人主播ID索引';

```