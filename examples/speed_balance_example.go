package main

import (
	"aimsg-server/config"
	"aimsg-server/global"
	"aimsg-server/service/backend"
	"aimsg-server/utils"
	"fmt"
	"log"
)

func main() {
	// 示例：如何使用 Speed API 余额监控功能
	
	// 1. 设置配置（在实际应用中，这些配置应该从 config.yaml 读取）
	global.CONFIG.SpeedAPI = config.SpeedAPI{
		Enabled:       true,
		BaseURL:       "https://api.tryspeed.com",
		Authorization: "Basic c2tfdGVzdF9tYmthamk2bVhGTjV1Q29IbWJrYWptbnZtZ25rNWF2YW1ia2FqbW53S2Ztc3pDRlY6",
		Threshold:     100000, // 10万 SATS
		FeiShuBotID:   "your-feishu-bot-id-here",
	}

	fmt.Println("=== Speed API 余额监控示例 ===")

	// 2. 直接使用 Speed API 客户端
	fmt.Println("\n1. 直接调用 Speed API:")
	client := utils.NewSpeedAPIClient(
		global.CONFIG.SpeedAPI.BaseURL,
		global.CONFIG.SpeedAPI.Authorization,
	)

	balance, err := client.GetSATSBalance()
	if err != nil {
		log.Printf("获取余额失败: %v", err)
	} else {
		fmt.Printf("当前 SATS 余额: %d\n", balance)
	}

	// 3. 使用监控服务
	fmt.Println("\n2. 使用余额监控服务:")
	monitor := &backend.SpeedBalanceMonitorService{}

	// 获取余额状态
	status, err := monitor.GetBalanceStatus()
	if err != nil {
		log.Printf("获取余额状态失败: %v", err)
	} else {
		fmt.Printf("余额状态: %+v\n", status)
	}

	// 执行余额检查（如果余额低于阈值会发送通知）
	fmt.Println("\n3. 执行余额检查:")
	err = monitor.CheckBalance()
	if err != nil {
		log.Printf("余额检查失败: %v", err)
	} else {
		fmt.Println("余额检查完成")
	}

	fmt.Println("\n=== 示例完成 ===")
	fmt.Println("\n注意事项:")
	fmt.Println("1. 请确保 Speed API 授权信息正确")
	fmt.Println("2. 请配置正确的飞书 Bot ID 以接收通知")
	fmt.Println("3. 在生产环境中，配置信息应该从 config.yaml 文件读取")
}
