build: go.mod
	go mod tidy
	go mod download
	go build -o aimsg-server

build-payment: go.mod
	go mod tidy
	go mod download
	GOOS=linux GOARCH=amd64 go build -o aimsg-payment cmd/payment/main.go

install: aimsg-server.service config.pro.yaml
	mkdir /etc/aimsg-server
	mkdir /var/log/aimsg-server
	cp ./aimsg-server.service /lib/systemd/system/
	cp ./config.pro.yaml /etc/aimsg-server/config.pro.yaml
	systemctl daemon-reload

run: aimsg-server
	systemctl stop aimsg-server.service
	cp aimsg-server /usr/local/sbin
	systemctl start aimsg-server.service
