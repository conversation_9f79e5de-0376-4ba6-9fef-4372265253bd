package elevenlabs

import (
	"bytes"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"strings"
)

const (
	textToSpeechUrl = "https://api.elevenlabs.io/v1/text-to-speech/%s?output_format=mp3_44100_128"
	// 配置 API 参数
	apiURL = "https://api.minimax.chat/v1/t2a_v2?GroupId=1827907340364431395"
	apiKey = "***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
)

func TextToSpeech(text, voiceId string, stability, similarityBoost, style float64, xiApiKey string) (apiResBytes []byte, err error) {
	var (
		apiRes      *http.Response
		apiReq      *http.Request
		reqMapBytes []byte
		reqUrl      = fmt.Sprintf(textToSpeechUrl, voiceId)
	)
	reqMap := map[string]interface{}{
		"text":     text,
		"model_id": "eleven_multilingual_v2",
		"voice_settings": map[string]interface{}{
			"stability":        stability,
			"similarity_boost": similarityBoost,
			"style":            style,
		},
	}
	if reqMapBytes, err = json.Marshal(reqMap); err != nil {
		return
	}
	if apiReq, err = http.NewRequest(http.MethodPost, reqUrl, bytes.NewReader(reqMapBytes)); err != nil {
		return
	}
	apiReq.Header.Add("xi-api-key", xiApiKey)
	apiReq.Header.Add("Content-Type", "application/json")
	if apiRes, err = http.DefaultClient.Do(apiReq); err != nil {
		return
	}
	defer apiRes.Body.Close()
	if apiResBytes, err = io.ReadAll(apiRes.Body); err != nil {
		return
	}
	if apiRes.StatusCode != http.StatusOK {
		err = fmt.Errorf("status != 200")
	}
	return
}

// MinMaxRes 时间太紧了 先写在这里了啊 文瑞
type MinMaxRes struct {
	Data struct {
		Audio  string `json:"audio"`
		Status int    `json:"status"`
		Ced    string `json:"ced"`
	} `json:"data"`
	ExtraInfo struct {
		AudioLength             int    `json:"audio_length"`
		AudioSampleRate         int    `json:"audio_sample_rate"`
		AudioSize               int    `json:"audio_size"`
		Bitrate                 int    `json:"bitrate"`
		WordCount               int    `json:"word_count"`
		InvisibleCharacterRatio int    `json:"invisible_character_ratio"`
		UsageCharacters         int    `json:"usage_characters"`
		AudioFormat             string `json:"audio_format"`
		AudioChannel            int    `json:"audio_channel"`
	} `json:"extra_info"`
	TraceId  string `json:"trace_id"`
	BaseResp struct {
		StatusCode int    `json:"status_code"`
		StatusMsg  string `json:"status_msg"`
	} `json:"base_resp"`
}

// TextToSpeechWithMinMax 1_03军锋需求修改文字转语音
func TextToSpeechWithMinMax(text, voiceId string, vol float64, pitch int, speed float64) (apiResBytes []byte, err error) {
	var minMaxRes MinMaxRes
	// 将 voiceId 转换为 timber_weights 的结构
	var timberWeights []map[string]interface{}
	voiceIds := strings.Split(voiceId, ",") // 按逗号分隔字符串
	for _, id := range voiceIds {
		timberWeights = append(timberWeights, map[string]interface{}{
			"voice_id": id,
			"weight":   1, // 默认权重
		})
	}
	// 构造请求数据
	requestBody := map[string]interface{}{
		"model":          "speech-02-turbo",
		"text":           text,
		"stream":         false,
		"timber_weights": timberWeights,
		"voice_setting": map[string]interface{}{
			"voice_id": "",
			"speed":    speed,
			"vol":      vol,
			"pitch":    pitch,
			"emotion":  "happy",
		},
		"audio_setting": map[string]interface{}{
			"sample_rate": 32000,
			"bitrate":     128000,
			"format":      "mp3",
			"channel":     1,
		},
	}
	// 将请求数据序列化为 JSON
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return
	}

	// 创建 HTTP 请求
	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return
	}

	// 设置请求头
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiKey))
	req.Header.Set("Content-Type", "application/json")

	// 执行 HTTP 请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return
	}

	err = json.Unmarshal(body, &minMaxRes)
	if err != nil {
		return
	}
	// 将 hex 编码的字符串转换为 []byte
	audioData, err := hex.DecodeString(minMaxRes.Data.Audio)
	if err != nil {
		return
	}
	return audioData, nil
}
