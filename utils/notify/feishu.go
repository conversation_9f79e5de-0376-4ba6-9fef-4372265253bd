package notify

import (
	"aimsg-server/utils"
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"golang.org/x/exp/rand"
)

const FeishuWebhookURL = "https://open.feishu.cn/open-apis/bot/v2/hook/%s"

// TextMessage 结构体
type TextMessage struct {
	MsgType string `json:"msg_type"`
	Content struct {
		Text string `json:"text"`
	} `json:"content"`
	Timestamp string `json:"timestamp"`
	Sign      string `json:"sign"`
}

type PostContent struct {
	MsgType string `json:"msg_type"`
	Content struct {
		Post struct {
			ZhCn struct {
				Title   string           `json:"title"`
				Content [][]ContentItems `json:"content"`
			} `json:"zh_cn"`
		} `json:"post"`
	} `json:"content"`
}

type ContentItems struct {
	Tag    string `json:"tag"`
	Text   string `json:"text,omitempty"`
	Href   string `json:"href,omitempty"`
	UserID string `json:"user_id,omitempty"`
}

type InteractiveMessage struct {
	MsgType string `json:"msg_type"`
	Card    struct {
		Schema string `json:"schema"`
		Config struct {
			UpdateMulti bool `json:"update_multi"`
			Style       struct {
				TextSize struct {
					NormalV2 struct {
						Default string `json:"default"`
						PC      string `json:"pc"`
						Mobile  string `json:"mobile"`
					} `json:"normal_v2"`
				} `json:"text_size"`
			} `json:"style"`
		} `json:"config"`
		Body struct {
			Direction string        `json:"direction"`
			Padding   string        `json:"padding"`
			Elements  []CardElement `json:"elements"`
		} `json:"body"`
		Header struct {
			Title struct {
				Tag     string `json:"tag"`
				Content string `json:"content"`
			} `json:"title"`
			Subtitle struct {
				Tag     string `json:"tag"`
				Content string `json:"content"`
			} `json:"subtitle"`
			Template string `json:"template"`
			Padding  string `json:"padding"`
		} `json:"header"`
	} `json:"card"`
}

type Button struct {
	Tag  string `json:"tag"`
	Text struct {
		Tag     string `json:"tag"`
		Content string `json:"content"`
	} `json:"text"`
	Type      string `json:"type"`
	Width     string `json:"width"`
	Size      string `json:"size"`
	Behaviors []struct {
		Type       string `json:"type"`
		DefaultURL string `json:"default_url"`
		PcURL      string `json:"pc_url"`
		IosURL     string `json:"ios_url"`
		AndroidURL string `json:"android_url"`
	} `json:"behaviors"`
	Margin string `json:"margin"`
}

type CardElement struct {
	Tag       string `json:"tag"`
	Content   string `json:"content,omitempty"`
	TextAlign string `json:"text_align,omitempty"`
	TextSize  string `json:"text_size,omitempty"`
	Margin    string `json:"margin,omitempty"`
	Behaviors []struct {
		Type       string `json:"type"`
		DefaultURL string `json:"default_url,omitempty"`
		PcURL      string `json:"pc_url,omitempty"`
		IosURL     string `json:"ios_url,omitempty"`
		AndroidURL string `json:"android_url,omitempty"`
	} `json:"behaviors,omitempty"`
}

type FeiShuNotifyResp struct {
	StatusCode    int         `json:"StatusCode"`
	StatusMessage string      `json:"StatusMessage"`
	Code          int         `json:"code"`
	Data          interface{} `json:"data"`
	Msg           string      `json:"msg"`
}

func GenSign(secret string, timestamp int64) (string, error) {
	stringToSign := fmt.Sprintf("%v", timestamp) + "\n" + secret
	var data []byte
	h := hmac.New(sha256.New, []byte(stringToSign))
	_, err := h.Write(data)
	if err != nil {
		return "", err
	}
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))
	return signature, nil
}

// SendFeiShuText 发送飞书文本消息
func SendFeiShuText(message, hook, secret string) (err error) {
	var (
		sign     string
		jsonData []byte
		apiResp  *http.Response
	)
	timeStamp := time.Now().Unix()
	if sign, err = GenSign(secret, timeStamp); err != nil {
		return
	}
	msg := TextMessage{
		MsgType:   "text",
		Timestamp: strconv.FormatInt(timeStamp, 10),
		Sign:      sign,
	}
	msg.Content.Text = message
	if jsonData, err = json.Marshal(msg); err != nil {
		return
	}
	reqUrl := fmt.Sprintf(FeishuWebhookURL, hook)
	if apiResp, err = http.Post(reqUrl, "application/json", bytes.NewBuffer(jsonData)); err != nil {
		return
	}
	defer apiResp.Body.Close()
	if apiResp.StatusCode != http.StatusOK {
		err = fmt.Errorf("request error, StatusCode:%d", apiResp.StatusCode)
		return
	}
	if apiRespBytes, readAllErr := io.ReadAll(apiResp.Body); readAllErr != nil {
		err = readAllErr
		return
	} else {
		fmt.Println(string(apiRespBytes))
	}
	return
}

func SendFeiShuTalkV2(botId, sendText, title string) (string, error) {

	var content InteractiveMessage
	content.MsgType = "interactive"
	content.Card.Schema = "2.0"
	content.Card.Config.UpdateMulti = true
	content.Card.Config.Style.TextSize.NormalV2.Default = "normal"
	content.Card.Config.Style.TextSize.NormalV2.PC = "normal"
	content.Card.Config.Style.TextSize.NormalV2.Mobile = "heading"
	content.Card.Body.Direction = "vertical"
	content.Card.Body.Padding = "12px 12px 12px 12px"
	content.Card.Body.Elements = []CardElement{
		{
			Tag:       "markdown",
			Content:   sendText,
			TextAlign: "left",
			TextSize:  "normal_v2",
			Margin:    "0px 0px 0px 0px",
		},
	}
	content.Card.Header.Title.Tag = "plain_text"
	content.Card.Header.Title.Content = title
	content.Card.Header.Template = "red"
	content.Card.Header.Padding = "12px 12px 12px 12px"

	reqUrl := fmt.Sprintf(FeishuWebhookURL, botId)
	payload, err := json.Marshal(content)
	if err != nil {
		return "", err
	}
	req, _ := http.NewRequest("POST", reqUrl, bytes.NewReader(payload))

	req.Header.Add("Content-Type", "application/json")

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return "", err
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return "", err
	}
	return string(body), err
}

func SendFeiShuTalkV2WithRetry(botId, sendText, title string) error {
	str, err := SendFeiShuTalkV2(botId, sendText, title)
	if err != nil {
		return err
	}

	resp := &FeiShuNotifyResp{}
	err = json.Unmarshal([]byte(str), resp)
	if err != nil {
		return err
	}
	if resp.Code == 0 {
		return nil
	}

	time.AfterFunc(time.Duration(rand.Int31n(10)+1)*time.Second, func() {
		defer utils.Recover()
		SendFeiShuTalkV2(botId, sendText, title)
	})
	return nil
}
