package notify

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"
)

const FeishuWebhookURL = "https://open.feishu.cn/open-apis/bot/v2/hook/%s"

// TextMessage 结构体
type TextMessage struct {
	MsgType string `json:"msg_type"`
	Content struct {
		Text string `json:"text"`
	} `json:"content"`
	Timestamp string `json:"timestamp"`
	Sign      string `json:"sign"`
}

func GenSign(secret string, timestamp int64) (string, error) {
	stringToSign := fmt.Sprintf("%v", timestamp) + "\n" + secret
	var data []byte
	h := hmac.New(sha256.New, []byte(stringToSign))
	_, err := h.Write(data)
	if err != nil {
		return "", err
	}
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))
	return signature, nil
}

// SendFeiShuText 发送飞书文本消息
func SendFeiShuText(message, hook, secret string) (err error) {
	var (
		sign     string
		jsonData []byte
		apiResp  *http.Response
	)
	timeStamp := time.Now().Unix()
	if sign, err = GenSign(secret, timeStamp); err != nil {
		return
	}
	msg := TextMessage{
		MsgType:   "text",
		Timestamp: strconv.FormatInt(timeStamp, 10),
		Sign:      sign,
	}
	msg.Content.Text = message
	if jsonData, err = json.Marshal(msg); err != nil {
		return
	}
	reqUrl := fmt.Sprintf(FeishuWebhookURL, hook)
	if apiResp, err = http.Post(reqUrl, "application/json", bytes.NewBuffer(jsonData)); err != nil {
		return
	}
	defer apiResp.Body.Close()
	if apiResp.StatusCode != http.StatusOK {
		err = fmt.Errorf("request error, StatusCode:%d", apiResp.StatusCode)
		return
	}
	if apiRespBytes, readAllErr := io.ReadAll(apiResp.Body); readAllErr != nil {
		err = readAllErr
		return
	} else {
		fmt.Println(string(apiRespBytes))
	}
	return
}
