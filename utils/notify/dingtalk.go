package notify

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
)

const DiySecret = "ydAnlUx7kdiL5E5OmPmJ5g"
const DiyHook = "7d3a49da-4006-4414-98c9-f5b8689bf778"
const AuditSecret = "sNaEEtboYOGwobTUTdph5b"

// const AuditHook = "3e2d34ca-4546-4760-b5b5-5d5870d254c0"
const AuditHook = "5a2a2198-18c7-469c-b7b1-e30837316554"
const TestSecret = "BHkjeYBcKNxCkfxmkb6yud"
const TestHook = "328b4476-46d9-4f99-b295-141f91d5db38"
const TestToken = "b79580c71f6218e15c17074a831526fad8dda07bec89ea3d9bf6948cd77629e9"
const DiyDingToken = "a2a5d336a2478284b407c23a85fbb1ae25e0722bdd5f6dfac23a065967b4c785"
const AuditDingToken = "84a42f1975117d9d36c0c9214171ab01f4ae185a532f8e23395ff1dabeb3dfde"

type Message struct {
	Msgtype  string `json:"msgtype"`
	Markdown struct {
		Title string `json:"title"`
		Text  string `json:"text"`
	} `json:"markdown"`
	At struct {
		AtMobiles []string `json:"atMobiles"`
		IsAtAll   bool     `json:"isAtAll"`
	} `json:"at"`
}

// SendDingTalk 钉钉消息通知
func SendDingTalk(message Message, token string, secret string) (string, error) {
	// 增加飞书通知
	str := fmt.Sprintf("%s\n%s", message.Markdown.Title, message.Markdown.Text)
	hook := ""
	feiShuSecret := ""
	if token == TestToken {
		hook = TestHook
		feiShuSecret = TestSecret
	}
	if token == DiyDingToken {
		hook = DiyHook
		feiShuSecret = DiySecret
	}
	if token == AuditDingToken {
		hook = AuditHook
		feiShuSecret = AuditSecret
	}
	if hook != "" {
		err := SendFeiShuText(str, hook, feiShuSecret)
		if err != nil {
			return "", err
		}
	}
	return "", nil
}

func sign(timestamp, secret string) string {
	strToHash := fmt.Sprintf("%s\n%s", timestamp, secret)
	hmac256 := hmac.New(sha256.New, []byte(secret))
	hmac256.Write([]byte(strToHash))
	data := hmac256.Sum(nil)
	return base64.StdEncoding.EncodeToString(data)
}
