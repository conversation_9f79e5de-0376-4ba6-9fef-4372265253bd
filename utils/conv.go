package utils

import "fmt"

// Uint converts `any` to uint.
func Uint(any interface{}) uint {
	if any == nil {
		return 0
	}
	if v, ok := any.(uint); ok {
		return v
	}
	return uint(Uint64(any))
}

// Uint64 converts `any` to uint64.
func Uint64(any interface{}) uint64 {
	if any == nil {
		return 0
	}
	switch value := any.(type) {
	case int:
		return uint64(value)
	case int8:
		return uint64(value)
	case int16:
		return uint64(value)
	case int32:
		return uint64(value)
	case int64:
		return uint64(value)
	case uint:
		return uint64(value)
	case uint8:
		return uint64(value)
	case uint16:
		return uint64(value)
	case uint32:
		return uint64(value)
	case uint64:
		return value
	case float32:
		return uint64(value)
	case float64:
		return uint64(value)
	case bool:
		if value {
			return 1
		}
		return 0
	default:
		return 0
	}
}

func FormatNumber(num int) string {
	var result string
	if num >= 1000000 {
		result = fmt.Sprintf("%.1fm", float64(num)/1000000.0)
	} else if num >= 1000 {
		result = fmt.Sprintf("%.1fk", float64(num)/1000.0)
	} else {
		result = fmt.Sprintf("%d", num)
	}
	return result
}

func FormatNumberV2(num int) string {
	var result string
	// 如果num小于1000000,就格式化成%d k
	// 如果num大于等于1000000,就格式化成%d m
	if num >= 1000000 {
		result = fmt.Sprintf("%dm", num/1000000)
	} else {
		result = fmt.Sprintf("%dk", num/1000)
	}
	return result
}
