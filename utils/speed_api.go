package utils

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// SpeedBalanceResponse Speed API 余额响应结构
type SpeedBalanceResponse struct {
	Object    string `json:"object"`
	Available []struct {
		Amount         int64  `json:"amount"`
		TargetCurrency string `json:"target_currency"`
	} `json:"available"`
}

// SpeedAPIClient Speed API 客户端
type SpeedAPIClient struct {
	BaseURL       string
	Authorization string
	HTTPClient    *http.Client
}

// NewSpeedAPIClient 创建新的 Speed API 客户端
func NewSpeedAPIClient(baseURL, authorization string) *SpeedAPIClient {
	return &SpeedAPIClient{
		BaseURL:       baseURL,
		Authorization: authorization,
		HTTPClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// GetBalance 获取余额信息
func (c *SpeedAPIClient) GetBalance() (*SpeedBalanceResponse, error) {
	url := fmt.Sprintf("%s/balances", c.BaseURL)
	
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}
	
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Authorization", c.Authorization)
	
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API 请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}
	
	var balanceResp SpeedBalanceResponse
	if err := json.Unmarshal(body, &balanceResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}
	
	return &balanceResp, nil
}

// GetSATSBalance 获取 SATS 余额
func (c *SpeedAPIClient) GetSATSBalance() (int64, error) {
	balance, err := c.GetBalance()
	if err != nil {
		return 0, err
	}
	
	for _, item := range balance.Available {
		if item.TargetCurrency == "SATS" {
			return item.Amount, nil
		}
	}
	
	return 0, fmt.Errorf("未找到 SATS 余额")
}
