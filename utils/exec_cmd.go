package utils

import (
	"bytes"
	"context"
	"fmt"
	"os/exec"
	"strings"
	"time"
)

// ExecuteCommand 执行给定的 shell 命令并返回输出或错误信息
func ExecuteCommand(command string, args ...string) (string, error) {
	// 输出即将执行的命令及其参数
	fmt.Printf("Executing command: %s %s\n", command, strings.Join(args, " "))

	// 定义要执行的命令及其参数
	cmd := exec.Command(command, args...)

	// 创建一个缓冲区来存储命令的输出
	var out bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &out

	// 执行命令并等待其完成
	if err := cmd.Run(); err != nil {
		return "", fmt.Errorf("command execution failed: %w, output: %s", err, out.String())
	}

	// 返回命令输出
	return out.String(), nil
}

// ExecuteCommand 执行给定的 shell 命令并返回输出或错误信息
// 超时参数 timeout 用于设定命令的最长执行时间
func ExecuteCommandWithTimeout(timeout time.Duration, command string, args ...string) (string, error) {
	// 输出即将执行的命令及其参数
	_execCmd := fmt.Sprintf("%s %s", command, strings.Join(args, " "))
	fmt.Printf("Executing command: %s\n", _execCmd)

	// 创建一个带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 定义要执行的命令及其参数
	cmd := exec.CommandContext(ctx, command, args...)

	// 创建一个缓冲区来存储命令的输出
	var out bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &out

	// 启动命令
	if err := cmd.Start(); err != nil {
		return "", fmt.Errorf("command start failed: %w, execCmd: %s", err, _execCmd)
	}

	// 等待命令完成
	if err := cmd.Wait(); err != nil {
		// 检查是否因超时而失败
		if ctx.Err() == context.DeadlineExceeded {
			return "", fmt.Errorf("command timed out, execCmd: %s", _execCmd)
		}
		return "", fmt.Errorf("command execution failed: %w, output: %s, execCmd: %s", err, out.String(), _execCmd)
	}

	// 返回命令输出
	return out.String(), nil
}
