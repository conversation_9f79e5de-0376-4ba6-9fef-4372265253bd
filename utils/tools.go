package utils

import (
	"math/rand"
	"strconv"
	"time"
)

func RemoveElement(arr []string, value string) []string {
	result := make([]string, 0)

	for _, v := range arr {
		if v != value {
			result = append(result, v)
		}
	}

	return result
}

func DiffList(arr1, arr2 []string) []string {
	result := make([]string, 0)
	arr2Map := make(map[string]bool, 0)

	for _, str := range arr2 {
		arr2Map[str] = true
	}

	for _, str := range arr1 {
		if _, exists := arr2Map[str]; !exists {
			result = append(result, str)
		}
	}
	return result
}

func Shuffle(arr []int) []int {
	rander := rand.New(rand.NewSource(time.Now().UnixNano()))
	rander.Shuffle(len(arr), func(i, j int) {
		arr[i], arr[j] = arr[j], arr[i]
	})
	return arr
}

func BoolToString(val bool) string {
	return strconv.FormatBool(val)
}

func BoolToIntString(val bool) string {
	if val {
		return "1"
	} else {
		return "0"
	}
}

func StringToBool(str string) bool {
	boolVal, _ := strconv.ParseBool(str)
	return boolVal
}
