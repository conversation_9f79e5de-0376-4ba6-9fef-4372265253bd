package utils

import (
	"aimsg-server/global"
	"fmt"
	"net/url"
	"strings"
)

const (
	s3Domain = "https://images.aipersona.cloud/"
)

func S3Url(s3File string, appId uint) string {
	if s3File == "" {
		return ""
	}
	if strings.HasPrefix(s3File, "http") {
		return s3File
	}
	s3Prefix := s3Domain
	appInfo, appExist := global.AppMap[appId]
	if appExist {
		s3Prefix = appInfo.S3ImgPrefix
	}
	return fmt.Sprintf("%s%s", s3Prefix, s3File)
}

func PubS3Url(s3FullUrl string) (pubUrl string) {
	// 解析URL
	parsedUrl, err := url.Parse(s3FullUrl)
	if err != nil {
		// 解析失败，返回空字符串
		return ""
	}

	// 获取路径部分
	path := parsedUrl.Path

	// 如果路径是以 "/public" 开头，则去掉前面的斜杠
	if strings.HasPrefix(path, "/public") {
		path = strings.TrimPrefix(path, "/")
	}

	return path
}

func ReplaceS3Domain(fullUrl, s3PrefixUrl string) string {
	return strings.Replace(fullUrl, s3PrefixUrl, "", 1)
}
