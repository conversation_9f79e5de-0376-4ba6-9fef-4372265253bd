package utils

import (
	"fmt"
	"net/smtp"
	"strings"
)

func ImagesToHTML(images []string, prefix string) string {
	var htmlContent strings.Builder
	for _, img := range images {
		src := prefix + img
		htmlContent.WriteString(fmt.Sprintf(`<p><img src="%s" alt="Image"></p>`, src))
	}
	return htmlContent.String()
}

func SendEmailHtml(smtpHost string, smtpPort int, from, password, to, subject, body string) error {
	// 构建邮件内容
	message := fmt.Sprintf("From: %s\r\n", from)
	message += fmt.Sprintf("To: %s\r\n", to)
	message += fmt.Sprintf("Subject: %s\r\n", subject)
	message += "MIME-version: 1.0;\nContent-Type: text/html; charset=\"UTF-8\";\n\n"
	message += "\r\n" + body

	// 配置SMTP客户端
	auth := smtp.PlainAuth("", from, password, smtpHost)

	// 发送邮件
	err := smtp.SendMail(smtpHost+":"+fmt.Sprint(smtpPort), auth, from, []string{to}, []byte(message))
	if err != nil {
		return fmt.Errorf("error sending mail: %v", err)
	}

	return nil
}
