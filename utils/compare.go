package utils

import (
	"strconv"
	"strings"
)

// CompareVersions 比较两个版本号字符串的大小 (version1 > version2 返回 1, version1 < version2 返回 -1, 否则返回 0)
func CompareVersions(version1, version2 string) int {
	// 将版本号字符串拆分成整数列表
	v1 := splitAndConvert(version1)
	v2 := splitAndConvert(version2)

	// 补齐版本号的长度，使其长度相同
	for len(v1) < len(v2) {
		v1 = append(v1, 0)
	}
	for len(v2) < len(v1) {
		v2 = append(v2, 0)
	}

	// 逐个比较版本号的各个部分
	for i := 0; i < len(v1); i++ {
		if v1[i] < v2[i] {
			return -1
		} else if v1[i] > v2[i] {
			return 1
		}
	}

	return 0 // 两个版本号相等
}

// splitAndConvert 将版本号字符串拆分并转换为整数列表
func splitAndConvert(version string) []int {
	parts := strings.Split(version, ".")
	var result []int
	for _, part := range parts {
		num, _ := strconv.Atoi(part)
		result = append(result, num)
	}
	return result
}
