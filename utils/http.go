package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

var defaultClient = &http.Client{
	Timeout: 10 * time.Second,
	Transport: &http.Transport{
		MaxIdleConns:    10,
		IdleConnTimeout: 30 * time.Second,
	},
}

func getClient(timeout int) *http.Client {
	if timeout > 0 {
		return &http.Client{Timeout: time.Duration(timeout) * time.Second}
	}
	return defaultClient
}

func DoReq(apiReq *http.Request, resObj interface{}, timeOutSecond int) (logStr string, curlStr string, err error) {
	var (
		apiResponse      *http.Response
		dataStr          string
		headerBytes      []byte
		headerStr        string
		apiResponseBytes []byte
		apiResponseStr   string
	)
	var raw []byte
	switch apiReq.Method {
	case http.MethodGet:
		raw = []byte{}
	case http.MethodPost:
		raw, _ = io.ReadAll(apiReq.Body)
		apiReq.Body = io.NopCloser(bytes.NewBuffer(raw))
	default:
		raw, _ = io.ReadAll(apiReq.Body)
		apiReq.Body = io.NopCloser(bytes.NewBuffer(raw))
	}
	dataStr = string(raw)

	// 构造 Curl 命令字符串
	curlStr = fmt.Sprintf("curl -X %s '%s'", apiReq.Method, apiReq.URL.String())
	for k, v := range apiReq.Header {
		curlStr += fmt.Sprintf(" -H '%s: %s'", k, strings.Join(v, ","))
	}
	if len(dataStr) > 0 {
		curlStr += fmt.Sprintf(" -d '%s'", dataStr)
	}

	headerBytes, _ = json.Marshal(apiReq.Header)
	headerStr = string(headerBytes)

	logStr = fmt.Sprintf("请求地址%s\n请求方法%s\n请求头%s\n请求数据%s", apiReq.URL.String(), apiReq.Method, headerStr, dataStr)
	if timeOutSecond <= 0 {
		timeOutSecond = 10
	}
	var hClient = getClient(timeOutSecond)
	if apiResponse, err = hClient.Do(apiReq); err != nil {
		logStr = fmt.Sprintf("%s\n错误信息%s", logStr, err.Error())
		return
	}
	defer func(Body io.ReadCloser) {
		if cloErr := Body.Close(); cloErr != nil {
			fmt.Println(cloErr.Error())
		}
	}(apiResponse.Body)
	logStr = fmt.Sprintf("%s\n响应码%d", logStr, apiResponse.StatusCode)
	if apiResponseBytes, err = io.ReadAll(apiResponse.Body); err != nil {
		return
	}
	apiResponseStr = string(apiResponseBytes)
	logStr = fmt.Sprintf("%s\n响应信息%s", logStr, apiResponseStr)
	if apiResponse.StatusCode != 200 {
		err = fmt.Errorf("RequestFail, StatusCode != 200")
		return
	}
	if err = json.Unmarshal(apiResponseBytes, &resObj); err != nil {
		err = fmt.Errorf("json Unmarshal error is %v", err.Error())
		return
	}
	return
}

func DoReqReturnHeader(apiReq *http.Request, resObj interface{}, timeOutSecond int) (logStr string, curlStr string, apiResponseHeader http.Header, err error) {
	var (
		apiResponse      *http.Response
		dataStr          string
		headerBytes      []byte
		headerStr        string
		apiResponseBytes []byte
		apiResponseStr   string
	)
	var raw []byte
	switch apiReq.Method {
	case http.MethodGet:
		raw = []byte{}
	case http.MethodPost:
		raw, _ = io.ReadAll(apiReq.Body)
		apiReq.Body = io.NopCloser(bytes.NewBuffer(raw))
	default:
		raw, _ = io.ReadAll(apiReq.Body)
		apiReq.Body = io.NopCloser(bytes.NewBuffer(raw))
	}
	dataStr = string(raw)

	// 构造 Curl 命令字符串
	curlStr = fmt.Sprintf("curl -X %s '%s'", apiReq.Method, apiReq.URL.String())
	for k, v := range apiReq.Header {
		curlStr += fmt.Sprintf(" -H '%s: %s'", k, strings.Join(v, ","))
	}
	if len(dataStr) > 0 {
		curlStr += fmt.Sprintf(" -d '%s'", dataStr)
	}

	headerBytes, _ = json.Marshal(apiReq.Header)
	headerStr = string(headerBytes)

	logStr = fmt.Sprintf("请求地址%s\n请求方法%s\n请求头%s\n请求数据%s", apiReq.URL.String(), apiReq.Method, headerStr, dataStr)
	if timeOutSecond <= 0 {
		timeOutSecond = 10
	}
	var hClient = getClient(timeOutSecond)
	if apiResponse, err = hClient.Do(apiReq); err != nil {
		logStr = fmt.Sprintf("%s\n错误信息%s", logStr, err.Error())
		return
	}
	defer func(Body io.ReadCloser) {
		if cloErr := Body.Close(); cloErr != nil {
			fmt.Println(cloErr.Error())
		}
	}(apiResponse.Body)
	apiResponseHeader = apiResponse.Header
	logStr = fmt.Sprintf("%s\n响应码%d", logStr, apiResponse.StatusCode)
	if apiResponseBytes, err = io.ReadAll(apiResponse.Body); err != nil {
		return
	}
	apiResponseStr = string(apiResponseBytes)
	logStr = fmt.Sprintf("%s\n响应信息%s", logStr, apiResponseStr)
	if apiResponse.StatusCode != 200 {
		err = fmt.Errorf("RequestFail, StatusCode != 200")
		return
	}
	if err = json.Unmarshal(apiResponseBytes, &resObj); err != nil {
		err = fmt.Errorf("json Unmarshal error is %v", err.Error())
		return
	}
	return
}

func HttpGet(reqUrl string, resObj interface{}, timeOutSecond int) (logStr string, curlStr string, err error) {
	var (
		apiReq *http.Request
	)
	if apiReq, err = http.NewRequest(http.MethodGet, reqUrl, nil); err != nil {
		return
	}
	if logStr, curlStr, err = DoReq(apiReq, resObj, timeOutSecond); err != nil {
		return
	}
	return
}

func HttpGetWithHeader(reqUrl string, header map[string]string, resObj interface{}, timeOutSecond int) (logStr string, curlStr string, err error) {
	var (
		apiReq *http.Request
	)
	if apiReq, err = http.NewRequest(http.MethodGet, reqUrl, nil); err != nil {
		return
	}
	for k, v := range header {
		apiReq.Header.Set(k, v)
	}
	if logStr, curlStr, err = DoReq(apiReq, resObj, timeOutSecond); err != nil {
		return
	}
	return
}

func HttpPostJson(reqUrl string, data map[string]interface{}, resObj interface{}, timeOutSecond int) (logStr string, curlStr string, err error) {
	var (
		apiReq    *http.Request
		dataBytes []byte
	)
	if dataBytes, err = json.Marshal(data); err != nil {
		return
	}
	if apiReq, err = http.NewRequest(http.MethodPost, reqUrl, bytes.NewReader(dataBytes)); err != nil {
		return
	}
	apiReq.Header.Set("Content-Type", "application/json;charset=UTF-8")
	if logStr, curlStr, err = DoReq(apiReq, resObj, timeOutSecond); err != nil {
		return
	}
	return
}

func HttpPostJsonWithHeader(reqUrl string, data map[string]interface{}, header map[string]string, resObj interface{}, timeOutSecond int) (logStr string, curlStr string, err error) {
	var (
		apiReq    *http.Request
		dataBytes []byte
	)
	if dataBytes, err = json.Marshal(data); err != nil {
		return
	}
	if apiReq, err = http.NewRequest(http.MethodPost, reqUrl, bytes.NewReader(dataBytes)); err != nil {
		return
	}
	for k, v := range header {
		apiReq.Header.Set(k, v)
	}
	apiReq.Header.Set("Content-Type", "application/json;charset=UTF-8")
	if logStr, curlStr, err = DoReq(apiReq, resObj, timeOutSecond); err != nil {
		return
	}
	return
}

func HttpPostJsonWithHeaderReturnHeader(reqUrl string, data map[string]interface{}, header map[string]string, resObj interface{}, timeOutSecond int) (logStr string, curlStr string, apiResponseHeader http.Header, err error) {
	var (
		apiReq    *http.Request
		dataBytes []byte
	)
	if dataBytes, err = json.Marshal(data); err != nil {
		return
	}
	if apiReq, err = http.NewRequest(http.MethodPost, reqUrl, bytes.NewReader(dataBytes)); err != nil {
		return
	}
	for k, v := range header {
		apiReq.Header.Set(k, v)
	}
	apiReq.Header.Set("Content-Type", "application/json;charset=UTF-8")
	if logStr, curlStr, apiResponseHeader, err = DoReqReturnHeader(apiReq, resObj, timeOutSecond); err != nil {
		return
	}
	return
}

func HttpPostForm(reqUrl string, data map[string]interface{}, resObj interface{}, timeOutSecond int) (logStr string, curlStr string, err error) {
	var (
		apiReq    *http.Request
		dataStr   string
		reqValues = url.Values{}
	)
	for k, v := range data {
		reqValues.Set(k, fmt.Sprintf("%v", v))
	}
	dataStr = reqValues.Encode()
	if apiReq, err = http.NewRequest(http.MethodPost, reqUrl, strings.NewReader(dataStr)); err != nil {
		return
	}
	apiReq.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	if logStr, curlStr, err = DoReq(apiReq, resObj, timeOutSecond); err != nil {
		return
	}
	return
}

func HttpPostFormWithHeader(reqUrl string, data map[string]interface{}, header map[string]string, resObj interface{}, timeOutSecond int) (logStr string, curlStr string, err error) {
	var (
		apiReq    *http.Request
		dataStr   string
		reqValues = url.Values{}
	)
	for k, v := range data {
		reqValues.Set(k, fmt.Sprintf("%v", v))
	}
	dataStr = reqValues.Encode()
	if apiReq, err = http.NewRequest(http.MethodPost, reqUrl, strings.NewReader(dataStr)); err != nil {
		return
	}
	for k, v := range header {
		apiReq.Header.Set(k, v)
	}
	apiReq.Header.Set("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8")
	if logStr, curlStr, err = DoReq(apiReq, resObj, timeOutSecond); err != nil {
		return
	}
	return
}

func QuoteHeader(header http.Header) string {
	var headers []string
	for key, values := range header {
		for _, value := range values {
			headers = append(headers, fmt.Sprintf(`%s: %s`, key, value))
		}
	}
	return `'` + strings.Join(headers, `\n`) + `'`
}
