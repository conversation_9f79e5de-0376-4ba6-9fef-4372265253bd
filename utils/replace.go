package utils

import (
	"fmt"
	"strings"
)

func MapReplaceKey(dataMap map[string]interface{}, prefix string) map[string]interface{} {
	newMap := make(map[string]interface{})
	for key, value := range dataMap {
		newKey := fmt.Sprintf("%s_%s", prefix, key)
		switch v := value.(type) {
		case map[string]interface{}:
			newMap[newKey] = MapReplaceKey(v, prefix)
		case []interface{}:
			newMap[newKey] = SliceReplaceKey(v, prefix)
		default:
			newMap[newKey] = value
		}
	}
	return newMap
}

func SliceReplaceKey(slice []interface{}, prefix string) []interface{} {
	newSlice := make([]interface{}, len(slice))
	for i, item := range slice {
		switch v := item.(type) {
		case map[string]interface{}:
			newSlice[i] = MapReplaceKey(v, prefix)
		case []interface{}:
			newSlice[i] = SliceReplaceKey(v, prefix)
		default:
			newSlice[i] = item // Handle non-map and non-slice values
		}
	}
	return newSlice
}

func MapReduceKey(dataMap map[string]interface{}, prefix string) map[string]interface{} {
	newMap := make(map[string]interface{})
	for key, value := range dataMap {
		newKey := key
		if strings.Contains(key, prefix) {
			newKey = strings.Replace(newKey, prefix, "", 1)
		}
		switch v := value.(type) {
		case map[string]interface{}:
			newMap[newKey] = MapReduceKey(v, prefix)
		case []interface{}:
			newMap[newKey] = SliceReduceKey(v, prefix)
		default:
			newMap[newKey] = value
		}
	}
	return newMap
}

func SliceReduceKey(slice []interface{}, prefix string) []interface{} {
	newSlice := make([]interface{}, len(slice))
	for i, item := range slice {
		switch v := item.(type) {
		case map[string]interface{}:
			newSlice[i] = MapReduceKey(v, prefix)
		case []interface{}:
			newSlice[i] = SliceReduceKey(v, prefix)
		default:
			newSlice[i] = item // Handle non-map and non-slice values
		}
	}
	return newSlice
}
