package httpclient

import (
	"encoding/json"
	"io"
	"net/http"
	"time"
)

type Response struct {
	StartTime  time.Time
	StatusCode int
	Body       []byte
	Headers    http.Header
}

func NewResponse(resp *http.Response, startTime time.Time) (*Response, error) {
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	return &Response{
		StartTime:  startTime,
		StatusCode: resp.StatusCode,
		Body:       body,
		Headers:    resp.Header,
	}, nil
}

func (r *Response) Duration() time.Duration {
	return time.Since(r.StartTime)
}

func (r *Response) Unmarshal(v interface{}) error {
	return json.Unmarshal(r.Body, v)
}

func (r *Response) String() string {
	return string(r.Body)
}

func (r *Response) Is2xx() bool {
	return r.StatusCode >= 200 && r.StatusCode < 300
}

func (r *Response) Is4xx() bool {
	return r.StatusCode >= 400 && r.StatusCode < 500
}

func (r *Response) Is5xx() bool {
	return r.StatusCode >= 500 && r.StatusCode < 600
}

func (r *Response) Is200() bool {
	return r.StatusCode == 200
}

func (r *Response) Is201() bool {
	return r.StatusCode == 201
}

func (r *Response) Is400() bool {
	return r.StatusCode == 400
}

func (r *Response) Is401() bool {
	return r.StatusCode == 401
}

func (r *Response) Is403() bool {
	return r.StatusCode == 403
}

func (r *Response) Is404() bool {
	return r.StatusCode == 404
}

func (r *Response) Is500() bool {
	return r.StatusCode == 500
}

func (r *Response) Is503() bool {
	return r.StatusCode == 503
}
