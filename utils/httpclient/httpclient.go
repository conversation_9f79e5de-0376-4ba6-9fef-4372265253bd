package httpclient

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"time"
)

type Http struct {
	SensitiveFields []string
	ctx             context.Context
	client          *http.Client
	request         Request
	response        Response
	debug           bool
}

func NewHttp(ctx context.Context, url, method string, options ...func(*Http)) *Http {
	http := &Http{
		ctx:    ctx,
		client: &http.Client{},
		request: Request{
			Url:    url,
			Method: method,
		},
	}

	for _, option := range options {
		option(http)
	}

	return http
}

func NewHttpWithTls(url, method string, clientKey, clientCert []byte, options ...func(*Http)) (*Http, error) {
	cert, err := tls.X509KeyPair(clientCert, clientKey)
	if err != nil {
		return nil, err
	}

	tlsConfig := &tls.Config{
		InsecureSkipVerify: true,
		Certificates: []tls.Certificate{
			cert,
		},
	}

	http := &Http{
		client: &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: tlsConfig,
			},
		},
		request: Request{
			Url:    url,
			Method: method,
		},
	}

	for _, option := range options {
		option(http)
	}

	return http, nil
}

func (h *Http) GetResponse() *Response {
	return &h.response
}

func basicAuth(username, password string) string {
	auth := username + ":" + password
	return base64.StdEncoding.EncodeToString([]byte(auth))
}

func WithDebug() func(*Http) {
	return func(h *Http) {
		h.debug = true
	}
}

func WithBasicAuth(username, password string) func(*Http) {
	return func(h *Http) {
		if h.request.Headers == nil {
			h.request.Headers = http.Header{}
		}
		h.request.Headers.Add("Authorization", "Basic "+basicAuth(username, password))
	}
}

func WithHeaders(headers http.Header) func(*Http) {
	return func(h *Http) {
		if h.request.Headers == nil {
			h.request.Headers = http.Header{}
		}
		for k, v := range headers {
			h.request.Headers.Add(k, v[0])
		}
	}
}

func WithQuery(query url.Values) func(*Http) {
	return func(h *Http) {
		h.request.Url += "?" + query.Encode()
	}
}

func WithBody(body []byte) func(*Http) {
	return func(h *Http) {
		h.request.Body = body
	}
}

func WithBodyStruct(body interface{}) func(*Http) {
	return func(h *Http) {
		b, err := json.Marshal(body)
		if err != nil {
			return
		}
		h.request.Body = b
	}
}

func WithClient(client *http.Client) func(*Http) {
	return func(h *Http) {
		h.client = client
	}
}

func WithTimeout(timeout time.Duration) func(*Http) {
	return func(h *Http) {
		h.client.Timeout = timeout
	}
}

func WithTransport(transport http.RoundTripper) func(*Http) {
	return func(h *Http) {
		h.client.Transport = transport
	}
}

func WithTls(clientKey, clientCert []byte) func(*Http) {
	cert, err := tls.X509KeyPair(clientCert, clientKey)
	if err != nil {
		return func(h *Http) {
		}
	}

	tlsConfig := &tls.Config{
		InsecureSkipVerify: true,
		Certificates: []tls.Certificate{
			cert,
		},
	}

	return func(h *Http) {
		if h.client == nil {
			h.client = &http.Client{}
		}
		h.client.Transport = &http.Transport{
			TLSClientConfig: tlsConfig,
		}
	}
}

func WithBearer(token string) func(*Http) {
	return func(h *Http) {
		if h.request.Headers == nil {
			h.request.Headers = http.Header{}
		}
		h.request.Headers.Add("Authorization", "Bearer "+token)
	}
}

func WithContentType(contentType Metadata) func(*Http) {
	return func(h *Http) {
		if h.request.Headers == nil {
			h.request.Headers = http.Header{}
		}
		h.request.Headers.Add("Content-Type", contentType.String())
	}
}

func WithSensitive(fields ...string) func(*Http) {
	return func(h *Http) {
		h.SensitiveFields = fields
	}
}

func (h *Http) Send() (*Response, error) {
	startTime := time.Now()
	if h.client != nil {
		h.client = &http.Client{}
	}
	var body io.Reader
	if h.request.Body != nil {
		body = bytes.NewBuffer(h.request.Body)
	}

	var req *http.Request
	var err error

	req, err = http.NewRequestWithContext(h.ctx, h.request.Method, h.request.Url, body)
	if err != nil {
		return nil, err
	}

	req.Header = h.request.Headers
	httpResponse, err := h.client.Do(req)
	if err != nil {
		return nil, err
	}

	resp, err := NewResponse(httpResponse, startTime)
	if err != nil {
		return nil, err
	}

	h.response = *resp

	defer func() {
		if h.debug {
			go func(payload []byte, response string) {
				log.Printf("[httpdebug] payload: %v", string(payload))
				log.Printf("[httpdebug] response: %v", response)
			}(h.request.Body, resp.String())
		}
	}()

	return resp, nil
}

func (h *Http) Debug() {
	fmt.Println("URL: ", h.request.Url)
	fmt.Println("Method: ", h.request.Method)
	fmt.Println("Headers: ", h.request.Headers)
	fmt.Println("Body: ", string(h.request.Body))
	fmt.Println("Response: ", h.response.String())
}
