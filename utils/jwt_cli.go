package utils

import (
	"aimsg-server/global"
	"fmt"
	"git.costnovel.com/cashbox-ai/ai-par-model/model"
	"github.com/golang-jwt/jwt/v5"
	"time"
)

var (
	JwtCliObj = &jwtCli{}
)

type jwtCli struct{}

func (j *jwtCli) CreateCliClaims(user *model.DigitalUser) jwt.MapClaims {
	now := time.Now()
	ep, _ := ParseDuration(global.CONFIG.JWT.ExpiresTime)
	claims := jwt.MapClaims{
		"id":     user.Id,
		"app_id": user.AppID,
		"exp":    jwt.NewNumericDate(time.Now().Add(ep)), // 过期时间，表示 JWT 的有效期截止时间。一旦超过了这个时间，JWT 将被认为是过期的，不再被接受使用。
		"nbf":    jwt.NewNumericDate(now.Add(-1000)),     // 生效时间，表示 JWT 的有效期开始时间。在该时间之前，JWT 将被认为是无效的。
		"iat":    jwt.NewNumericDate(now),                // 签发时间，表示 JWT 的签发时间。
		"aud":    nil,                                    // 受众，表示 JWT 的预期接收者。它可以是单个接收者的标识符，也可以是多个接收者的标识符的列表。
		"iss":    "go_server",                            // 签发者，表示 JWT 的签发者。
		"sub":    fmt.Sprintf("%d", user.Id),             // 主题，表示 JWT 所面向的用户。它通常存放用户的唯一标识符，用于标识该 JWT 所代表的用户身份。
	}
	return claims
}

// CreateToken 创建一个token
func (j *jwtCli) CreateToken(claims *jwt.MapClaims) (string, error) {
	token := jwt.New(jwt.SigningMethodHS256)
	token.Claims = claims
	return token.SignedString([]byte(global.CONFIG.JWT.SigningKey))
}

// CreateTokenByOldToken 旧token 换新token 使用归并回源避免并发问题
func (j *jwtCli) CreateTokenByOldToken(oldToken string, claims *jwt.MapClaims) (string, error) {
	v, err, _ := global.ConcurrencyControl.Do("JWT:"+oldToken, func() (interface{}, error) {
		return j.CreateToken(claims)
	})
	return v.(string), err
}

// 解析 token
func (j *jwtCli) ParseToken(tokenString string) (jwt.MapClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &jwt.MapClaims{}, func(token *jwt.Token) (i interface{}, e error) {
		return []byte(global.CONFIG.JWT.SigningKey), nil
	})
	if err != nil {
		return nil, TokenInvalid
	}
	if token != nil {
		claims, ok := token.Claims.(*jwt.MapClaims)
		if ok && token.Valid {
			return *claims, nil
		}
		return nil, TokenInvalid
	} else {
		return nil, TokenInvalid
	}
}
