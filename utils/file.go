package utils

import (
	"fmt"
	"os"
	"path/filepath"
)

/*
def is_image_file(file_name):
    image_extensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp']
    file_extension = file_name.lower().split('.')[-1]
    return file_extension in image_extensions

*/

var ImageExtensionsMap = map[string]bool{
	".jpg":  true,
	".jpeg": true,
	".png":  true,
	".gif":  true,
	".bmp":  true,
}

func IsImageFile(fileName string) bool {
	fileExt := filepath.Ext(fileName)
	if fileExt == "" {
		return false
	}
	return ImageExtensionsMap[fileExt]

}

func FindFirstMP4File(dir string) (absPath string, err error) {
	var (
		files []os.DirEntry
	)
	if files, err = os.ReadDir(dir); err != nil {
		return
	}
	for _, file := range files {
		if !file.IsDir() && filepath.Ext(file.Name()) == ".mp4" {
			absPath, err = filepath.Abs(filepath.Join(dir, file.Name()))
			return
		}
	}
	return "", fmt.Errorf("no MP4 file found in %s", dir)
}
