package agora

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

type AgoSendRes struct {
	Code      string      `json:"code"`
	Result    string      `json:"result"`
	RequestId string      `json:"request_id"`
	Debug     interface{} `json:"debug"`
}

func (m *AgoraCli) SendPeerMsg(senderId, receiverId, payload string, waitForAck bool) (apiRes AgoSendRes, err error) {
	var (
		reqBodyBytes     []byte
		apiResponse      *http.Response
		apiResponseBytes []byte
	)
	apiUrl := fmt.Sprintf("https://api.agora.io/dev/v2/project/%s/rtm/users/%s/peer_messages?wait_for_ack=%t", m.AppID, senderId, waitForAck)
	reqBody := map[string]interface{}{
		"destination":                 receiverId,
		"enable_offline_messaging":    true,
		"enable_historical_messaging": true,
		"payload":                     payload,
	}
	if reqBodyBytes, err = json.Marshal(reqBody); err != nil {
		return
	}
	if apiResponse, err = m.client.Post(apiUrl, "application/json", bytes.NewReader(reqBodyBytes)); err != nil {
		return
	}
	defer apiResponse.Body.Close()
	if apiResponseBytes, err = io.ReadAll(apiResponse.Body); err != nil {
		return
	}
	if apiResponse.StatusCode != 200 {
		err = fmt.Errorf("requestFail, statusCode:%d", apiResponse.StatusCode)
		return
	}
	if err = json.Unmarshal(apiResponseBytes, &apiRes); err != nil {
		err = fmt.Errorf("json Unmarshal error is %v", err.Error())
		return
	}
	if apiRes.Result != "success" {
		err = fmt.Errorf("failed: %s", apiRes.Result)
		return
	}
	return
}
