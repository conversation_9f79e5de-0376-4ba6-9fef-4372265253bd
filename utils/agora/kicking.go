package agora

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

type Privilege string

const PrivilegeJoinChannel = "join_channel"   // 加入频道
const PrivilegePublishAudio = "publish_audio" // 发送音频流
const PrivilegePublishVideo = "publish_video" // 发送视频流

var PrivilegeMap = map[Privilege]string{
	PrivilegeJoinChannel:  "加入频道",
	PrivilegePublishAudio: "发送音频流",
	PrivilegePublishVideo: "发送视频流",
}

type CreateKickingRuleReq struct {
	Appid         string      `json:"appid"`           // 项目的 App ID
	Cname         string      `json:"cname"`           // 频道名称
	Uid           int         `json:"uid"`             // 用户 ID。不能设为 0
	Ip            string      `json:"ip"`              // 用户的 IP 地址。不能设为 0
	Time          int         `json:"time"`            // 封禁时间，单位为分钟，取值范围为 [1,1440] time 和 time_in_seconds 两个参数只需设置其中的一个。如果同时设置，则 time_in_seconds 生效，如果都不设置，服务端会自动将封禁时间设为 60 分钟，即 3600 秒
	TimeInSeconds int         `json:"time_in_seconds"` // 封禁时间，单位为秒，取值范围为 [10,86430] time 和 time_in_seconds 两个参数只需设置其中的一个。如果同时设置，则 time_in_seconds 生效，如果都不设置，服务端会自动将封禁时间设为 60 分钟，即 3600 秒
	Privileges    []Privilege `json:"privileges"`      // 你想要封禁的用户权限
}

type PutKickingRuleReq struct {
	Appid         string `json:"appid"`           // 项目的AppID
	ID            int    `json:"id"`              // 规则ID
	Time          int    `json:"time"`            // 封禁时间，单位为分钟，取值范围为 [1,1440] time 和 time_in_seconds 两个参数只需设置其中的一个。如果同时设置，则 time_in_seconds 生效，如果都不设置，服务端会自动将封禁时间设为 60 分钟，即 3600 秒
	TimeInSeconds int    `json:"time_in_seconds"` // 封禁时间，单位为秒，取值范围为 [10,86430] time 和 time_in_seconds 两个参数只需设置其中的一个。如果同时设置，则 time_in_seconds 生效，如果都不设置，服务端会自动将封禁时间设为 60 分钟，即 3600 秒
}

type DelKickingRuleReq struct {
	Appid string `json:"appid"`
	Id    int    `json:"id"`
}

type PutKickingRuleRes struct {
	Status  string `json:"status"`
	Message string `json:"message"`
	Result  struct {
		Id int       `json:"id"`
		Ts time.Time `json:"ts"`
	} `json:"result"`
}

type DelKickingRuleRes struct {
	Status  string `json:"status"`
	Message string `json:"message"`
	Id      int    `json:"id"`
}

type CreateKickingRuleRes struct {
	Status  string `json:"status"`
	Message string `json:"message"`
	Id      int    `json:"id"`
}

type GetKickingRuleRes struct {
	Status  string `json:"status"`
	Message string `json:"message"`
	Rules   []struct {
		Id         int       `json:"id"`
		Appid      string    `json:"appid"`
		Uid        int       `json:"uid"`
		Opid       int       `json:"opid"`
		Cname      string    `json:"cname"`
		Ip         string    `json:"ip"`
		Ts         time.Time `json:"ts"`
		Privileges []string  `json:"privileges"`
		CreateAt   time.Time `json:"createAt"`
		UpdateAt   time.Time `json:"updateAt"`
	} `json:"rules"`
}

// CreateKickingRule 创建封禁用户权限的规则
// https://doc.shengwang.cn/doc/rtc/restful/channel-management/operations/post-dev-v1-kicking-rule
func (m *AgoraCli) CreateKickingRule(reqData CreateKickingRuleReq) (apiRes CreateKickingRuleRes, err error) {
	var (
		reqBodyBytes     []byte
		apiResponse      *http.Response
		apiResponseBytes []byte
	)
	apiUrl := "https://api.sd-rtn.com/dev/v1/kicking-rule"
	if reqBodyBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if apiResponse, err = m.client.Post(apiUrl, "application/json", bytes.NewReader(reqBodyBytes)); err != nil {
		return
	}
	defer apiResponse.Body.Close()
	if apiResponseBytes, err = io.ReadAll(apiResponse.Body); err != nil {
		return
	}
	if apiResponse.StatusCode != 200 {
		err = fmt.Errorf("requestFail, statusCode:%d", apiResponse.StatusCode)
		return
	}
	if err = json.Unmarshal(apiResponseBytes, &apiRes); err != nil {
		err = fmt.Errorf("json Unmarshal error is %v", err.Error())
		return
	}
	if apiRes.Status != "success" {
		err = fmt.Errorf("failed: %s", apiRes.Message)
		return
	}
	return
}

// GetKickingRule 获取封禁用户权限的规则列表
// https://doc.shengwang.cn/doc/rtc/restful/channel-management/operations/get-dev-v1-kicking-rule
func (m *AgoraCli) GetKickingRule() (apiRes GetKickingRuleRes, err error) {
	var (
		apiResponse      *http.Response
		apiResponseBytes []byte
	)
	apiUrl := "https://api.sd-rtn.com/dev/v1/kicking-rule"
	if apiResponse, err = m.client.Get(apiUrl); err != nil {
		return
	}
	defer apiResponse.Body.Close()
	if apiResponseBytes, err = io.ReadAll(apiResponse.Body); err != nil {
		return
	}
	if apiResponse.StatusCode != 200 {
		err = fmt.Errorf("requestFail, statusCode:%d", apiResponse.StatusCode)
		return
	}
	if err = json.Unmarshal(apiResponseBytes, &apiRes); err != nil {
		err = fmt.Errorf("json Unmarshal error is %v", err.Error())
		return
	}
	if apiRes.Status != "success" {
		err = fmt.Errorf("failed: %s", apiRes.Message)
		return
	}
	return
}

// PutKickingRule 更新指定规则的过期时间
// https://doc.shengwang.cn/doc/rtc/restful/channel-management/operations/put-dev-v1-kicking-rule
func (m *AgoraCli) PutKickingRule(reqData PutKickingRuleReq) (apiRes PutKickingRuleRes, err error) {
	var (
		reqBodyBytes     []byte
		apiRequest       *http.Request
		apiResponse      *http.Response
		apiResponseBytes []byte
	)
	apiUrl := "https://api.sd-rtn.com/dev/v1/kicking-rule"
	if reqBodyBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if apiRequest, err = http.NewRequest(http.MethodPut, apiUrl, bytes.NewReader(reqBodyBytes)); err != nil {
		return
	}
	apiRequest.Header.Set("Content-Type", "application/json")
	if apiResponse, err = m.client.Do(apiRequest); err != nil {
		return
	}
	defer apiResponse.Body.Close()
	if apiResponseBytes, err = io.ReadAll(apiResponse.Body); err != nil {
		return
	}
	if apiResponse.StatusCode != 200 {
		err = fmt.Errorf("requestFail, statusCode:%d", apiResponse.StatusCode)
		return
	}
	if err = json.Unmarshal(apiResponseBytes, &apiRes); err != nil {
		err = fmt.Errorf("json Unmarshal error is %v", err.Error())
		return
	}
	if apiRes.Status != "success" {
		err = fmt.Errorf("failed: %s", apiRes.Message)
		return
	}
	return
}

// DelKickingRule 删除指定的封禁用户权限规则
// https://doc.shengwang.cn/doc/rtc/restful/channel-management/operations/delete-dev-v1-kicking-rule
func (m *AgoraCli) DelKickingRule(reqData DelKickingRuleReq) (apiRes DelKickingRuleRes, err error) {
	var (
		reqBodyBytes     []byte
		apiRequest       *http.Request
		apiResponse      *http.Response
		apiResponseBytes []byte
	)
	apiUrl := "https://api.sd-rtn.com/dev/v1/kicking-rule"
	if reqBodyBytes, err = json.Marshal(reqData); err != nil {
		return
	}
	if apiRequest, err = http.NewRequest(http.MethodDelete, apiUrl, bytes.NewReader(reqBodyBytes)); err != nil {
		return
	}
	apiRequest.Header.Set("Content-Type", "application/json")
	if apiResponse, err = m.client.Do(apiRequest); err != nil {
		return
	}
	defer apiResponse.Body.Close()
	if apiResponseBytes, err = io.ReadAll(apiResponse.Body); err != nil {
		return
	}
	if apiResponse.StatusCode != 200 {
		err = fmt.Errorf("requestFail, statusCode:%d", apiResponse.StatusCode)
		return
	}
	if err = json.Unmarshal(apiResponseBytes, &apiRes); err != nil {
		err = fmt.Errorf("json Unmarshal error is %v", err.Error())
		return
	}
	if apiRes.Status != "success" {
		err = fmt.Errorf("failed: %s", apiRes.Message)
		return
	}
	return
}
