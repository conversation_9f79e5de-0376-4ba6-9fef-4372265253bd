package agora

import (
	rtmtokenbuilder "github.com/AgoraIO-Community/go-tokenbuilder/rtmtokenbuilder"
	rtctokenbuilder "github.com/AgoraIO/Tools/DynamicKey/AgoraDynamicKey/go/src/rtctokenbuilder2"
)

func GenerateAgoraToken(appID, appCertificate, userId string, expireSec int) (token string, err error) {
	expire := uint32(expireSec)
	token, err = rtmtokenbuilder.BuildToken(appID, appCertificate, userId, expire, "")
	return
}

func BuildTokenWithRtm(appID, appCertificate, userId, channelName string, expireSec int) (token string, err error) {
	expire := uint32(expireSec)
	privilegeExpire := uint32(expireSec)
	token, err = rtctokenbuilder.BuildTokenWithRtm(appID, appCertificate, channelName, userId, rtctokenbuilder.RolePublisher, expire, privilegeExpire)
	return
}

func BuildTokenWithUid(appID, appCertificate, channelName string, userId uint, expireSec int) (token string, err error) {
	expire := uint32(expireSec)
	privilegeExpire := uint32(expireSec)
	userId32 := uint32(userId)
	token, err = rtctokenbuilder.BuildTokenWithUid(appID, appCertificate, channelName, userId32, rtctokenbuilder.RolePublisher, expire, privilegeExpire)
	return
}
