package agora

import (
	"net/http"
	"net/url"
	"time"
)

type <PERSON>goraCli struct {
	AppID   string
	AppCert string
	client  *http.Client
}

func NewAgoraCli(appId, appCert, customId, customSecret string) *AgoraCli {
	client := &http.Client{
		Transport: &http.Transport{
			MaxIdleConnsPerHost: 100,
			Proxy: func(req *http.Request) (*url.URL, error) {
				req.SetBasicAuth(customId, customSecret)
				return nil, nil
			},
		},
		Timeout: time.Second * 10,
	}
	return &AgoraCli{
		AppID:   appId,
		AppCert: appCert,
		client:  client,
	}
}
