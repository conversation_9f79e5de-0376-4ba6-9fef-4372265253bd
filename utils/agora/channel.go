package agora

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

type ChannelUserPropertyRes struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    struct {
		Join      int   `json:"join"`
		Uid       int64 `json:"uid"`
		InChannel bool  `json:"in_channel"`
		Platform  int   `json:"platform"`
		Role      int   `json:"role"`
	} `json:"data"`
}

type ChannelUserRes struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    struct {
		ChannelExist  bool    `json:"channel_exist"`
		Mode          int     `json:"mode"`
		Broadcasters  []int64 `json:"broadcasters"`
		Audience      []uint  `json:"audience"`
		Users         []uint  `json:"users"`
		AudienceTotal int     `json:"audience_total"`
		Total         int     `json:"total"`
	} `json:"data"`
}

// ChannelUserProperty 该方法可查询指定频道中某个用户的状态。请求成功后，返回的参数包括用户是否在频道中、加入频道的时间和用户角色等。
// https://doc.shengwang.cn/doc/rtc/restful/channel-management/operations/get-user-property
func (m *AgoraCli) ChannelUserProperty(cname string, uid uint) (apiRes ChannelUserPropertyRes, err error) {
	var (
		apiResponse      *http.Response
		apiResponseBytes []byte
	)
	apiUrl := fmt.Sprintf("https://api.sd-rtn.com/dev/v1/channel/user/property/%s/%d/%s", m.AppID, uid, cname)
	if apiResponse, err = m.client.Get(apiUrl); err != nil {
		return
	}
	defer apiResponse.Body.Close()
	if apiResponseBytes, err = io.ReadAll(apiResponse.Body); err != nil {
		return
	}
	if apiResponse.StatusCode != 200 {
		err = fmt.Errorf("requestFail, statusCode:%d", apiResponse.StatusCode)
		return
	}
	if err = json.Unmarshal(apiResponseBytes, &apiRes); err != nil {
		err = fmt.Errorf("json Unmarshal error is %v", err.Error())
		return
	}
	if !apiRes.Success {
		err = fmt.Errorf("failed: %s", apiRes.Message)
		return
	}
	return
}

// ChannelUserProperty 该方法可查询指定频道中某个用户的状态。请求成功后，返回的参数包括用户是否在频道中、加入频道的时间和用户角色等。
// https://doc.shengwang.cn/doc/rtc/restful/channel-management/operations/get-user-property
func (m *AgoraCli) ChannelUser(cname string) (apiRes ChannelUserRes, err error) {
	var (
		apiResponse      *http.Response
		apiResponseBytes []byte
	)
	apiUrl := fmt.Sprintf("https://api.sd-rtn.com/dev/v1/channel/user/%s/%s", m.AppID, cname)
	if apiResponse, err = m.client.Get(apiUrl); err != nil {
		return
	}
	defer apiResponse.Body.Close()
	if apiResponseBytes, err = io.ReadAll(apiResponse.Body); err != nil {
		return
	}
	if apiResponse.StatusCode != 200 {
		err = fmt.Errorf("requestFail, statusCode:%d", apiResponse.StatusCode)
		return
	}
	if err = json.Unmarshal(apiResponseBytes, &apiRes); err != nil {
		err = fmt.Errorf("json Unmarshal error is %v", err.Error())
		return
	}
	if !apiRes.Success {
		err = fmt.Errorf("failed: %s", apiRes.Message)
		return
	}
	return
}
