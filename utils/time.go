package utils

import (
	"fmt"
	"time"
)

func TimeInRange(checkTime time.Time, startStr, endStr string) bool {
	// 将字符串格式的时间范围转换为 time.Time 类型
	startTime, err := time.Parse("15:04:05", startStr)
	if err != nil {
		fmt.Println("解析开始时间失败:", err)
		return false
	}
	endTime, err := time.Parse("15:04:05", endStr)
	if err != nil {
		fmt.Println("解析结束时间失败:", err)
		return false
	}
	startTimeCheck := time.Date(checkTime.Year(), checkTime.Month(), checkTime.Day(), startTime.Hour(), startTime.Minute(), startTime.Second(), 0, checkTime.Location())
	endTimeCheck := time.Date(checkTime.Year(), checkTime.Month(), checkTime.Day(), endTime.Hour(), endTime.Minute(), endTime.Second(), 0, checkTime.Location())
	return checkTime.After(startTimeCheck) && checkTime.Before(endTimeCheck)
}

func IsValidTimeRange(startStr, endStr string) bool {
	startTime, err := time.Parse("15:04:05", startStr)
	if err != nil {
		return false
	}

	endTime, err := time.Parse("15:04:05", endStr)
	if err != nil {
		return false
	}

	return startTime.Before(endTime)
}

// DaysBetween 计算两个时间之间的天数差值
func DaysBetween(startTime, endTime time.Time) int {
	// 将时间转换为当天零点
	tStart := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 0, 0, 0, 0, startTime.Location())
	tEnd := time.Date(endTime.Year(), endTime.Month(), endTime.Day(), 0, 0, 0, 0, endTime.Location())

	// 计算差值天数
	days := int(tEnd.Sub(tStart).Hours() / 24)
	// 处理负数情况
	if days < 0 {
		days = -days
	}
	return days
}

// NowTime 返回当前时间time对象
func NowTime() (now time.Time) {
	return time.Now().Local()
}

// NowTimeStr 返回当前时间字符串
func NowTimeStr() (now string) {
	return time.Now().Local().Format(time.DateTime)
}

// StartOfMin 获取这一分钟的开始时间
func StartOfMin(t ...time.Time) time.Time {
	var nTime time.Time
	if len(t) > 0 {
		nTime = t[0]
	} else {
		nTime = NowTime()
	}
	return time.Date(nTime.Year(), nTime.Month(), nTime.Day(), nTime.Hour(), nTime.Minute(), 0, 0, nTime.Location())
}

// StartOfHour 获取这一小时的开始时间
func StartOfHour(t ...time.Time) time.Time {
	var nTime time.Time
	if len(t) > 0 {
		nTime = t[0]
	} else {
		nTime = NowTime()
	}
	return time.Date(nTime.Year(), nTime.Month(), nTime.Day(), nTime.Hour(), 0, 0, 0, nTime.Location())
}

// StartOfDay 获取这一天的开始时间
func StartOfDay(t ...time.Time) time.Time {
	var nTime time.Time
	if len(t) > 0 {
		nTime = t[0]
	} else {
		nTime = NowTime()
	}
	return time.Date(nTime.Year(), nTime.Month(), nTime.Day(), 0, 0, 0, 0, nTime.Location())
}

// StartOfWeek 周开始时间,mondayStart周一是否是开始时间
func StartOfWeek(mondayStart bool, t ...time.Time) time.Time {
	var nTime time.Time
	if len(t) > 0 {
		nTime = t[0]
	} else {
		nTime = NowTime()
	}

	weekDay := int(nTime.Weekday())
	if mondayStart {
		weekDay = (weekDay + 6) % 7
	}
	return StartOfDay(nTime).AddDate(0, 0, -weekDay)
}

// StartOfMonth 月开始时间
func StartOfMonth(t ...time.Time) time.Time {
	var nTime time.Time
	if len(t) > 0 {
		nTime = t[0]
	} else {
		nTime = NowTime()
	}
	return time.Date(nTime.Year(), nTime.Month(), 1, 0, 0, 0, 0, nTime.Location())
}

// StartOfYear 年开始时间
func StartOfYear(t ...time.Time) time.Time {
	var nTime time.Time
	if len(t) > 0 {
		nTime = t[0]
	} else {
		nTime = NowTime()
	}
	return time.Date(nTime.Year(), 1, 1, 0, 0, 0, 0, nTime.Location())
}

// EndOfMin 获取这一分钟的结束时间
func EndOfMin(t ...time.Time) time.Time {
	return StartOfMin(t...).Add(time.Minute - time.Nanosecond)
}

// EndOfHour 获取这一小时的结束时间
func EndOfHour(t ...time.Time) time.Time {
	return StartOfHour(t...).Add(time.Hour - time.Nanosecond)
}

// EndOfDay 获取这一天的结束时间
func EndOfDay(t ...time.Time) time.Time {
	return StartOfDay(t...).Add(time.Hour*24 - time.Nanosecond)
}

// EndOfWeek 周结束时间,mondayStart周一是否是开始时间
func EndOfWeek(mondayStart bool, t ...time.Time) time.Time {
	return StartOfWeek(mondayStart, t...).AddDate(0, 0, 7).Add(-time.Nanosecond)
}

// EndOfMonth 月结束时间
func EndOfMonth(t ...time.Time) time.Time {
	return StartOfMonth(t...).AddDate(0, 1, 0).Add(-time.Nanosecond)
}

// EndOfYear 年结束时间
func EndOfYear(t ...time.Time) time.Time {
	return StartOfYear(t...).AddDate(1, 0, 0).Add(-time.Nanosecond)
}

// CurrentWeekRange 获取当前周的开始结束时间
func CurrentWeekRange(mondayStart bool) (currentWeekStart, currentWeekEnd time.Time) {
	currentWeekStart = StartOfWeek(mondayStart)
	currentWeekEnd = EndOfWeek(mondayStart)
	return
}

func CurrentWeekRangeTime(mondayStart bool) (res []time.Time) {
	weekStart, weekEnd := CurrentWeekRange(mondayStart)
	for d := weekStart; !d.After(weekEnd); d = d.AddDate(0, 0, 1) {
		res = append(res, d)
	}
	return
}

// LastWeekRange 获取上一周的开始结束时间
func LastWeekRange(mondayStart bool) (lastWeekStart, lastWeekEnd time.Time) {
	currentWeekStart := StartOfWeek(mondayStart)
	lastWeekStart = currentWeekStart.AddDate(0, 0, -7)
	lastWeekEnd = lastWeekStart.AddDate(0, 0, 7).Add(-time.Nanosecond)
	return
}
