package utils

import (
	"math/rand"
	"time"
)

func RandomStringList(strList []string, num int) []string {
	if len(strList) <= num {
		return strList
	}
	rand.Seed(time.Now().UnixNano()) // Seed the random number generator
	// Create a map to track selected indices
	selectedIndices := make(map[int]bool)
	// Generate num unique random indices
	for len(selectedIndices) < num {
		randIndex := rand.Intn(len(strList))
		selectedIndices[randIndex] = true
	}
	// Populate the result list with selected strings
	res := make([]string, 0, num)
	for index := range selectedIndices {
		res = append(res, strList[index])
	}
	return res
}

func RandList[T any](list []T) (res T) {
	listLen := len(list)
	if listLen == 0 {
		return
	}
	source := rand.NewSource(time.Now().UnixNano())
	rng := rand.New(source)
	randIndex := rng.Intn(listLen)
	res = list[randIndex]
	return
}

func RandFloat64() (res float64) {
	source := rand.NewSource(time.Now().UnixNano())
	rng := rand.New(source)
	return rng.Float64()
}

func RandSelect[T any](valList []T, num int) (res []T) {
	listLen := len(valList)
	if listLen <= num {
		return valList
	}
	source := rand.NewSource(time.Now().UnixNano())
	rng := rand.New(source)
	selectedIndices := make(map[int]bool)
	for len(selectedIndices) < num {
		randIndex := rng.Intn(listLen)
		selectedIndices[randIndex] = true
	}
	// Populate the result list with selected elements
	res = make([]T, 0, num)
	for index := range selectedIndices {
		res = append(res, valList[index])
	}
	return
}

func ShuffleList[T any](valList []T) (res []T) {
	listLen := len(valList)
	if listLen == 0 {
		return
	}
	source := rand.NewSource(time.Now().UnixNano())
	rng := rand.New(source)

	shuffledList := make([]T, listLen)
	copy(shuffledList, valList)
	for i := len(shuffledList) - 1; i > 0; i-- {
		j := rng.Intn(i + 1)
		shuffledList[i], shuffledList[j] = shuffledList[j], shuffledList[i]
	}
	return shuffledList
}

func RandIntn(maxNum int) (res int) {
	return rand.New(rand.NewSource(time.Now().UnixNano())).Intn(maxNum)
}
