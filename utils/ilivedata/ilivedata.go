package ilivedata

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"io"
	"net/http"
	"strings"
	"time"
)

const (
	endpointURL           = "https://isafe.ilivedata.com/api/v1/image/check"
	endpointHost          = "isafe.ilivedata.com"
	endpointPath          = "/api/v1/image/check"
	iso8601DateFormatNoMS = "2006-01-02T15:04:05Z"
)

type ILiveData struct {
	ProjectID string
	SecretKey string
}

func (m ILiveData) Check(reqData map[string]interface{}) (res ImgCheckRes, err error) {
	var (
		apiResponseBytes []byte
		queryBody        []byte
		now              = time.Now().UTC().Format(iso8601DateFormatNoMS)
	)
	if queryBody, err = json.Marshal(reqData); err != nil {
		return
	}
	preparedString := []string{
		"POST",
		endpointHost,
		endpointPath,
		m.sha256AndHexEncode(string(queryBody)),
		"X-AppId:" + m.ProjectID,
		"X-TimeStamp:" + now,
	}
	stringToSign := strings.Join(preparedString, "\n")
	signature := m.signAndBase64Encode(stringToSign, m.SecretKey)
	apiResponseBytes, err = m.request(string(queryBody), signature, now)

	if err = json.Unmarshal(apiResponseBytes, &res); err != nil {
		return
	}

	return
}

func (m ILiveData) signAndBase64Encode(data string, secrectKey string) string {
	var mac = hmac.New(sha256.New, []byte(secrectKey))
	mac.Write([]byte(data))
	return base64.StdEncoding.EncodeToString(mac.Sum(nil))
}

func (m ILiveData) sha256AndHexEncode(data string) string {
	var sha256Hash = sha256.New()
	sha256Hash.Write([]byte(data))
	return hex.EncodeToString(sha256Hash.Sum(nil))
}

func (m ILiveData) request(body string, signature string, timeStamp string) (apiResponseBytes []byte, err error) {
	var (
		apiResponse *http.Response
		httpClient  = http.Client{}
	)
	apiReq, _ := http.NewRequest("POST", endpointURL, strings.NewReader(body))
	apiReq.Header.Set("X-AppId", m.ProjectID)
	apiReq.Header.Set("X-TimeStamp", timeStamp)
	apiReq.Header.Set("Authorization", signature)
	apiReq.Header.Set("Content-Type", "application/json")
	apiReq.Header.Set("User-Agent", "Golang_HTTP_Client/1.0")

	if apiResponse, err = httpClient.Do(apiReq); err != nil {
		return
	}
	defer apiResponse.Body.Close()
	if apiResponseBytes, err = io.ReadAll(apiResponse.Body); err != nil {
		return
	}
	return
}
