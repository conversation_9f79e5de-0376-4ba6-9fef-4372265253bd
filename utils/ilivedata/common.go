package ilivedata

type ImgCheckRes struct {
	ErrorCode    int    `json:"errorCode"`
	ErrorMessage string `json:"errorMessage"`
	ImageSpams   []struct {
		Code   int `json:"code"`
		Result int `json:"result"`
		Tags   []struct {
			Tag        int `json:"tag"`
			Level      int `json:"level"`
			Confidence int `json:"confidence"`
		} `json:"tags"`
	} `json:"imageSpams"`
	Code      int           `json:"code"`
	Result    int           `json:"result"`
	TaskId    string        `json:"taskId"`
	Gender    []interface{} `json:"gender"`
	ExtraInfo struct {
		CartoonScore int           `json:"cartoonScore"`
		GenderResult []interface{} `json:"genderResult"`
		NumHuman     int           `json:"numHuman"`
		NumFace      int           `json:"numFace"`
	} `json:"extraInfo"`
}
