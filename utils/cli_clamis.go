package utils

import (
	"aimsg-server/global"
	"github.com/gin-gonic/gin"
	"strings"
)

// GetDigitalUserID 从Gin的Context中获取从jwt解析出来的用户ID
func GetDigitalUserID(c *gin.Context) uint {
	if userIdAny, exists := c.Get(global.GinContextUserId); !exists {
		return 0
	} else {
		userIdUint := userIdAny.(uint)
		return userIdUint
	}
}

// GetAppID 从Gin的Context中获取从jwt解析出来的应用ID
func GetAppID(c *gin.Context) uint {
	if appIdAny, exists := c.Get(global.GinContextAppId); !exists {
		return 0
	} else {
		appIdUint := appIdAny.(uint)
		return appIdUint
	}
}

// FromTokenGetUserID 从请求头中获取用户ID
func FromTokenGetUserID(c *gin.Context) (userId uint) {
	authorizationStr := c.Get<PERSON>eader("Authorization")
	if authorizationStr == "" {
		return
	}
	authorizationStrArr := strings.Split(authorizationStr, " ")
	if len(authorizationStrArr) != 2 || authorizationStrArr[0] != "Bearer" {
		return
	}
	token := authorizationStrArr[1]
	if token == "" {
		return
	}
	claims, err := JwtCliObj.ParseToken(token)
	if err != nil {
		return
	}
	userIdInterface, userIdExist := claims["id"]
	if !userIdExist {
		return
	}
	userId = Uint(userIdInterface)
	return
}
