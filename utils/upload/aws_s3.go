package upload

import (
	"aimsg-server/global"
	"bytes"
	"encoding/base64"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/awserr"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"mime"
	"mime/multipart"
	"os"
	"path/filepath"
)

type AwsS3 struct {
	Session    *session.Session
	s3Client   *s3.S3
	bucketName string
}

func NewAwsS3() *AwsS3 {
	sess, _ := session.NewSession(&aws.Config{
		Region:           aws.String(global.CONFIG.AwsS3.Region),
		Endpoint:         aws.String(global.CONFIG.AwsS3.Endpoint), // minio在这里设置地址,可以兼容
		S3ForcePathStyle: aws.Bool(global.CONFIG.AwsS3.S3ForcePathStyle),
		DisableSSL:       aws.Bool(global.CONFIG.AwsS3.DisableSSL),
		Credentials: credentials.NewStaticCredentials(
			global.CONFIG.AwsS3.SecretID,
			global.CONFIG.AwsS3.SecretKey,
			"",
		),
	})
	s3Client := s3.New(sess)

	return &AwsS3{
		Session:    sess,
		s3Client:   s3Client,
		bucketName: global.CONFIG.AwsS3.Bucket,
	}
}

func (s *AwsS3) UploadFile(fileName string, objectName string) (err error) {
	if objectName == "" {
		objectName = filepath.Base(fileName)
	}

	file, err := os.Open(fileName)
	if err != nil {
		return err
	}
	defer file.Close()

	contentType := mime.TypeByExtension(filepath.Ext(fileName))
	if contentType == "" {
		contentType = "application/octet-stream"
	}
	_, err = s.s3Client.PutObject(&s3.PutObjectInput{
		Bucket:      aws.String(s.bucketName),
		Key:         aws.String(objectName),
		Body:        file,
		ContentType: aws.String(contentType),
	})
	if err != nil {
		return err
	}
	return nil
}

func (s *AwsS3) UploadMultipartFile(file multipart.File, key, contentType string) (err error) {
	_, err = s.s3Client.PutObject(&s3.PutObjectInput{
		Bucket:      aws.String(s.bucketName),
		Key:         aws.String(key),
		Body:        file,
		ContentType: aws.String(contentType),
	})
	if err != nil {
		return err
	}
	return nil
}

func (s *AwsS3) GetS3FileBase64(objectName string) (string, error) {
	input := &s3.GetObjectInput{
		Bucket: aws.String(s.bucketName),
		Key:    aws.String(objectName),
	}
	response, err := s.s3Client.GetObject(input)
	if err != nil {
		return "", err
	}
	defer response.Body.Close()

	fileContent := make([]byte, *response.ContentLength)
	_, err = response.Body.Read(fileContent)
	if err != nil {
		return "", err
	}

	base64Content := base64.StdEncoding.EncodeToString(fileContent)
	return base64Content, nil
}

func (s *AwsS3) PutObjectTag(objectName string, tags map[string]string) error {
	tagSet := make([]*s3.Tag, len(tags))
	i := 0
	for key, value := range tags {
		tagSet[i] = &s3.Tag{
			Key:   aws.String(key),
			Value: aws.String(value),
		}
		i++
	}

	_, err := s.s3Client.PutObjectTagging(&s3.PutObjectTaggingInput{
		Bucket: aws.String(s.bucketName),
		Key:    aws.String(objectName),
		Tagging: &s3.Tagging{
			TagSet: tagSet,
		},
	})
	if err != nil {
		return err
	}
	return nil
}

func (s *AwsS3) UploadBytesToS3(bytesData []byte, objectName, contentType string) (err error) {
	if _, err = s.s3Client.PutObject(&s3.PutObjectInput{
		Bucket:      aws.String(s.bucketName),
		Key:         aws.String(objectName),
		Body:        bytes.NewReader(bytesData),
		ContentType: aws.String(contentType),
	}); err != nil {
		return
	}
	return
}

func (s *AwsS3) FileExists(fileKey string) (bool, error) {
	_, err := s.s3Client.HeadObject(&s3.HeadObjectInput{
		Bucket: aws.String(s.bucketName),
		Key:    aws.String(fileKey),
	})
	if err != nil {
		if aerr, ok := err.(awserr.Error); ok && aerr.Code() == "NotFound" {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

func (s *AwsS3) ListFilesInS3Path(prefix string) (filesArr []string, err error) {
	var (
		listObjectsV2Output *s3.ListObjectsV2Output
	)

	if listObjectsV2Output, err = s.s3Client.ListObjectsV2(&s3.ListObjectsV2Input{
		Bucket: aws.String(s.bucketName),
		Prefix: aws.String(prefix),
	}); err != nil {
		return
	}
	for _, content := range listObjectsV2Output.Contents {
		filesArr = append(filesArr, *content.Key)
	}
	return
}
