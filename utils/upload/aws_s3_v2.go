package upload

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/aws"
	v4 "github.com/aws/aws-sdk-go-v2/aws/signer/v4"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"io"
	"mime"
	"mime/multipart"
	"os"
	"path/filepath"
	"time"
)

type AwsS3V2 struct {
	ctx           context.Context
	S3Client      *s3.Client
	PresignClient *s3.PresignClient
	bucketName    string
}

func NewAwsS3V2(ctx context.Context, accessKeyID, secretAccessKey, bucketName, region string) *AwsS3V2 {
	credentialsCache := aws.NewCredentialsCache(credentials.NewStaticCredentialsProvider(accessKeyID, secretAccessKey, ""))
	cfg := aws.Config{
		Region:      region,
		Credentials: credentialsCache,
	}
	s3Client := s3.NewFromConfig(cfg)
	presignClient := s3.NewPresignClient(s3Client)
	return &AwsS3V2{
		ctx:           ctx,
		S3Client:      s3Client,
		PresignClient: presignClient,
		bucketName:    bucketName,
	}
}

func (s *AwsS3V2) UploadFile(fileName string, objectKey string) (err error) {
	if objectKey == "" {
		objectKey = filepath.Base(fileName)
	}
	var (
		file *os.File
	)
	file, err = os.Open(fileName)
	if err != nil {
		return
	}
	defer func(file *os.File) {
		closeErr := file.Close()
		if closeErr != nil {
			fmt.Println(closeErr.Error())
		}
	}(file)
	// Get the content type of the file
	contentType := mime.TypeByExtension(filepath.Ext(fileName))
	if contentType == "" {
		contentType = "application/octet-stream"
	}
	// Upload the file to the S3 bucket
	_, err = s.S3Client.PutObject(s.ctx, &s3.PutObjectInput{
		Bucket:      aws.String(s.bucketName),
		Key:         aws.String(objectKey),
		Body:        file,
		ContentType: aws.String(contentType),
	})
	return
}

func (s *AwsS3V2) UploadMultipartFile(file multipart.File, key, contentType string) (err error) {
	_, err = s.S3Client.PutObject(s.ctx, &s3.PutObjectInput{
		Bucket:      aws.String(s.bucketName),
		Key:         aws.String(key),
		Body:        file,
		ContentType: aws.String(contentType),
	})
	return
}

func (s *AwsS3V2) GetObjectContent(objectKey string) (bodyBytes []byte, err error) {
	var (
		result *s3.GetObjectOutput
	)
	if result, err = s.S3Client.GetObject(s.ctx, &s3.GetObjectInput{
		Bucket: aws.String(s.bucketName),
		Key:    aws.String(objectKey),
	}); err != nil {
		return
	}
	defer func(Body io.ReadCloser) {
		closeErr := Body.Close()
		if closeErr != nil {
			fmt.Println(closeErr.Error())
		}
	}(result.Body)
	bodyBytes, err = io.ReadAll(result.Body)
	return
}

func (s *AwsS3V2) DownloadFile(objectKey string, fileName string) (err error) {
	var (
		file      *os.File
		bodyBytes []byte
	)
	if fileName == "" {
		fileName = filepath.Base(objectKey)
	}
	// Get the object content
	if bodyBytes, err = s.GetObjectContent(objectKey); err != nil {
		return
	}
	// Create the file
	if file, err = os.Create(fileName); err != nil {
		return
	}
	defer func(file *os.File) {
		closeErr := file.Close()
		if closeErr != nil {
			fmt.Println(closeErr.Error())
		}
	}(file)
	// Write the body to the file
	_, err = file.Write(bodyBytes)
	return
}

func (s *AwsS3V2) DownloadLargeObject(objectKey string) ([]byte, error) {
	var partMiBs int64 = 10
	downloader := manager.NewDownloader(s.S3Client, func(d *manager.Downloader) {
		d.PartSize = partMiBs * 1024 * 1024
	})
	buffer := manager.NewWriteAtBuffer([]byte{})
	_, err := downloader.Download(context.TODO(), buffer, &s3.GetObjectInput{
		Bucket: aws.String(s.bucketName),
		Key:    aws.String(objectKey),
	})
	if err != nil {
		return nil, err
	}
	return buffer.Bytes(), err
}

func (s *AwsS3V2) CopyToFolder(objectKey string, folderName string) error {
	_, err := s.S3Client.CopyObject(s.ctx, &s3.CopyObjectInput{
		Bucket:     aws.String(s.bucketName),
		CopySource: aws.String(fmt.Sprintf("%v/%v", s.bucketName, objectKey)),
		Key:        aws.String(fmt.Sprintf("%v/%v", folderName, objectKey)),
	})
	return err
}

func (s *AwsS3V2) CopyToBucket(sourceBucket string, destinationBucket string, objectKey string) error {
	_, err := s.S3Client.CopyObject(s.ctx, &s3.CopyObjectInput{
		Bucket:     aws.String(destinationBucket),
		CopySource: aws.String(fmt.Sprintf("%v/%v", sourceBucket, objectKey)),
		Key:        aws.String(objectKey),
	})
	return err
}

func (s *AwsS3V2) ListObjectsPrefix(prefix string) (contents []types.Object, err error) {
	inputReq := &s3.ListObjectsV2Input{
		Bucket: aws.String(s.bucketName),
		Prefix: aws.String(prefix),
	}
	paginator := s3.NewListObjectsV2Paginator(s.S3Client, inputReq)
	for paginator.HasMorePages() {
		var output *s3.ListObjectsV2Output
		output, err = paginator.NextPage(s.ctx)
		if err != nil {
			return
		}
		for _, content := range output.Contents {
			if content.Size != nil && *content.Size > 0 {
				contents = append(contents, content)
			}
		}
	}
	return contents, err
}

func (s *AwsS3V2) ListFilesInS3Path(prefix string) (filesArr []string, err error) {
	var (
		contents []types.Object
	)
	if contents, err = s.ListObjectsPrefix(prefix); err != nil {
		return
	}
	for _, content := range contents {
		filesArr = append(filesArr, *content.Key)
	}
	return
}

func (s *AwsS3V2) DeleteObjects(objectKeys []string) error {
	var objectIds []types.ObjectIdentifier
	for _, key := range objectKeys {
		objectIds = append(objectIds, types.ObjectIdentifier{Key: aws.String(key)})
	}
	_, err := s.S3Client.DeleteObjects(s.ctx, &s3.DeleteObjectsInput{
		Bucket: aws.String(s.bucketName),
		Delete: &types.Delete{Objects: objectIds},
	})
	return err
}

func (s *AwsS3V2) GetS3FileBase64(objectKey string) (string, error) {
	objectContentBytes, err := s.GetObjectContent(objectKey)
	if err != nil {
		return "", err
	}
	base64Content := base64.StdEncoding.EncodeToString(objectContentBytes)
	return base64Content, nil
}

func (s *AwsS3V2) UploadLargeObject(objectKey string, largeObject []byte) error {
	largeBuffer := bytes.NewReader(largeObject)
	var partMiBs int64 = 10
	uploader := manager.NewUploader(s.S3Client, func(u *manager.Uploader) {
		u.PartSize = partMiBs * 1024 * 1024
	})
	_, err := uploader.Upload(s.ctx, &s3.PutObjectInput{
		Bucket: aws.String(s.bucketName),
		Key:    aws.String(objectKey),
		Body:   largeBuffer,
	})
	return err
}

func (s *AwsS3V2) PutObjectTag(objectName string, tags map[string]string) (err error) {
	var (
		tagSet []types.Tag
	)
	for key, value := range tags {
		tagSet = append(tagSet, types.Tag{
			Key:   aws.String(key),
			Value: aws.String(value),
		})
	}
	_, err = s.S3Client.PutObjectTagging(s.ctx, &s3.PutObjectTaggingInput{
		Bucket: aws.String(s.bucketName),
		Key:    aws.String(objectName),
		Tagging: &types.Tagging{
			TagSet: tagSet,
		},
	})
	return
}

func (s *AwsS3V2) UploadBytesToS3(bytesData []byte, objectName, contentType string) (err error) {
	if _, err = s.S3Client.PutObject(s.ctx, &s3.PutObjectInput{
		Bucket:      aws.String(s.bucketName),
		Key:         aws.String(objectName),
		Body:        bytes.NewReader(bytesData),
		ContentType: aws.String(contentType),
	}); err != nil {
		return
	}
	return
}

func (s *AwsS3V2) FileExists(fileKey string) bool {
	_, err := s.S3Client.HeadObject(s.ctx, &s3.HeadObjectInput{
		Bucket: aws.String(s.bucketName),
		Key:    aws.String(fileKey),
	})
	if err != nil {
		return false
	}
	return true
}

func (s *AwsS3V2) PresignPutObject(objectKey, contentType string, lifetimeSecs int64) (*v4.PresignedHTTPRequest, error) {
	request, err := s.PresignClient.PresignPutObject(s.ctx, &s3.PutObjectInput{
		Bucket:      aws.String(s.bucketName),
		Key:         aws.String(objectKey),
		ContentType: aws.String(contentType),
	}, func(opts *s3.PresignOptions) {
		opts.Expires = time.Duration(lifetimeSecs * int64(time.Second))
	})
	return request, err
}

func (s *AwsS3V2) PresignPostObject(objectKey, contentType string, lifetimeSecs int64) (*s3.PresignedPostRequest, error) {
	request, err := s.PresignClient.PresignPostObject(s.ctx, &s3.PutObjectInput{
		Bucket:      aws.String(s.bucketName),
		Key:         aws.String(objectKey),
		ContentType: aws.String(contentType),
	}, func(opts *s3.PresignPostOptions) {
		opts.Expires = time.Duration(lifetimeSecs * int64(time.Second))
	})
	return request, err
}

func (s *AwsS3V2) PathObjectsCount(prefix string) (fileCount int, err error) {
	inputReq := &s3.ListObjectsV2Input{
		Bucket: aws.String(s.bucketName),
		Prefix: aws.String(prefix),
	}
	paginator := s3.NewListObjectsV2Paginator(s.S3Client, inputReq)
	for paginator.HasMorePages() {
		var output *s3.ListObjectsV2Output
		output, err = paginator.NextPage(context.TODO())
		if err != nil {
			return
		}
		fileCount += len(output.Contents)
	}
	return
}
