package utils

import (
	"regexp"
	"strconv"
	"strings"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func CheckIsEmail(email string) bool {
	// Regular expression for validating an email address
	emailRegex := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`

	re := regexp.MustCompile(emailRegex)
	return re.MatchString(email)
}

func ReplaceBracketsWithStars(text string) string {
	// 正则表达式，匹配英文括号 () 和中文括号 （）
	pattern := regexp.MustCompile(`[()（）]`)

	// 使用 ReplaceAllString 方法替换括号为 *
	modifiedText := pattern.ReplaceAllString(text, "*")

	return modifiedText
}

func CapitalizeAndAddHyphen(s string) string {
	if len(s) == 0 {
		return s
	}
	// 使用字符串切片和 ToUpper 方法
	return strings.ToUpper(s[:1]) + s[1:] + "-"
}

func StrToUint(s string) uint {
	return uint(StrToInt(s))
}

func StrToInt(s string) int {
	atoi, err := strconv.Atoi(s)
	if err != nil {
		return 0
	}
	return atoi
}

func FloatToDecimal128(floatValue float64) primitive.Decimal128 {
	floatStr := strconv.FormatFloat(floatValue, 'f', -1, 64)
	primDec, err := primitive.ParseDecimal128(floatStr)
	if err != nil {
		return primitive.NewDecimal128(0, 0)
	}
	return primDec
}

func CheckStrLen(str string, maxLen int) string {
	if len(str) >= maxLen {
		return ""
	}
	return str
}
