package utils

import (
	"aimsg-server/global"
	"context"
	"fmt"
	"git.costnovel.com/center/middleware-sdk/risk"
	"net"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type IpGeoRes struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Accuracy  string `json:"accuracy"`
		Adcode    string `json:"adcode"`
		Areacode  string `json:"areacode"`
		Asnumber  string `json:"asnumber"`
		City      string `json:"city"`
		Continent string `json:"continent"`
		Country   string `json:"country"`
		Isp       string `json:"isp"`
		Latwgs    string `json:"latwgs"`
		Lngwgs    string `json:"lngwgs"`
		Owner     string `json:"owner"`
		Province  string `json:"province"`
		Radius    string `json:"radius"`
		Source    string `json:"source"`
		Timezone  string `json:"timezone"`
		Zipcode   string `json:"zipcode"`
	} `json:"data"`
}

func GetIpInfoByIpGeoApi(ip string) (ipInfo IpGeoRes, err error) {
	var (
		apiLog  string
		curlStr string
		reqUrl  = fmt.Sprintf("https://game-geo.51payment.live/geo/ip/query?ip=%s", ip)
	)
	if ip == "127.0.0.1" {
		return
	}
	if apiLog, curlStr, err = HttpGet(reqUrl, &ipInfo, 10); err != nil {
		err = fmt.Errorf("请求日志:%s,CURL请求:%s, 获取ip信息失败 %v", apiLog, curlStr, err)
		return
	}
	if ipInfo.Code != 0 {
		err = fmt.Errorf("获取ip信息失败,Code != 0")
		return
	}
	return
}

func CheckIpAudit(loginIp string, ipInfo IpGeoRes) bool {
	if loginIp == "127.0.0.1" {
		return false
	}
	if loginIp == "" {
		return false
	}
	return checkIp(loginIp, ipInfo)
}

func CheckIpAuditMiddleware(ctx context.Context, req risk.ReviewerDataReq) bool {
	reviewerData, err := risk.IsReviewer(ctx, true, req)
	if err != nil {
		return false
	}
	return reviewerData.IsReviewer
}

func SyncRiskDataMiddleware(ctx context.Context, req risk.ReviewerDataReq) {
	defer func() {
		if rErr := recover(); rErr != nil {
			global.LOG.Error("SyncRiskDataMiddleware panic", zap.Any("panic", rErr))
		}
	}()
	err := risk.SyncRiskData(ctx, true, req)
	if err != nil {
		return
	}
	return
}

func checkIp(loginIp string, ipInfo IpGeoRes) bool {
	// 获取ip的ASN
	ipAsn := strings.ToLower(ipInfo.Data.Isp)
	ipOwner := strings.ToLower(ipInfo.Data.Owner)
	if !strings.Contains(ipAsn, "google fiber inc") {
		if strings.Contains(ipAsn, "google") {
			return true
		}
	}
	if strings.Contains(ipAsn, "apple") {
		return true
	}
	if strings.Contains(ipOwner, "apple") {
		return true
	}
	// 判断是否是指定的ip段或者曾经的Google测试人员的ip
	if CheckIpInGoogleIpRange(loginIp) {
		return true
	}
	// 判断是否是谷歌的ip
	isGoo := global.GooIpMap[loginIp]
	if isGoo {
		return true
	}
	return false
}

func CheckIpInGoogleIpRange(loginIp string) (audit bool) {
	// 判断loginIp是否在google的ip范围内
	// 解析要检查的IP地址
	checkIP := net.ParseIP(loginIp)
	if checkIP == nil {
		fmt.Println("Invalid IP address")
		return false
	}
	// 检查IP地址是否在Google的IP范围内
	for _, r := range global.GoogleIpRanges {
		if bytesInRange(checkIP, r.Start, r.End) {
			return true
		}
	}
	return false
}

// bytesInRange 检查IP地址是否在给定的范围内
func bytesInRange(ip, start, end net.IP) bool {
	return bytesLessThanOrEqual(start, ip) && bytesLessThanOrEqual(ip, end)
}

// bytesLessThanOrEqual 检查两个IP地址的字节大小关系
func bytesLessThanOrEqual(a, b net.IP) bool {
	aBytes := a.To4()
	bBytes := b.To4()
	// 如果任意一个地址不是有效的 IPv4 地址，返回 false
	if aBytes == nil || bBytes == nil {
		return false
	}
	for i := 0; i < 4; i++ {
		if aBytes[i] < bBytes[i] {
			return true
		} else if aBytes[i] > bBytes[i] {
			return false
		}
	}
	return true
}

func GetCountryCode(c *gin.Context) string {
	var (
		loginIp = c.ClientIP()
	)
	if loginIp == "127.0.0.1" {
		return "US"
	}
	ipInfo, ipGeoErr := GetIpInfoByIpGeoApi(loginIp)
	if ipGeoErr != nil {
		global.LOG.Error("GetIpInfoByIpGeoApi error", zap.Error(ipGeoErr))
	} else {
		return ipInfo.Data.Areacode
	}
	// 最终才是从header头里面拿
	return c.GetHeader("Cf-Ipcountry")
}
