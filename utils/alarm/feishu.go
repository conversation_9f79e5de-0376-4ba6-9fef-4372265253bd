package alarm

import (
	"fmt"
	"github.com/CatchZeng/feishu/pkg/feishu"
)

func P0(data ...any) {
	if len(data) < 2 {
		return
	}
	token := "fb017d06-f6e9-45e9-9545-672b59d9120b"
	secret := "pIxsl8GCFfjJFBIa9zdszf"

	client := feishu.NewClient(token, secret)
	var line []feishu.PostItem

	l2 := data[1:]
	for _, item := range l2 {
		line = append(line, feishu.NewText(fmt.Sprintf("%v\r\n\r\n\r\n", item)))
	}
	msg := feishu.NewPostMessage()
	msg.SetZHTitle(fmt.Sprintf("%s", data[0])).
		AppendZHContent(line)

	client.Send(msg)
}
