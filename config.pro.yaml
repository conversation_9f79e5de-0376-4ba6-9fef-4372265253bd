vita:
  url: https://api.okjannu.com
aliyun-oss:
  endpoint: yourEndpoint
  access-key-id: yourAccessKeyId
  access-key-secret: yourAccessKeySecret
  bucket-name: yourBucketName
  bucket-url: yourBucketUrl
  base-path: yourBasePath
autocode:
  server-model: /model/%s
  server-router: /router/%s
  server: /aimsg-server
  server-api: /api/v1/%s
  server-plug: /plugin/%s
  server-initialize: /initialize
  root: /Users/<USER>/code/kxcl/cashbox-ai
  web-table: /view/backend
  web: /aimsg-web/src
  server-service: /service/%s
  server-request: /model/%s/request/
  web-api: /api/backend
  web-form: /view/backend
  transfer-restart: true
aws-s3:
  bucket: cashbox-ai
  region: us-west-2
  endpoint: ""
  secret-id: ********************
  secret-key: pQKuk9WRNdgwWf6WF3HPISNd6aGsbWBhGxTI5aMw
  base-url: https://images.aipersona.cloud/
  path-prefix: aimsg-server
  s3-force-path-style: false
  disable-ssl: false
captcha:
  key-long: 4
  img-width: 240
  img-height: 80
  open-captcha: 0
  open-captcha-timeout: 3600
cors:
  mode: strict-whitelist
  whitelist:
    - allow-origin: example1.com
      allow-methods: POST, GET
      allow-headers: Content-Type,AccessToken,X-CSRF-Token, Authorization, Token,X-Token,X-User-Id
      expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
      allow-credentials: true
    - allow-origin: example2.com
      allow-methods: GET, POST
      allow-headers: content-type
      expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
      allow-credentials: true
db-list:
  - type: ""
    alias-name: ""
    prefix: ""
    port: ""
    config: ""
    db-name: ""
    username: ""
    password: ""
    path: ""
    engine: ""
    log-mode: ""
    max-idle-conns: 10
    max-open-conns: 100
    singular: false
    log-zap: false
    disable: true
dd_notify:
  token: "84a42f1975117d9d36c0c9214171ab01f4ae185a532f8e23395ff1dabeb3dfde"
  secret: "SECb56afad25acaca25f23417eeab2066faae9de94d1d529d65d69b3f600f1833b9"
  payout-bot-id: "89ef3581-b2ee-4768-b4d0-bea91901efef"
diy_dd:
  token: "a2a5d336a2478284b407c23a85fbb1ae25e0722bdd5f6dfac23a065967b4c785"
  secret: "SECee1b71468d366235cab81a8f89474a654c6da5af94a3474146b50152fdfc759a"
email:
  to: <EMAIL>
  from: <EMAIL>
  host: smtp.163.com
  secret: xxx
  nickname: test
  port: 465
  is-ssl: true
excel:
  dir: ./resource/excel/
hua-wei-obs:
  path: you-path
  bucket: you-bucket
  endpoint: you-endpoint
  access-key: you-access-key
  secret-key: you-secret-key
jwt:
  signing-key: IvQHEpQDKaFAnJGbwVcJN1hrXXkrRCZJuWCqWvHprjhWlgZX4tugbDns55KRUejN
  expires-time: 30d
  buffer-time: 1d
  issuer: "go-server"
local:
  path: uploads/file
  store-path: uploads/file
mongo:
  coll: "msg_history_0403"
  options: ""
  database: "ai-btc"
  username: "cashbox"
  password: "cashbox"
  auth-source: ""
  min-pool-size: 0
  max-pool-size: 100
  socket-timeout-ms: 0
  connect-timeout-ms: 0
  is-zap: false
  hosts:
    - host: "*************"
      port: "27017"
mssql:
  prefix: ""
  port: ""
  config: ""
  db-name: ""
  username: ""
  password: ""
  path: ""
  engine: ""
  log-mode: ""
  max-idle-conns: 10
  max-open-conns: 100
  singular: false
  log-zap: false
mysql:
  prefix: ""
  port: 3306
  config: charset=utf8mb4&parseTime=True&loc=Local
  db-name: digital_clone
  username: admin
  password: DuA9tSic0xrxlRpabD8Q
  path: btc.cyakkzolh53i.us-west-2.rds.amazonaws.com
  engine: ""
  log-mode: error
  max-idle-conns: 20
  max-open-conns: 200
  singular: false
  log-zap: true
oracle:
  prefix: ""
  port: ""
  config: ""
  db-name: ""
  username: ""
  password: ""
  path: ""
  engine: ""
  log-mode: ""
  max-idle-conns: 10
  max-open-conns: 100
  singular: false
  log-zap: false
pgsql:
  prefix: ""
  port: ""
  config: ""
  db-name: ""
  username: ""
  password: ""
  path: ""
  engine: ""
  log-mode: ""
  max-idle-conns: 10
  max-open-conns: 100
  singular: false
  log-zap: false
qiniu:
  zone: ZoneHuaDong
  bucket: ""
  img-path: ""
  access-key: ""
  secret-key: ""
  use-https: false
  use-cdn-domains: false
redis:
  addr: master.btc.bo51va.usw2.cache.amazonaws.com:6379
  password: "MKg9sU8WYddCQFN7"
  db: 0
sqlite:
  prefix: ""
  port: ""
  config: ""
  db-name: ""
  username: ""
  password: ""
  path: ""
  engine: ""
  log-mode: ""
  max-idle-conns: 10
  max-open-conns: 100
  singular: false
  log-zap: false
system:
  env: public
  name: "Cloud-Server"
  url_prefix: https://server.btcminingturbo.com/api
  db-type: mysql
  oss-type: local
  router-prefix: "api"
  addr: 8400
  iplimit-count: 15000
  iplimit-time: 3600
  use-multipoint: false
  use-redis: true
  use-mongo: false
tencent-cos:
  bucket: xxxxx-10005608
  region: ap-shanghai
  secret-id: your-secret-id
  secret-key: your-secret-key
  base-url: https://gin.vue.admin
  path-prefix: aimsg-server
zap:
  level: info
  prefix: '[aimsg-server]'
  format: console
  director: log
  encode-level: LowercaseColorLevelEncoder
  stacktrace-key: stacktrace
  max-age: 0
  show-line: true
  log-in-console: true
kafka:
  host:
    - logbus-kafka
ab_test:
  fix_hash_rate_register_from:  2069490848
